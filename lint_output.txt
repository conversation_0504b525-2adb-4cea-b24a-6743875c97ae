
> expo-app@2.0.9 lint
> eslint . --ext .ts,.tsx,.js,.jsx --ignore-pattern 'scripts/' --ignore-pattern 'supabase/functions/' --ignore-pattern '*.config.js'

(node:67214) ESLintIgnoreWarning: The ".eslintignore" file is no longer supported. Switch to using the "ignores" property in "eslint.config.js": https://eslint.org/docs/latest/use/configure/migration-guide#ignoring-files
(Use `node --trace-warnings ...` to show where the warning was created)

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/animation/DelightfulButton.tsx
  148:11  warning  Inline style: { marginLeft: 'icon ? BeautyMinimalTheme.spacing.xs : 0' }  react-native/no-inline-styles

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/animation/FormulaGenerationLoading.tsx
  258:23  error  Replace `⏎··········style={[styles.chemicalBubble,·styles.bubble1Position,·bubbleStyle1]}⏎·······` with `·style={[styles.chemicalBubble,·styles.bubble1Position,·bubbleStyle1]}`  prettier/prettier
  261:23  error  Replace `⏎··········style={[styles.chemicalBubble,·styles.bubble2Position,·bubbleStyle2]}⏎·······` with `·style={[styles.chemicalBubble,·styles.bubble2Position,·bubbleStyle2]}`  prettier/prettier
  264:23  error  Replace `⏎··········style={[styles.chemicalBubble,·styles.bubble3Position,·bubbleStyle3]}⏎·······` with `·style={[styles.chemicalBubble,·styles.bubble3Position,·bubbleStyle3]}`  prettier/prettier

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/animation/PhotoAnalysisLoading.tsx
  206:19  error    Replace `⏎············styles.scanLine,⏎············{·backgroundColor:·stageConfig.color·},⏎············scanLineStyle,⏎··········` with `styles.scanLine,·{·backgroundColor:·stageConfig.color·},·scanLineStyle`  prettier/prettier
  259:11  warning  Inline style: { borderWidth: 3 }                                                                                                                                                                                 react-native/no-inline-styles

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/camera/CameraCapture.tsx
  237:6  warning  React Hook useCallback has a missing dependency: 'targetPulse'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/chat/ChatGPTInterface.tsx
  234:6  warning  React Hook useEffect has a missing dependency: 'contextData'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/chat/ConversationsList.tsx
  373:5  warning  React Hook useCallback has a missing dependency: 'ConversationItem'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/chat/OptimisticExample.tsx
   30:16  error    'Text' is defined but never used. Allowed unused vars must match /^_/u                                                        @typescript-eslint/no-unused-vars
   37:13  error    'Haptics' is defined but never used. Allowed unused vars must match /^_/u                                                     @typescript-eslint/no-unused-vars
   80:13  error    'newState' is defined but never used. Allowed unused args must match /^_/u                                                    @typescript-eslint/no-unused-vars
  117:18  warning  Inline style: {
  position: 'absolute',
  top: -2,
  right: -2,
  width: 6,
  height: 6,
  borderRadius: 3,
  opacity: 0.7
}  react-native/no-inline-styles

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/chat/OptimisticFeedback.tsx
   8:3   error  'interpolate' is defined but never used. Allowed unused vars must match /^_/u  @typescript-eslint/no-unused-vars
  10:10  error  'Wifi' is defined but never used. Allowed unused vars must match /^_/u         @typescript-eslint/no-unused-vars

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/client-history/CompactFormulaDisplay.tsx
  128:5  error  'onPress' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/client-history/ServiceHistoryCard.tsx
    9:3  error    'Clock' is defined but never used. Allowed unused vars must match /^_/u                                                                                                                    @typescript-eslint/no-unused-vars
   99:6  warning  React Hook useEffect has missing dependencies: 'badgesScale', 'cardScale', 'editButtonOpacity', 'editButtonScale', and 'starsOpacity'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  617:3  error    Unused style detected: styles.detailsContainer                                                                                                                                             react-native/no-unused-styles
  622:3  error    Unused style detected: styles.detailItem                                                                                                                                                   react-native/no-unused-styles
  627:3  error    Unused style detected: styles.detailLabel                                                                                                                                                  react-native/no-unused-styles
  632:3  error    Unused style detected: styles.detailText                                                                                                                                                   react-native/no-unused-styles

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/client-history/__tests__/CompactFormulaDisplay.test.tsx
  23:22  error  Require statement not part of import statement  @typescript-eslint/no-var-requires

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/debug/BrandLinesDebug.tsx
  213:15  warning  Color literal: { color: 'white' }  react-native/no-color-literals
  250:28  warning  Color literal: { color: 'white' }  react-native/no-color-literals

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/feedback/FeedbackTabContent.tsx
   31:3   error    'interpolate' is defined but never used. Allowed unused vars must match /^_/u                                                                               @typescript-eslint/no-unused-vars
   32:3   error    'runOnJS' is defined but never used. Allowed unused vars must match /^_/u                                                                                   @typescript-eslint/no-unused-vars
   59:3   error    'clientId' is defined but never used. Allowed unused args must match /^_/u                                                                                  @typescript-eslint/no-unused-vars
   82:9   error    'pullToRefreshProgress' is assigned a value but never used. Allowed unused vars must match /^_/u                                                            @typescript-eslint/no-unused-vars
   91:6   warning  React Hook React.useEffect has a missing dependency: 'statsCardScale'. Either include it or remove the dependency array                                     react-hooks/exhaustive-deps
  208:13  error    'serviceForModal' is assigned a value but never used. Allowed unused vars must match /^_/u                                                                  @typescript-eslint/no-unused-vars
  367:28  error    React Hook "useAnimatedStyle" cannot be called inside a callback. React Hooks must be called in a React function component or a custom React Hook function  react-hooks/rules-of-hooks
  490:28  error    React Hook "useAnimatedStyle" cannot be called inside a callback. React Hooks must be called in a React function component or a custom React Hook function  react-hooks/rules-of-hooks
  516:22  error    React Hook "useAnimatedStyle" is called conditionally. React Hooks must be called in the exact same order in every component render                         react-hooks/rules-of-hooks
  522:26  error    React Hook "useAnimatedStyle" is called conditionally. React Hooks must be called in the exact same order in every component render                         react-hooks/rules-of-hooks
  582:22  error    React Hook "useAnimatedStyle" is called conditionally. React Hooks must be called in the exact same order in every component render                         react-hooks/rules-of-hooks
  588:26  error    React Hook "useAnimatedStyle" is called conditionally. React Hooks must be called in the exact same order in every component render                         react-hooks/rules-of-hooks

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/formulation/InstructionsFlow.new.tsx
   16:10  error    'Ionicons' is defined but never used. Allowed unused vars must match /^_/u              @typescript-eslint/no-unused-vars
   29:3   error    'MaterialsList' is defined but never used. Allowed unused vars must match /^_/u         @typescript-eslint/no-unused-vars
   30:3   error    'TimerCard' is defined but never used. Allowed unused vars must match /^_/u             @typescript-eslint/no-unused-vars
   31:3   error    'ZoneVisualizer' is defined but never used. Allowed unused vars must match /^_/u        @typescript-eslint/no-unused-vars
   45:10  error    'HairZone' is defined but never used. Allowed unused vars must match /^_/u              @typescript-eslint/no-unused-vars
   45:20  error    'HairZoneDisplay' is defined but never used. Allowed unused vars must match /^_/u       @typescript-eslint/no-unused-vars
   47:10  error    'commonStyles' is defined but never used. Allowed unused vars must match /^_/u          @typescript-eslint/no-unused-vars
   54:43  warning  Unexpected any. Specify a different type                                                @typescript-eslint/no-explicit-any
  129:3   error    'initialStep' is assigned a value but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/formulation/InstructionsFlow.tsx
   16:10  error    'Ionicons' is defined but never used. Allowed unused vars must match /^_/u              @typescript-eslint/no-unused-vars
   29:3   error    'MaterialsList' is defined but never used. Allowed unused vars must match /^_/u         @typescript-eslint/no-unused-vars
   30:3   error    'TimerCard' is defined but never used. Allowed unused vars must match /^_/u             @typescript-eslint/no-unused-vars
   31:3   error    'ZoneVisualizer' is defined but never used. Allowed unused vars must match /^_/u        @typescript-eslint/no-unused-vars
   45:10  error    'HairZone' is defined but never used. Allowed unused vars must match /^_/u              @typescript-eslint/no-unused-vars
   45:20  error    'HairZoneDisplay' is defined but never used. Allowed unused vars must match /^_/u       @typescript-eslint/no-unused-vars
   47:10  error    'commonStyles' is defined but never used. Allowed unused vars must match /^_/u          @typescript-eslint/no-unused-vars
   54:43  warning  Unexpected any. Specify a different type                                                @typescript-eslint/no-explicit-any
  129:3   error    'initialStep' is assigned a value but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/formulation/InstructionsFlowMonitoring.tsx
   27:21  error    'BarChart' is defined but never used. Allowed unused vars must match /^_/u                                            @typescript-eslint/no-unused-vars
   28:1   error    'react-native' import is duplicated                                                                                   no-duplicate-imports
   94:25  error    'setAbTestResults' is assigned a value but never used. Allowed unused vars must match /^_/u                           @typescript-eslint/no-unused-vars
  114:6   warning  React Hook useEffect has a missing dependency: 'loadDashboardData'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  135:7   warning  Unexpected console statement                                                                                          no-console
  158:22  error    'error' is defined but never used                                                                                     @typescript-eslint/no-unused-vars
  172:14  error    'error' is defined but never used                                                                                     @typescript-eslint/no-unused-vars
  181:14  error    'error' is defined but never used                                                                                     @typescript-eslint/no-unused-vars

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/formulation/InstructionsFlowWrapper.tsx
   30:1   error    './InstructionsFlow.new' import is duplicated  no-duplicate-imports
   56:5   warning  Unexpected console statement                   no-console
   67:7   warning  Unexpected console statement                   no-console
  171:11  warning  Unexpected console statement                   no-console
  177:9   warning  Unexpected console statement                   no-console
  195:5   warning  Unexpected console statement                   no-console
  201:44  warning  Unexpected any. Specify a different type       @typescript-eslint/no-explicit-any
  210:7   warning  Unexpected console statement                   no-console

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/formulation/StepDetailCard.tsx
  189:14  warning  Color literal: { borderColor: 'transparent' }  react-native/no-color-literals

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/formulation/__tests__/InstructionsFlow.basic.test.tsx
  35:26  error  Require statement not part of import statement  @typescript-eslint/no-var-requires
  41:30  error  Require statement not part of import statement  @typescript-eslint/no-var-requires
  47:26  error  Require statement not part of import statement  @typescript-eslint/no-var-requires

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/formulation/__tests__/InstructionsFlow.integration.test.tsx
   20:8   error    'InstructionsFlowLegacy' is defined but never used. Allowed unused vars must match /^_/u                @typescript-eslint/no-unused-vars
  127:13  error    'OriginalInstructionsFlowNew' is assigned a value but never used. Allowed unused vars must match /^_/u  @typescript-eslint/no-unused-vars
  282:15  error    'rerender' is assigned a value but never used. Allowed unused vars must match /^_/u                     @typescript-eslint/no-unused-vars
  308:18  error    'userId' is assigned a value but never used. Allowed unused vars must match /^_/u                       @typescript-eslint/no-unused-vars
  311:50  warning  Unexpected `await` inside a loop                                                                        no-await-in-loop

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/formulation/__tests__/InstructionsFlow.simple.test.tsx
   84:21  error  Require statement not part of import statement                           @typescript-eslint/no-var-requires
   85:18  error  Require statement not part of import statement                           @typescript-eslint/no-var-requires
   89:35  error  'props' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars
  169:26  error  Require statement not part of import statement                           @typescript-eslint/no-var-requires
  170:30  error  Require statement not part of import statement                           @typescript-eslint/no-var-requires

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/formulation/instructions/components/TimerCard.tsx
   82:6   warning  React Hook useEffect has a missing dependency: 'pulseAnim'. Either include it or remove the dependency array               react-hooks/exhaustive-deps
  240:17  warning  Color literal: {
  borderTopColor: 'transparent',
  borderRightColor: 'transparent',
  borderBottomColor: 'transparent'
}  react-native/no-color-literals

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/formulation/instructions/components/ZoneVisualizer.tsx
    5:10  error    'commonStyles' is defined but never used. Allowed unused vars must match /^_/u  @typescript-eslint/no-unused-vars
  143:51  warning  Inline style: { opacity: 1 }                                                    react-native/no-inline-styles
  147:51  warning  Inline style: { opacity: 0.6 }                                                  react-native/no-inline-styles
  233:19  warning  Color literal: { borderColor: 'white' }                                         react-native/no-color-literals
  242:15  warning  Color literal: { color: 'white' }                                               react-native/no-color-literals

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/formulation/instructions/demo/StepComponents.demo.tsx
  134:29  warning  Unexpected console statement       no-console
  155:31  warning  Unexpected console statement       no-console
  273:23  warning  Color literal: { color: 'white' }  react-native/no-color-literals
  295:25  warning  Color literal: { color: 'white' }  react-native/no-color-literals

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/formulation/instructions/steps/ApplicationStep.tsx
   18:10  error    'HairZone' is defined but never used. Allowed unused vars must match /^_/u         @typescript-eslint/no-unused-vars
   18:20  error    'HairZoneDisplay' is defined but never used. Allowed unused vars must match /^_/u  @typescript-eslint/no-unused-vars
   31:9   warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
   35:10  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
   37:11  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  237:22  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  240:25  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/formulation/instructions/steps/CompletionStep.tsx
    2:34  error    'Animated' is defined but never used. Allowed unused vars must match /^_/u          @typescript-eslint/no-unused-vars
    2:44  error    'TouchableOpacity' is defined but never used. Allowed unused vars must match /^_/u  @typescript-eslint/no-unused-vars
  225:14  warning  Color literal: { color: 'white' }                                                   react-native/no-color-literals
  232:17  warning  Color literal: { color: 'white' }                                                   react-native/no-color-literals
  266:15  warning  Color literal: { color: 'white' }                                                   react-native/no-color-literals
  274:17  warning  Color literal: { backgroundColor: 'white' }                                         react-native/no-color-literals
  280:14  warning  Color literal: { color: 'white' }                                                   react-native/no-color-literals
  378:20  warning  Color literal: { color: 'white' }                                                   react-native/no-color-literals
  386:23  warning  Color literal: { color: 'white' }                                                   react-native/no-color-literals
  413:22  warning  Color literal: { color: 'white' }                                                   react-native/no-color-literals

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/formulation/instructions/steps/RinsingStep.tsx
   24:29  warning  Unexpected any. Specify a different type                                       @typescript-eslint/no-explicit-any
   29:3   error    'formulaData' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars
  247:18  warning  Inline style: { opacity: 0.1 }                                                 react-native/no-inline-styles

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/hair/HairConditionIndicator.tsx
  78:6  warning  React Hook React.useEffect has missing dependencies: 'glowAnim', 'pulseAnim', and 'scaleAnim'. Either include them or remove the dependency array  react-hooks/exhaustive-deps

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/hair/HairLevelIndicator.tsx
   50:6   warning  React Hook React.useEffect has missing dependencies: 'progressAnim' and 'scaleAnim'. Either include them or remove the dependency array                                              react-hooks/exhaustive-deps
  120:17  warning  Inline style: {
  color: "clampedLevel <= 4 ? '#FFFFFF' : '#1F2937'",
  textShadowColor: "clampedLevel <= 4 ? 'rgba(0,0,0,0.3)' : 'rgba(255,255,255,0.5)'",
  textShadowRadius: 2
}  react-native/no-inline-styles
  120:17  warning  Color literal: {
  color: "clampedLevel <= 4 ? '#FFFFFF' : '#1F2937'",
  textShadowColor: "clampedLevel <= 4 ? 'rgba(0,0,0,0.3)' : 'rgba(255,255,255,0.5)'"
}                        react-native/no-color-literals

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/hair/HairToneIndicator.tsx
  76:6  warning  React Hook React.useEffect has missing dependencies: 'glowAnim', 'rotationAnim', and 'scaleAnim'. Either include them or remove the dependency array  react-hooks/exhaustive-deps

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/reports/InventoryReports.tsx
  277:13  warning  Color literal: { backgroundColor: 'rgba(255, 255, 255, 0.2)' }  react-native/no-color-literals

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/ui/ModernChatInterface.tsx
  120:6   warning  React Hook useEffect has a missing dependency: 'attachmentScale'. Either include it or remove the dependency array    react-hooks/exhaustive-deps
  134:6   warning  React Hook useCallback has a missing dependency: 'sendButtonScale'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  451:29  warning  Unexpected any. Specify a different type                                                                              @typescript-eslint/no-explicit-any

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/ui/SkeletonClientProfile.tsx
  11:10  error    'BeautyMinimalTheme' is defined but never used. Allowed unused vars must match /^_/u  @typescript-eslint/no-unused-vars
  45:82  warning  Unexpected any. Specify a different type                                              @typescript-eslint/no-explicit-any

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/ui/SkeletonServiceCard.tsx
  43:82  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/ui/VisualDiagnosisStep.tsx
  584:18  warning  Color literal: { borderColor: 'transparent' }  react-native/no-color-literals

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/examples/HooksTestingExamples.test.ts
  466:11  error  'now' is assigned a value but never used. Allowed unused vars must match /^_/u  @typescript-eslint/no-unused-vars

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/examples/RefactoredInstructionsFlow.tsx
   39:10  error    'commonStyles' is defined but never used. Allowed unused vars must match /^_/u                                                                                                    @typescript-eslint/no-unused-vars
  232:30  error    'phaseIndex' is defined but never used. Allowed unused args must match /^_/u                                                                                                      @typescript-eslint/no-unused-vars
  233:9   warning  Unexpected console statement                                                                                                                                                      no-console
  236:32  error    'phaseIndex' is defined but never used. Allowed unused args must match /^_/u                                                                                                      @typescript-eslint/no-unused-vars
  237:9   warning  Unexpected console statement                                                                                                                                                      no-console
  241:9   warning  Unexpected console statement                                                                                                                                                      no-console
  256:7   warning  Unexpected console statement                                                                                                                                                      no-console
  261:7   warning  Unexpected console statement                                                                                                                                                      no-console
  272:6   warning  React Hook useEffect has a missing dependency: 'progressTracking'. Either include it or remove the dependency array                                                               react-hooks/exhaustive-deps
  296:6   warning  React Hook useEffect has missing dependencies: 'hapticFeedback', 'instructionFlow', 'progressTracking', and 'stepValidation'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  330:7   warning  Unexpected console statement                                                                                                                                                      no-console
  341:9   error    'handleChecklistItemToggle' is assigned a value but never used. Allowed unused vars must match /^_/u                                                                              @typescript-eslint/no-unused-vars
  556:15  warning  Color literal: { color: 'white' }                                                                                                                                                 react-native/no-color-literals
  561:14  warning  Color literal: { color: 'white' }                                                                                                                                                 react-native/no-color-literals
  572:17  warning  Color literal: { color: 'white' }                                                                                                                                                 react-native/no-color-literals
  577:16  warning  Color literal: { color: 'white' }                                                                                                                                                 react-native/no-color-literals
  588:14  warning  Color literal: { color: 'white' }                                                                                                                                                 react-native/no-color-literals
  593:15  warning  Color literal: { color: 'white' }                                                                                                                                                 react-native/no-color-literals
  637:20  warning  Color literal: { color: 'white' }                                                                                                                                                 react-native/no-color-literals
  676:11  warning  Color literal: { backgroundColor: 'white' }                                                                                                                                       react-native/no-color-literals
  700:18  warning  Color literal: { color: 'white' }                                                                                                                                                 react-native/no-color-literals

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/examples/inventory-analytics-usage.tsx
   24:5   error    'updateReportSettings' is assigned a value but never used. Allowed unused vars must match /^_/u  @typescript-eslint/no-unused-vars
   65:24  warning  Inline style: { flex: 1, padding: 16 }                                                           react-native/no-inline-styles
   66:20  warning  Inline style: { fontSize: 24, fontWeight: 'bold', marginBottom: 20 }                             react-native/no-inline-styles
   71:20  warning  Inline style: { marginBottom: 20 }                                                               react-native/no-inline-styles
   72:22  warning  Inline style: { fontSize: 18, fontWeight: 'bold', marginBottom: 10 }                             react-native/no-inline-styles
   90:20  warning  Inline style: { marginBottom: 20 }                                                               react-native/no-inline-styles
   91:22  warning  Inline style: { fontSize: 18, fontWeight: 'bold', marginBottom: 10 }                             react-native/no-inline-styles
   98:38  warning  Inline style: { padding: 8, backgroundColor: '#fff3cd', marginBottom: 4 }                        react-native/no-inline-styles
   98:38  warning  Color literal: { backgroundColor: '#fff3cd' }                                                    react-native/no-color-literals
  114:20  warning  Inline style: { marginBottom: 20 }                                                               react-native/no-inline-styles
  115:22  warning  Inline style: { fontSize: 18, fontWeight: 'bold', marginBottom: 10 }                             react-native/no-inline-styles
  132:20  warning  Inline style: { marginBottom: 20 }                                                               react-native/no-inline-styles
  133:22  warning  Inline style: { fontSize: 18, fontWeight: 'bold', marginBottom: 10 }                             react-native/no-inline-styles
  135:36  warning  Inline style: { padding: 8, backgroundColor: '#f8f9fa', marginBottom: 4 }                        react-native/no-inline-styles
  135:36  warning  Color literal: { backgroundColor: '#f8f9fa' }                                                    react-native/no-color-literals
  145:20  warning  Inline style: { marginBottom: 20 }                                                               react-native/no-inline-styles
  146:22  warning  Inline style: { fontSize: 18, fontWeight: 'bold', marginBottom: 10 }                             react-native/no-inline-styles
  165:20  warning  Inline style: { marginBottom: 20 }                                                               react-native/no-inline-styles
  166:22  warning  Inline style: { fontSize: 18, fontWeight: 'bold', marginBottom: 10 }                             react-native/no-inline-styles
  210:5   error    'products' is assigned a value but never used. Allowed unused vars must match /^_/u              @typescript-eslint/no-unused-vars
  211:5   error    'movements' is assigned a value but never used. Allowed unused vars must match /^_/u             @typescript-eslint/no-unused-vars
  215:5   error    'loadInventoryReport' is assigned a value but never used. Allowed unused vars must match /^_/u   @typescript-eslint/no-unused-vars
  216:5   error    'clearInventoryReport' is assigned a value but never used. Allowed unused vars must match /^_/u  @typescript-eslint/no-unused-vars

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/hooks/useAnimations.ts
    8:12  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  118:70  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/hooks/useHapticFeedback.ts
  232:11  error    'impact' is assigned a value but never used. Allowed unused vars must match /^_/u  @typescript-eslint/no-unused-vars
  260:9   warning  Unexpected console statement                                                       no-console
  273:11  warning  Unexpected `await` inside a loop                                                   no-await-in-loop
  275:9   warning  Unexpected `await` inside a loop                                                   no-await-in-loop

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/hooks/useInstructionFlow.ts
  129:6  warning  React Hook useEffect has missing dependencies: 'autoSave', 'flowState.startTime', and 'loadProgress'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  140:6  warning  React Hook useEffect has a missing dependency: 'saveProgress'. Either include it or remove the dependency array                                           react-hooks/exhaustive-deps
  197:6  warning  React Hook useCallback has a missing dependency: 'completeFlow'. Either include it or remove the dependency array                                         react-hooks/exhaustive-deps
  240:6  warning  React Hook useCallback has an unnecessary dependency: 'flowState.currentStep'. Either exclude it or remove the dependency array                           react-hooks/exhaustive-deps
  338:7  warning  Unexpected console statement                                                                                                                              no-console
  355:7  warning  Unexpected console statement                                                                                                                              no-console
  372:7  warning  Unexpected console statement                                                                                                                              no-console

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/hooks/useOptimistic.ts
  186:15  error  'optimisticArray' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars
  221:15  error  'optimisticArray' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/hooks/useProgressTracking.ts
  203:9  warning  The 'finalWeights' object makes the dependencies of useMemo Hook (at line 324) change on every render. Move it inside the useMemo callback. Alternatively, wrap the initialization of 'finalWeights' in its own useMemo() Hook  react-hooks/exhaustive-deps
  347:6  warning  React Hook useEffect has a missing dependency: 'saveProgress'. Either include it or remove the dependency array                                                                                                                 react-hooks/exhaustive-deps
  423:6  warning  React Hook useCallback has an unnecessary dependency: 'progressState.milestones'. Either exclude it or remove the dependency array                                                                                              react-hooks/exhaustive-deps
  531:7  warning  Unexpected console statement                                                                                                                                                                                                    no-console
  549:7  warning  Unexpected console statement                                                                                                                                                                                                    no-console
  557:7  warning  Unexpected console statement                                                                                                                                                                                                    no-console
  587:6  warning  React Hook useEffect has a missing dependency: 'loadProgress'. Either include it or remove the dependency array                                                                                                                 react-hooks/exhaustive-deps
  594:6  warning  React Hook useEffect has a missing dependency: 'checkMilestones'. Either include it or remove the dependency array                                                                                                              react-hooks/exhaustive-deps

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/hooks/useStepTimer.ts
  167:6   warning  React Hook useEffect has a missing dependency: 'saveState'. Either include it or remove the dependency array                                                                                                                                                                     react-hooks/exhaustive-deps
  270:6   warning  React Hook useCallback has missing dependencies: 'config' and 'timerState.currentPhase'. Either include them or remove the dependency array                                                                                                                                      react-hooks/exhaustive-deps
  337:6   warning  React Hook useCallback has a missing dependency: 'clearState'. Either include it or remove the dependency array                                                                                                                                                                  react-hooks/exhaustive-deps
  478:7   warning  Unexpected console statement                                                                                                                                                                                                                                                     no-console
  504:7   warning  Unexpected console statement                                                                                                                                                                                                                                                     no-console
  512:7   warning  Unexpected console statement                                                                                                                                                                                                                                                     no-console
  522:24  warning  The ref value 'alertTimeoutsRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'alertTimeoutsRef.current' to a variable inside the effect, and use that variable in the cleanup function  react-hooks/exhaustive-deps
  531:6   warning  React Hook useEffect has a missing dependency: 'loadState'. Either include it or remove the dependency array                                                                                                                                                                     react-hooks/exhaustive-deps

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/hooks/useStepValidation.ts
  268:11  warning  Unexpected console statement                                                                                                                              no-console
  304:5   warning  React Hook useCallback has missing dependencies: 'calculateProfessionalScore' and 'getActionForRule'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  406:5   warning  React Hook useCallback has missing dependencies: 'getActiveRulesForStep' and 'runValidation'. Either include them or remove the dependency array          react-hooks/exhaustive-deps
  418:6   warning  React Hook useCallback has a missing dependency: 'runValidation'. Either include it or remove the dependency array                                        react-hooks/exhaustive-deps
  440:5   warning  React Hook useCallback has a missing dependency: 'runValidation'. Either include it or remove the dependency array                                        react-hooks/exhaustive-deps
  453:6   warning  React Hook useCallback has a missing dependency: 'runValidation'. Either include it or remove the dependency array                                        react-hooks/exhaustive-deps
  465:6   warning  React Hook useCallback has a missing dependency: 'runValidation'. Either include it or remove the dependency array                                        react-hooks/exhaustive-deps
  477:6   warning  React Hook useCallback has a missing dependency: 'runValidation'. Either include it or remove the dependency array                                        react-hooks/exhaustive-deps
  490:6   warning  React Hook useCallback has a missing dependency: 'runValidation'. Either include it or remove the dependency array                                        react-hooks/exhaustive-deps
  505:5   warning  React Hook useCallback has a missing dependency: 'runValidation'. Either include it or remove the dependency array                                        react-hooks/exhaustive-deps
  568:11  warning  Unexpected console statement                                                                                                                              no-console
  603:5   warning  Unexpected console statement                                                                                                                              no-console
  608:5   warning  Unexpected console statement                                                                                                                              no-console

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/lib/edge-functions.ts
  41:20  warning  Unexpected console statement  no-console
  51:18  warning  Unexpected console statement  no-console

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/lib/supabase.ts
  61:44  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/services/inventoryConsumptionService.ts
  351:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  351:49  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  351:74  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  352:19  warning  Unexpected `await` inside a loop          no-await-in-loop
  353:30  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  354:29  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  355:29  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  356:30  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  362:19  warning  Unexpected `await` inside a loop          no-await-in-loop
  447:23  warning  Unexpected `await` inside a loop          no-await-in-loop

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/src/service/components/CompletionStep.tsx
     6:3   error    'TouchableOpacity' is defined but never used. Allowed unused vars must match /^_/u                                                                          @typescript-eslint/no-unused-vars
    14:3   error    'Platform' is defined but never used. Allowed unused vars must match /^_/u                                                                                  @typescript-eslint/no-unused-vars
    25:3   error    'runOnJS' is defined but never used. Allowed unused vars must match /^_/u                                                                                   @typescript-eslint/no-unused-vars
   100:6   warning  React Hook useEffect has missing dependencies: 'confettiY', 'opacity', 'scale', and 'sparkleScale'. Either include them or remove the dependency array      react-hooks/exhaustive-deps
   249:6   warning  React Hook useEffect has a missing dependency: 'feedbackCardScale'. Either include it or remove the dependency array                                        react-hooks/exhaustive-deps
   271:9   error    'animatedButtonStyles' is assigned a value but never used. Allowed unused vars must match /^_/u                                                             @typescript-eslint/no-unused-vars
   687:45  error    React Hook "useAnimatedStyle" cannot be called inside a callback. React Hooks must be called in a React function component or a custom React Hook function  react-hooks/rules-of-hooks
   773:29  error    React Hook "useAnimatedStyle" cannot be called inside a callback. React Hooks must be called in a React function component or a custom React Hook function  react-hooks/rules-of-hooks
  1007:3   error    Unused style detected: styles.satisfactionButtonActive                                                                                                      react-native/no-unused-styles
  1091:3   error    Unused style detected: styles.satisfactionButtonSuccess                                                                                                     react-native/no-unused-styles
  1100:13  warning  Color literal: { backgroundColor: 'transparent' }                                                                                                           react-native/no-color-literals

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/src/service/components/DesiredColorStep.tsx
  126:6   warning  React Hook React.useEffect has missing dependencies: 'data.desiredAnalysisResult', 'data.overallReflect', 'data.overallTone', 'data.overallUndertone', and 'data.viabilityAnalysis'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  418:47  warning  Unexpected any. Specify a different type                                                                                                                                                                                                 @typescript-eslint/no-explicit-any
  421:40  warning  Unexpected any. Specify a different type                                                                                                                                                                                                 @typescript-eslint/no-explicit-any
  422:41  warning  Unexpected any. Specify a different type                                                                                                                                                                                                 @typescript-eslint/no-explicit-any
  423:45  warning  Unexpected any. Specify a different type                                                                                                                                                                                                 @typescript-eslint/no-explicit-any
  424:44  warning  Unexpected any. Specify a different type                                                                                                                                                                                                 @typescript-eslint/no-explicit-any
  425:44  warning  Unexpected any. Specify a different type                                                                                                                                                                                                 @typescript-eslint/no-explicit-any
  725:50  warning  Inline style: { marginVertical: 12, padding: 16 }                                                                                                                                                                                        react-native/no-inline-styles

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/src/service/components/DiagnosisStep.tsx
  727:6   warning  React Hook useEffect has missing dependencies: 'data.clientId', 'data.diagnosisMethod', 'data.lastChemicalProcessDate', 'data.lastChemicalProcessType', 'data.overallReflect', 'data.overallTone', and 'onUpdate'. Either include them or remove the dependency array. If 'onUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  983:52  warning  Inline style: { marginVertical: 12, padding: 16 }                                                                                                                                                                                                                                                                                                                                          react-native/no-inline-styles

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/src/service/components/FormulationStep-original.tsx
   97:6   warning  React Hook React.useEffect has missing dependencies: 'data.formula', 'data.isFormulaFromAI', 'data.selectedBrand', 'data.selectedLine', 'formula', 'selectedBrand', 'selectedLine', 'setFormula', 'setIsFormulaFromAI', 'setSelectedBrand', and 'setSelectedLine'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  112:6   warning  React Hook React.useEffect has missing dependencies: 'isFormulaFromAI', 'onUpdate', 'selectedBrand', and 'selectedLine'. Either include them or remove the dependency array. If 'onUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback                        react-hooks/exhaustive-deps
  124:6   warning  React Hook React.useEffect has missing dependencies: 'analyzeViability' and 'onUpdate'. Either include them or remove the dependency array. If 'onUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback                                                         react-hooks/exhaustive-deps
  193:51  warning  Inline style: { backgroundColor: '#8B4513' }                                                                                                                                                                                                                                                                           react-native/no-inline-styles
  193:51  warning  Color literal: { backgroundColor: '#8B4513' }                                                                                                                                                                                                                                                                          react-native/no-color-literals
  199:51  warning  Inline style: { backgroundColor: '#F5DEB3' }                                                                                                                                                                                                                                                                           react-native/no-inline-styles
  199:51  warning  Color literal: { backgroundColor: '#F5DEB3' }                                                                                                                                                                                                                                                                          react-native/no-color-literals
  478:24  warning  Color literal: { backgroundColor: 'white' }                                                                                                                                                                                                                                                                            react-native/no-color-literals
  546:20  warning  Color literal: { backgroundColor: 'white' }                                                                                                                                                                                                                                                                            react-native/no-color-literals
  561:10  warning  Color literal: { backgroundColor: 'white' }                                                                                                                                                                                                                                                                            react-native/no-color-literals
  623:30  warning  Color literal: { backgroundColor: 'white' }                                                                                                                                                                                                                                                                            react-native/no-color-literals
  685:17  warning  Color literal: { color: 'white' }                                                                                                                                                                                                                                                                                      react-native/no-color-literals
  743:25  warning  Color literal: { color: 'white' }                                                                                                                                                                                                                                                                                      react-native/no-color-literals
  781:23  warning  Color literal: { color: 'white' }                                                                                                                                                                                                                                                                                      react-native/no-color-literals

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/src/service/components/FormulationStep.tsx
  182:6   warning  React Hook React.useEffect has missing dependencies: 'data.formula', 'data.isFormulaFromAI', 'data.selectedBrand', 'data.selectedLine', 'formula', 'selectedBrand', 'selectedLine', 'setFormula', 'setIsFormulaFromAI', 'setSelectedBrand', and 'setSelectedLine'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  197:6   warning  React Hook React.useEffect has missing dependencies: 'isFormulaFromAI', 'onUpdate', 'selectedBrand', and 'selectedLine'. Either include them or remove the dependency array. If 'onUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback                        react-hooks/exhaustive-deps
  209:6   warning  React Hook React.useEffect has missing dependencies: 'analyzeViability' and 'onUpdate'. Either include them or remove the dependency array. If 'onUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback                                                         react-hooks/exhaustive-deps
  752:20  warning  Color literal: { backgroundColor: 'white' }                                                                                                                                                                                                                                                                            react-native/no-color-literals
  767:10  warning  Color literal: { backgroundColor: 'white' }                                                                                                                                                                                                                                                                            react-native/no-color-literals
  829:30  warning  Color literal: { backgroundColor: 'white' }                                                                                                                                                                                                                                                                            react-native/no-color-literals

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/src/service/components/ServiceBreadcrumbs.tsx
  136:18  warning  Color literal: { backgroundColor: 'transparent' }  react-native/no-color-literals

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/src/service/components/SimpleDiagnosisStep.tsx
  104:6   warning  React Hook useEffect has a missing dependency: 'progressAnim'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  131:6   warning  React Hook useEffect has a missing dependency: 'scaleAnim'. Either include it or remove the dependency array     react-hooks/exhaustive-deps
  549:15  warning  Color literal: { backgroundColor: 'white' }                                                                      react-native/no-color-literals
  566:16  warning  Color literal: { borderColor: 'transparent' }                                                                    react-native/no-color-literals
  591:16  warning  Color literal: { color: 'white' }                                                                                react-native/no-color-literals
  617:22  warning  Color literal: { color: 'white' }                                                                                react-native/no-color-literals
  672:22  warning  Color literal: { backgroundColor: 'white' }                                                                      react-native/no-color-literals
  759:20  warning  Color literal: { backgroundColor: 'white' }                                                                      react-native/no-color-literals
  769:19  warning  Color literal: { borderColor: 'transparent' }                                                                    react-native/no-color-literals
  792:22  warning  Color literal: { color: 'white' }                                                                                react-native/no-color-literals
  795:25  warning  Color literal: { color: 'white' }                                                                                react-native/no-color-literals
  826:16  warning  Color literal: { backgroundColor: 'black' }                                                                      react-native/no-color-literals

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/src/service/hooks/useFormulation.ts
   70:5   warning  Unexpected console statement  no-console
   77:7   warning  Unexpected console statement  no-console
   83:9   warning  Unexpected console statement  no-console
   85:9   warning  Unexpected console statement  no-console
   88:9   warning  Unexpected console statement  no-console
   94:9   warning  Unexpected console statement  no-console
  128:9   warning  Unexpected console statement  no-console
  139:7   warning  Unexpected console statement  no-console
  143:7   warning  Unexpected console statement  no-console
  490:5   warning  Unexpected console statement  no-console
  498:7   warning  Unexpected console statement  no-console
  503:5   warning  Unexpected console statement  no-console
  505:7   warning  Unexpected console statement  no-console
  508:11  warning  Unexpected console statement  no-console
  516:11  warning  Unexpected console statement  no-console
  522:11  warning  Unexpected console statement  no-console
  528:7   warning  Unexpected console statement  no-console

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/src/service/hooks/usePhotoAnalysis.ts
  86:6  warning  React Hook useCallback has a missing dependency: 'pickImage'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/src/service/utils/serviceHelpers.ts
  121:31  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/stores/__tests__/inventory-analytics-store.test.ts
  10:14  error  Replace `·Product,·StockMovement,·InventoryReport,·ConsumptionAnalysis·` with `⏎··Product,⏎··StockMovement,⏎··InventoryReport,⏎··ConsumptionAnalysis,⏎`  prettier/prettier
  10:39  error  'InventoryReport' is defined but never used. Allowed unused vars must match /^_/u                                                                        @typescript-eslint/no-unused-vars
  10:56  error  'ConsumptionAnalysis' is defined but never used. Allowed unused vars must match /^_/u                                                                    @typescript-eslint/no-unused-vars

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/stores/__tests__/inventory-facade-integration.test.ts
   14:10  error  'supabase' is defined but never used. Allowed unused vars must match /^_/u   @typescript-eslint/no-unused-vars
  231:74  error  'movements' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/stores/__tests__/inventory-store-facade-simple.test.ts
  158:37  error  Require statement not part of import statement  @typescript-eslint/no-var-requires
  230:37  error  Require statement not part of import statement  @typescript-eslint/no-var-requires
  243:37  error  Require statement not part of import statement  @typescript-eslint/no-var-requires
  261:37  error  Require statement not part of import statement  @typescript-eslint/no-var-requires
  278:37  error  Require statement not part of import statement  @typescript-eslint/no-var-requires
  295:37  error  Require statement not part of import statement  @typescript-eslint/no-var-requires
  309:37  error  Require statement not part of import statement  @typescript-eslint/no-var-requires
  325:37  error  Require statement not part of import statement  @typescript-eslint/no-var-requires
  341:37  error  Require statement not part of import statement  @typescript-eslint/no-var-requires
  359:37  error  Require statement not part of import statement  @typescript-eslint/no-var-requires

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/stores/__tests__/performance.test.ts
   19:15  error  'Product' is defined but never used. Allowed unused vars must match /^_/u        @typescript-eslint/no-unused-vars
   19:24  error  'StockMovement' is defined but never used. Allowed unused vars must match /^_/u  @typescript-eslint/no-unused-vars
  141:30  error  Require statement not part of import statement                                   @typescript-eslint/no-var-requires
  172:30  error  Require statement not part of import statement                                   @typescript-eslint/no-var-requires
  396:30  error  Require statement not part of import statement                                   @typescript-eslint/no-var-requires
  631:59  error  Require statement not part of import statement                                   @typescript-eslint/no-var-requires
  769:26  error  Require statement not part of import statement                                   @typescript-eslint/no-var-requires

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/stores/__tests__/stock-store.test.ts
  11:39  error  'InventoryAlert' is defined but never used. Allowed unused vars must match /^_/u  @typescript-eslint/no-unused-vars

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/stores/__tests__/stores.setup.js
   17:42  error  'setTimeout' is not defined                   no-undef
   18:46  error  'setImmediate' is not defined                 no-undef
   79:28  error  'performance' is not defined                  no-undef
   83:21  error  'performance' is not defined                  no-undef
   90:24  error  '_context' is defined but never used          no-unused-vars
  402:34  error  'setTimeout' is not defined                   no-undef
  403:34  error  'setImmediate' is not defined                 no-undef
  417:9   error  'performance' is not defined                  no-undef
  419:26  error  'performance' is not defined                  no-undef
  420:27  error  'performance' is not defined                  no-undef
  421:27  error  'performance' is not defined                  no-undef
  442:9   error  Unexpected lexical declaration in case block  no-case-declarations
  443:9   error  Unexpected lexical declaration in case block  no-case-declarations
  448:42  error  'setTimeout' is not defined                   no-undef
  471:43  error  'performance' is not defined                  no-undef
  473:5   error  'performance' is not defined                  no-undef
  477:5   error  'performance' is not defined                  no-undef
  479:7   error  'performance' is not defined                  no-undef
  480:14  error  '_error' is defined but never used            no-unused-vars

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/stores/__tests__/test-utils.ts
    8:9   error    Replace `⏎··Product,⏎··StockMovement,⏎··InventoryAlert,⏎··ProductMapping,⏎` with `·Product,·StockMovement,·InventoryAlert,·ProductMapping·`  prettier/prettier
  319:9   error    'quantity' is never reassigned. Use 'const' instead                                                                                          prefer-const
  665:13  warning  Unexpected `await` inside a loop                                                                                                             no-await-in-loop

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/stores/client-history-store.ts
  234:9  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/stores/inventory-analytics-store.ts
  457:15  error  'daysUntilEmpty' is assigned a value but never used. Allowed unused vars must match /^_/u  @typescript-eslint/no-unused-vars
  594:15  error  'totalValue' is assigned a value but never used. Allowed unused vars must match /^_/u      @typescript-eslint/no-unused-vars

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/stores/stock-store.new.ts
   5:27  error  'isLocalId' is defined but never used. Allowed unused vars must match /^_/u        @typescript-eslint/no-unused-vars
  12:3   error  'InventoryReport' is defined but never used. Allowed unused vars must match /^_/u  @typescript-eslint/no-unused-vars

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/styles/colors.ts
   3:3   error  Delete `·`   prettier/prettier
  17:1   error  Delete `··`  prettier/prettier
  27:1   error  Delete `··`  prettier/prettier
  34:12  error  Insert `⏎`   prettier/prettier

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/utils/featureFlags.ts
  106:12  error  'error' is defined but never used  @typescript-eslint/no-unused-vars
  106:20  error  Delete `⏎··`                       prettier/prettier
  122:12  error  'error' is defined but never used  @typescript-eslint/no-unused-vars
  122:20  error  Delete `⏎··`                       prettier/prettier
  136:12  error  'error' is defined but never used  @typescript-eslint/no-unused-vars
  136:20  error  Delete `⏎··`                       prettier/prettier
  159:12  error  'error' is defined but never used  @typescript-eslint/no-unused-vars
  159:20  error  Delete `⏎··`                       prettier/prettier
  170:12  error  'error' is defined but never used  @typescript-eslint/no-unused-vars
  181:12  error  'error' is defined but never used  @typescript-eslint/no-unused-vars
  181:20  error  Delete `⏎··`                       prettier/prettier

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/utils/memoization-utils.ts
  91:6  warning  React Hook useMemo was passed a dependency list that is not an array literal. This means we can't statically verify whether you've passed the correct dependencies  react-hooks/exhaustive-deps
  91:6  warning  React Hook useMemo has a missing dependency: 'deps'. Either include it or remove the dependency array                                                               react-hooks/exhaustive-deps

✖ 371 problems (153 errors, 218 warnings)
  15 errors and 0 warnings potentially fixable with the `--fix` option.

