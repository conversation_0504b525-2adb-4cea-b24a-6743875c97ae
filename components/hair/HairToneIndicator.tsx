import React from 'react';
import { View, Text, StyleSheet, ViewStyle } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
  interpolate,
  withRepeat,
  withSequence,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { getUndertoneIcon, HAIR_TONE_ICONS, _UNDERTONE_ICONS } from '@/constants/hair-iconography';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';

interface HairToneIndicatorProps {
  tone: string;
  undertone?: string;
  showTemperature?: boolean;
  showLabel?: boolean;
  size?: 'small' | 'medium' | 'large';
  animated?: boolean;
  interactive?: boolean;
  onPress?: () => void;
  style?: ViewStyle;
}

/**
 * Professional Hair Tone Indicator
 *
 * Visual indicator for hair tones and undertones with professional color theory.
 * Shows warm/cool temperature and specific undertone characteristics.
 */
export const HairToneIndicator: React.FC<HairToneIndicatorProps> = ({
  tone,
  undertone,
  showTemperature = true,
  showLabel = true,
  size = 'medium',
  animated = true,
  interactive = false,
  onPress,
  style,
}) => {
  const scaleAnim = useSharedValue(0);
  const rotationAnim = useSharedValue(0);
  const glowAnim = useSharedValue(0);

  React.useEffect(() => {
    if (animated) {
      scaleAnim.value = withSpring(1, { damping: 15, stiffness: 150 });

      // Subtle rotation animation for warm tones
      if (getTemperatureCategory(undertone || tone) === 'warm') {
        rotationAnim.value = withRepeat(
          withSequence(withTiming(3, { duration: 2000 }), withTiming(-3, { duration: 2000 })),
          -1,
          true
        );
      }

      // Glow effect for special tones
      if (undertone?.includes('Platino') || undertone?.includes('Violeta')) {
        glowAnim.value = withRepeat(
          withSequence(withTiming(1, { duration: 1500 }), withTiming(0.3, { duration: 1500 })),
          -1,
          true
        );
      }
    } else {
      scaleAnim.value = 1;
      rotationAnim.value = 0;
      glowAnim.value = 0;
    }
  }, [tone, undertone, animated, glowAnim, rotationAnim, scaleAnim]);

  // Get temperature category from tone/undertone
  const getTemperatureCategory = (toneValue: string) => {
    const warmTones = ['Dorado', 'Cobrizo', 'Rojizo', 'Caoba', 'dorado', 'cálido'];
    const coolTones = ['Cenizo', 'Platino', 'Violeta', 'Beige', 'cenizo', 'frío'];

    const lowerTone = toneValue.toLowerCase();
    if (warmTones.some(warm => lowerTone.includes(warm.toLowerCase()))) return 'warm';
    if (coolTones.some(cool => lowerTone.includes(cool.toLowerCase()))) return 'cool';
    return 'neutral';
  };

  const temperatureCategory = getTemperatureCategory(undertone || tone);
  const temperatureData = HAIR_TONE_ICONS[temperatureCategory];
  const undertoneData = undertone ? getUndertoneIcon(undertone) : null;

  // Size configurations
  const sizeConfigs = {
    small: {
      containerSize: 32,
      iconSize: 16,
      fontSize: 11,
      padding: 6,
    },
    medium: {
      containerSize: 44,
      iconSize: 20,
      fontSize: 13,
      padding: 8,
    },
    large: {
      containerSize: 56,
      iconSize: 24,
      fontSize: 15,
      padding: 10,
    },
  };

  const config = sizeConfigs[size];

  const animatedContainerStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scaleAnim.value }, { rotate: `${rotationAnim.value}deg` }],
  }));

  const animatedGlowStyle = useAnimatedStyle(() => ({
    shadowOpacity: interpolate(glowAnim.value, [0, 1], [0.1, 0.4]),
    shadowRadius: interpolate(glowAnim.value, [0, 1], [2, 8]),
  }));

  const handlePress = () => {
    if (interactive && onPress) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      onPress();
    }
  };

  // Temperature-based gradient colors
  const getTemperatureGradient = () => {
    switch (temperatureCategory) {
      case 'warm':
        return ['#FEF3C7', '#FBBF24', '#D97706']; // Golden gradient
      case 'cool':
        return ['#DBEAFE', '#93C5FD', '#3B82F6']; // Cool blue gradient
      default:
        return ['#F3F4F6', '#D1D5DB', '#6B7280']; // Neutral gray gradient
    }
  };

  const gradientColors = getTemperatureGradient();
  const TemperatureIcon = temperatureData.icon;
  const UndertoneIcon = undertoneData?.icon;

  return (
    <Animated.View style={[styles.container, animatedContainerStyle, style]}>
      {/* Temperature Background with Gradient */}
      <Animated.View
        style={[
          styles.temperatureContainer,
          {
            width: config.containerSize,
            height: config.containerSize,
            borderRadius: config.containerSize / 2,
          },
          animatedGlowStyle,
          interactive && styles.interactiveContainer,
        ]}
        onTouchEnd={handlePress}
      >
        <LinearGradient
          colors={gradientColors}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={[
            styles.gradientBackground,
            {
              borderRadius: config.containerSize / 2,
              padding: config.padding,
            },
          ]}
        >
          {/* Temperature Icon */}
          {showTemperature && (
            <View style={styles.temperatureIconContainer}>
              <TemperatureIcon size={config.iconSize * 0.7} color={temperatureData.color} />
            </View>
          )}

          {/* Undertone Icon Overlay */}
          {UndertoneIcon && (
            <View style={styles.undertoneIconContainer}>
              <UndertoneIcon size={config.iconSize * 0.5} color={undertoneData.color} />
            </View>
          )}
        </LinearGradient>
      </Animated.View>

      {/* Labels */}
      {showLabel && (
        <View style={styles.labelContainer}>
          <Text style={[styles.toneLabel, { fontSize: config.fontSize }]}>{tone}</Text>
          {undertone && (
            <Text style={[styles.undertoneLabel, { fontSize: config.fontSize - 2 }]}>
              {undertone}
            </Text>
          )}
          {showTemperature && (
            <Text
              style={[
                styles.temperatureLabel,
                {
                  fontSize: config.fontSize - 3,
                  color: temperatureData.color,
                },
              ]}
            >
              {temperatureData.description?.split(' - ')[0]}
            </Text>
          )}
        </View>
      )}
    </Animated.View>
  );
};

/**
 * Hair Tone Palette Component
 *
 * Shows a collection of hair tones for comparison or selection
 */
interface HairTonePaletteProps {
  tones: Array<{ tone: string; undertone?: string; selected?: boolean }>;
  onToneSelect?: (tone: string, undertone?: string) => void;
  maxItemsPerRow?: number;
  style?: ViewStyle;
}

export const HairTonePalette: React.FC<HairTonePaletteProps> = ({
  tones,
  onToneSelect,
  maxItemsPerRow = 4,
  style,
}) => {
  const renderToneRows = () => {
    const rows = [];
    for (let i = 0; i < tones.length; i += maxItemsPerRow) {
      const rowTones = tones.slice(i, i + maxItemsPerRow);
      rows.push(
        <View key={i} style={styles.paletteRow}>
          {rowTones.map((toneData, index) => (
            <HairToneIndicator
              key={`${toneData.tone}-${index}`}
              tone={toneData.tone}
              undertone={toneData.undertone}
              size="medium"
              showLabel={true}
              showTemperature={true}
              interactive={!!onToneSelect}
              onPress={() => onToneSelect?.(toneData.tone, toneData.undertone)}
              style={[styles.paletteItem, toneData.selected && styles.selectedTone]}
            />
          ))}
        </View>
      );
    }
    return rows;
  };

  return (
    <View style={[styles.paletteContainer, style]}>
      <Text style={styles.paletteTitle}>Paleta de Tonos Profesional</Text>
      {renderToneRows()}
    </View>
  );
};

/**
 * Color Temperature Guide Component
 *
 * Educational component showing warm/cool tone relationships
 */
export const ColorTemperatureGuide: React.FC<{ style?: ViewStyle }> = ({ style }) => {
  return (
    <View style={[styles.guideContainer, style]}>
      <Text style={styles.guideTitle}>Guía de Temperatura Cromática</Text>

      <View style={styles.temperatureSection}>
        <View style={styles.temperatureSide}>
          <Text style={styles.temperatureSideTitle}>Tonos Cálidos</Text>
          <Text style={styles.temperatureDescription}>
            Base dorada/cobriza - agregan calidez y luminosidad
          </Text>
          <View style={styles.temperatureExamples}>
            {['Dorado', 'Cobrizo', 'Caoba'].map(tone => (
              <HairToneIndicator
                key={tone}
                tone={tone}
                size="small"
                showLabel={false}
                style={styles.temperatureExample}
              />
            ))}
          </View>
        </View>

        <View style={styles.temperatureDivider} />

        <View style={styles.temperatureSide}>
          <Text style={styles.temperatureSideTitle}>Tonos Fríos</Text>
          <Text style={styles.temperatureDescription}>
            Base ceniza/violeta - neutralizan amarillos y naranjas
          </Text>
          <View style={styles.temperatureExamples}>
            {['Cenizo', 'Platino', 'Violeta'].map(tone => (
              <HairToneIndicator
                key={tone}
                tone={tone}
                size="small"
                showLabel={false}
                style={styles.temperatureExample}
              />
            ))}
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  // Single Indicator
  container: {
    alignItems: 'center',
    gap: BeautyMinimalTheme.spacing.xs,
  },
  temperatureContainer: {
    elevation: 3,
    shadowColor: BeautyMinimalTheme.neutrals.charcoal,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  interactiveContainer: {
    elevation: 5,
  },
  gradientBackground: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  temperatureIconContainer: {
    position: 'absolute',
    top: 2,
    right: 2,
  },
  undertoneIconContainer: {
    position: 'absolute',
    bottom: 2,
    left: 2,
  },
  labelContainer: {
    alignItems: 'center',
    gap: 2,
  },
  toneLabel: {
    fontWeight: '700',
    color: BeautyMinimalTheme.semantic.text.primary,
    textAlign: 'center',
  },
  undertoneLabel: {
    fontWeight: '500',
    color: BeautyMinimalTheme.semantic.text.secondary,
    textAlign: 'center',
  },
  temperatureLabel: {
    fontWeight: '500',
    textAlign: 'center',
    fontStyle: 'italic',
  },

  // Palette Component
  paletteContainer: {
    backgroundColor: BeautyMinimalTheme.neutrals.pure,
    borderRadius: BeautyMinimalTheme.radius.lg,
    padding: BeautyMinimalTheme.spacing.lg,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.neutrals.mist,
  },
  paletteTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    textAlign: 'center',
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  paletteRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  paletteItem: {
    flex: 1,
    alignItems: 'center',
  },
  selectedTone: {
    backgroundColor: BeautyMinimalTheme.semantic.interactive.primary.default + '20',
    borderRadius: BeautyMinimalTheme.radius.md,
    padding: BeautyMinimalTheme.spacing.xs,
  },

  // Temperature Guide
  guideContainer: {
    backgroundColor: BeautyMinimalTheme.neutrals.cloud,
    borderRadius: BeautyMinimalTheme.radius.lg,
    padding: BeautyMinimalTheme.spacing.lg,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.neutrals.mist,
  },
  guideTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    textAlign: 'center',
    marginBottom: BeautyMinimalTheme.spacing.lg,
  },
  temperatureSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  temperatureSide: {
    flex: 1,
    alignItems: 'center',
  },
  temperatureDivider: {
    width: 1,
    height: 80,
    backgroundColor: BeautyMinimalTheme.neutrals.mist,
    marginHorizontal: BeautyMinimalTheme.spacing.md,
  },
  temperatureSideTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.body,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  temperatureDescription: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.secondary,
    textAlign: 'center',
    marginBottom: BeautyMinimalTheme.spacing.sm,
    lineHeight: 16,
  },
  temperatureExamples: {
    flexDirection: 'row',
    gap: BeautyMinimalTheme.spacing.xs,
  },
  temperatureExample: {
    // Individual styling handled by HairToneIndicator
  },
});

export default HairToneIndicator;
