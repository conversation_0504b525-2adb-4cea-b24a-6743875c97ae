import React from 'react';
import { View, Text, StyleSheet, ViewStyle } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import {
  _getHairLevelIcon,
  PROFESSIONAL_HAIR_COLORS,
  HairLevel,
} from '@/constants/hair-iconography';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';

interface HairLevelIndicatorProps {
  level: number;
  showLabel?: boolean;
  size?: 'small' | 'medium' | 'large';
  animated?: boolean;
  style?: ViewStyle;
}

/**
 * Professional Hair Level Indicator
 *
 * Displays accurate hair color level (1-10) with professional color swatches.
 * Used throughout diagnosis components for visual color reference.
 */
export const HairLevelIndicator: React.FC<HairLevelIndicatorProps> = ({
  level,
  showLabel = true,
  size = 'medium',
  animated = true,
  style,
}) => {
  const scaleAnim = useSharedValue(0);
  const progressAnim = useSharedValue(0);

  React.useEffect(() => {
    if (animated) {
      scaleAnim.value = withSpring(1, { damping: 15, stiffness: 150 });
      progressAnim.value = withTiming(level / 10, { duration: 800 });
    } else {
      scaleAnim.value = 1;
      progressAnim.value = level / 10;
    }
  }, [level, animated, progressAnim, scaleAnim]);

  // Clamp level to valid range
  const clampedLevel = Math.max(1, Math.min(10, Math.round(level)));
  const colorData = PROFESSIONAL_HAIR_COLORS[clampedLevel as HairLevel];

  // Size configurations
  const sizeConfigs = {
    small: {
      diameter: 24,
      fontSize: 10,
      labelSize: 11,
      borderWidth: 1.5,
    },
    medium: {
      diameter: 36,
      fontSize: 12,
      labelSize: 13,
      borderWidth: 2,
    },
    large: {
      diameter: 48,
      fontSize: 14,
      labelSize: 15,
      borderWidth: 2.5,
    },
  };

  const config = sizeConfigs[size];

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scaleAnim.value }],
  }));

  const progressStyle = useAnimatedStyle(() => ({
    opacity: interpolate(progressAnim.value, [0, 1], [0.6, 1]),
  }));

  return (
    <Animated.View style={[styles.container, animatedStyle, style]}>
      {/* Professional Color Swatch */}
      <Animated.View
        style={[
          styles.levelCircle,
          {
            width: config.diameter,
            height: config.diameter,
            borderRadius: config.diameter / 2,
            borderWidth: config.borderWidth,
            borderColor: BeautyMinimalTheme.neutrals.mist,
          },
          progressStyle,
        ]}
      >
        <LinearGradient
          colors={[colorData.primary, colorData.secondary]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={[
            styles.gradientFill,
            {
              borderRadius: config.diameter / 2 - config.borderWidth,
            },
          ]}
        >
          {/* Level Number Overlay */}
          <View style={styles.levelNumberContainer}>
            <Text
              style={[
                styles.levelNumber,
                {
                  fontSize: config.fontSize,
                  color: clampedLevel <= 4 ? '#FFFFFF' : '#1F2937',
                  textShadowColor: clampedLevel <= 4 ? 'rgba(0,0,0,0.3)' : 'rgba(255,255,255,0.5)',
                  textShadowOffset: { width: 0, height: 1 },
                  textShadowRadius: 2,
                },
              ]}
            >
              {clampedLevel}
            </Text>
          </View>
        </LinearGradient>
      </Animated.View>

      {/* Professional Label */}
      {showLabel && (
        <View style={styles.labelContainer}>
          <Text style={[styles.levelLabel, { fontSize: config.labelSize }]}>{colorData.name}</Text>
          <Text style={styles.levelSubLabel}>Nivel {clampedLevel}</Text>
        </View>
      )}
    </Animated.View>
  );
};

/**
 * Hair Level Scale Component
 *
 * Shows complete 1-10 hair level scale for reference
 */
interface HairLevelScaleProps {
  currentLevel?: number;
  onLevelSelect?: (level: number) => void;
  style?: ViewStyle;
}

export const HairLevelScale: React.FC<HairLevelScaleProps> = ({
  currentLevel,
  _onLevelSelect,
  style,
}) => {
  return (
    <View style={[styles.scaleContainer, style]}>
      <Text style={styles.scaleTitle}>Escala Profesional de Niveles</Text>

      <View style={styles.scaleRow}>
        {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(level => (
          <View key={level} style={styles.scaleItem}>
            <HairLevelIndicator
              level={level}
              size="small"
              showLabel={false}
              animated={false}
              style={[
                styles.scaleIndicator,
                currentLevel === level && styles.currentLevelIndicator,
              ]}
            />
            <Text style={[styles.scaleNumber, currentLevel === level && styles.currentLevelNumber]}>
              {level}
            </Text>
          </View>
        ))}
      </View>

      <View style={styles.scaleLabels}>
        <Text style={styles.scaleEndLabel}>Oscuro</Text>
        <Text style={styles.scaleEndLabel}>Claro</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  // Single Indicator
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  levelCircle: {
    backgroundColor: BeautyMinimalTheme.neutrals.pure,
    elevation: 2,
    shadowColor: BeautyMinimalTheme.neutrals.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  gradientFill: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  levelNumberContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  levelNumber: {
    fontWeight: '700',
    includeFontPadding: false,
    textAlignVertical: 'center',
  },
  labelContainer: {
    marginTop: BeautyMinimalTheme.spacing.xs,
    alignItems: 'center',
  },
  levelLabel: {
    fontWeight: '600',
    color: BeautyMinimalTheme.semantic.text.primary,
    textAlign: 'center',
    marginBottom: 2,
  },
  levelSubLabel: {
    fontSize: 10,
    color: BeautyMinimalTheme.semantic.text.secondary,
    fontWeight: '500',
  },

  // Scale Component
  scaleContainer: {
    backgroundColor: BeautyMinimalTheme.neutrals.pure,
    borderRadius: BeautyMinimalTheme.radius.lg,
    padding: BeautyMinimalTheme.spacing.md,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.neutrals.mist,
  },
  scaleTitle: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    textAlign: 'center',
    marginBottom: BeautyMinimalTheme.spacing.md,
  },
  scaleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },
  scaleItem: {
    alignItems: 'center',
    flex: 1,
  },
  scaleIndicator: {
    marginBottom: BeautyMinimalTheme.spacing.xs,
  },
  currentLevelIndicator: {
    transform: [{ scale: 1.2 }],
    elevation: 4,
    shadowOpacity: 0.2,
  },
  scaleNumber: {
    fontSize: 10,
    fontWeight: '500',
    color: BeautyMinimalTheme.semantic.text.secondary,
  },
  currentLevelNumber: {
    fontWeight: '700',
    color: BeautyMinimalTheme.semantic.interactive.primary.default,
  },
  scaleLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: BeautyMinimalTheme.spacing.sm,
  },
  scaleEndLabel: {
    fontSize: 11,
    fontWeight: '500',
    color: BeautyMinimalTheme.semantic.text.tertiary,
    fontStyle: 'italic',
  },
});

export default HairLevelIndicator;
