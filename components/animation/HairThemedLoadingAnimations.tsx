/**
 * HAIR-THEMED LOADING ANIMATIONS
 *
 * Professional beauty animations for Salonier AI components
 * Features hair salon imagery with subtle, non-distracting animations
 * Maintains 90% neutral / 10% beauty color theme
 * All animations target 60fps performance
 */

import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withRepeat,
  withSequence,
  withSpring,
  interpolate,
  interpolateColor,
  Easing,
  withDelay,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import { Scissors, Palette, TestTube2, Camera, Activity } from 'lucide-react-native';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { MicroInteractions } from '@/constants/micro-interactions';
import { ColorConstants } from '@/styles/colors';

const { width: _SCREEN_WIDTH } = Dimensions.get('window');

// Animation contexts for AI processing
export type HairLoadingContext =
  | 'photo-analysis'
  | 'hair-diagnosis'
  | 'formula-generation'
  | 'color-matching'
  | 'ai-processing';

interface HairThemedLoadingProps {
  context: HairLoadingContext;
  size?: 'small' | 'medium' | 'large';
  showIcon?: boolean;
  triggerHaptics?: boolean;
}

// Professional hair salon color palette using BeautyMinimalTheme
const HAIR_COLORS = {
  platinum: BeautyMinimalTheme.neutrals.cloud, // Light silver
  blonde: BeautyMinimalTheme.beautyColors.rose[200], // Light rose-gold
  brunette: BeautyMinimalTheme.beautyColors.amethyst[700], // Deep purple-brown
  auburn: BeautyMinimalTheme.beautyColors.rose[600], // Auburn coral
  black: BeautyMinimalTheme.neutrals.charcoal, // Deep charcoal
} as const;

// Animation configurations per context
const CONTEXT_CONFIG = {
  'photo-analysis': {
    icon: Camera,
    primaryColor: BeautyMinimalTheme.beautyColors.sage[500],
    animation: 'camera_focus',
    duration: MicroInteractions.durations.gentle,
    message: 'Analizando imagen capilar...',
  },
  'hair-diagnosis': {
    icon: Activity,
    primaryColor: BeautyMinimalTheme.beautyColors.amethyst[600],
    animation: 'hair_strand_analysis',
    duration: MicroInteractions.durations.slow,
    message: 'Evaluando estructura capilar...',
  },
  'formula-generation': {
    icon: TestTube2,
    primaryColor: BeautyMinimalTheme.beautyColors.rose[500],
    animation: 'chemical_mixing',
    duration: MicroInteractions.durations.normal,
    message: 'Creando fórmula química...',
  },
  'color-matching': {
    icon: Palette,
    primaryColor: BeautyMinimalTheme.beautyColors.sage[600],
    animation: 'color_palette_rotation',
    duration: MicroInteractions.durations.fast,
    message: 'Encontrando colores perfectos...',
  },
  'ai-processing': {
    icon: Scissors,
    primaryColor: BeautyMinimalTheme.beautyColors.amethyst[500],
    animation: 'scissors_cutting',
    duration: MicroInteractions.durations.normal,
    message: 'Procesando con IA profesional...',
  },
} as const;

/**
 * CAMERA FOCUS ANIMATION
 * Simulates professional photo analysis with focus rings
 */
const CameraFocusAnimation: React.FC<{ size: number }> = ({ size }) => {
  const focusRing1 = useSharedValue(0);
  const focusRing2 = useSharedValue(0);
  const cameraFlash = useSharedValue(0);

  useEffect(() => {
    // Animated focus rings
    focusRing1.value = withRepeat(
      withSequence(
        withTiming(1, { duration: 800, easing: Easing.out(Easing.quad) }),
        withTiming(0.3, { duration: 200 })
      ),
      -1,
      false
    );

    focusRing2.value = withRepeat(
      withDelay(
        400,
        withSequence(
          withTiming(1, { duration: 800, easing: Easing.out(Easing.quad) }),
          withTiming(0.3, { duration: 200 })
        )
      ),
      -1,
      false
    );

    // Occasional flash effect
    cameraFlash.value = withRepeat(
      withSequence(
        withDelay(2000, withTiming(1, { duration: 100 })),
        withTiming(0, { duration: 200 }),
        withDelay(1800, withTiming(0, { duration: 0 }))
      ),
      -1,
      false
    );
  }, [cameraFlash, focusRing1, focusRing2]);

  const focusRing1Style = useAnimatedStyle(() => ({
    opacity: focusRing1.value,
    transform: [{ scale: interpolate(focusRing1.value, [0, 1], [0.8, 1.2]) }],
  }));

  const focusRing2Style = useAnimatedStyle(() => ({
    opacity: focusRing2.value,
    transform: [{ scale: interpolate(focusRing2.value, [0, 1], [1.0, 1.4]) }],
  }));

  const flashStyle = useAnimatedStyle(() => ({
    opacity: cameraFlash.value,
  }));

  return (
    <View style={[styles.animationContainer, { width: size, height: size }]}>
      <Animated.View style={[styles.focusRing, focusRing1Style]} />
      <Animated.View style={[styles.focusRing, focusRing2Style]} />
      <View style={styles.iconContainer}>
        <Camera size={size * 0.4} color={BeautyMinimalTheme.beautyColors.sage[500]} />
      </View>
      <Animated.View style={[styles.flashOverlay, flashStyle]} />
    </View>
  );
};

/**
 * HAIR STRAND ANALYSIS ANIMATION
 * Flowing hair strands being analyzed with color detection
 */
const HairStrandAnalysis: React.FC<{ size: number }> = ({ size }) => {
  const strandFlow = useSharedValue(0);
  const colorAnalysis = useSharedValue(0);
  const pulse = useSharedValue(1);

  useEffect(() => {
    // Flowing hair movement
    strandFlow.value = withRepeat(
      withTiming(1, {
        duration: MicroInteractions.durations.gentle,
        easing: Easing.inOut(Easing.sine),
      }),
      -1,
      true
    );

    // Color analysis scanning
    colorAnalysis.value = withRepeat(
      withSequence(
        withTiming(1, { duration: 1000 }),
        withDelay(500, withTiming(0, { duration: 200 }))
      ),
      -1,
      false
    );

    // Gentle pulsing
    pulse.value = withRepeat(withSpring(1.1, MicroInteractions.springs.gentle), -1, true);
  }, [colorAnalysis, pulse, strandFlow]);

  const strandStyle = useAnimatedStyle(() => ({
    transform: [
      {
        translateX: interpolate(strandFlow.value, [0, 1], [-2, 2]),
      },
      {
        rotateZ: `${interpolate(strandFlow.value, [0, 1], [-5, 5])}deg`,
      },
    ],
  }));

  const analysisStyle = useAnimatedStyle(() => ({
    opacity: colorAnalysis.value,
    transform: [{ scale: pulse.value }],
  }));

  return (
    <View style={[styles.animationContainer, { width: size, height: size }]}>
      <View style={styles.hairStrandContainer}>
        {/* Hair strands in different colors */}
        {Object.entries(HAIR_COLORS).map(([key, color], index) => (
          <Animated.View
            key={key}
            style={[
              styles.hairStrand,
              strandStyle,
              {
                backgroundColor: color,
                left: (index * size) / 6,
                animationDelay: `${index * 100}ms`,
              },
            ]}
          />
        ))}
      </View>
      <Animated.View style={[styles.iconContainer, analysisStyle]}>
        <Activity size={size * 0.4} color={BeautyMinimalTheme.beautyColors.amethyst[600]} />
      </Animated.View>
    </View>
  );
};

/**
 * CHEMICAL MIXING ANIMATION
 * Test tubes with bubbling chemical reactions
 */
const ChemicalMixingAnimation: React.FC<{ size: number }> = ({ size }) => {
  const bubble1 = useSharedValue(0);
  const bubble2 = useSharedValue(0);
  const bubble3 = useSharedValue(0);
  const mixingRotation = useSharedValue(0);

  useEffect(() => {
    // Bubbling effects with staggered timing
    bubble1.value = withRepeat(
      withSequence(withTiming(1, { duration: 800 }), withTiming(0, { duration: 200 })),
      -1,
      false
    );

    bubble2.value = withRepeat(
      withDelay(
        300,
        withSequence(withTiming(1, { duration: 600 }), withTiming(0, { duration: 200 }))
      ),
      -1,
      false
    );

    bubble3.value = withRepeat(
      withDelay(
        600,
        withSequence(withTiming(1, { duration: 700 }), withTiming(0, { duration: 200 }))
      ),
      -1,
      false
    );

    // Gentle mixing rotation
    mixingRotation.value = withRepeat(
      withTiming(360, {
        duration: MicroInteractions.rotations.slow,
        easing: Easing.linear,
      }),
      -1,
      false
    );
  }, [bubble1, bubble2, bubble3, mixingRotation]);

  const bubbleStyle1 = useAnimatedStyle(() => ({
    opacity: bubble1.value,
    transform: [
      { scale: interpolate(bubble1.value, [0, 1], [0.5, 1.2]) },
      { translateY: interpolate(bubble1.value, [0, 1], [0, -10]) },
    ],
  }));

  const bubbleStyle2 = useAnimatedStyle(() => ({
    opacity: bubble2.value,
    transform: [
      { scale: interpolate(bubble2.value, [0, 1], [0.5, 1.2]) },
      { translateY: interpolate(bubble2.value, [0, 1], [0, -10]) },
    ],
  }));

  const bubbleStyle3 = useAnimatedStyle(() => ({
    opacity: bubble3.value,
    transform: [
      { scale: interpolate(bubble3.value, [0, 1], [0.5, 1.2]) },
      { translateY: interpolate(bubble3.value, [0, 1], [0, -10]) },
    ],
  }));

  const mixingStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${mixingRotation.value}deg` }],
  }));

  return (
    <View style={[styles.animationContainer, { width: size, height: size }]}>
      <Animated.View style={[styles.iconContainer, mixingStyle]}>
        <TestTube2 size={size * 0.4} color={BeautyMinimalTheme.beautyColors.rose[500]} />
      </Animated.View>

      {/* Chemical bubbles */}
      <Animated.View
        style={[styles.chemicalBubble, { top: size * 0.2, left: size * 0.3 }, bubbleStyle1]}
      />
      <Animated.View
        style={[styles.chemicalBubble, { top: size * 0.15, right: size * 0.3 }, bubbleStyle2]}
      />
      <Animated.View
        style={[styles.chemicalBubble, { top: size * 0.25, left: size * 0.5 }, bubbleStyle3]}
      />
    </View>
  );
};

/**
 * COLOR PALETTE ROTATION ANIMATION
 * Professional color wheel with smooth rotation
 */
const ColorPaletteRotation: React.FC<{ size: number }> = ({ size }) => {
  const paletteRotation = useSharedValue(0);
  const colorShift = useSharedValue(0);

  useEffect(() => {
    paletteRotation.value = withRepeat(
      withTiming(360, {
        duration: MicroInteractions.rotations.medium,
        easing: Easing.linear,
      }),
      -1,
      false
    );

    colorShift.value = withRepeat(withTiming(1, { duration: 2000 }), -1, true);
  }, [colorShift, paletteRotation]);

  const paletteStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${paletteRotation.value}deg` }],
  }));

  const colorDotStyles = [
    useAnimatedStyle(() => ({
      backgroundColor: interpolateColor(
        colorShift.value,
        [0, 1],
        [Object.values(HAIR_COLORS)[0 % 5], Object.values(HAIR_COLORS)[(0 + 2) % 5]]
      ),
    })),
    useAnimatedStyle(() => ({
      backgroundColor: interpolateColor(
        colorShift.value,
        [0, 1],
        [Object.values(HAIR_COLORS)[1 % 5], Object.values(HAIR_COLORS)[(1 + 2) % 5]]
      ),
    })),
    useAnimatedStyle(() => ({
      backgroundColor: interpolateColor(
        colorShift.value,
        [0, 1],
        [Object.values(HAIR_COLORS)[2 % 5], Object.values(HAIR_COLORS)[(2 + 2) % 5]]
      ),
    })),
    useAnimatedStyle(() => ({
      backgroundColor: interpolateColor(
        colorShift.value,
        [0, 1],
        [Object.values(HAIR_COLORS)[3 % 5], Object.values(HAIR_COLORS)[(3 + 2) % 5]]
      ),
    })),
    useAnimatedStyle(() => ({
      backgroundColor: interpolateColor(
        colorShift.value,
        [0, 1],
        [Object.values(HAIR_COLORS)[4 % 5], Object.values(HAIR_COLORS)[(4 + 2) % 5]]
      ),
    })),
    useAnimatedStyle(() => ({
      backgroundColor: interpolateColor(
        colorShift.value,
        [0, 1],
        [Object.values(HAIR_COLORS)[5 % 5], Object.values(HAIR_COLORS)[(5 + 2) % 5]]
      ),
    })),
  ];

  return (
    <View style={[styles.animationContainer, { width: size, height: size }]}>
      <Animated.View style={[styles.paletteContainer, paletteStyle]}>
        <View style={styles.iconContainer}>
          <Palette size={size * 0.3} color={BeautyMinimalTheme.beautyColors.sage[600]} />
        </View>

        {/* Color dots around the palette */}
        {Array.from({ length: 6 }, (_, index) => (
          <Animated.View
            key={index}
            style={[
              styles.colorDot,
              colorDotStyles[index],
              {
                transform: [
                  { rotate: `${index * 60}deg` },
                  { translateY: -size * 0.3 },
                  { rotate: `-${index * 60}deg` },
                ],
              },
            ]}
          />
        ))}
      </Animated.View>
    </View>
  );
};

/**
 * SCISSORS CUTTING ANIMATION
 * Professional scissors with cutting motion
 */
const ScissorsCuttingAnimation: React.FC<{ size: number }> = ({ size }) => {
  const scissorsOpen = useSharedValue(0);
  const sparkle = useSharedValue(0);

  useEffect(() => {
    // Scissors opening and closing
    scissorsOpen.value = withRepeat(
      withSequence(
        withTiming(1, {
          duration: MicroInteractions.durations.fast,
          easing: Easing.out(Easing.quad),
        }),
        withTiming(0, {
          duration: MicroInteractions.durations.fast,
          easing: Easing.in(Easing.quad),
        }),
        withDelay(500, withTiming(0, { duration: 0 }))
      ),
      -1,
      false
    );

    // Sparkle effect on cut
    sparkle.value = withRepeat(
      withSequence(
        withDelay(400, withTiming(1, { duration: 200 })),
        withTiming(0, { duration: 300 }),
        withDelay(1100, withTiming(0, { duration: 0 }))
      ),
      -1,
      false
    );
  }, [scissorsOpen, sparkle]);

  const scissorsStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${interpolate(scissorsOpen.value, [0, 1], [0, 15])}deg` }],
  }));

  const sparkleStyle = useAnimatedStyle(() => ({
    opacity: sparkle.value,
    transform: [{ scale: interpolate(sparkle.value, [0, 1], [0.5, 1.2]) }],
  }));

  return (
    <View style={[styles.animationContainer, { width: size, height: size }]}>
      <Animated.View style={[styles.iconContainer, scissorsStyle]}>
        <Scissors size={size * 0.4} color={BeautyMinimalTheme.beautyColors.amethyst[500]} />
      </Animated.View>

      {/* Sparkle effects */}
      <Animated.View
        style={[styles.sparkleEffect, { top: size * 0.2, right: size * 0.2 }, sparkleStyle]}
      />
      <Animated.View
        style={[styles.sparkleEffect, { bottom: size * 0.3, left: size * 0.2 }, sparkleStyle]}
      />
    </View>
  );
};

/**
 * MAIN HAIR THEMED LOADING COMPONENT
 */
export const HairThemedLoading: React.FC<HairThemedLoadingProps> = ({
  context,
  size = 'medium',
  _showIcon = true,
  triggerHaptics = false,
}) => {
  const config = CONTEXT_CONFIG[context];
  const sizeValue = size === 'small' ? 40 : size === 'large' ? 80 : 60;

  const hapticTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (triggerHaptics) {
      // Trigger initial haptic
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      // Set up periodic haptic feedback for long-running operations
      hapticTimeoutRef.current = setTimeout(() => {
        Haptics.selectionAsync();
      }, 3000);

      return () => {
        if (hapticTimeoutRef.current) {
          clearTimeout(hapticTimeoutRef.current);
        }
      };
    }
  }, [triggerHaptics, context]);

  const renderAnimation = () => {
    switch (context) {
      case 'photo-analysis':
        return <CameraFocusAnimation size={sizeValue} />;
      case 'hair-diagnosis':
        return <HairStrandAnalysis size={sizeValue} />;
      case 'formula-generation':
        return <ChemicalMixingAnimation size={sizeValue} />;
      case 'color-matching':
        return <ColorPaletteRotation size={sizeValue} />;
      case 'ai-processing':
        return <ScissorsCuttingAnimation size={sizeValue} />;
      default:
        return <ScissorsCuttingAnimation size={sizeValue} />;
    }
  };

  return (
    <View
      style={styles.container}
      accessibilityRole="progressbar"
      accessibilityLabel={config.message}
    >
      {renderAnimation()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  animationContainer: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },

  // Camera Focus Animation
  focusRing: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    borderRadius: 50,
    borderWidth: 2,
    borderColor: BeautyMinimalTheme.beautyColors.sage[300],
    backgroundColor: ColorConstants.TRANSPARENT,
  },
  flashOverlay: {
    position: 'absolute',
    width: '120%',
    height: '120%',
    borderRadius: 50,
    backgroundColor: BeautyMinimalTheme.neutrals.pure,
    zIndex: 1,
  },

  // Hair Strand Analysis
  hairStrandContainer: {
    position: 'absolute',
    width: '100%',
    height: '80%',
    top: '10%',
  },
  hairStrand: {
    position: 'absolute',
    width: 2,
    height: '80%',
    borderRadius: 1,
    opacity: 0.7,
  },

  // Chemical Mixing
  chemicalBubble: {
    position: 'absolute',
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: BeautyMinimalTheme.beautyColors.rose[300],
  },

  // Color Palette
  paletteContainer: {
    position: 'relative',
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  colorDot: {
    position: 'absolute',
    width: 8,
    height: 8,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.neutrals.mist,
  },

  // Scissors Animation
  sparkleEffect: {
    position: 'absolute',
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: BeautyMinimalTheme.beautyColors.amethyst[400],
  },
});

export default HairThemedLoading;
