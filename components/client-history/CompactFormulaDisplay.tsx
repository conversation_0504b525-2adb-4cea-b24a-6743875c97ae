/**
 * COMPACT FORMULA DISPLAY COMPONENT
 *
 * Transforms plain text formulas into rich visual displays for client history.
 * Follows Salonier's beauty-minimalist design system with professional visual hierarchy.
 *
 * Features:
 * - Color swatches and visual indicators
 * - Brand hierarchy display (Brand → Line → Shade)
 * - Processing information as badges
 * - AI confidence indicators
 * - Visual formula breakdown with ratios
 * - Professional appearance suitable for salon use
 */

import React, { memo, useMemo } from 'react';
import { View, Text, StyleSheet, ViewStyle } from 'react-native';
import { Palette, Clock, Droplet, TrendingUp, Star } from 'lucide-react-native';
import Animated, { FadeIn } from 'react-native-reanimated';
import { BeautyMinimalTheme } from '@/constants/beauty-minimal-theme';
import { PreviousFormula } from '@/stores/client-history-store';

interface CompactFormulaDisplayProps {
  service: PreviousFormula;
  variant?: 'compact' | 'detailed';
  showConfidence?: boolean;
  onPress?: () => void;
  style?: ViewStyle;
  testID?: string;
}

interface ParsedFormula {
  products: Array<{
    brand: string;
    line: string;
    shade: string;
    ratio: string;
  }>;
  oxidant?: {
    volume: string;
    ratio: string;
  };
  mixingRatio?: string;
  additives?: string[];
}

/**
 * Parse formula text into structured data for visual display
 */
const parseFormulaText = (formulaText: string, brand: string, line: string): ParsedFormula => {
  const formula: ParsedFormula = {
    products: [],
  };

  // Extract oxidant information
  const oxidantMatch = formulaText.match(/oxidante?\s*(\d+)\s*vol/i);
  if (oxidantMatch) {
    formula.oxidant = {
      volume: `${oxidantMatch[1]}vol`,
      ratio: '1:1', // Default ratio
    };
  }

  // Extract mixing ratios
  const ratioMatch = formulaText.match(/(\d+:\d+)/);
  if (ratioMatch) {
    formula.mixingRatio = ratioMatch[1];
  }

  // Extract product shades/colors
  const shadeMatches = formulaText.match(/(\d+\.?\d*)/g);
  if (shadeMatches && brand && line) {
    formula.products.push({
      brand,
      line,
      shade: shadeMatches[0] || '6.0',
      ratio: formula.mixingRatio?.split(':')[0] || '1',
    });
  }

  return formula;
};

/**
 * Generate color representation for shade
 */
const getShadeColor = (shade: string): string => {
  const level = parseFloat(shade.split('.')[0] || '6');

  // Map hair levels to approximate colors
  if (level <= 2) return '#2D1B10'; // Very dark brown
  if (level <= 4) return '#4A2C17'; // Dark brown
  if (level <= 6) return '#8B4513'; // Medium brown
  if (level <= 8) return '#CD853F'; // Light brown
  return '#F5DEB3'; // Very light/blonde
};

/**
 * Get confidence color based on satisfaction score
 */
const getConfidenceIndicator = (satisfaction: number) => {
  if (satisfaction >= 4) {
    return {
      color: BeautyMinimalTheme.semantic.status.success,
      text: 'Alta confianza',
      icon: '⭐',
    };
  } else if (satisfaction >= 3) {
    return {
      color: BeautyMinimalTheme.semantic.status.warning,
      text: 'Confianza media',
      icon: '👌',
    };
  } else {
    return {
      color: BeautyMinimalTheme.semantic.status.error,
      text: 'Baja confianza',
      icon: '⚠️',
    };
  }
};

export const CompactFormulaDisplay = memo<CompactFormulaDisplayProps>(
  ({
    service,
    variant = 'compact',
    showConfidence = true,
    _onPress,
    style,
    testID = 'compact-formula-display',
  }) => {
    const parsedFormula = useMemo(
      () => parseFormulaText(service.formula, service.brand, service.line),
      [service.formula, service.brand, service.line]
    );

    const confidenceIndicator = useMemo(
      () => getConfidenceIndicator(service.satisfaction),
      [service.satisfaction]
    );

    const containerStyle = [
      styles.container,
      variant === 'detailed' && styles.containerDetailed,
      style,
    ];

    return (
      <Animated.View
        style={containerStyle}
        entering={FadeIn.duration(300)}
        testID={testID}
        accessibilityLabel={`Fórmula de ${service.brand} ${service.line}`}
      >
        {/* Header with Brand Hierarchy */}
        <View style={styles.header}>
          <View style={styles.brandInfo}>
            <Palette
              size={16}
              color={BeautyMinimalTheme.semantic.interactive.professional.default}
            />
            <View style={styles.brandText}>
              <Text style={styles.brandName}>{service.brand}</Text>
              <Text style={styles.lineName}>{service.line}</Text>
            </View>
          </View>

          {showConfidence && (
            <View
              style={[
                styles.confidenceBadge,
                { backgroundColor: confidenceIndicator.color + '15' },
              ]}
            >
              <Text style={styles.confidenceIcon}>{confidenceIndicator.icon}</Text>
              <Text style={[styles.confidenceText, { color: confidenceIndicator.color }]}>
                {confidenceIndicator.text}
              </Text>
            </View>
          )}
        </View>

        {/* Color Swatches and Formula */}
        <View style={styles.formulaSection}>
          <View style={styles.colorSwatches}>
            {parsedFormula.products.map((product, index) => {
              const shadeColor = getShadeColor(product.shade);
              return (
                <View key={index} style={styles.colorSwatch}>
                  <View
                    style={[styles.colorCircle, { backgroundColor: shadeColor }]}
                    accessibilityLabel={`Color ${product.shade}`}
                  />
                  <Text style={styles.shadeText}>{product.shade}</Text>
                </View>
              );
            })}
          </View>

          <View style={styles.formulaDetails}>
            <Text style={styles.formulaText} numberOfLines={variant === 'compact' ? 2 : undefined}>
              {service.formula}
            </Text>
          </View>
        </View>

        {/* Processing Information */}
        <View style={styles.processingInfo}>
          <View style={styles.processingBadge}>
            <Clock size={12} color={BeautyMinimalTheme.semantic.text.secondary} />
            <Text style={styles.processingText}>{service.processingTime}min</Text>
          </View>

          {parsedFormula.oxidant && (
            <View style={styles.processingBadge}>
              <Droplet size={12} color={BeautyMinimalTheme.semantic.text.secondary} />
              <Text style={styles.processingText}>{parsedFormula.oxidant.volume}</Text>
            </View>
          )}

          {parsedFormula.mixingRatio && (
            <View style={styles.processingBadge}>
              <TrendingUp size={12} color={BeautyMinimalTheme.semantic.text.secondary} />
              <Text style={styles.processingText}>{parsedFormula.mixingRatio}</Text>
            </View>
          )}

          <View style={styles.ratingBadge}>
            <Star
              size={12}
              color={BeautyMinimalTheme.semantic.status.warning}
              fill={BeautyMinimalTheme.semantic.status.warning}
            />
            <Text style={styles.ratingText}>{service.satisfaction}/5</Text>
          </View>
        </View>

        {/* Detailed Mode: Additional Information */}
        {variant === 'detailed' && service.notes && (
          <View style={styles.notesSection}>
            <Text style={styles.notesLabel}>Notas</Text>
            <Text style={styles.notesText}>{service.notes}</Text>
          </View>
        )}
      </Animated.View>
    );
  }
);

CompactFormulaDisplay.displayName = 'CompactFormulaDisplay';

const styles = StyleSheet.create({
  container: {
    backgroundColor: BeautyMinimalTheme.semantic.background.secondary,
    borderRadius: BeautyMinimalTheme.radius.md,
    padding: BeautyMinimalTheme.spacing.sm,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.semantic.border.subtle,
  },

  containerDetailed: {
    padding: BeautyMinimalTheme.spacing.lg,
  },

  // Header Section
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },

  brandInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: BeautyMinimalTheme.spacing.sm,
  },

  brandText: {
    flex: 1,
  },

  brandName: {
    fontSize: BeautyMinimalTheme.typography.sizes.subheading,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.primary,
    lineHeight:
      BeautyMinimalTheme.typography.sizes.subheading *
      BeautyMinimalTheme.typography.lineHeights.tight,
  },

  lineName: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginTop: 2,
  },

  confidenceBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: BeautyMinimalTheme.spacing.sm,
    paddingVertical: BeautyMinimalTheme.spacing.xs,
    borderRadius: BeautyMinimalTheme.radius.full,
    gap: BeautyMinimalTheme.spacing.xs,
  },

  confidenceIcon: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
  },

  confidenceText: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
  },

  // Formula Section
  formulaSection: {
    marginBottom: BeautyMinimalTheme.spacing.sm,
  },

  colorSwatches: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: BeautyMinimalTheme.spacing.sm,
    gap: BeautyMinimalTheme.spacing.md,
  },

  colorSwatch: {
    alignItems: 'center',
    gap: BeautyMinimalTheme.spacing.xs,
  },

  colorCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: BeautyMinimalTheme.semantic.border.default,
    ...BeautyMinimalTheme.shadows.subtle,
  },

  shadeText: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    color: BeautyMinimalTheme.semantic.text.secondary,
  },

  formulaDetails: {
    backgroundColor: BeautyMinimalTheme.semantic.background.tertiary,
    borderRadius: BeautyMinimalTheme.radius.sm,
    padding: BeautyMinimalTheme.spacing.sm,
  },

  formulaText: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.primary,
    lineHeight:
      BeautyMinimalTheme.typography.sizes.small * BeautyMinimalTheme.typography.lineHeights.normal,
  },

  // Processing Information
  processingInfo: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: BeautyMinimalTheme.spacing.sm,
    alignItems: 'center',
  },

  processingBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: BeautyMinimalTheme.semantic.transparency.professional.transparent10,
    paddingHorizontal: BeautyMinimalTheme.spacing.sm,
    paddingVertical: BeautyMinimalTheme.spacing.xs,
    borderRadius: BeautyMinimalTheme.radius.sm,
    gap: BeautyMinimalTheme.spacing.xs,
  },

  processingText: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    fontWeight: BeautyMinimalTheme.typography.weights.medium,
    color: BeautyMinimalTheme.semantic.text.secondary,
  },

  ratingBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: BeautyMinimalTheme.semantic.transparency.status.warning.transparent10,
    paddingHorizontal: BeautyMinimalTheme.spacing.sm,
    paddingVertical: BeautyMinimalTheme.spacing.xs,
    borderRadius: BeautyMinimalTheme.radius.sm,
    gap: BeautyMinimalTheme.spacing.xs,
    marginLeft: 'auto', // Push to right
  },

  ratingText: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.status.warning,
  },

  // Detailed Mode: Notes
  notesSection: {
    marginTop: BeautyMinimalTheme.spacing.md,
    paddingTop: BeautyMinimalTheme.spacing.md,
    borderTopWidth: 1,
    borderTopColor: BeautyMinimalTheme.semantic.border.subtle,
  },

  notesLabel: {
    fontSize: BeautyMinimalTheme.typography.sizes.caption,
    fontWeight: BeautyMinimalTheme.typography.weights.semibold,
    color: BeautyMinimalTheme.semantic.text.secondary,
    marginBottom: BeautyMinimalTheme.spacing.xs,
    textTransform: 'uppercase',
  },

  notesText: {
    fontSize: BeautyMinimalTheme.typography.sizes.small,
    color: BeautyMinimalTheme.semantic.text.primary,
    lineHeight:
      BeautyMinimalTheme.typography.sizes.small * BeautyMinimalTheme.typography.lineHeights.normal,
  },
});
