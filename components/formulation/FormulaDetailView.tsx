import React from 'react';
import { View, StyleSheet } from 'react-native';
import { EnhancedFormulationView } from './EnhancedFormulationView';
import { MaterialsSummaryCard } from './MaterialsSummaryCard';
import FormulaTips from './FormulaTips';

// Type for service with populated formula data
type ServiceWithFormula = {
  id: string;
  serviceDate: string | null;
  serviceType: string;
  formula: {
    id: string;
    formulaText: string;
    formulaData: unknown; // JSON structure from database
    totalCost: number | null;
    processingTime: number | null;
    technique: string | null;
    brand: string | null;
    line: string | null;
    developerVolume: number | null;
  } | null;
  aiAnalysis: unknown; // JSON structure with hair analysis
};

interface FormulaDetailViewProps {
  service: ServiceWithFormula;
  showMaterials?: boolean;
  showTips?: boolean;
  compact?: boolean;
}

export function FormulaDetailView({
  service,
  showMaterials = true,
  showTips = true,
  compact = false,
}: FormulaDetailViewProps) {
  const { formula, aiAnalysis } = service;

  // Parse analysis data for tips
  const analysisData = React.useMemo(() => {
    if (!aiAnalysis) return {};

    try {
      // Try to extract relevant analysis data
      const analysis = aiAnalysis as Record<string, unknown>;
      const hairAnalysis = analysis?.hair_analysis as Record<string, unknown>;

      return {
        hasGreyHair: Boolean(hairAnalysis?.grey_coverage),
        averageDepthLevel: Number(hairAnalysis?.current_level) || 5,
        averageLevel: Number(hairAnalysis?.current_level) || 5,
        zonePhysicalAnalysis: hairAnalysis?.condition || {},
      };
    } catch {
      // Silent fail for analysis parsing
      return {};
    }
  }, [aiAnalysis]);

  // Parse formula data for structured display
  const formulationData = React.useMemo(() => {
    if (!formula?.formulaData) return null;

    try {
      // Try to parse the structured formula data
      if (typeof formula.formulaData === 'string') {
        return JSON.parse(formula.formulaData);
      }
      return formula.formulaData;
    } catch {
      // Silent fail for formula parsing
      return null;
    }
  }, [formula?.formulaData]);

  if (!formula) {
    return null;
  }

  return (
    <View style={[styles.container, compact && styles.containerCompact]}>
      {/* Enhanced Formulation View - Shows structured steps */}
      {formulationData && (
        <EnhancedFormulationView
          formulationData={formulationData}
          formulaText={formula.formulaText}
          compact={compact}
          style={styles.formulationView}
        />
      )}

      {/* Fallback to text if no structured data */}
      {!formulationData && formula.formulaText && (
        <View style={styles.fallbackContainer}>
          <EnhancedFormulationView
            formulationData={{
              instructions: formula.formulaText,
              technique: formula.technique || 'Aplicación completa',
              processingTime: formula.processingTime || 30,
              brand: formula.brand || '',
              line: formula.line || '',
            }}
            formulaText={formula.formulaText}
            compact={compact}
            style={styles.formulationView}
          />
        </View>
      )}

      {/* Materials Summary */}
      {showMaterials && (
        <MaterialsSummaryCard
          formulationData={formulationData}
          formulaText={formula.formulaText}
          selectedBrand={formula.brand || ''}
          selectedLine={formula.line || ''}
          compact={compact}
          style={[styles.materialsCard, compact && styles.materialsCardCompact]}
        />
      )}

      {/* Formula Tips and Warnings */}
      {showTips && !compact && (
        <View style={styles.tipsContainer}>
          <FormulaTips
            analysis={analysisData}
            technique={formula.technique || 'full_color'}
            targetLevel={analysisData.averageLevel || 7}
            viabilityAnalysis={null}
          />
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    gap: 16,
  },
  containerCompact: {
    gap: 12,
  },
  formulationView: {
    marginBottom: 0,
  },
  fallbackContainer: {
    // Styling for fallback text display
  },
  materialsCard: {
    // Default materials card styling
  },
  materialsCardCompact: {
    // Compact materials card styling
  },
  tipsContainer: {
    marginTop: 8,
  },
});
