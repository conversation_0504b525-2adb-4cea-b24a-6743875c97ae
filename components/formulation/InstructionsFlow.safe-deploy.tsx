/**
 * InstructionsFlow - Safe Deployment Implementation
 *
 * This is the critical replacement file that activates the A/B testing system.
 * It replaces the original 3,448-line monolith with the wrapper component
 * that intelligently routes to either the new modular implementation
 * or the legacy version based on feature flags.
 *
 * DEPLOYMENT SAFETY:
 * - Zero breaking changes to external API
 * - Instant rollback via feature flags
 * - Error boundaries with automatic fallback
 * - Performance monitoring and alerting
 *
 * TO DEPLOY:
 * 1. Backup original: cp InstructionsFlow.tsx InstructionsFlow.original.tsx
 * 2. Replace: cp InstructionsFlow.safe-deploy.tsx InstructionsFlow.tsx
 * 3. Commit and deploy
 */

// Simple re-export to the wrapper component
// This maintains exact API compatibility while enabling A/B testing
export { default } from './InstructionsFlowWrapper';

// Re-export types for TypeScript compatibility
export type { InstructionsFlowProps } from './InstructionsFlow.new';
