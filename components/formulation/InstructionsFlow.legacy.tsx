import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  Animated,
  TextInput,
} from 'react-native';
import {
  ChevronRight,
  ChevronLeft,
  Check,
  Clock,
  Beaker,
  Palette,
  // Timer as TimerIcon,
  Trophy,
  // Play,
  // Pause,
  RotateCcw,
  AlertCircle,
  // Volume2,
  // VolumeX,
  Package,
  GitMerge,
  Target,
  Calculator,
  FlaskConical,
  Brush,
  Calendar,
  Lightbulb,
  Sparkles,
  Plus,
  Minus,
  Scissors,
  Target as TargetIcon,
} from 'lucide-react-native';
// import { LinearGradient } from 'expo-linear-gradient'; // Removed for consistency
import { VisualFormulationData } from '@/types/visual-formulation';
import { HairZone, HairZoneDisplay } from '@/types/hair-diagnosis';
import * as Haptics from 'expo-haptics';
import { commonStyles } from '@/styles/commonStyles';
import Colors from '@/constants/colors';

const { width: _screenWidth, height: _screenHeight } = Dimensions.get('window');

// Custom animated touchable for micro-animations
interface AnimatedTouchableProps {
  children: React.ReactNode;
  onPress?: () => void;
  style?: Record<string, unknown>;
  disabled?: boolean;
  activeOpacity?: number;
}

const AnimatedTouchable = ({ children, onPress, style, ...props }: AnimatedTouchableProps) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.97,
      useNativeDriver: true,
      speed: 20,
      bounciness: 0,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
      speed: 20,
      bounciness: 0,
    }).start();
  };

  return (
    <TouchableOpacity
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      activeOpacity={1}
      {...props}
    >
      <Animated.View style={[style, { transform: [{ scale: scaleAnim }] }]}>
        {children}
      </Animated.View>
    </TouchableOpacity>
  );
};

// Using app theme colors instead of custom palette
// All colors now use Colors.light theme constants

interface InstructionsFlowProps {
  formulaData: VisualFormulationData;
  onClose?: () => void;
  onComplete?: () => void;
}

interface ChecklistScreenProps {
  formulaData: VisualFormulationData;
  checkedItems: Record<string, boolean>;
  setCheckedItems: (items: Record<string, boolean>) => void;
}

interface ScreenComponentProps {
  formulaData: VisualFormulationData;
}

interface Ingredient {
  name: string;
  amount: number | string;
  unit: string;
  type?: string;
}

interface Zone {
  zone: string;
  ingredients: Ingredient[];
}

interface FlowStep {
  id: string;
  title: string;
  icon: React.ComponentType<{ size: number; color: string }>;
  color: string;
}

const FLOW_STEPS: FlowStep[] = [
  {
    id: 'checklist',
    title: 'Lista de Verificación',
    icon: Package,
    color: Colors.light.primary,
  },
  {
    id: 'transformation',
    title: 'Tu Transformación',
    icon: GitMerge,
    color: Colors.light.accent,
  },
  {
    id: 'formulas',
    title: 'Fórmulas Personalizadas',
    icon: Target,
    color: Colors.light.primary,
  },
  {
    id: 'proportions',
    title: 'Guía de Proporciones',
    icon: Calculator,
    color: Colors.light.primaryDark,
  },
  {
    id: 'calculator',
    title: 'Calculadora Visual',
    icon: Calculator,
    color: Colors.light.primary,
  },
  {
    id: 'mixing',
    title: 'Estación de Mezcla',
    icon: FlaskConical,
    color: Colors.light.secondary,
  },
  {
    id: 'application',
    title: 'Guía de Aplicación',
    icon: Brush,
    color: Colors.light.accent,
  },
  {
    id: 'timeline',
    title: 'Cronograma',
    icon: Calendar,
    color: Colors.light.primaryDark,
  },
  {
    id: 'tips',
    title: 'Tips Profesionales',
    icon: Lightbulb,
    color: Colors.light.warning,
  },
  {
    id: 'result',
    title: 'Resultado Esperado',
    icon: Sparkles,
    color: Colors.light.success,
  },
];

export default function InstructionsFlow({
  formulaData,
  onClose,
  onComplete,
}: InstructionsFlowProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [checkedItems, setCheckedItems] = useState<string[]>([]);
  const slideAnim = useRef(new Animated.Value(0)).current;
  const fadeAnim = useRef(new Animated.Value(1)).current;

  // Progress dot animations
  const progressAnims = useRef(FLOW_STEPS.map(() => new Animated.Value(1))).current;

  useEffect(() => {
    // Animate step transition
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: -50,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start(() => {
      slideAnim.setValue(50);
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    });

    // Animate progress dots
    progressAnims.forEach((anim, index) => {
      if (index === currentStep) {
        Animated.sequence([
          Animated.timing(anim, {
            toValue: 1.3,
            duration: 200,
            useNativeDriver: true,
          }),
          Animated.timing(anim, {
            toValue: 1.2,
            duration: 100,
            useNativeDriver: true,
          }),
        ]).start();
      } else if (index < currentStep) {
        Animated.timing(anim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }).start();
      } else {
        Animated.timing(anim, {
          toValue: 0.8,
          duration: 200,
          useNativeDriver: true,
        }).start();
      }
    });
  }, [currentStep, fadeAnim, progressAnims, slideAnim]);

  const goToNextStep = async () => {
    if (currentStep < FLOW_STEPS.length - 1) {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      setCompletedSteps([...completedSteps, currentStep]);
      setCurrentStep(currentStep + 1);
    } else {
      completeFlow();
    }
  };

  const goToPreviousStep = async () => {
    if (currentStep > 0) {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      setCurrentStep(currentStep - 1);
    }
  };

  const completeFlow = async () => {
    await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    setCompletedSteps([...completedSteps, currentStep]);
    if (onComplete) {
      onComplete();
    }
  };

  const renderStepContent = () => {
    const step = FLOW_STEPS[currentStep];

    switch (step.id) {
      case 'checklist':
        return (
          <ChecklistScreen
            formulaData={formulaData}
            checkedItems={checkedItems}
            setCheckedItems={setCheckedItems}
          />
        );
      case 'transformation':
        return <TransformationScreen formulaData={formulaData} />;
      case 'formulas':
        return <FormulasScreen formulaData={formulaData} />;
      case 'proportions':
        return <ProportionsScreen formulaData={formulaData} />;
      case 'calculator':
        return <CalculatorScreen formulaData={formulaData} />;
      case 'mixing':
        return <MixingScreen formulaData={formulaData} />;
      case 'application':
        return <ApplicationScreen formulaData={formulaData} />;
      case 'timeline':
        return <TimelineScreen formulaData={formulaData} />;
      case 'tips':
        return <TipsScreen formulaData={formulaData} />;
      case 'result':
        return <ResultScreen formulaData={formulaData} />;
      default:
        return null;
    }
  };

  return (
    <View style={styles.container} testID="instructions-flow-legacy">
      {/* Header */}
      <View style={[styles.header, { backgroundColor: FLOW_STEPS[currentStep].color }]}>
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <ChevronLeft size={28} color="white" />
        </TouchableOpacity>

        <View style={styles.headerContent}>
          <Text style={styles.stepNumber}>
            Paso {currentStep + 1} de {FLOW_STEPS.length}
          </Text>
          <Text style={styles.stepTitle}>{FLOW_STEPS[currentStep].title}</Text>
        </View>

        <View style={styles.soundButton} />
      </View>

      {/* Progress Indicators */}
      <View style={styles.progressContainer}>
        {/* Progress Bar */}
        <View style={styles.progressBarContainer}>
          <View style={styles.progressBarTrack} />
          <Animated.View
            style={[
              styles.progressBarFill,
              {
                width: `${((currentStep + 1) / FLOW_STEPS.length) * 100}%`,
                backgroundColor: FLOW_STEPS[currentStep].color,
              },
            ]}
          />
        </View>

        {/* Progress Dots */}
        <View style={styles.progressDotsContainer}>
          {FLOW_STEPS.map((step, index) => (
            <Animated.View
              key={step.id}
              style={[
                styles.progressDot,
                {
                  backgroundColor: index <= currentStep ? step.color : Colors.light.surface,
                  transform: [{ scale: progressAnims[index] }],
                },
                index <= currentStep ? styles.progressDotActive : styles.progressDotInactive,
              ]}
            />
          ))}
        </View>
      </View>

      {/* Content */}
      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          },
        ]}
      >
        {renderStepContent()}
      </Animated.View>

      {/* Navigation */}
      <View style={styles.navigation}>
        <AnimatedTouchable
          style={[styles.navButton, styles.navButtonSecondary]}
          onPress={goToPreviousStep}
          disabled={currentStep === 0}
        >
          <ChevronLeft
            size={24}
            color={currentStep === 0 ? Colors.light.gray : Colors.light.text}
          />
          <Text style={[styles.navButtonText, currentStep === 0 && styles.navButtonTextDisabled]}>
            Anterior
          </Text>
        </AnimatedTouchable>

        <AnimatedTouchable
          style={[
            styles.navButton,
            styles.navButtonPrimary,
            { backgroundColor: FLOW_STEPS[currentStep].color },
          ]}
          onPress={goToNextStep}
        >
          <Text style={styles.navButtonTextPrimary}>
            {currentStep === FLOW_STEPS.length - 1 ? 'Completar' : 'Siguiente'}
          </Text>
          <ChevronRight size={24} color="white" />
        </AnimatedTouchable>
      </View>
    </View>
  );
}

// Screen Components

// Pantalla 1: Lista de Verificación
function ChecklistScreen({ formulaData, checkedItems, setCheckedItems }: ChecklistScreenProps) {
  // Crear datos mock completos si no vienen todos los productos
  const mockProducts = [
    {
      id: 'color-1',
      name: 'Illumina Color 8/69 (60g)',
      type: 'color',
      zone: 'General',
    },
    {
      id: 'color-2',
      name: 'Illumina Color 8/36 (30g)',
      type: 'color',
      zone: 'General',
    },
    {
      id: 'developer-1',
      name: 'Welloxon Perfect 20vol (135ml)',
      type: 'developer',
      zone: 'General',
    },
    {
      id: 'additive-1',
      name: 'Color Fresh 0/68 (5g)',
      type: 'additive',
      zone: 'General',
    },
  ];

  // Extraer TODOS los productos de la fórmula, incluyendo developers y additives
  let products = [];

  if (formulaData.zones && formulaData.zones.length > 0) {
    // Extraer productos de las zonas
    products = formulaData.zones.flatMap((zone: Zone) =>
      zone.ingredients.map((ing: Ingredient, index: number) => ({
        id: `${zone.zone}-${ing.name}-${index}`,
        name: `${ing.name} (${ing.amount}${ing.unit})`,
        type: ing.type || 'color',
        zone:
          zone.zone === HairZone.ROOTS
            ? HairZoneDisplay[HairZone.ROOTS]
            : zone.zone === HairZone.MIDS
              ? HairZoneDisplay[HairZone.MIDS]
              : zone.zone === HairZone.ENDS
                ? HairZoneDisplay[HairZone.ENDS]
                : 'General',
      }))
    );

    // Añadir developer si existe
    if (formulaData.mixingProportions?.developer) {
      const dev = formulaData.mixingProportions.developer;
      products.push({
        id: 'developer-main',
        name: `${dev.name} ${dev.volume}vol (${dev.amount || '135'}ml)`,
        type: 'developer',
        zone: 'General',
      });
    }

    // Añadir additives si existen
    if (formulaData.additives && formulaData.additives.length > 0) {
      formulaData.additives.forEach((add: Ingredient, index: number) => {
        products.push({
          id: `additive-${index}`,
          name: `${add.name} (${add.amount}${add.unit || 'g'})`,
          type: 'additive',
          zone: 'General',
        });
      });
    }
  }

  // Si no hay productos, usar mock
  if (products.length === 0) {
    products = mockProducts;
  }

  const materials = [
    { id: 'bowl', name: 'Bowl de mezcla', type: 'material' },
    { id: 'brush', name: 'Pincel aplicador', type: 'material' },
    { id: 'gloves', name: 'Guantes protectores', type: 'material' },
    { id: 'cape', name: 'Capa de cliente', type: 'material' },
    { id: 'towel', name: 'Toallas', type: 'material' },
  ];

  const toggleItem = async (id: string) => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setCheckedItems((prev: string[]) =>
      prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]
    );
  };

  // Calculate completion percentage
  const totalItems = products.length + materials.length;
  const completedItems = checkedItems.length;
  const _completionPercentage = totalItems > 0 ? (completedItems / totalItems) * 100 : 0;

  const allItems = [...products, ...materials];
  const progress = (checkedItems.length / allItems.length) * 100;

  return (
    <ScrollView style={styles.screenContent} showsVerticalScrollIndicator={false}>
      <View style={[styles.checklistHeader, { backgroundColor: Colors.light.primary }]}>
        <Package size={48} color="white" />
        <Text style={styles.checklistHeaderTitle}>Verifica que tienes todo listo</Text>
        <View style={styles.progressBar}>
          <Animated.View
            style={[
              styles.progressFill,
              { width: `${progress}%`, backgroundColor: Colors.light.background },
            ]}
          />
        </View>
        <Text style={styles.progressText}>{Math.round(progress)}% Completo</Text>
      </View>

      <View style={styles.checklistContent}>
        <Text style={styles.sectionTitle}>Productos de la Fórmula</Text>

        {/* Agrupar productos por tipo */}
        {['color', 'developer', 'additive'].map(type => {
          const productsOfType = products.filter((p: Ingredient) => p.type === type);
          if (productsOfType.length === 0) return null;

          const typeLabels = {
            color: 'Tintes',
            developer: 'Oxidantes',
            additive: 'Aditivos',
          };

          const typeColors = {
            color: Colors.light.primary,
            developer: Colors.light.accent,
            additive: Colors.light.warning,
          };

          return (
            <View key={type} style={styles.productGroup}>
              <View style={styles.productGroupHeader}>
                <View
                  style={[
                    styles.productGroupIndicator,
                    {
                      backgroundColor: typeColors[type as keyof typeof typeColors],
                    },
                  ]}
                />
                <Text style={styles.productGroupTitle}>
                  {typeLabels[type as keyof typeof typeLabels]} ({productsOfType.length})
                </Text>
              </View>
              {productsOfType.map((item: Ingredient) => (
                <TouchableOpacity
                  key={item.id}
                  style={styles.checkItem}
                  onPress={() => toggleItem(item.id)}
                >
                  <View
                    style={[
                      styles.checkbox,
                      checkedItems.includes(item.id) && styles.checkboxChecked,
                    ]}
                  >
                    {checkedItems.includes(item.id) && (
                      <Animated.View
                        style={[
                          styles.animatedCheckmark,
                          {
                            transform: [
                              {
                                scale: checkedItems.includes(item.id) ? 1 : 0,
                              },
                            ],
                          },
                        ]}
                      >
                        <Check size={16} color="white" />
                      </Animated.View>
                    )}
                  </View>
                  <View style={styles.checkItemInfo}>
                    <Text style={styles.checkItemName}>{item.name}</Text>
                    <Text style={styles.checkItemZone}>{item.zone}</Text>
                  </View>
                  <View
                    style={[
                      styles.typeIndicator,
                      {
                        backgroundColor: typeColors[type as keyof typeof typeColors],
                      },
                    ]}
                  />
                </TouchableOpacity>
              ))}
            </View>
          );
        })}

        <Text style={[styles.sectionTitle, commonStyles.marginTop20]}>Materiales de Trabajo</Text>
        {materials.map(item => (
          <TouchableOpacity
            key={item.id}
            style={styles.checkItem}
            onPress={() => toggleItem(item.id)}
          >
            <View
              style={[styles.checkbox, checkedItems.includes(item.id) && styles.checkboxChecked]}
            >
              {checkedItems.includes(item.id) && (
                <Animated.View
                  style={[
                    styles.animatedCheckmark,
                    {
                      transform: [
                        {
                          scale: checkedItems.includes(item.id) ? 1 : 0,
                        },
                      ],
                    },
                  ]}
                >
                  <Check size={16} color="white" />
                </Animated.View>
              )}
            </View>
            <Text style={styles.checkItemName}>{item.name}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </ScrollView>
  );
}

// Pantalla 2: Tu Transformación de Color
function TransformationScreen({ formulaData }: ScreenComponentProps) {
  const transition = formulaData.colorTransition;

  return (
    <ScrollView style={styles.screenContent} showsVerticalScrollIndicator={false}>
      <View style={styles.transformationHeader}>
        <View style={styles.transformationIconContainer}>
          <Sparkles size={48} color={Colors.light.primary} />
          <View style={styles.transformationIconOverlay}>
            <Palette size={24} color={Colors.light.accent} />
          </View>
        </View>
        <Text style={styles.transformationTitle}>Tu Transformación de Color</Text>
        <Text style={styles.transformationSubtitle}>Visualiza tu cambio personalizado</Text>
      </View>

      <View style={styles.colorComparison}>
        <View style={styles.colorBlock}>
          <View
            style={[
              styles.colorCircle,
              {
                backgroundColor: transition.current.hex || Colors.light.secondary,
              },
            ]}
          >
            <Text style={styles.colorLevel}>{transition.current.level}</Text>
          </View>
          <Text style={styles.colorLabel}>Color Actual</Text>
          <Text style={styles.colorTone}>{transition.current.tone}</Text>
        </View>

        <View style={styles.arrowContainer}>
          <ChevronRight size={32} color={Colors.light.accent} />
        </View>

        <View style={styles.colorBlock}>
          <View
            style={[
              styles.colorCircle,
              {
                backgroundColor: transition.target.hex || Colors.light.primaryLight,
              },
            ]}
          >
            <Text style={styles.colorLevel}>{transition.target.level}</Text>
          </View>
          <Text style={styles.colorLabel}>Color Objetivo</Text>
          <Text style={styles.colorTone}>{transition.target.tone}</Text>
        </View>
      </View>

      <View style={styles.difficultyCard}>
        <Text style={styles.difficultyLabel}>Nivel de Complejidad</Text>
        <View
          style={[
            styles.difficultyBadge,
            {
              backgroundColor:
                transition.difficulty === 'easy'
                  ? Colors.light.success
                  : transition.difficulty === 'moderate'
                    ? Colors.light.warning
                    : transition.difficulty === 'challenging'
                      ? Colors.light.primary
                      : Colors.light.error,
            },
          ]}
        >
          <Text style={styles.difficultyText}>
            {transition.difficulty === 'easy'
              ? 'Fácil'
              : transition.difficulty === 'moderate'
                ? 'Moderado'
                : transition.difficulty === 'challenging'
                  ? 'Desafiante'
                  : 'Complejo'}
          </Text>
        </View>
      </View>

      {transition.sessions && transition.sessions > 1 && (
        <View style={styles.sessionsCard}>
          <AlertCircle size={20} color={Colors.light.warning} />
          <Text style={styles.sessionsText}>
            Se recomienda realizar en {transition.sessions} sesiones
          </Text>
        </View>
      )}

      <View style={styles.statsContainer}>
        <View style={styles.statCard}>
          <Text style={styles.statValue}>
            {Math.abs(transition.target.level - transition.current.level).toFixed(0)}
          </Text>
          <Text style={styles.statLabel}>Niveles de cambio</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statValue}>{formulaData.applicationGuide.totalTime}</Text>
          <Text style={styles.statLabel}>Minutos totales</Text>
        </View>
      </View>
    </ScrollView>
  );
}

// Pantalla 3: Fórmulas Personalizadas
function FormulasScreen({ formulaData }: ScreenComponentProps) {
  const [selectedZone, setSelectedZone] = useState(0);
  const zones = formulaData.zones || [];

  // Detect if this is a single formula or multi-zone
  const isSingleFormula = zones.length === 1 && zones[0].zone === 'global';

  // Group ingredients by type to avoid duplicates
  const groupIngredientsByType = (ingredients: Ingredient[]) => {
    const groups: Record<string, Ingredient[]> = {
      color: [],
      developer: [],
      additive: [],
      treatment: [],
    };

    ingredients.forEach(ing => {
      const type = ing.type || 'color';
      if (!groups[type]) groups[type] = [];
      groups[type].push(ing);
    });

    return groups;
  };

  return (
    <ScrollView style={styles.screenContent} showsVerticalScrollIndicator={false}>
      <View style={styles.formulasHeader}>
        <Target size={48} color={Colors.light.primary} />
        <Text style={styles.formulasTitle}>
          {isSingleFormula ? 'Fórmula Personalizada' : 'Fórmulas por Zona'}
        </Text>
      </View>

      {!isSingleFormula && (
        <View style={styles.zoneSelector}>
          {zones.map((zone: Zone, index: number) => (
            <TouchableOpacity
              key={zone.zone}
              style={[styles.zoneTab, selectedZone === index && styles.zoneTabActive]}
              onPress={() => setSelectedZone(index)}
            >
              <View style={styles.zoneTabContent}>
                <View
                  style={[
                    styles.zoneTabIndicator,
                    {
                      backgroundColor:
                        zone.zone === HairZone.ROOTS
                          ? Colors.light.primary
                          : zone.zone === HairZone.MIDS
                            ? Colors.light.warning
                            : zone.zone === HairZone.ENDS
                              ? Colors.light.accent
                              : Colors.light.secondary,
                    },
                  ]}
                />
                <Text
                  style={[styles.zoneTabText, selectedZone === index && styles.zoneTabTextActive]}
                >
                  {zone.zone === HairZone.ROOTS
                    ? HairZoneDisplay[HairZone.ROOTS]
                    : zone.zone === HairZone.MIDS
                      ? HairZoneDisplay[HairZone.MIDS]
                      : zone.zone === HairZone.ENDS
                        ? HairZoneDisplay[HairZone.ENDS]
                        : 'General'}
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      )}

      {zones.length > 0 && (
        <View style={styles.formulaCard}>
          <Text style={styles.formulaZoneTitle}>
            {zones[selectedZone]?.title || 'Fórmula Principal'}
          </Text>

          {/* Group ingredients by type */}
          {(() => {
            const groups = groupIngredientsByType(zones[selectedZone]?.ingredients || []);
            const typeOrder = ['color', 'developer', 'additive', 'treatment'];
            const typeLabels: Record<string, string> = {
              color: 'Colorantes',
              developer: 'Oxidantes',
              additive: 'Aditivos',
              treatment: 'Tratamientos',
            };

            return typeOrder.map(type => {
              const ingredients = groups[type];
              if (!ingredients || ingredients.length === 0) return null;

              return (
                <View key={type} style={styles.ingredientGroup}>
                  <Text style={styles.ingredientGroupTitle}>{typeLabels[type]}</Text>
                  {ingredients.map((ing: Ingredient, index: number) => (
                    <View key={`${type}-${index}`} style={styles.ingredientRow}>
                      <View
                        style={[
                          styles.ingredientIcon,
                          {
                            backgroundColor:
                              ing.type === 'color'
                                ? Colors.light.primary
                                : ing.type === 'developer'
                                  ? Colors.light.accent
                                  : ing.type === 'additive'
                                    ? Colors.light.warning
                                    : Colors.light.secondary,
                          },
                        ]}
                      >
                        <Text style={styles.ingredientIconText}>
                          {ing.type === 'color'
                            ? 'T'
                            : ing.type === 'developer'
                              ? 'O'
                              : ing.type === 'additive'
                                ? 'A'
                                : 'T'}
                        </Text>
                      </View>
                      <View style={styles.ingredientInfo}>
                        <Text style={styles.ingredientName}>{ing.name}</Text>
                        {ing.code && <Text style={styles.ingredientCode}>Código: {ing.code}</Text>}
                      </View>
                      <View style={styles.ingredientAmountContainer}>
                        <Text style={styles.ingredientAmount}>{ing.amount}</Text>
                        <Text style={styles.ingredientUnit}>{ing.unit}</Text>
                      </View>
                    </View>
                  ))}
                </View>
              );
            });
          })()}

          {zones[selectedZone]?.mixingRatio && (
            <View style={styles.ratioInfo}>
              <Text style={styles.ratioLabel}>Proporción de mezcla:</Text>
              <Text style={styles.ratioValue}>{zones[selectedZone].mixingRatio}</Text>
            </View>
          )}

          {zones[selectedZone]?.processingTime && (
            <View style={styles.timeInfo}>
              <Clock size={16} color={Colors.light.secondary} />
              <Text style={styles.timeText}>
                Tiempo de proceso: {zones[selectedZone].processingTime} min
              </Text>
            </View>
          )}
        </View>
      )}
    </ScrollView>
  );
}

// Pantalla 4: Guía de Proporciones
function ProportionsScreen({ formulaData }: ScreenComponentProps) {
  const proportions = formulaData.mixingProportions;
  const [selectedRatio, setSelectedRatio] = useState(proportions.ratio);

  const calculateAmounts = (base: number, ratio: string) => {
    const ratioNum = parseFloat(ratio.split(':')[1]);
    return {
      color: base,
      developer: base * ratioNum,
      total: base + base * ratioNum,
    };
  };

  return (
    <ScrollView style={styles.screenContent} showsVerticalScrollIndicator={false}>
      <View style={styles.proportionsHeader}>
        <Calculator size={48} color={Colors.light.primaryDark} />
        <Text style={styles.proportionsTitle}>Guía de Proporciones</Text>
      </View>

      <View style={styles.ratioSelector}>
        {proportions.presets.map(
          (preset: { name: string; ratio: string; description: string }, _index: number) => (
            <TouchableOpacity
              key={preset.ratio}
              style={[
                styles.ratioOption,
                selectedRatio === preset.ratio && styles.ratioOptionActive,
              ]}
              onPress={() => setSelectedRatio(preset.ratio)}
            >
              <Text
                style={[styles.ratioText, selectedRatio === preset.ratio && styles.ratioTextActive]}
              >
                {preset.ratio}
              </Text>
              <Text
                style={[
                  styles.ratioDescription,
                  selectedRatio === preset.ratio && styles.ratioDescriptionActive,
                ]}
              >
                {preset.description}
              </Text>
            </TouchableOpacity>
          )
        )}
      </View>

      <View style={styles.visualProportions}>
        <View style={styles.proportionBars}>
          <View style={[styles.proportionBar, styles.proportionBarPrimary]}>
            <Text style={styles.proportionBarText}>Tinte</Text>
          </View>
          <View
            style={[
              styles.proportionBar,
              {
                flex: parseFloat(selectedRatio.split(':')[1]),
                backgroundColor: Colors.light.accent,
              },
            ]}
          >
            <Text style={styles.proportionBarText}>Oxidante</Text>
          </View>
        </View>
      </View>

      <View style={styles.referenceTable}>
        <Text style={styles.tableTitle}>Tabla de Referencia Rápida</Text>
        <View style={styles.tableHeader}>
          <Text style={styles.tableHeaderCell}>Tinte</Text>
          <Text style={styles.tableHeaderCell}>→</Text>
          <Text style={styles.tableHeaderCell}>Oxidante</Text>
          <Text style={styles.tableHeaderCell}>Total</Text>
        </View>
        {[30, 45, 60, 90].map(amount => {
          const calc = calculateAmounts(amount, selectedRatio);
          return (
            <View key={amount} style={styles.tableRow}>
              <Text style={styles.tableCell}>{calc.color}g</Text>
              <Text style={styles.tableCell}>→</Text>
              <Text style={styles.tableCell}>{calc.developer.toFixed(0)}g</Text>
              <Text style={styles.tableCell}>{calc.total.toFixed(0)}g</Text>
            </View>
          );
        })}
      </View>
    </ScrollView>
  );
}

// Pantalla 5: Calculadora Visual
function CalculatorScreen({ formulaData }: ScreenComponentProps) {
  const [hairLength, setHairLength] = useState('medium');
  const [baseAmount, setBaseAmount] = useState('60');

  const multipliers = {
    short: 0.8,
    medium: 1,
    long: 1.5,
    'extra-long': 2,
  };

  const calculateTotal = () => {
    const base = parseFloat(baseAmount) || 0;
    const multiplier = multipliers[hairLength as keyof typeof multipliers];
    const ratioNum = parseFloat(formulaData.mixingProportions.ratio.split(':')[1]);

    const colorAmount = base * multiplier;
    const developerAmount = colorAmount * ratioNum;

    return {
      color: colorAmount,
      developer: developerAmount,
      total: colorAmount + developerAmount,
    };
  };

  const amounts = calculateTotal();

  return (
    <ScrollView style={styles.screenContent} showsVerticalScrollIndicator={false}>
      <View style={styles.calculatorHeader}>
        <Calculator size={48} color={Colors.light.primary} />
        <Text style={styles.calculatorTitle}>Calculadora Visual</Text>
      </View>

      <View style={styles.hairLengthSelector}>
        <Text style={styles.selectorTitle}>Largo del Cabello</Text>
        <View style={styles.lengthOptions}>
          {Object.entries({
            short: { label: 'Corto', height: 40 },
            medium: { label: 'Medio', height: 55 },
            long: { label: 'Largo', height: 70 },
            'extra-long': { label: 'Extra Largo', height: 85 },
          }).map(([key, { label, height }]) => (
            <TouchableOpacity
              key={key}
              style={[styles.lengthOption, hairLength === key && styles.lengthOptionActive]}
              onPress={() => setHairLength(key)}
            >
              <Scissors
                size={24}
                color={hairLength === key ? Colors.light.primary : Colors.light.gray}
              />
              <View
                style={[
                  styles.hairSilhouette,
                  {
                    height,
                    backgroundColor: hairLength === key ? Colors.light.primary : Colors.light.gray,
                  },
                ]}
              />
              <Text style={[styles.lengthText, hairLength === key && styles.lengthTextActive]}>
                {label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.amountInput}>
        <Text style={styles.inputLabel}>Cantidad Base (gramos)</Text>
        <View style={styles.inputRow}>
          <TouchableOpacity
            style={styles.adjustButton}
            onPress={() => setBaseAmount(String(Math.max(0, parseInt(baseAmount) - 5)))}
          >
            <Minus size={20} color={Colors.light.secondary} />
          </TouchableOpacity>
          <TextInput
            style={styles.amountInputField}
            value={baseAmount}
            onChangeText={setBaseAmount}
            keyboardType="numeric"
            placeholder="60"
          />
          <TouchableOpacity
            style={styles.adjustButton}
            onPress={() => setBaseAmount(String(parseInt(baseAmount) + 5))}
          >
            <Plus size={20} color={Colors.light.secondary} />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.calculationResult}>
        <View style={styles.resultRow}>
          <View style={[styles.resultIcon, { backgroundColor: Colors.light.primary }]} />
          <Text style={styles.resultLabel}>Tinte:</Text>
          <Text style={styles.resultValue}>{amounts.color.toFixed(0)}g</Text>
        </View>
        <View style={styles.resultRow}>
          <View style={[styles.resultIcon, { backgroundColor: Colors.light.accent }]} />
          <Text style={styles.resultLabel}>Oxidante:</Text>
          <Text style={styles.resultValue}>{amounts.developer.toFixed(0)}g</Text>
        </View>
        <View style={[styles.resultRow, styles.totalRow]}>
          <View style={[styles.resultIcon, { backgroundColor: Colors.light.secondary }]} />
          <Text style={[styles.resultLabel, styles.totalLabel]}>Total:</Text>
          <Text style={[styles.resultValue, styles.totalValue]}>{amounts.total.toFixed(0)}g</Text>
        </View>
      </View>

      <View style={styles.visualRepresentation}>
        <View style={styles.bowlVisualization}>
          <View
            style={[
              styles.bowlContent,
              { height: `${Math.min(100, (amounts.total / 200) * 100)}%` },
            ]}
          >
            <View style={[styles.colorLayer, { flex: amounts.color }]} />
            <View style={[styles.developerLayer, { flex: amounts.developer }]} />
          </View>
        </View>
      </View>
    </ScrollView>
  );
}

// Pantalla 6: Estación de Mezcla (mejorada)
function MixingScreen({ formulaData: _formulaData }: ScreenComponentProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const animValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(animValue, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(animValue, {
          toValue: 0,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, [animValue]);

  // Pre-calculate interpolations to avoid inline usage
  const rotateInterpolation = animValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '5deg'],
  });

  const brushRotateInterpolation = animValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['-15deg', '15deg'],
  });

  const mixingSteps = [
    { id: 1, text: 'Verter el tinte en el bowl', icon: '🎨' },
    { id: 2, text: 'Añadir el oxidante gradualmente', icon: '💧' },
    { id: 3, text: 'Mezclar con movimientos envolventes', icon: '🔄' },
    { id: 4, text: 'Verificar consistencia homogénea', icon: '✓' },
  ];

  return (
    <ScrollView style={styles.screenContent} showsVerticalScrollIndicator={false}>
      <View style={styles.mixingHeader}>
        <FlaskConical size={48} color={Colors.light.primary} />
        <Text style={styles.mixingTitle}>Estación de Mezcla Profesional</Text>
      </View>

      <View style={styles.mixingAnimation}>
        <View style={styles.bowlContainer}>
          <View style={styles.bowl}>
            {/* Mixture content */}
            <Animated.View
              style={[
                styles.mixture,
                {
                  transform: [
                    {
                      rotate: rotateInterpolation,
                    },
                  ],
                },
              ]}
            >
              <View style={[styles.mixtureGradient, { backgroundColor: Colors.light.primary }]} />
              <View style={[styles.colorSwirl, styles.colorSwirlLarge]} />
              <View style={[styles.colorSwirl, styles.colorSwirlSmall]} />
            </Animated.View>

            {/* Mixing brush visual */}
            <Animated.View
              style={[
                styles.mixingBrush,
                {
                  transform: [
                    {
                      rotate: brushRotateInterpolation,
                    },
                  ],
                },
              ]}
            >
              <Brush size={32} color={Colors.light.text} style={commonStyles.opacity70} />
            </Animated.View>
          </View>
          <Text style={styles.bowlLabel}>Mezcla cremosa y homogénea</Text>
        </View>
      </View>

      <View style={styles.stepsContainer}>
        {mixingSteps.map((step, index) => (
          <TouchableOpacity
            key={step.id}
            style={[styles.mixStep, index <= currentStep && styles.mixStepActive]}
            onPress={() => setCurrentStep(index)}
          >
            <View style={[styles.stepIcon, index <= currentStep && styles.stepIconActive]}>
              <Text style={styles.stepEmoji}>{step.icon}</Text>
            </View>
            <Text style={[styles.stepText, index <= currentStep && styles.stepTextActive]}>
              {step.text}
            </Text>
            {index < currentStep && (
              <Check size={20} color={Colors.light.success} style={styles.stepCheck} />
            )}
          </TouchableOpacity>
        ))}
      </View>

      <View style={styles.mixtureInfo}>
        <Text style={styles.mixtureTitle}>Consistencia Ideal</Text>
        <Text style={styles.mixtureDescription}>
          La mezcla debe tener una textura cremosa similar al yogur bebible. No debe ser ni muy
          líquida ni muy espesa.
        </Text>
      </View>
    </ScrollView>
  );
}

// Pantalla 7: Guía de Aplicación
function ApplicationScreen({ formulaData }: ScreenComponentProps) {
  const [selectedZone, setSelectedZone] = useState(0);
  const zones = formulaData.applicationGuide.zones;
  const technique = formulaData.applicationGuide.technique || 'full_color';

  // Professional application techniques based on service type
  const applicationTechniques: Record<
    string,
    { steps: string[]; diagrams?: string[]; tips?: string[] }
  > = {
    full_color: [
      {
        id: 1,
        title: 'División del cabello',
        description:
          'Divide en 4 secciones: 2 superiores y 2 inferiores. Usa clips para mantener separadas.',
        icon: Scissors,
      },
      {
        id: 2,
        title: 'Aplicación en nuca',
        description:
          'Comienza por la nuca con secciones de 0.5cm. Aplica de raíz a punta con pincel a 45°.',
        icon: Brush,
      },
      {
        id: 3,
        title: 'Laterales',
        description:
          'Continúa con secciones horizontales. Satura bien sin tocar el cuero cabelludo (2mm).',
        icon: Target,
      },
      {
        id: 4,
        title: 'Corona',
        description: 'Finaliza con la corona. Aplica más rápido aquí ya que procesa más lento.',
        icon: Sparkles,
      },
      {
        id: 5,
        title: 'Emulsión',
        description: 'Masajea suavemente con las yemas para distribuir uniformemente.',
        icon: RotateCcw,
      },
    ],
    zonal: [
      {
        id: 1,
        title: 'Preparación por zonas',
        description: 'Separa claramente raíces (0-3cm), medios y puntas con clips.',
        icon: GitMerge,
      },
      {
        id: 2,
        title: 'Aplicación raíces',
        description: 'Aplica perpendicular al cuero cabelludo. No superponer con color anterior.',
        icon: Target,
      },
      {
        id: 3,
        title: 'Transición medios',
        description: 'Usa técnica de barrido diagonal para difuminar la unión.',
        icon: Brush,
      },
      {
        id: 4,
        title: 'Puntas porosas',
        description: 'Aplica con técnica envolvente. Mayor saturación si están dañadas.',
        icon: Beaker,
      },
      {
        id: 5,
        title: 'Sellado final',
        description: 'Peina suavemente para distribuir y sellar cutículas.',
        icon: Check,
      },
    ],
    balayage: [
      {
        id: 1,
        title: 'Secciones en V',
        description: 'Crea secciones triangulares desde la coronilla.',
        icon: GitMerge,
      },
      {
        id: 2,
        title: 'Técnica de pintado',
        description: 'Aplica con pincel plano en movimientos ascendentes suaves.',
        icon: Brush,
      },
      {
        id: 3,
        title: 'Degradado natural',
        description: 'Más producto en puntas, menos en medios. Evita raíces.',
        icon: Sparkles,
      },
      {
        id: 4,
        title: 'Papel separador',
        description: 'Usa papel de algodón para aislar secciones sin calor.',
        icon: Package,
      },
      {
        id: 5,
        title: 'Difuminado',
        description: 'Usa los dedos para suavizar transiciones.',
        icon: RotateCcw,
      },
    ],
    highlights: [
      {
        id: 1,
        title: 'Selección de mechones',
        description: 'Toma secciones finas de 0.3cm con peine de cola.',
        icon: Scissors,
      },
      {
        id: 2,
        title: 'Aplicación en papel',
        description: 'Coloca sobre papel aluminio, aplica producto y dobla.',
        icon: Package,
      },
      {
        id: 3,
        title: 'Técnica zigzag',
        description: 'Alterna mechones para resultado natural.',
        icon: GitMerge,
      },
      {
        id: 4,
        title: 'Saturación completa',
        description: 'Asegura cobertura total dentro del papel.',
        icon: Beaker,
      },
      {
        id: 5,
        title: 'Control de calor',
        description: 'Evita fuentes de calor directo sobre el aluminio.',
        icon: AlertCircle,
      },
    ],
  };

  const currentTechniques = applicationTechniques[technique] || applicationTechniques.full_color;

  const getZoneDiagram = (zoneId: string) => {
    const diagrams: Record<string, string[]> = {
      roots: { top: '5%', height: '28%', backgroundColor: Colors.light.primary },
      mids: { top: '35%', height: '30%', backgroundColor: Colors.light.warning },
      ends: { top: '67%', height: '28%', backgroundColor: Colors.light.accent },
      crown: {
        top: '5%',
        left: '25%',
        right: '25%',
        height: '30%',
        backgroundColor: Colors.light.secondary,
        borderRadius: 100,
      },
      nape: {
        top: '70%',
        left: '20%',
        right: '20%',
        height: '25%',
        backgroundColor: Colors.light.primaryDark,
      },
    };
    return diagrams[zoneId] || { top: '5%', height: '20%', backgroundColor: '#gray' };
  };

  return (
    <ScrollView style={styles.screenContent} showsVerticalScrollIndicator={false}>
      <View style={styles.applicationHeader}>
        <Brush size={48} color={Colors.light.warning} />
        <Text style={styles.applicationTitle}>Guía de Aplicación Profesional</Text>
        <Text style={styles.applicationSubtitle}>
          {technique === 'zonal'
            ? 'Aplicación por Zonas'
            : technique === 'balayage'
              ? 'Técnica Balayage'
              : technique === 'highlights'
                ? 'Mechas con Papel'
                : 'Color Completo'}
        </Text>
      </View>

      <View style={styles.headDiagram}>
        <View style={styles.headShape}>
          {/* Hair section divisions */}
          <View style={styles.sectionLines}>
            <View style={[styles.sectionLine, styles.horizontalLine]} />
            <View style={[styles.sectionLine, styles.verticalLine]} />
          </View>

          {zones.map((zone: HairZoneDisplay, index: number) => (
            <TouchableOpacity
              key={zone.id}
              style={[
                styles.zoneArea,
                getZoneDiagram(zone.id),
                selectedZone === index && styles.zoneAreaActive,
              ]}
              onPress={() => setSelectedZone(index)}
            >
              <Text style={styles.zoneNumber}>{zone.order}</Text>
              {selectedZone === index && (
                <View style={styles.zoneArrows}>
                  {technique === 'full_color' && (
                    <ChevronRight size={16} color="white" style={styles.arrowIcon} />
                  )}
                  {technique === 'zonal' && (
                    <ChevronLeft size={16} color="white" style={styles.arrowIcon} />
                  )}
                </View>
              )}
            </TouchableOpacity>
          ))}
        </View>

        {/* Application direction legend */}
        <View style={styles.directionLegend}>
          <View style={styles.legendItem}>
            <ChevronRight size={14} color={Colors.light.primary} />
            <Text style={styles.legendText}>Dirección aplicación</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.saturationDot, commonStyles.opacity100]} />
            <Text style={styles.legendText}>100% saturación</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.saturationDot, commonStyles.opacity60]} />
            <Text style={styles.legendText}>60% saturación</Text>
          </View>
        </View>
      </View>

      <View style={styles.zoneDetails}>
        <Text style={styles.zoneName}>{zones[selectedZone].name}</Text>
        <Text style={styles.zoneDescription}>{zones[selectedZone].description}</Text>

        {/* Professional Application Techniques */}
        <View style={styles.techniquesContainer}>
          <Text style={styles.techniquesTitle}>Técnicas Profesionales:</Text>
          {currentTechniques.map(
            (tech: { steps: string[]; diagrams?: string[]; tips?: string[] }, _index: number) => {
              const Icon = tech.icon;
              return (
                <View key={tech.id} style={styles.techniqueCard}>
                  <View style={styles.techniqueIcon}>
                    <Icon size={24} color={Colors.light.primary} />
                  </View>
                  <View style={styles.techniqueContent}>
                    <Text style={styles.techniqueTitle}>{tech.title}</Text>
                    <Text style={styles.techniqueDescription}>{tech.description}</Text>
                  </View>
                </View>
              );
            }
          )}
        </View>

        {/* Zone-specific steps if available */}
        {formulaData.applicationGuide.steps && formulaData.applicationGuide.steps.length > 0 && (
          <View style={styles.applicationSteps}>
            <Text style={styles.stepsTitle}>Pasos Específicos:</Text>
            {formulaData.applicationGuide.steps
              .filter(
                (step: string) =>
                  !step.zone || step.zone === zones[selectedZone].id || step.zone === 'all'
              )
              .map((step: string, _stepIndex: number) => (
                <View key={index} style={styles.applicationStep}>
                  <View style={styles.stepNumber}>
                    <Text style={styles.stepNumberText}>{index + 1}</Text>
                  </View>
                  <Text style={styles.stepDescription}>{step.description}</Text>
                </View>
              ))}
          </View>
        )}

        {/* Professional Tips */}
        <View style={styles.applicationTips}>
          <View style={styles.tipCard}>
            <AlertCircle size={20} color={Colors.light.warning} />
            <Text style={styles.tipText}>
              {technique === 'zonal'
                ? 'Evita superponer color en zonas previamente teñidas'
                : technique === 'balayage'
                  ? 'Mantén el pincel en ángulo de 45° para transiciones suaves'
                  : technique === 'highlights'
                    ? 'No presiones el papel aluminio para evitar marcas'
                    : 'Aplica primero en zonas de mayor resistencia (canas)'}
            </Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );
}

// Pantalla 8: Cronograma (sin timer)
function TimelineScreen({ formulaData }: ScreenComponentProps) {
  const timeline = formulaData.timeline;
  const totalTime = formulaData.applicationGuide.totalTime;

  return (
    <ScrollView style={styles.screenContent} showsVerticalScrollIndicator={false}>
      <View style={styles.timelineHeader}>
        <Calendar size={48} color={Colors.light.accent} />
        <Text style={styles.timelineTitle}>Cronograma del Proceso</Text>
        <Text style={styles.totalTime}>Tiempo Total: {totalTime} minutos</Text>
      </View>

      <View style={styles.timelineContainer}>
        {timeline.map(
          (
            phase: { phase: string; duration: string; description: string; icon: string },
            index: number
          ) => (
            <View key={phase.id} style={styles.timelineItem}>
              <View style={styles.timelineMarker}>
                <View style={styles.timelineDot} />
                {index < timeline.length - 1 && <View style={styles.timelineLine} />}
              </View>

              <View style={styles.timelineContent}>
                <View style={styles.timelineCard}>
                  <View style={styles.timelineCardHeader}>
                    <Text style={styles.phaseTitle}>{phase.label}</Text>
                    <Text style={styles.phaseDuration}>
                      {phase.startTime}-{phase.endTime} min
                    </Text>
                  </View>
                  <Text style={styles.phaseDescription}>{phase.description}</Text>
                  <View style={styles.phaseZones}>
                    {phase.zones.map((zone: string) => (
                      <View key={zone} style={styles.zoneChip}>
                        <Text style={styles.zoneChipText}>
                          {zone === HairZone.ROOTS
                            ? HairZoneDisplay[HairZone.ROOTS]
                            : zone === HairZone.MIDS
                              ? HairZoneDisplay[HairZone.MIDS]
                              : zone === HairZone.ENDS
                                ? HairZoneDisplay[HairZone.ENDS]
                                : 'Todo'}
                        </Text>
                      </View>
                    ))}
                  </View>
                </View>
              </View>
            </View>
          )
        )}
      </View>

      <View style={styles.timelineNote}>
        <AlertCircle size={20} color={Colors.light.secondary} />
        <Text style={styles.timelineNoteText}>
          Los tiempos son aproximados. Ajusta según las condiciones del cabello.
        </Text>
      </View>
    </ScrollView>
  );
}

// Pantalla 9: Tips Profesionales
function TipsScreen({ formulaData }: ScreenComponentProps) {
  const tips = formulaData.tips;

  const additionalTips = [
    {
      id: 'room-temp',
      type: 'tip',
      title: 'Temperatura Ambiente',
      description: 'Mantén el salón entre 20-25°C para un procesamiento óptimo',
      icon: '🌡️',
    },
    {
      id: 'strand-test',
      type: 'warning',
      title: 'Prueba de Mechón',
      description: 'Siempre realiza una prueba antes de la aplicación completa',
      icon: '⚠️',
    },
    {
      id: 'timing',
      type: 'tip',
      title: 'Control de Tiempo',
      description: 'Revisa el proceso cada 10 minutos para evaluar el desarrollo',
      icon: '⏱️',
    },
    {
      id: 'porosity',
      type: 'info',
      title: 'Porosidad del Cabello',
      description: 'El cabello poroso procesa más rápido. Ajusta los tiempos',
      icon: 'ℹ️',
    },
  ];

  const allTips = [...tips, ...additionalTips];

  return (
    <ScrollView style={styles.screenContent} showsVerticalScrollIndicator={false}>
      <View style={styles.tipsHeader}>
        <Lightbulb size={48} color={Colors.light.warning} />
        <Text style={styles.tipsTitle}>Tips Profesionales</Text>
      </View>

      <View style={styles.tipsContainer}>
        {allTips.map(
          (tip: { id: string; category: string; tip: string; icon: string }, _index: number) => (
            <View
              key={tip.id}
              style={[
                styles.tipCard,
                tip.type === 'warning' && styles.tipCardWarning,
                tip.type === 'info' && styles.tipCardInfo,
              ]}
            >
              <Text style={styles.tipIcon}>{tip.icon}</Text>
              <View style={styles.tipContent}>
                <Text style={styles.tipTitle}>{tip.title}</Text>
                <Text style={styles.tipDescription}>{tip.description}</Text>
              </View>
            </View>
          )
        )}
      </View>

      <View style={styles.proTip}>
        <View style={[styles.proTipGradient, { backgroundColor: Colors.light.warning }]}>
          <Trophy size={24} color="white" />
          <Text style={styles.proTipText}>
            Consejo Pro: Aplica siempre de puntas a raíces en cabellos vírgenes
          </Text>
        </View>
      </View>
    </ScrollView>
  );
}

// Pantalla 10: Resultado Esperado
function ResultScreen({ formulaData }: ScreenComponentProps) {
  const result = formulaData.expectedResult || {
    description: 'Color uniforme y brillante',
    coverage: '100%',
    duration: '6-8 semanas',
    maintenance: ['Usar shampoo sin sulfatos', 'Tratamiento hidratante semanal'],
  };

  return (
    <ScrollView
      style={styles.screenContent}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.scrollContentContainer}
    >
      <View style={[styles.resultHeroSection, { backgroundColor: Colors.light.success }]}>
        <Sparkles size={64} color="white" />
        <Text style={styles.resultHeroTitle}>Servicio Completado</Text>
        <Text style={styles.resultHeroSubtitle}>Tu transformación está completa</Text>
      </View>

      <View style={styles.resultColorCard}>
        <Text style={styles.resultSectionTitle}>Transformación Completa</Text>

        {/* Before/After Comparison */}
        <View style={styles.colorTransformation}>
          <View style={styles.colorComparisonItem}>
            <Text style={styles.colorLabel}>Antes</Text>
            <View
              style={[
                styles.resultColorCircle,
                {
                  backgroundColor:
                    formulaData.colorTransition?.current?.hex || Colors.light.primary,
                  transform: [{ scale: 0.9 }],
                },
              ]}
            >
              <Text style={styles.resultColorLevel}>
                {formulaData.colorTransition?.current?.level || '5'}
              </Text>
            </View>
          </View>

          <View style={styles.transformArrow}>
            <ChevronRight size={28} color={Colors.light.primary} />
          </View>

          <View style={styles.colorComparisonItem}>
            <Text style={styles.colorLabel}>Después</Text>
            <View
              style={[
                styles.resultColorCircle,
                {
                  backgroundColor:
                    formulaData.colorTransition?.target?.hex || Colors.light.primaryLight,
                },
              ]}
            >
              <Animated.View style={styles.resultColorInner}>
                <Text style={styles.resultColorLevel}>
                  {formulaData.colorTransition?.target?.level || '8'}
                </Text>
                <View style={styles.resultColorDivider} />
                <Text style={styles.resultColorTone}>
                  {formulaData.colorTransition?.target?.tone || '69'}
                </Text>
              </Animated.View>
            </View>
          </View>
        </View>

        <Text style={styles.resultColorDescription}>{result.description}</Text>
      </View>

      <View style={styles.resultMetrics}>
        <View style={styles.metricCard}>
          <View style={[styles.metricIcon, { backgroundColor: Colors.light.secondary }]}>
            <TargetIcon size={24} color="white" />
          </View>
          <Text style={styles.metricValue}>{result.coverage}</Text>
          <Text style={styles.metricLabel}>Cobertura de Canas</Text>
        </View>

        <View style={styles.metricCard}>
          <View style={[styles.metricIcon, { backgroundColor: Colors.light.accent }]}>
            <Calendar size={24} color="white" />
          </View>
          <Text style={styles.metricValue}>{result.duration}</Text>
          <Text style={styles.metricLabel}>Duración Estimada</Text>
        </View>

        <View style={styles.metricCard}>
          <View style={[styles.metricIcon, { backgroundColor: Colors.light.warning }]}>
            <Sparkles size={24} color="white" />
          </View>
          <Text style={styles.metricValue}>Alto</Text>
          <Text style={styles.metricLabel}>Brillo y Luminosidad</Text>
        </View>
      </View>

      <View style={styles.maintenanceCard}>
        <View style={styles.maintenanceHeader}>
          <View style={styles.maintenanceIcon}>
            <AlertCircle size={24} color={Colors.light.primary} />
          </View>
          <Text style={styles.maintenanceTitle}>Cuidados Post-Color</Text>
        </View>

        <View style={styles.maintenanceContent}>
          {result.maintenance.map((item: string, index: number) => (
            <View key={index} style={styles.maintenanceStep}>
              <View style={styles.maintenanceCheckbox}>
                <Check size={16} color="white" />
              </View>
              <Text style={styles.maintenanceText}>{item}</Text>
            </View>
          ))}
        </View>

        <View style={styles.maintenanceTip}>
          <Text style={styles.maintenanceTipText}>
            Programa tu próxima visita en 4-6 semanas para mantener el color vibrante
          </Text>
        </View>
      </View>

      <View style={styles.completionCard}>
        <View style={[styles.completionGradient, { backgroundColor: Colors.light.primary }]}>
          <Trophy size={40} color="white" />
          <Text style={styles.completionTitle}>¡Proceso Completado con Éxito!</Text>
          <Text style={styles.completionSubtitle}>
            Has realizado una coloración profesional de alta calidad
          </Text>
        </View>
      </View>

      {/* Summary Button */}
      <AnimatedTouchable
        style={styles.summaryButton}
        onPress={() => {
          // TODO: Navigate to summary or share options
          // Debug logging removed for production
        }}
      >
        <View style={styles.summaryButtonContent}>
          <Package size={24} color="white" />
          <Text style={styles.summaryButtonText}>Ver Resumen de la Fórmula</Text>
          <ChevronRight size={20} color="white" />
        </View>
      </AnimatedTouchable>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.surface,
  },
  header: {
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  closeButton: {
    padding: 5,
  },
  headerContent: {
    flex: 1,
    alignItems: 'center',
  },
  stepNumber: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginBottom: 5,
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.light.textLight,
  },
  soundButton: {
    width: 34,
  },
  progressContainer: {
    paddingVertical: 20,
    paddingHorizontal: 30,
  },
  progressBarContainer: {
    height: 4,
    backgroundColor: Colors.light.surface,
    borderRadius: 2,
    overflow: 'hidden',
    marginBottom: 16,
  },
  progressBarTrack: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    backgroundColor: Colors.light.surface,
  },
  progressBarFill: {
    height: '100%',
    borderRadius: 2,
  },
  progressDotsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
  },
  progressDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  content: {
    flex: 1,
  },
  navigation: {
    flexDirection: 'row',
    padding: 20,
    gap: 10,
    backgroundColor: Colors.light.background,
    borderTopWidth: 1,
    borderTopColor: Colors.light.surface,
  },
  navButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    borderRadius: 12,
    gap: 8,
  },
  navButtonSecondary: {
    backgroundColor: Colors.light.surface,
  },
  navButtonPrimary: {
    backgroundColor: Colors.light.primary,
  },
  navButtonText: {
    fontSize: 16,
    color: Colors.light.text,
    fontWeight: '500',
  },
  navButtonTextDisabled: {
    color: Colors.light.gray,
  },
  navButtonTextPrimary: {
    fontSize: 16,
    color: Colors.light.textLight,
    fontWeight: '600',
  },
  screenContent: {
    flex: 1,
    paddingBottom: 20,
  },

  // Checklist Screen Styles
  checklistHeader: {
    padding: 30,
    alignItems: 'center',
  },
  checklistHeaderTitle: {
    fontSize: 18,
    color: Colors.light.textLight,
    fontWeight: '600',
    marginTop: 15,
    marginBottom: 20,
  },
  progressBar: {
    width: '100%',
    height: 8,
    backgroundColor: Colors.light.backgroundOpacity30,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    color: Colors.light.textLight,
    fontSize: 14,
    marginTop: 10,
  },
  checklistContent: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 15,
  },
  checkItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    marginBottom: 10,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.03,
    shadowRadius: 2,
    elevation: 2,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: Colors.light.surface,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  checkboxChecked: {
    backgroundColor: Colors.light.success,
    borderColor: Colors.light.success,
  },
  checkItemInfo: {
    flex: 1,
  },
  checkItemName: {
    fontSize: 15,
    color: Colors.light.text,
    fontWeight: '500',
  },
  checkItemZone: {
    fontSize: 12,
    color: Colors.light.gray,
    marginTop: 2,
  },
  typeIndicator: {
    width: 8,
    height: 40,
    borderRadius: 4,
    marginLeft: 12,
  },
  productGroup: {
    marginBottom: 20,
  },
  productGroupHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  productGroupIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  productGroupTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.gray,
    flex: 1,
  },

  // Transformation Screen Styles
  transformationHeader: {
    alignItems: 'center',
    padding: 30,
  },
  transformationTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.light.text,
    marginTop: 15,
  },
  transformationSubtitle: {
    fontSize: 14,
    color: Colors.light.gray,
    marginTop: 5,
  },
  transformationIconContainer: {
    position: 'relative',
    width: 80,
    height: 80,
    alignItems: 'center',
    justifyContent: 'center',
  },
  transformationIconOverlay: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: Colors.light.background,
    borderRadius: 20,
    padding: 6,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  colorComparison: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-evenly',
    paddingHorizontal: 20,
    marginBottom: 30,
    width: '100%',
  },
  colorBlock: {
    alignItems: 'center',
  },
  colorCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.03,
    shadowRadius: 4,
    elevation: 2,
  },
  colorLevel: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.light.textLight,
  },
  colorLabel: {
    fontSize: 14,
    color: Colors.light.gray,
    marginTop: 10,
  },
  colorTone: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.light.text,
    marginTop: 5,
  },
  arrowContainer: {
    paddingHorizontal: 10,
  },
  difficultyCard: {
    backgroundColor: Colors.light.background,
    marginHorizontal: 20,
    padding: 20,
    borderRadius: 15,
    alignItems: 'center',
    marginBottom: 20,
  },
  difficultyLabel: {
    fontSize: 14,
    color: Colors.light.gray,
    marginBottom: 10,
  },
  difficultyBadge: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 12,
  },
  difficultyText: {
    color: Colors.light.textLight,
    fontWeight: '600',
  },
  sessionsCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.warning + '20',
    marginHorizontal: 20,
    padding: 20,
    borderRadius: 10,
    marginBottom: 20,
  },
  sessionsText: {
    flex: 1,
    marginLeft: 10,
    color: Colors.light.secondary,
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    gap: 15,
  },
  statCard: {
    flex: 1,
    backgroundColor: Colors.light.background,
    padding: 20,
    borderRadius: 15,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.light.primary,
  },
  statLabel: {
    fontSize: 12,
    color: Colors.light.gray,
    marginTop: 5,
  },

  // Formulas Screen Styles
  formulasHeader: {
    alignItems: 'center',
    padding: 30,
  },
  formulasTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.light.text,
    marginTop: 15,
  },
  zoneSelector: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 20,
    gap: 10,
  },
  zoneTab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
  },
  zoneTabActive: {
    backgroundColor: Colors.light.primary,
  },
  zoneTabText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.light.text,
  },
  zoneTabTextActive: {
    color: Colors.light.textLight,
  },
  formulaCard: {
    backgroundColor: Colors.light.background,
    marginHorizontal: 20,
    padding: 20,
    borderRadius: 15,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.03,
    shadowRadius: 8,
    elevation: 3,
  },
  formulaZoneTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 20,
  },
  ingredientRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  ingredientIcon: {
    width: 40,
    height: 40,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  ingredientIconText: {
    color: Colors.light.textLight,
    fontWeight: 'bold',
    fontSize: 16,
  },
  ingredientInfo: {
    flex: 1,
  },
  ingredientName: {
    fontSize: 15,
    fontWeight: '500',
    color: Colors.light.text,
  },
  ingredientCode: {
    fontSize: 12,
    color: Colors.light.gray,
  },
  ingredientAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.primary,
  },
  ratioInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: Colors.light.surface,
  },
  ratioLabel: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  ratioValue: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
  },
  timeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 15,
    gap: 8,
  },
  timeText: {
    fontSize: 14,
    color: Colors.light.text,
  },
  ingredientGroup: {
    marginBottom: 20,
  },
  ingredientGroupTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.gray,
    marginBottom: 10,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  ingredientAmountContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    gap: 4,
  },
  ingredientUnit: {
    fontSize: 12,
    color: Colors.light.gray,
    fontWeight: '400',
  },
  zoneTabContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  zoneTabIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },

  // Proportions Screen Styles
  proportionsHeader: {
    alignItems: 'center',
    padding: 30,
  },
  proportionsTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.light.text,
    marginTop: 15,
  },
  ratioSelector: {
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  ratioOption: {
    backgroundColor: Colors.light.background,
    padding: 20,
    borderRadius: 12,
    marginBottom: 10,
    borderWidth: 2,
    borderColor: Colors.light.transparent,
  },
  ratioOptionActive: {
    borderColor: Colors.light.primary,
    backgroundColor: Colors.light.primaryTransparent5,
  },
  ratioText: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
  },
  ratioTextActive: {
    color: Colors.light.primary,
  },
  ratioDescription: {
    fontSize: 12,
    color: Colors.light.gray,
    marginTop: 5,
  },
  ratioDescriptionActive: {
    color: Colors.light.primary,
  },
  visualProportions: {
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  proportionBars: {
    flexDirection: 'row',
    height: 60,
    borderRadius: 16,
    overflow: 'hidden',
    gap: 2,
  },
  proportionBar: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  proportionBarText: {
    color: Colors.light.textLight,
    fontWeight: '600',
  },
  referenceTable: {
    backgroundColor: Colors.light.background,
    marginHorizontal: 20,
    padding: 20,
    borderRadius: 15,
  },
  tableTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 15,
    textAlign: 'center',
  },
  tableHeader: {
    flexDirection: 'row',
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.surface,
    marginBottom: 10,
  },
  tableHeaderCell: {
    flex: 1,
    fontSize: 12,
    fontWeight: '600',
    color: Colors.light.gray,
    textAlign: 'center',
  },
  tableRow: {
    flexDirection: 'row',
    paddingVertical: 8,
  },
  tableCell: {
    flex: 1,
    fontSize: 14,
    color: Colors.light.text,
    textAlign: 'center',
  },

  // Calculator Screen Styles
  calculatorHeader: {
    alignItems: 'center',
    padding: 30,
  },
  calculatorTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.light.text,
    marginTop: 15,
  },
  hairLengthSelector: {
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  selectorTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 15,
  },
  lengthOptions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  lengthOption: {
    alignItems: 'center',
    padding: 10,
  },
  lengthOptionActive: {
    transform: [{ scale: 1.1 }],
  },
  lengthText: {
    fontSize: 12,
    color: Colors.light.gray,
  },
  lengthTextActive: {
    color: Colors.light.primary,
    fontWeight: '600',
  },
  amountInput: {
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  inputLabel: {
    fontSize: 14,
    color: Colors.light.gray,
    marginBottom: 10,
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 15,
  },
  adjustButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: Colors.light.background,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.03,
    shadowRadius: 4,
    elevation: 3,
  },
  amountInputField: {
    fontSize: 28,
    fontWeight: '600',
    color: Colors.light.text,
    textAlign: 'center',
    backgroundColor: Colors.light.background,
    paddingVertical: 10,
    paddingHorizontal: 30,
    borderRadius: 15,
    minWidth: 120,
  },
  calculationResult: {
    backgroundColor: Colors.light.background,
    marginHorizontal: 20,
    padding: 20,
    borderRadius: 15,
    marginBottom: 20,
  },
  resultRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  resultIcon: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 10,
  },
  resultLabel: {
    flex: 1,
    fontSize: 14,
    color: Colors.light.gray,
  },
  resultValue: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
  },
  totalRow: {
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: Colors.light.surface,
  },
  totalLabel: {
    fontWeight: '600',
    color: Colors.light.text,
  },
  totalValue: {
    fontSize: 22,
    color: Colors.light.primary,
  },
  visualRepresentation: {
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  bowlVisualization: {
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: Colors.light.surface,
    borderWidth: 3,
    borderColor: Colors.light.surface,
    overflow: 'hidden',
    justifyContent: 'flex-end',
  },
  bowlContent: {
    width: '100%',
    borderBottomLeftRadius: 75,
    borderBottomRightRadius: 75,
    overflow: 'hidden',
  },
  colorLayer: {
    backgroundColor: Colors.light.primary,
  },
  developerLayer: {
    backgroundColor: Colors.light.accent,
  },

  // Mixing Screen Styles
  mixingHeader: {
    alignItems: 'center',
    padding: 30,
  },
  mixingTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.light.text,
    marginTop: 15,
  },
  mixingAnimation: {
    alignItems: 'center',
    marginBottom: 30,
  },
  bowlContainer: {
    alignItems: 'center',
  },
  bowl: {
    width: 180,
    height: 180,
    borderRadius: 90,
    backgroundColor: Colors.light.surface,
    borderWidth: 3,
    borderColor: Colors.light.surface,
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
  },
  mixture: {
    width: 160,
    height: 160,
    borderRadius: 80,
    overflow: 'hidden',
  },
  mixtureGradient: {
    flex: 1,
  },
  bowlLabel: {
    marginTop: 15,
    fontSize: 14,
    color: Colors.light.gray,
    fontStyle: 'italic',
  },
  mixingBrush: {
    position: 'absolute',
    top: '40%',
    left: '40%',
  },
  colorSwirl: {
    position: 'absolute',
  },
  stepsContainer: {
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  mixStep: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    marginBottom: 10,
    position: 'relative',
  },
  mixStepActive: {
    backgroundColor: Colors.light.primaryTransparent5,
    borderWidth: 1,
    borderColor: Colors.light.primary,
  },
  stepIcon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: Colors.light.surface,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  stepIconActive: {
    backgroundColor: Colors.light.primary,
  },
  stepEmoji: {
    fontSize: 20,
  },
  stepText: {
    flex: 1,
    fontSize: 15,
    color: Colors.light.gray,
  },
  stepTextActive: {
    color: Colors.light.text,
    fontWeight: '500',
  },
  stepCheck: {
    position: 'absolute',
    right: 15,
  },
  mixtureInfo: {
    backgroundColor: Colors.light.surface,
    marginHorizontal: 20,
    padding: 20,
    borderRadius: 15,
  },
  mixtureTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 10,
  },
  mixtureDescription: {
    fontSize: 14,
    color: Colors.light.gray,
    lineHeight: 20,
  },

  // Application Screen Styles
  applicationHeader: {
    alignItems: 'center',
    padding: 30,
  },
  applicationTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.light.text,
    marginTop: 15,
  },
  applicationSubtitle: {
    fontSize: 14,
    color: Colors.light.gray,
    marginTop: 5,
  },
  headDiagram: {
    alignItems: 'center',
    marginBottom: 30,
  },
  headShape: {
    width: 200,
    height: 250,
    backgroundColor: Colors.light.surface,
    borderRadius: 100,
    borderWidth: 3,
    borderColor: Colors.light.surface,
    position: 'relative',
    overflow: 'hidden',
  },
  zoneArea: {
    position: 'absolute',
    left: 0,
    right: 0,
    alignItems: 'center',
    justifyContent: 'center',
    opacity: 0.7,
  },
  zoneAreaActive: {
    opacity: 1,
    zIndex: 10,
  },
  zoneNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.light.textLight,
    textShadowColor: Colors.common.shadowColor,
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  },
  zoneArrows: {
    position: 'absolute',
    right: 10,
    flexDirection: 'row',
  },
  arrowIcon: {
    opacity: 0.8,
  },
  sectionLines: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  sectionLine: {
    position: 'absolute',
    backgroundColor: Colors.light.gray,
    opacity: 0.3,
  },
  horizontalLine: {
    top: '50%',
    left: 0,
    right: 0,
    height: 1,
  },
  verticalLine: {
    top: 0,
    bottom: 0,
    left: '50%',
    width: 1,
  },
  directionLegend: {
    marginTop: 15,
    paddingHorizontal: 20,
    gap: 10,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  legendText: {
    fontSize: 12,
    color: Colors.light.gray,
  },
  saturationDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: Colors.light.primary,
  },
  zoneDetails: {
    paddingHorizontal: 20,
  },
  zoneName: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 10,
  },
  zoneDescription: {
    fontSize: 14,
    color: Colors.light.gray,
    marginBottom: 20,
  },
  applicationSteps: {
    backgroundColor: Colors.light.background,
    padding: 20,
    borderRadius: 15,
  },
  stepsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 15,
  },
  applicationStep: {
    flexDirection: 'row',
    marginBottom: 15,
  },
  stepNumber: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: Colors.light.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  stepNumberText: {
    color: Colors.light.textLight,
    fontWeight: '600',
    fontSize: 12,
  },
  stepDescription: {
    flex: 1,
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 20,
  },
  techniquesContainer: {
    marginBottom: 20,
  },
  techniquesTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 15,
  },
  techniqueCard: {
    flexDirection: 'row',
    backgroundColor: Colors.light.background,
    padding: 20,
    borderRadius: 12,
    marginBottom: 10,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.03,
    shadowRadius: 2,
    elevation: 1,
  },
  techniqueIcon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: Colors.light.surface,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 15,
  },
  techniqueContent: {
    flex: 1,
  },
  techniqueTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 5,
  },
  techniqueDescription: {
    fontSize: 13,
    color: Colors.light.gray,
    lineHeight: 18,
  },
  applicationTips: {
    marginTop: 20,
  },
  tipCard: {
    flexDirection: 'row',
    backgroundColor: Colors.light.warning + '20',
    padding: 20,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: Colors.light.warning,
    alignItems: 'center',
  },
  tipText: {
    flex: 1,
    marginLeft: 10,
    fontSize: 13,
    color: Colors.light.secondary,
    lineHeight: 18,
  },

  // Timeline Screen Styles
  timelineHeader: {
    alignItems: 'center',
    padding: 30,
  },
  timelineTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.light.text,
    marginTop: 15,
  },
  totalTime: {
    fontSize: 16,
    color: Colors.light.gray,
    marginTop: 10,
  },
  timelineContainer: {
    paddingHorizontal: 20,
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  timelineMarker: {
    width: 40,
    alignItems: 'center',
  },
  timelineDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: Colors.light.primary,
    marginTop: 5,
  },
  timelineLine: {
    width: 2,
    flex: 1,
    backgroundColor: Colors.light.surface,
    marginTop: 5,
  },
  timelineContent: {
    flex: 1,
    paddingLeft: 10,
  },
  timelineCard: {
    backgroundColor: Colors.light.background,
    padding: 20,
    borderRadius: 12,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.03,
    shadowRadius: 2,
    elevation: 2,
  },
  timelineCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  phaseTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
  },
  phaseDuration: {
    fontSize: 14,
    color: Colors.light.primary,
    fontWeight: '500',
  },
  phaseDescription: {
    fontSize: 14,
    color: Colors.light.gray,
    marginBottom: 10,
  },
  phaseZones: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 5,
  },
  zoneChip: {
    backgroundColor: Colors.light.surface,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  zoneChipText: {
    fontSize: 12,
    color: Colors.light.text,
  },
  timelineNote: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.surface,
    marginHorizontal: 20,
    marginTop: 20,
    padding: 20,
    borderRadius: 12,
  },
  timelineNoteText: {
    flex: 1,
    marginLeft: 10,
    fontSize: 14,
    color: Colors.light.gray,
  },

  // Tips Screen Styles
  tipsHeader: {
    alignItems: 'center',
    padding: 30,
  },
  tipsTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.light.text,
    marginTop: 15,
  },
  tipsContainer: {
    paddingHorizontal: 20,
  },
  tipCard: {
    flexDirection: 'row',
    backgroundColor: Colors.light.background,
    padding: 20,
    borderRadius: 12,
    marginBottom: 10,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.03,
    shadowRadius: 2,
    elevation: 2,
  },
  tipCardWarning: {
    backgroundColor: Colors.light.warning + '20',
    borderWidth: 1,
    borderColor: Colors.light.warning,
  },
  tipCardInfo: {
    backgroundColor: Colors.light.accent + '20',
    borderWidth: 1,
    borderColor: Colors.light.accent,
  },
  tipIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  tipContent: {
    flex: 1,
  },
  tipTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 5,
  },
  tipDescription: {
    fontSize: 14,
    color: Colors.light.gray,
    lineHeight: 20,
  },
  proTip: {
    marginHorizontal: 20,
    marginTop: 20,
  },
  proTipGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    borderRadius: 15,
    gap: 12,
  },
  proTipText: {
    flex: 1,
    color: Colors.light.textLight,
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 20,
  },

  // Result Screen Styles
  maintenanceTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 15,
  },
  maintenanceText: {
    flex: 1,
    marginLeft: 10,
    fontSize: 14,
    color: Colors.light.text,
  },

  // Calculator Screen - Additional Styles
  hairSilhouette: {
    width: 3,
    backgroundColor: Colors.light.primary,
    marginBottom: 5,
    borderRadius: 1.5,
  },

  // Mixing Screen - Additional Styles
  bowlContainer: {
    alignItems: 'center',
  },
  bowl: {
    width: 180,
    height: 180,
    borderRadius: 90,
    backgroundColor: Colors.light.surface,
    borderWidth: 3,
    borderColor: Colors.light.surface,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.03,
    shadowRadius: 4,
    elevation: 2,
  },
  mixture: {
    width: 160,
    height: 160,
    borderRadius: 80,
    overflow: 'hidden',
  },
  mixtureGradient: {
    flex: 1,
  },
  bowlLabel: {
    marginTop: 15,
    fontSize: 14,
    color: Colors.light.gray,
    fontStyle: 'italic',
  },

  // Result Screen - New Design Styles
  resultHeroSection: {
    padding: 40,
    alignItems: 'center',
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
  },
  resultHeroTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.light.textLight,
    marginTop: 15,
  },
  resultHeroSubtitle: {
    fontSize: 16,
    color: Colors.light.backgroundOpacity90,
    marginTop: 5,
  },
  resultColorCard: {
    backgroundColor: Colors.light.background,
    marginHorizontal: 20,
    marginTop: -20,
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.03,
    shadowRadius: 10,
    elevation: 5,
  },
  resultSectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 20,
  },
  resultColorCircle: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.03,
    shadowRadius: 4,
    elevation: 2,
  },
  resultColorInner: {
    backgroundColor: Colors.light.background,
    paddingHorizontal: 30,
    paddingVertical: 20,
    borderRadius: 12,
    alignItems: 'center',
  },
  resultColorLevel: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.light.text,
  },
  resultColorDivider: {
    width: 40,
    height: 2,
    backgroundColor: Colors.light.gray,
    marginVertical: 8,
  },
  resultColorTone: {
    fontSize: 16,
    color: Colors.light.gray,
  },
  resultColorDescription: {
    fontSize: 16,
    color: Colors.light.text,
    textAlign: 'center',
  },
  colorTransformation: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 20,
    gap: 20,
  },
  colorComparisonItem: {
    alignItems: 'center',
    gap: 10,
  },
  colorLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.gray,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  transformArrow: {
    paddingHorizontal: 10,
  },
  resultMetrics: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginTop: 20,
    gap: 15,
  },
  metricCard: {
    flex: 1,
    backgroundColor: Colors.light.background,
    padding: 20,
    borderRadius: 15,
    alignItems: 'center',
  },
  metricIcon: {
    width: 50,
    height: 50,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  metricValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginBottom: 5,
  },
  metricLabel: {
    fontSize: 11,
    color: Colors.light.gray,
    textAlign: 'center',
  },
  maintenanceCard: {
    backgroundColor: Colors.light.background,
    marginHorizontal: 20,
    marginTop: 20,
    padding: 20,
    borderRadius: 15,
  },
  maintenanceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  maintenanceIcon: {
    width: 40,
    height: 40,
    borderRadius: 12,
    backgroundColor: Colors.light.primaryTransparent10,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  maintenanceContent: {
    gap: 12,
  },
  maintenanceStep: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  maintenanceCheckbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.light.success,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  maintenanceTip: {
    backgroundColor: Colors.light.surface,
    padding: 20,
    borderRadius: 10,
    marginTop: 15,
  },
  maintenanceTipText: {
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 20,
  },
  completionCard: {
    marginHorizontal: 20,
    marginTop: 20,
    borderRadius: 12,
    overflow: 'hidden',
  },
  completionGradient: {
    padding: 30,
    alignItems: 'center',
  },
  completionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.light.textLight,
    marginTop: 15,
    textAlign: 'center',
  },
  completionSubtitle: {
    fontSize: 14,
    color: Colors.light.backgroundOpacity90,
    marginTop: 8,
    textAlign: 'center',
  },
  summaryButton: {
    marginHorizontal: 20,
    marginTop: 20,
    marginBottom: 30,
  },
  summaryButtonContent: {
    backgroundColor: Colors.light.secondary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 18,
    borderRadius: 12,
    gap: 12,
  },
  summaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.textLight,
    flex: 1,
    textAlign: 'center',
  },
  animatedCheckmark: {
    // Container for animated checkmark - no additional styling needed
  },
  // Progress dot opacity states
  progressDotActive: {
    opacity: 1,
  },
  progressDotInactive: {
    opacity: 0.6,
  },
  // Proportion bar styles
  proportionBarPrimary: {
    flex: 1,
    backgroundColor: Colors.light.primary,
  },
  // Color swirl decorative elements
  colorSwirlLarge: {
    position: 'absolute',
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.light.primaryLight,
    opacity: 0.5,
    top: '20%',
    left: '10%',
  },
  colorSwirlSmall: {
    position: 'absolute',
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.light.accent,
    opacity: 0.3,
    bottom: '30%',
    right: '20%',
  },
  // Scroll view content container
  scrollContentContainer: {
    paddingBottom: 30,
  },
});
