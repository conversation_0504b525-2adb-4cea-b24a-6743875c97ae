import React, { useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { PreparationStep, MixingStep } from './steps';
import { AnimatedTouchable } from './components';
import { VisualFormulationData } from '@/types/visual-formulation';
import Colors from '@/constants/colors';

interface RefactoredInstructionsFlowDemoProps {
  formulaData: VisualFormulationData;
}

/**
 * DEMO: RefactoredInstructionsFlow
 *
 * This is a demonstration of the refactored components working together.
 *
 * Phase 1 Progress:
 * ✅ Extracted AnimatedTouchable component
 * ✅ Extracted PreparationStep component
 * ✅ Extracted MixingStep component
 * ✅ Created shared styles system
 * ✅ Created safe backup (InstructionsFlow.legacy.tsx)
 *
 * Next phases will continue with the remaining step components
 * and eventually replace the original file incrementally.
 */
export function RefactoredInstructionsFlowDemo({
  formulaData,
}: RefactoredInstructionsFlowDemoProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [checkedItems, setCheckedItems] = useState<string[]>([]);

  const steps = ['Preparación', 'Mezcla'];

  return (
    <View style={styles.container}>
      {/* Step Navigation */}
      <View style={styles.navigation}>
        {steps.map((stepName, index) => (
          <AnimatedTouchable
            key={stepName}
            style={[styles.navButton, currentStep === index && styles.navButtonActive]}
            onPress={() => setCurrentStep(index)}
          >
            <Text
              style={[styles.navButtonText, currentStep === index && styles.navButtonTextActive]}
            >
              {stepName}
            </Text>
          </AnimatedTouchable>
        ))}
      </View>

      {/* Step Content */}
      <View style={styles.content}>
        {currentStep === 0 && (
          <PreparationStep
            formulaData={formulaData}
            checkedItems={checkedItems}
            setCheckedItems={setCheckedItems}
          />
        )}
        {currentStep === 1 && <MixingStep formulaData={formulaData} />}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.surface,
  },
  navigation: {
    flexDirection: 'row',
    padding: 10,
    backgroundColor: Colors.light.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.surface,
  },
  navButton: {
    flex: 1,
    padding: 15,
    margin: 5,
    backgroundColor: Colors.light.surface,
    borderRadius: 8,
    alignItems: 'center',
  },
  navButtonActive: {
    backgroundColor: Colors.light.primary,
  },
  navButtonText: {
    fontSize: 16,
    color: Colors.light.text,
    fontWeight: '500',
  },
  navButtonTextActive: {
    color: Colors.light.textLight,
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
});
