import React, { useState, useRef, useEffect } from 'react';
import { View, Text, TouchableOpacity, ScrollView, Animated } from 'react-native';
import { FlaskConical, Brush, Check } from 'lucide-react-native';
import { VisualFormulationData } from '@/types/visual-formulation';
import { commonStyles } from '@/styles/commonStyles';
import Colors from '@/constants/colors';
import { instructionStyles } from '../styles';

interface MixingStepProps {
  formulaData: VisualFormulationData;
}

interface MixingStepData {
  id: number;
  text: string;
  icon: string;
}

export function MixingStep({ formulaData: _formulaData }: MixingStepProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const animValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(animValue, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(animValue, {
          toValue: 0,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, [animValue]);

  // Pre-calculate interpolations to avoid inline usage
  const rotateInterpolation = animValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '5deg'],
  });

  const brushRotateInterpolation = animValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['-15deg', '15deg'],
  });

  const mixingSteps: MixingStepData[] = [
    { id: 1, text: 'Verter el tinte en el bowl', icon: '🎨' },
    { id: 2, text: 'Añadir el oxidante gradualmente', icon: '💧' },
    { id: 3, text: 'Mezclar con movimientos envolventes', icon: '🔄' },
    { id: 4, text: 'Verificar consistencia homogénea', icon: '✓' },
  ];

  return (
    <ScrollView style={instructionStyles.screenContent} showsVerticalScrollIndicator={false}>
      <View style={instructionStyles.mixingHeader}>
        <FlaskConical size={48} color={Colors.light.primary} />
        <Text style={instructionStyles.mixingTitle}>Estación de Mezcla Profesional</Text>
      </View>

      <View style={instructionStyles.mixingAnimation}>
        <View style={instructionStyles.bowlContainer}>
          <View style={instructionStyles.bowl}>
            {/* Mixture content */}
            <Animated.View
              style={[
                instructionStyles.mixture,
                {
                  transform: [
                    {
                      rotate: rotateInterpolation,
                    },
                  ],
                },
              ]}
            >
              <View
                style={[
                  instructionStyles.mixtureGradient,
                  { backgroundColor: Colors.light.primary },
                ]}
              />
              <View style={[instructionStyles.colorSwirl, instructionStyles.colorSwirlLarge]} />
              <View style={[instructionStyles.colorSwirl, instructionStyles.colorSwirlSmall]} />
            </Animated.View>

            {/* Mixing brush visual */}
            <Animated.View
              style={[
                instructionStyles.mixingBrush,
                {
                  transform: [
                    {
                      rotate: brushRotateInterpolation,
                    },
                  ],
                },
              ]}
            >
              <Brush size={32} color={Colors.light.text} style={commonStyles.opacity70} />
            </Animated.View>
          </View>
          <Text style={instructionStyles.bowlLabel}>Mezcla cremosa y homogénea</Text>
        </View>
      </View>

      <View style={instructionStyles.stepsContainer}>
        {mixingSteps.map((step, index) => (
          <TouchableOpacity
            key={step.id}
            style={[
              instructionStyles.mixStep,
              index <= currentStep && instructionStyles.mixStepActive,
            ]}
            onPress={() => setCurrentStep(index)}
          >
            <View
              style={[
                instructionStyles.stepIcon,
                index <= currentStep && instructionStyles.stepIconActive,
              ]}
            >
              <Text style={instructionStyles.stepEmoji}>{step.icon}</Text>
            </View>
            <Text
              style={[
                instructionStyles.stepText,
                index <= currentStep && instructionStyles.stepTextActive,
              ]}
            >
              {step.text}
            </Text>
            {index < currentStep && (
              <Check size={20} color={Colors.light.success} style={instructionStyles.stepCheck} />
            )}
          </TouchableOpacity>
        ))}
      </View>

      <View style={instructionStyles.mixtureInfo}>
        <Text style={instructionStyles.mixtureTitle}>Consistencia Ideal</Text>
        <Text style={instructionStyles.mixtureDescription}>
          La mezcla debe tener una textura cremosa similar al yogur bebible. No debe ser ni muy
          líquida ni muy espesa.
        </Text>
      </View>
    </ScrollView>
  );
}
