import React, { useState } from 'react';
import { View, Text, ScrollView, StyleSheet } from 'react-native';
import {
  LucideIcon,
  Brush,
  Scissors,
  Target,
  Sparkles,
  RotateCcw,
  GitMerge,
  Beaker,
  Check,
  Package,
  AlertCircle,
} from 'lucide-react-native';
import Colors from '@/constants/colors';
import { ColorConstants } from '@/styles/colors';
import { VisualFormulationData } from '@/types/visual-formulation';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { HairZone, HairZoneDisplay } from '@/types/hair-diagnosis';
import { ZoneVisualizer } from '../components/ZoneVisualizer';
import { instructionStyles as styles } from '../styles';

interface ApplicationStepProps {
  formulaData: VisualFormulationData;
  testID?: string;
}

interface ApplicationZone {
  id: string;
  name: string;
  description: string;
  order: number;
}

interface ApplicationStep {
  id: number;
  title: string;
  description: string;
  timing?: string;
}

interface ApplicationTechnique {
  id: number;
  title: string;
  description: string;
  icon: LucideIcon; // Lucide icon component
}

interface ApplicationGuide {
  zones: ApplicationZone[];
  technique?: 'full_color' | 'zonal' | 'balayage' | 'highlights';
  steps?: ApplicationStep[];
}

export const ApplicationStep: React.FC<ApplicationStepProps> = ({
  formulaData,
  testID = 'application-step',
}) => {
  const [selectedZone, setSelectedZone] = useState(0);

  // Fallback for missing applicationGuide
  const applicationGuide: ApplicationGuide = formulaData.applicationGuide || {
    zones: [
      { id: 'roots', name: 'Raíces', description: 'Aplicar desde el cuero cabelludo', order: 1 },
      { id: 'mids', name: 'Medios', description: 'Zona intermedia del cabello', order: 2 },
      { id: 'ends', name: 'Puntas', description: 'Extremos del cabello', order: 3 },
    ],
    technique: 'full_color',
  };

  const zones = applicationGuide.zones;
  const technique = applicationGuide.technique || 'full_color';

  // Professional application techniques based on service type
  const applicationTechniques: Record<string, ApplicationTechnique[]> = {
    full_color: [
      {
        id: 1,
        title: 'División del cabello',
        description:
          'Divide en 4 secciones: 2 superiores y 2 inferiores. Usa clips para mantener separadas.',
        icon: Scissors,
      },
      {
        id: 2,
        title: 'Aplicación en nuca',
        description:
          'Comienza por la nuca con secciones de 0.5cm. Aplica de raíz a punta con pincel a 45°.',
        icon: Brush,
      },
      {
        id: 3,
        title: 'Laterales',
        description:
          'Continúa con secciones horizontales. Satura bien sin tocar el cuero cabelludo (2mm).',
        icon: Target,
      },
      {
        id: 4,
        title: 'Corona',
        description: 'Finaliza con la corona. Aplica más rápido aquí ya que procesa más lento.',
        icon: Sparkles,
      },
      {
        id: 5,
        title: 'Emulsión',
        description: 'Masajea suavemente con las yemas para distribuir uniformemente.',
        icon: RotateCcw,
      },
    ],
    zonal: [
      {
        id: 1,
        title: 'Preparación por zonas',
        description: 'Separa claramente raíces (0-3cm), medios y puntas con clips.',
        icon: GitMerge,
      },
      {
        id: 2,
        title: 'Aplicación raíces',
        description: 'Aplica perpendicular al cuero cabelludo. No superponer con color anterior.',
        icon: Target,
      },
      {
        id: 3,
        title: 'Transición medios',
        description: 'Usa técnica de barrido diagonal para difuminar la unión.',
        icon: Brush,
      },
      {
        id: 4,
        title: 'Puntas porosas',
        description: 'Aplica con técnica envolvente. Mayor saturación si están dañadas.',
        icon: Beaker,
      },
      {
        id: 5,
        title: 'Sellado final',
        description: 'Peina suavemente para distribuir y sellar cutículas.',
        icon: Check,
      },
    ],
    balayage: [
      {
        id: 1,
        title: 'Secciones en V',
        description: 'Crea secciones triangulares desde la coronilla.',
        icon: GitMerge,
      },
      {
        id: 2,
        title: 'Técnica de pintado',
        description: 'Aplica con pincel plano en movimientos ascendentes suaves.',
        icon: Brush,
      },
      {
        id: 3,
        title: 'Degradado natural',
        description: 'Más producto en puntas, menos en medios. Evita raíces.',
        icon: Sparkles,
      },
      {
        id: 4,
        title: 'Papel separador',
        description: 'Usa papel de algodón para aislar secciones sin calor.',
        icon: Package,
      },
    ],
    highlights: [
      {
        id: 1,
        title: 'Selección de mechones',
        description: 'Elige mechones finos y uniformes con técnica zigzag.',
        icon: GitMerge,
      },
      {
        id: 2,
        title: 'Papel aluminio',
        description: 'Coloca sobre papel aluminio, aplica producto y dobla.',
        icon: Package,
      },
      {
        id: 3,
        title: 'Técnica zigzag',
        description: 'Alterna mechones para resultado natural.',
        icon: GitMerge,
      },
      {
        id: 4,
        title: 'Saturación completa',
        description: 'Asegura cobertura total dentro del papel.',
        icon: Beaker,
      },
      {
        id: 5,
        title: 'Control de calor',
        description: 'Evita fuentes de calor directo sobre el aluminio.',
        icon: AlertCircle,
      },
    ],
  };

  const currentTechniques = applicationTechniques[technique] || applicationTechniques.full_color;

  const getTipText = (technique: string) => {
    const tips = {
      zonal: 'Evita superponer color en zonas previamente teñidas',
      balayage: 'Mantén el pincel en ángulo de 45° para transiciones suaves',
      highlights: 'No presiones el papel aluminio para evitar marcas',
      full_color: 'Aplica primero en zonas de mayor resistencia (canas)',
    };
    return tips[technique as keyof typeof tips] || tips.full_color;
  };

  return (
    <ScrollView style={styles.screenContent} showsVerticalScrollIndicator={false} testID={testID}>
      {/* Zone Visualizer */}
      <ZoneVisualizer
        zones={zones}
        selectedZone={selectedZone}
        onZoneSelect={setSelectedZone}
        technique={technique}
        showLegend={true}
        testID={`${testID}-visualizer`}
      />

      {/* Professional Application Techniques */}
      <View style={localStyles.techniquesContainer}>
        <Text style={localStyles.techniquesTitle}>Técnicas Profesionales:</Text>
        {currentTechniques.map(tech => {
          const Icon = tech.icon;
          return (
            <View key={tech.id} style={localStyles.techniqueCard}>
              <View style={localStyles.techniqueIcon}>
                <Icon size={24} color={Colors.light.primary} />
              </View>
              <View style={localStyles.techniqueContent}>
                <Text style={localStyles.techniqueTitle}>{tech.title}</Text>
                <Text style={localStyles.techniqueDescription}>{tech.description}</Text>
              </View>
            </View>
          );
        })}
      </View>

      {/* Zone-specific steps if available */}
      {applicationGuide.steps && applicationGuide.steps.length > 0 && (
        <View style={localStyles.applicationSteps}>
          <Text style={localStyles.stepsTitle}>Pasos Específicos:</Text>
          {applicationGuide.steps
            .filter(
              (step: any) =>
                !step.zone || step.zone === zones[selectedZone].id || step.zone === 'all'
            )
            .map((step: any, index: number) => (
              <View key={index} style={localStyles.applicationStep}>
                <View style={localStyles.stepNumber}>
                  <Text style={localStyles.stepNumberText}>{index + 1}</Text>
                </View>
                <Text style={localStyles.stepDescription}>{step.description}</Text>
              </View>
            ))}
        </View>
      )}

      {/* Professional Tips */}
      <View style={localStyles.applicationTips}>
        <View style={localStyles.tipCard}>
          <AlertCircle size={20} color={Colors.light.warning} />
          <Text style={localStyles.tipText}>{getTipText(technique)}</Text>
        </View>
      </View>
    </ScrollView>
  );
};

const localStyles = StyleSheet.create({
  techniquesContainer: {
    marginTop: 20,
    paddingHorizontal: 16,
  },
  techniquesTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 16,
  },
  techniqueCard: {
    flexDirection: 'row',
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  techniqueIcon: {
    marginRight: 16,
    alignSelf: 'flex-start',
  },
  techniqueContent: {
    flex: 1,
  },
  techniqueTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 4,
  },
  techniqueDescription: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    lineHeight: 20,
  },
  applicationSteps: {
    marginTop: 20,
    paddingHorizontal: 16,
  },
  stepsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 16,
  },
  applicationStep: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  stepNumber: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.light.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  stepNumberText: {
    color: ColorConstants.WHITE,
    fontWeight: '700',
    fontSize: 14,
  },
  stepDescription: {
    flex: 1,
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 20,
    paddingTop: 6,
  },
  applicationTips: {
    marginTop: 20,
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  tipCard: {
    flexDirection: 'row',
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 16,
    alignItems: 'flex-start',
  },
  tipText: {
    flex: 1,
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 20,
    marginLeft: 12,
  },
});
