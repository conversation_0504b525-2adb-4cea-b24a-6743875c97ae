import React, { useState, useEffect, useRef } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Animated } from 'react-native';
import * as Haptics from 'expo-haptics';
import { Clock, Play, Pause, RotateCcw } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { StepCard } from './StepCard';

interface TimerCardProps {
  title?: string;
  subtitle?: string;
  initialTime: number; // in seconds
  onComplete?: () => void;
  onTimeChange?: (time: number) => void;
  autoStart?: boolean;
  showControls?: boolean;
  testID?: string;
}

export const TimerCard: React.FC<TimerCardProps> = ({
  title = 'Cronómetro',
  subtitle,
  initialTime,
  onComplete,
  onTimeChange,
  autoStart = false,
  showControls = true,
  testID = 'timer-card',
}) => {
  const [timeLeft, setTimeLeft] = useState(initialTime);
  const [isRunning, setIsRunning] = useState(autoStart);
  const [isCompleted, setIsCompleted] = useState(false);

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const progressAnim = useRef(new Animated.Value(1)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    if (isRunning && timeLeft > 0) {
      intervalRef.current = setInterval(() => {
        setTimeLeft(prev => {
          const newTime = prev - 1;
          onTimeChange?.(newTime);

          if (newTime <= 0) {
            setIsRunning(false);
            setIsCompleted(true);
            onComplete?.();

            // Haptic feedback when completed
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);

            // Pulse animation when completed
            Animated.sequence([
              Animated.timing(pulseAnim, {
                toValue: 1.1,
                duration: 200,
                useNativeDriver: true,
              }),
              Animated.timing(pulseAnim, {
                toValue: 1,
                duration: 200,
                useNativeDriver: true,
              }),
            ]).start();
          }

          return newTime;
        });
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isRunning, timeLeft, onComplete, onTimeChange, pulseAnim]);

  // Update progress animation
  useEffect(() => {
    const progress = initialTime > 0 ? timeLeft / initialTime : 0;

    Animated.timing(progressAnim, {
      toValue: progress,
      duration: 500,
      useNativeDriver: false,
    }).start();
  }, [timeLeft, initialTime, progressAnim]);

  const handlePlayPause = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    if (isCompleted) {
      return;
    }

    setIsRunning(!isRunning);
  };

  const handleReset = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    setTimeLeft(initialTime);
    setIsRunning(false);
    setIsCompleted(false);
    onTimeChange?.(initialTime);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(Math.abs(seconds) / 60);
    const secs = Math.abs(seconds) % 60;
    const sign = seconds < 0 ? '-' : '';
    return `${sign}${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getTimerColor = () => {
    if (isCompleted) return Colors.light.success;
    if (timeLeft <= 30) return Colors.light.error;
    if (timeLeft <= 120) return Colors.light.warning;
    return Colors.light.primary;
  };

  const progressPercentage = initialTime > 0 ? (timeLeft / initialTime) * 100 : 0;

  return (
    <StepCard
      title={title}
      subtitle={subtitle}
      icon={<Clock size={24} color={getTimerColor()} />}
      testID={testID}
    >
      <View style={styles.timerContainer}>
        {/* Progress Ring */}
        <View style={styles.progressContainer}>
          <View style={[styles.progressBackground, { borderColor: Colors.light.surface }]} />
          <Animated.View
            style={[
              styles.progressRing,
              {
                borderColor: getTimerColor(),
                transform: [
                  { scale: pulseAnim },
                  {
                    rotate: progressAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: ['0deg', '360deg'],
                    }),
                  },
                ],
              },
            ]}
          />

          {/* Time Display */}
          <View style={styles.timeDisplay}>
            <Text style={[styles.timeText, { color: getTimerColor() }]}>
              {formatTime(timeLeft)}
            </Text>
            {isCompleted && <Text style={styles.completedText}>Completado</Text>}
          </View>
        </View>

        {/* Progress Bar */}
        <View style={styles.progressBarContainer}>
          <View style={styles.progressBarTrack} />
          <Animated.View
            style={[
              styles.progressBarFill,
              {
                width: `${Math.max(0, progressPercentage)}%`,
                backgroundColor: getTimerColor(),
              },
            ]}
          />
        </View>

        {/* Controls */}
        {showControls && (
          <View style={styles.controls}>
            <TouchableOpacity
              style={[styles.controlButton, styles.resetButton]}
              onPress={handleReset}
              testID={`${testID}-reset`}
            >
              <RotateCcw size={20} color={Colors.light.textSecondary} />
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.controlButton,
                styles.playPauseButton,
                { backgroundColor: getTimerColor() },
                isCompleted && styles.disabledButton,
              ]}
              onPress={handlePlayPause}
              disabled={isCompleted}
              testID={`${testID}-play-pause`}
            >
              {isRunning ? <Pause size={24} color="white" /> : <Play size={24} color="white" />}
            </TouchableOpacity>

            <View style={styles.spacer} />
          </View>
        )}
      </View>
    </StepCard>
  );
};

const styles = StyleSheet.create({
  timerContainer: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  progressContainer: {
    width: 120,
    height: 120,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    position: 'relative',
  },
  progressBackground: {
    position: 'absolute',
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 4,
  },
  progressRing: {
    position: 'absolute',
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 4,
    borderTopColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: 'transparent',
  },
  timeDisplay: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  timeText: {
    fontSize: 24,
    fontWeight: '700',
    letterSpacing: 1,
  },
  completedText: {
    fontSize: 12,
    color: Colors.light.success,
    fontWeight: '600',
    marginTop: 4,
  },
  progressBarContainer: {
    width: '100%',
    height: 6,
    backgroundColor: Colors.light.surface,
    borderRadius: 3,
    overflow: 'hidden',
    marginBottom: 20,
  },
  progressBarTrack: {
    width: '100%',
    height: '100%',
    backgroundColor: Colors.light.surface,
  },
  progressBarFill: {
    position: 'absolute',
    top: 0,
    left: 0,
    height: '100%',
    borderRadius: 3,
  },
  controls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    paddingHorizontal: 20,
  },
  controlButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  resetButton: {
    backgroundColor: Colors.light.surface,
  },
  playPauseButton: {
    elevation: 4,
    shadowOpacity: 0.2,
  },
  disabledButton: {
    opacity: 0.5,
  },
  spacer: {
    width: 48,
  },
});
