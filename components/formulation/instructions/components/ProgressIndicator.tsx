import React, { useRef, useEffect } from 'react';
import { View, Animated, StyleSheet } from 'react-native';
import Colors from '@/constants/colors';

interface ProgressStep {
  id: string;
  color: string;
  title: string;
}

interface ProgressIndicatorProps {
  steps: ProgressStep[];
  currentStep: number;
  testID?: string;
}

export const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  steps,
  currentStep,
  testID = 'progress-indicator',
}) => {
  // Progress dot animations
  const progressAnims = useRef(steps.map(() => new Animated.Value(1))).current;

  useEffect(() => {
    // Animate progress dots when step changes
    const animations = steps.map((_, index) =>
      Animated.spring(progressAnims[index], {
        toValue: index === currentStep ? 1.2 : 1,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      })
    );

    Animated.parallel(animations).start();
  }, [currentStep, progressAnims, steps]);

  const progressPercentage = ((currentStep + 1) / steps.length) * 100;
  const currentStepColor = steps[currentStep]?.color || Colors.light.accent;

  return (
    <View style={styles.container} testID={testID}>
      {/* Progress Bar */}
      <View style={styles.progressBarContainer}>
        <View style={styles.progressBarTrack} />
        <Animated.View
          style={[
            styles.progressBarFill,
            {
              width: `${progressPercentage}%`,
              backgroundColor: currentStepColor,
            },
          ]}
        />
      </View>

      {/* Progress Dots */}
      <View style={styles.progressDotsContainer}>
        {steps.map((step, index) => (
          <Animated.View
            key={step.id}
            style={[
              styles.progressDot,
              {
                backgroundColor: index <= currentStep ? step.color : Colors.light.surface,
                borderColor: index <= currentStep ? step.color : Colors.light.textTertiary,
                transform: [{ scale: progressAnims[index] }],
              },
              index <= currentStep ? styles.progressDotActive : styles.progressDotInactive,
            ]}
            testID={`progress-dot-${index}`}
          />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: Colors.light.background,
  },
  progressBarContainer: {
    position: 'relative',
    height: 4,
    marginBottom: 15,
  },
  progressBarTrack: {
    height: 4,
    backgroundColor: Colors.light.surface,
    borderRadius: 2,
  },
  progressBarFill: {
    position: 'absolute',
    top: 0,
    left: 0,
    height: 4,
    borderRadius: 2,
  },
  progressDotsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  progressDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    borderWidth: 2,
  },
  progressDotActive: {
    elevation: 2,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  progressDotInactive: {
    borderWidth: 1,
  },
});
