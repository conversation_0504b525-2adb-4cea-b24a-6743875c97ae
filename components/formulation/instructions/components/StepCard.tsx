import React from 'react';
import { View, Text, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import Colors from '@/constants/colors';

interface StepCardProps {
  title?: string;
  subtitle?: string;
  icon?: React.ReactNode;
  children: React.ReactNode;
  style?: ViewStyle;
  headerStyle?: ViewStyle;
  titleStyle?: TextStyle;
  subtitleStyle?: TextStyle;
  testID?: string;
}

export const StepCard: React.FC<StepCardProps> = ({
  title,
  subtitle,
  icon,
  children,
  style,
  headerStyle,
  titleStyle,
  subtitleStyle,
  testID = 'step-card',
}) => {
  return (
    <View style={[styles.card, style]} testID={testID}>
      {(title || subtitle || icon) && (
        <View style={[styles.header, headerStyle]} testID={`${testID}-header`}>
          {icon && <View style={styles.iconContainer}>{icon}</View>}
          <View style={styles.headerText}>
            {title && (
              <Text style={[styles.title, titleStyle]} testID={`${testID}-title`}>
                {title}
              </Text>
            )}
            {subtitle && (
              <Text style={[styles.subtitle, subtitleStyle]} testID={`${testID}-subtitle`}>
                {subtitle}
              </Text>
            )}
          </View>
        </View>
      )}

      <View style={styles.content} testID={`${testID}-content`}>
        {children}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    padding: 20,
    marginHorizontal: 16,
    marginVertical: 8,
    elevation: 2,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  iconContainer: {
    marginRight: 12,
  },
  headerText: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    lineHeight: 20,
  },
  content: {
    flex: 1,
  },
});
