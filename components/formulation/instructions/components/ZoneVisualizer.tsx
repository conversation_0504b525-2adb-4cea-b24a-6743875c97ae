import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ViewStyle } from 'react-native';
import { ChevronRight, ChevronLeft } from 'lucide-react-native';
import Colors from '@/constants/colors';

interface HairZone {
  id: string;
  name: string;
  description: string;
  order: number;
}

interface ZoneVisualizerProps {
  zones: HairZone[];
  selectedZone: number;
  onZoneSelect: (index: number) => void;
  technique?: 'full_color' | 'zonal' | 'balayage' | 'highlights';
  showLegend?: boolean;
  testID?: string;
}

export const ZoneVisualizer: React.FC<ZoneVisualizerProps> = ({
  zones,
  selectedZone,
  onZoneSelect,
  technique = 'full_color',
  showLegend = true,
  testID = 'zone-visualizer',
}) => {
  const getZoneDiagram = (zoneId: string): ViewStyle => {
    const diagrams: Record<string, ViewStyle> = {
      roots: {
        top: '5%',
        left: 0,
        right: 0,
        height: '28%',
        backgroundColor: Colors.light.primary,
        borderRadius: 8,
      },
      mids: {
        top: '35%',
        left: 0,
        right: 0,
        height: '30%',
        backgroundColor: Colors.light.warning,
        borderRadius: 8,
      },
      ends: {
        top: '67%',
        left: 0,
        right: 0,
        height: '28%',
        backgroundColor: Colors.light.accent,
        borderRadius: 8,
      },
      crown: {
        top: '5%',
        left: '25%',
        right: '25%',
        height: '30%',
        backgroundColor: Colors.light.secondary,
        borderRadius: 50,
      },
      nape: {
        top: '70%',
        left: '20%',
        right: '20%',
        height: '25%',
        backgroundColor: Colors.light.primary,
        borderRadius: 8,
      },
    };
    return (
      diagrams[zoneId] || {
        top: '5%',
        height: '20%',
        backgroundColor: Colors.light.textTertiary,
        borderRadius: 8,
      }
    );
  };

  const getTechniqueTitle = (tech: string) => {
    const titles = {
      zonal: 'Aplicación por Zonas',
      balayage: 'Técnica Balayage',
      highlights: 'Mechas con Papel',
      full_color: 'Color Completo',
    };
    return titles[tech as keyof typeof titles] || 'Color Completo';
  };

  return (
    <View style={styles.container} testID={testID}>
      <View style={styles.header}>
        <Text style={styles.title}>Guía de Aplicación Profesional</Text>
        <Text style={styles.subtitle}>{getTechniqueTitle(technique)}</Text>
      </View>

      <View style={styles.headDiagram} testID={`${testID}-diagram`}>
        <View style={styles.headShape}>
          {/* Hair section divisions */}
          <View style={styles.sectionLines}>
            <View style={[styles.sectionLine, styles.horizontalLine]} />
            <View style={[styles.sectionLine, styles.verticalLine]} />
          </View>

          {zones.map((zone, index) => (
            <TouchableOpacity
              key={zone.id}
              style={[
                styles.zoneArea,
                getZoneDiagram(zone.id),
                selectedZone === index && styles.zoneAreaActive,
              ]}
              onPress={() => onZoneSelect(index)}
              testID={`${testID}-zone-${index}`}
            >
              <Text style={styles.zoneNumber}>{zone.order}</Text>
              {selectedZone === index && (
                <View style={styles.zoneArrows}>
                  {technique === 'full_color' && (
                    <ChevronRight size={16} color="white" style={styles.arrowIcon} />
                  )}
                  {technique === 'zonal' && (
                    <ChevronLeft size={16} color="white" style={styles.arrowIcon} />
                  )}
                </View>
              )}
            </TouchableOpacity>
          ))}
        </View>

        {/* Application direction legend */}
        {showLegend && (
          <View style={styles.directionLegend}>
            <View style={styles.legendItem}>
              <ChevronRight size={14} color={Colors.light.primary} />
              <Text style={styles.legendText}>Dirección aplicación</Text>
            </View>
            <View style={styles.legendItem}>
              <View style={[styles.saturationDot, styles.saturationFull]} />
              <Text style={styles.legendText}>100% saturación</Text>
            </View>
            <View style={styles.legendItem}>
              <View style={[styles.saturationDot, styles.saturationPartial]} />
              <Text style={styles.legendText}>60% saturación</Text>
            </View>
          </View>
        )}
      </View>

      {/* Zone Details */}
      {zones[selectedZone] && (
        <View style={styles.zoneDetails} testID={`${testID}-details`}>
          <Text style={styles.zoneName}>{zones[selectedZone].name}</Text>
          <Text style={styles.zoneDescription}>{zones[selectedZone].description}</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  headDiagram: {
    alignItems: 'center',
    marginBottom: 20,
  },
  headShape: {
    width: 180,
    height: 240,
    borderRadius: 90,
    backgroundColor: Colors.light.surface,
    position: 'relative',
    marginBottom: 16,
    elevation: 2,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionLines: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  sectionLine: {
    position: 'absolute',
    backgroundColor: Colors.light.textTertiary,
    opacity: 0.3,
  },
  horizontalLine: {
    top: '50%',
    left: 20,
    right: 20,
    height: 1,
  },
  verticalLine: {
    left: '50%',
    top: 20,
    bottom: 20,
    width: 1,
  },
  zoneArea: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    minHeight: 40,
  },
  zoneAreaActive: {
    borderWidth: 2,
    borderColor: Colors.light.background,
    elevation: 4,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  zoneNumber: {
    color: Colors.light.textLight,
    fontWeight: '700',
    fontSize: 16,
  },
  zoneArrows: {
    position: 'absolute',
    right: 8,
  },
  arrowIcon: {
    opacity: 0.8,
  },
  directionLegend: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: 16,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  legendText: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    marginLeft: 4,
  },
  saturationDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: Colors.light.primary,
  },
  saturationFull: {
    opacity: 1,
  },
  saturationPartial: {
    opacity: 0.6,
  },
  zoneDetails: {
    backgroundColor: Colors.light.surface,
    borderRadius: 8,
    padding: 16,
  },
  zoneName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 8,
  },
  zoneDescription: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    lineHeight: 20,
  },
});
