import React from 'react';
import { StyleSheet, TouchableOpacity, Text, View } from 'react-native';
import { MessageSquare } from 'lucide-react-native';
import { useRouter } from 'expo-router';
import Animated, { useSharedValue, useAnimatedStyle, withSpring } from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';

// Internal imports
import Colors from '@/constants/colors';
import { typography, spacing, radius, shadows } from '@/constants/theme';
import { useFormulaFeedbackStore } from '@/stores/formula-feedback-store';

interface FeedbackHistoryButtonProps {
  variant?: 'floating' | 'card' | 'mini';
  style?: object;
  showStats?: boolean;
}

export const FeedbackHistoryButton: React.FC<FeedbackHistoryButtonProps> = ({
  variant = 'floating',
  style,
  showStats = false,
}) => {
  const router = useRouter();
  const { feedbacks, pendingSyncCount } = useFormulaFeedbackStore();
  const scale = useSharedValue(1);

  // Calculate quick stats
  const totalFeedbacks = feedbacks.length;
  const recentFeedbacks = feedbacks.filter(f => {
    const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    return new Date(f.created_at) >= weekAgo;
  }).length;

  const handlePress = () => {
    // Animation
    scale.value = withSpring(0.95, { duration: 100 }, () => {
      scale.value = withSpring(1, { duration: 100 });
    });

    // Haptic feedback
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    // Navigate to feedback history
    router.push('/(tabs)/feedback-history');
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  // Floating variant (FAB style)
  if (variant === 'floating') {
    return (
      <Animated.View style={[styles.floatingButton, animatedStyle, style]}>
        <TouchableOpacity style={styles.floatingTouchable} onPress={handlePress} activeOpacity={1}>
          <MessageSquare size={24} color={Colors.light.textLight} />
          {totalFeedbacks > 0 && (
            <View style={styles.badge}>
              <Text style={styles.badgeText}>{totalFeedbacks}</Text>
            </View>
          )}
        </TouchableOpacity>
      </Animated.View>
    );
  }

  // Mini variant (small button for headers)
  if (variant === 'mini') {
    return (
      <Animated.View style={[animatedStyle, style]}>
        <TouchableOpacity style={styles.miniButton} onPress={handlePress} activeOpacity={0.7}>
          <MessageSquare size={16} color={Colors.light.primary} />
          {pendingSyncCount() > 0 && <View style={styles.syncDot} />}
        </TouchableOpacity>
      </Animated.View>
    );
  }

  // Card variant (for dashboard)
  return (
    <Animated.View style={[animatedStyle, style]}>
      <TouchableOpacity style={styles.cardButton} onPress={handlePress} activeOpacity={0.8}>
        <View style={styles.cardHeader}>
          <MessageSquare size={24} color={Colors.light.primary} />
          <Text style={styles.cardTitle}>Feedback</Text>
        </View>

        {showStats && totalFeedbacks > 0 && (
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>{totalFeedbacks}</Text>
              <Text style={styles.statLabel}>Total</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>{recentFeedbacks}</Text>
              <Text style={styles.statLabel}>Esta semana</Text>
            </View>
          </View>
        )}

        <View style={styles.cardFooter}>
          <Text style={styles.cardDescription}>
            {totalFeedbacks === 0 ? 'No hay feedback aún' : `Ver todos (${totalFeedbacks})`}
          </Text>
          {pendingSyncCount() > 0 && (
            <View style={styles.syncIndicator}>
              <View style={styles.syncDot} />
              <Text style={styles.syncText}>{pendingSyncCount()}</Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  // Floating button styles
  floatingButton: {
    position: 'absolute',
    bottom: 80, // Above tab bar
    right: spacing.md,
    zIndex: 1000,
  },
  floatingTouchable: {
    width: 56,
    height: 56,
    backgroundColor: Colors.light.primary,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    ...shadows.lg,
  },
  badge: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: Colors.light.error,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: Colors.light.background,
  },
  badgeText: {
    color: Colors.light.textLight,
    fontSize: 10,
    fontWeight: typography.weights.bold,
  },

  // Mini button styles
  miniButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.light.primaryBackground,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  syncDot: {
    position: 'absolute',
    top: 4,
    right: 4,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.light.warning,
  },

  // Card button styles
  cardButton: {
    backgroundColor: Colors.light.surface,
    borderRadius: radius.lg,
    padding: spacing.md,
    borderWidth: 1,
    borderColor: Colors.light.border,
    ...shadows.sm,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  cardTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginLeft: spacing.sm,
  },
  cardDescription: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: spacing.sm,
  },

  // Stats styles
  statsContainer: {
    flexDirection: 'row',
    marginBottom: spacing.sm,
    gap: spacing.md,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: Colors.light.primary,
  },
  statLabel: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
    marginTop: spacing.xs,
  },

  // Sync indicator styles
  syncIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.warningBackground,
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
    borderRadius: radius.sm,
  },
  syncText: {
    fontSize: typography.sizes.xs,
    color: Colors.light.warning,
    marginLeft: spacing.xs,
  },
});

export default FeedbackHistoryButton;
