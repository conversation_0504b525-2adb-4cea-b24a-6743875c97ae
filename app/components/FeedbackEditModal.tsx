import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, TextInput, Alert } from 'react-native';
import { X, Star, CheckCircle2, Save, RotateCcw } from 'lucide-react-native';
import Animated, { FadeIn } from 'react-native-reanimated';

// Internal imports
import Colors from '@/constants/colors';
import { typography, spacing, radius, shadows } from '@/constants/theme';
import { BaseButton } from '@/components/base/BaseButton';
import { BottomSheet } from '@/components/base/BottomSheet';
import { type FormulaFeedback } from '@/stores/formula-feedback-store';
import { logger } from '@/utils/logger';
import * as Haptics from 'expo-haptics';

interface FeedbackEditModalProps {
  visible: boolean;
  onClose: () => void;
  feedback: FormulaFeedback;
  onSave: (updates: Partial<FormulaFeedback>) => Promise<void>;
  loading?: boolean;
}

export const FeedbackEditModal: React.FC<FeedbackEditModalProps> = ({
  visible,
  onClose,
  feedback,
  onSave,
  loading = false,
}) => {
  // Form state
  const [rating, setRating] = useState(feedback.rating);
  const [workedAsExpected, setWorkedAsExpected] = useState(feedback.worked_as_expected);
  const [wouldUseAgain, setWouldUseAgain] = useState(feedback.would_use_again);
  const [actualResult, setActualResult] = useState(feedback.actual_result || '');
  const [adjustmentsMade, setAdjustmentsMade] = useState(feedback.adjustments_made || '');
  const [hairType, setHairType] = useState(feedback.hair_type || '');
  const [environmentalFactors, setEnvironmentalFactors] = useState(
    feedback.environmental_factors || ''
  );

  // Reset form when feedback changes
  useEffect(() => {
    if (feedback) {
      setRating(feedback.rating);
      setWorkedAsExpected(feedback.worked_as_expected);
      setWouldUseAgain(feedback.would_use_again);
      setActualResult(feedback.actual_result || '');
      setAdjustmentsMade(feedback.adjustments_made || '');
      setHairType(feedback.hair_type || '');
      setEnvironmentalFactors(feedback.environmental_factors || '');
    }
  }, [feedback]);

  // Check if form has changes
  const hasChanges = () => {
    return (
      rating !== feedback.rating ||
      workedAsExpected !== feedback.worked_as_expected ||
      wouldUseAgain !== feedback.would_use_again ||
      actualResult !== (feedback.actual_result || '') ||
      adjustmentsMade !== (feedback.adjustments_made || '') ||
      hairType !== (feedback.hair_type || '') ||
      environmentalFactors !== (feedback.environmental_factors || '')
    );
  };

  // Reset form to original values
  const resetForm = () => {
    setRating(feedback.rating);
    setWorkedAsExpected(feedback.worked_as_expected);
    setWouldUseAgain(feedback.would_use_again);
    setActualResult(feedback.actual_result || '');
    setAdjustmentsMade(feedback.adjustments_made || '');
    setHairType(feedback.hair_type || '');
    setEnvironmentalFactors(feedback.environmental_factors || '');
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  // Handle save
  const handleSave = async () => {
    if (!hasChanges()) {
      onClose();
      return;
    }

    if (rating === 0) {
      Alert.alert('Error', 'Por favor selecciona una calificación.');
      return;
    }

    try {
      const updates: Partial<FormulaFeedback> = {
        rating,
        worked_as_expected: workedAsExpected,
        would_use_again: wouldUseAgain,
        actual_result: actualResult.trim() || undefined,
        adjustments_made: adjustmentsMade.trim() || undefined,
        hair_type: hairType.trim() || undefined,
        environmental_factors: environmentalFactors.trim() || undefined,
      };

      await onSave(updates);

      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

      logger.info('Feedback updated successfully', 'FeedbackEditModal', {
        feedbackId: feedback.id,
        changes: Object.keys(updates),
      });
    } catch (error) {
      logger.error('Failed to update feedback', 'FeedbackEditModal', { error });
      Alert.alert('Error', 'No se pudo guardar los cambios. Inténtalo de nuevo.');
    }
  };

  // Handle close with unsaved changes
  const handleClose = () => {
    if (hasChanges()) {
      Alert.alert('Cambios sin guardar', '¿Quieres descartar los cambios realizados?', [
        { text: 'Cancelar', style: 'cancel' },
        { text: 'Descartar', style: 'destructive', onPress: onClose },
      ]);
    } else {
      onClose();
    }
  };

  // Star rating component
  const StarRating: React.FC<{
    rating: number;
    onRatingChange: (rating: number) => void;
    size?: number;
  }> = ({ rating: currentRating, onRatingChange, size = 32 }) => (
    <View style={styles.starContainer}>
      {[1, 2, 3, 4, 5].map(star => (
        <TouchableOpacity
          key={star}
          onPress={() => {
            onRatingChange(star);
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          }}
          style={styles.starButton}
        >
          <Star
            size={size}
            color={star <= currentRating ? Colors.light.warning : Colors.light.grayLight}
            fill={star <= currentRating ? Colors.light.warning : 'transparent'}
          />
        </TouchableOpacity>
      ))}
      <Text style={styles.ratingLabel}>({currentRating}/5)</Text>
    </View>
  );

  // Format creation date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <BottomSheet
      visible={visible}
      onClose={handleClose}
      title="Editar Feedback"
      height="full"
      scrollable={true}
    >
      <Animated.View entering={FadeIn.duration(200)} style={styles.container}>
        {/* Feedback info */}
        <View style={styles.infoCard}>
          <Text style={styles.infoTitle}>Feedback del Servicio</Text>
          <Text style={styles.infoDate}>Creado: {formatDate(feedback.created_at)}</Text>
          {feedback.updated_at !== feedback.created_at && (
            <Text style={styles.infoDate}>Actualizado: {formatDate(feedback.updated_at)}</Text>
          )}
        </View>

        {/* Rating section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Calificación general *</Text>
          <StarRating rating={rating} onRatingChange={setRating} />
        </View>

        {/* Success toggle */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>¿Funcionó como esperabas?</Text>
          <View style={styles.toggleContainer}>
            <TouchableOpacity
              style={[styles.toggleOption, workedAsExpected && styles.toggleOptionActive]}
              onPress={() => {
                setWorkedAsExpected(true);
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              }}
            >
              <CheckCircle2
                size={20}
                color={workedAsExpected ? Colors.light.success : Colors.light.textSecondary}
              />
              <Text
                style={[
                  styles.toggleOptionText,
                  workedAsExpected && { color: Colors.light.success },
                ]}
              >
                Sí
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.toggleOption, !workedAsExpected && styles.toggleOptionActive]}
              onPress={() => {
                setWorkedAsExpected(false);
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              }}
            >
              <X
                size={20}
                color={!workedAsExpected ? Colors.light.error : Colors.light.textSecondary}
              />
              <Text
                style={[
                  styles.toggleOptionText,
                  !workedAsExpected && { color: Colors.light.error },
                ]}
              >
                No
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Would use again toggle */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>¿Usarías esta fórmula de nuevo?</Text>
          <TouchableOpacity
            style={styles.checkbox}
            onPress={() => {
              setWouldUseAgain(!wouldUseAgain);
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            }}
          >
            <CheckCircle2
              size={24}
              color={wouldUseAgain ? Colors.light.success : Colors.light.grayLight}
              fill={wouldUseAgain ? Colors.light.success : 'transparent'}
            />
            <Text style={styles.checkboxLabel}>Sí, la usaría otra vez</Text>
          </TouchableOpacity>
        </View>

        {/* Result description */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>¿Qué resultado obtuviste?</Text>
          <TextInput
            style={styles.textInput}
            placeholder="Describe el resultado real obtenido..."
            value={actualResult}
            onChangeText={setActualResult}
            multiline
            numberOfLines={3}
            textAlignVertical="top"
          />
        </View>

        {/* Adjustments made */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>¿Qué ajustes hiciste?</Text>
          <TextInput
            style={styles.textInput}
            placeholder="Describe los cambios o ajustes que aplicaste..."
            value={adjustmentsMade}
            onChangeText={setAdjustmentsMade}
            multiline
            numberOfLines={3}
            textAlignVertical="top"
          />
        </View>

        {/* Hair type */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Tipo de cabello</Text>
          <TextInput
            style={styles.textInput}
            placeholder="Ej: Graso, rizado, teñido anteriormente..."
            value={hairType}
            onChangeText={setHairType}
          />
        </View>

        {/* Environmental factors */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Factores ambientales</Text>
          <TextInput
            style={styles.textInput}
            placeholder="Ej: Humedad alta, temperatura ambiente..."
            value={environmentalFactors}
            onChangeText={setEnvironmentalFactors}
          />
        </View>

        {/* Action buttons */}
        <View style={styles.actionsContainer}>
          <BaseButton
            title="Restablecer"
            variant="ghost"
            icon={RotateCcw}
            onPress={resetForm}
            disabled={!hasChanges() || loading}
            style={styles.resetButton}
          />
          <BaseButton
            title="Guardar cambios"
            icon={Save}
            onPress={handleSave}
            loading={loading}
            disabled={rating === 0 || loading}
            style={styles.saveButton}
          />
        </View>

        {/* Sync status */}
        {!feedback.is_synced && (
          <View style={styles.syncWarning}>
            <Text style={styles.syncWarningText}>
              Este feedback aún no se ha sincronizado con el servidor
            </Text>
          </View>
        )}
      </Animated.View>
    </BottomSheet>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingBottom: spacing.xl,
  },

  // Info card
  infoCard: {
    backgroundColor: Colors.light.surface,
    padding: spacing.md,
    borderRadius: radius.md,
    marginBottom: spacing.lg,
    ...shadows.sm,
  },
  infoTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  infoDate: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginBottom: spacing.xs,
  },

  // Sections
  section: {
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.sm,
  },

  // Star rating
  starContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.sm,
  },
  starButton: {
    padding: spacing.xs,
  },
  ratingLabel: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginLeft: spacing.sm,
  },

  // Toggle options
  toggleContainer: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  toggleOption: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: radius.md,
    borderWidth: 1,
    borderColor: Colors.light.border,
    backgroundColor: Colors.light.background,
  },
  toggleOptionActive: {
    backgroundColor: Colors.light.surface,
    ...shadows.sm,
  },
  toggleOptionText: {
    fontSize: typography.sizes.base,
    color: Colors.light.textSecondary,
    marginLeft: spacing.sm,
  },

  // Checkbox
  checkbox: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.sm,
  },
  checkboxLabel: {
    fontSize: typography.sizes.base,
    color: Colors.light.text,
    marginLeft: spacing.sm,
    flex: 1,
  },

  // Text inputs
  textInput: {
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: radius.md,
    padding: spacing.md,
    fontSize: typography.sizes.base,
    color: Colors.light.text,
    backgroundColor: Colors.light.background,
    minHeight: 44,
  },

  // Actions
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: spacing.lg,
    gap: spacing.md,
  },
  resetButton: {
    flex: 1,
  },
  saveButton: {
    flex: 2,
  },

  // Sync warning
  syncWarning: {
    backgroundColor: Colors.light.warningBackground,
    padding: spacing.sm,
    borderRadius: radius.md,
    marginTop: spacing.md,
    borderLeftWidth: 3,
    borderLeftColor: Colors.light.warning,
  },
  syncWarningText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.warningText,
    fontStyle: 'italic',
  },
});

export default FeedbackEditModal;
