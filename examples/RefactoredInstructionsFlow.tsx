/**
 * PHASE 3 EXAMPLE: Refactored InstructionsFlow using custom business logic hooks
 *
 * This example shows how to integrate the new hooks into the existing InstructionsFlow component.
 * The business logic is now completely separated from UI concerns, making it:
 * - More testable
 * - More reusable
 * - Easier to maintain
 * - Performance optimized
 */

import React, { useEffect, useCallback } from 'react';
import { View, Text, StyleSheet, ScrollView, Animated } from 'react-native';
import {
  ChevronRight,
  ChevronLeft,
  Package,
  GitMerge,
  Target,
  Calculator,
  FlaskConical,
  Brush,
  Calendar,
  Lightbulb,
  Sparkles,
} from 'lucide-react-native';

// Import our new custom hooks
import { useInstructionFlow, FlowStep } from '@/hooks/useInstructionFlow';
import { useStepValidation } from '@/hooks/useStepValidation';
import { useStepTimer, TimerPhase } from '@/hooks/useStepTimer';
import { useProgressTracking } from '@/hooks/useProgressTracking';
import { useAnimations } from '@/hooks/useAnimations';
import { useHapticFeedback } from '@/hooks/useHapticFeedback';

// Import types
import { VisualFormulationData } from '@/types/visual-formulation';
import Colors from '@/constants/colors';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { commonStyles } from '@/styles/commonStyles';

// Enhanced flow steps with business requirements
const ENHANCED_FLOW_STEPS: FlowStep[] = [
  {
    id: 'checklist',
    title: 'Lista de Verificación',
    icon: Package,
    color: Colors.light.primary,
    estimatedDuration: 3, // minutes
    requirements: ['protective_gloves', 'tools_sanitized', 'all_products_available'],
  },
  {
    id: 'transformation',
    title: 'Tu Transformación',
    icon: GitMerge,
    color: Colors.light.accent,
    estimatedDuration: 5,
    requirements: ['hair_analysis_complete', 'client_consultation_complete'],
  },
  {
    id: 'formulas',
    title: 'Fórmulas Personalizadas',
    icon: Target,
    color: Colors.light.primary,
    estimatedDuration: 4,
    requirements: ['formulation_validated', 'color_theory_applied'],
  },
  {
    id: 'proportions',
    title: 'Guía de Proporciones',
    icon: Calculator,
    color: Colors.light.primaryDark,
    estimatedDuration: 3,
    requirements: ['measurements_verified'],
  },
  {
    id: 'calculator',
    title: 'Calculadora Visual',
    icon: Calculator,
    color: Colors.light.primary,
    estimatedDuration: 2,
    isOptional: true,
  },
  {
    id: 'mixing',
    title: 'Estación de Mezcla',
    icon: FlaskConical,
    color: Colors.light.secondary,
    estimatedDuration: 8,
    requirements: ['strand_test_performed', 'mixing_bowls_ready'],
  },
  {
    id: 'application',
    title: 'Guía de Aplicación',
    icon: Brush,
    color: Colors.light.accent,
    estimatedDuration: 45,
    requirements: ['patch_test_completed', 'proper_ventilation'],
  },
  {
    id: 'timeline',
    title: 'Cronograma',
    icon: Calendar,
    color: Colors.light.primaryDark,
    estimatedDuration: 30,
  },
  {
    id: 'tips',
    title: 'Tips Profesionales',
    icon: Lightbulb,
    color: Colors.light.warning,
    estimatedDuration: 2,
    isOptional: true,
  },
  {
    id: 'result',
    title: 'Resultado Esperado',
    icon: Sparkles,
    color: Colors.light.success,
    estimatedDuration: 5,
  },
];

// Timer phases for each step (where applicable)
const STEP_TIMER_PHASES: Record<string, TimerPhase[]> = {
  mixing: [
    {
      id: 'preparation',
      name: 'Preparación',
      duration: 180, // 3 minutes
      description: 'Preparar productos y herramientas',
      color: Colors.light.primary,
      hapticPattern: 'light',
    },
    {
      id: 'mixing',
      name: 'Mezclado',
      duration: 300, // 5 minutes
      description: 'Mezclar productos según proporciones',
      color: Colors.light.secondary,
      hapticPattern: 'medium',
      alertIntervals: [60, 30], // Alert at 1min and 30s remaining
    },
  ],
  application: [
    {
      id: 'section_1',
      name: 'Sección 1 - Raíces',
      duration: 1200, // 20 minutes
      description: 'Aplicación en zona de raíces',
      color: Colors.light.accent,
      hapticPattern: 'light',
      alertIntervals: [300, 60], // Alert at 5min and 1min remaining
    },
    {
      id: 'section_2',
      name: 'Sección 2 - Medios',
      duration: 900, // 15 minutes
      description: 'Aplicación en medios y puntas',
      color: Colors.light.accent,
      hapticPattern: 'light',
    },
    {
      id: 'processing',
      name: 'Procesamiento',
      duration: 600, // 10 minutes
      description: 'Tiempo de procesamiento final',
      color: Colors.light.success,
      hapticPattern: 'warning',
      alertIntervals: [300, 120, 60], // Multiple alerts
    },
  ],
  timeline: [
    {
      id: 'processing_time',
      name: 'Tiempo de Procesamiento',
      duration: 1800, // 30 minutes
      description: 'Tiempo total de procesamiento del color',
      color: Colors.light.primaryDark,
      hapticPattern: 'light',
      alertIntervals: [600, 300, 60], // 10min, 5min, 1min warnings
    },
  ],
};

interface RefactoredInstructionsFlowProps {
  formulaData: VisualFormulationData;
  onClose?: () => void;
  onComplete?: () => void;
}

export default function RefactoredInstructionsFlow({
  formulaData,
  onClose,
  onComplete,
}: RefactoredInstructionsFlowProps) {
  // Initialize all custom hooks
  const instructionFlow = useInstructionFlow({
    steps: ENHANCED_FLOW_STEPS,
    formulaData,
    onStepChange: stepIndex => {
      // Update progress tracking when step changes
      progressTracking.recordStepStart(stepIndex);

      // Trigger step animations
      animations.animateStepTransition('forward');

      // Haptic feedback
      hapticFeedback.stepAdvance();
    },
    onComplete: () => {
      // Flow completion logic
      progressTracking.updateStepProgress(ENHANCED_FLOW_STEPS.length - 1, true);
      hapticFeedback.flowComplete();
      onComplete?.();
    },
  });

  const stepValidation = useStepValidation({
    steps: ENHANCED_FLOW_STEPS,
    formulaData,
    strictMode: false, // Allow warnings but not errors
    enableProfessionalScoring: true,
  });

  const stepTimer = useStepTimer(
    {
      phases: STEP_TIMER_PHASES[instructionFlow.getCurrentStepData().id] || [],
      autoAdvance: false, // Manual control
      allowPause: true,
      backgroundTracking: true,
      persistState: true,
      onPhaseChange: (_phase, _phaseIndex) => {
        // Timer phase changed: ${phase.name}
        hapticFeedback.phaseTransition();
      },
      onPhaseComplete: (_phase, _phaseIndex) => {
        // Timer phase completed: ${phase.name}
        hapticFeedback.timerAlert();
      },
      onTimerComplete: () => {
        // All timer phases completed
        hapticFeedback.stepComplete();

        // Auto-advance to next step if timer completes
        if (instructionFlow.canAdvance) {
          instructionFlow.advanceStep();
        }
      },
    },
    `timer_${formulaData.id}_${instructionFlow.currentStep}`
  );

  const progressTracking = useProgressTracking({
    steps: ENHANCED_FLOW_STEPS,
    onMilestoneAchieved: _milestone => {
      // Milestone achieved: ${milestone.name}
      hapticFeedback.milestone();
      // Could show a celebration modal here
    },
    onProgressUpdate: _metrics => {
      // Progress updated: ${metrics.overallProgress.toFixed(1)}%
    },
  });

  const animations = useAnimations(ENHANCED_FLOW_STEPS.length);

  const hapticFeedback = useHapticFeedback();

  // Update progress tracking when checklist changes
  useEffect(() => {
    progressTracking.updateChecklistProgress(instructionFlow.checkedItems);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [instructionFlow.checkedItems]);

  // Validate current step and update quality score
  useEffect(() => {
    const validationResult = stepValidation.validateStep(instructionFlow.currentStep, {
      checkedItems: instructionFlow.checkedItems,
      completedSteps: instructionFlow.completedSteps,
      elapsedTime: instructionFlow.getElapsedTime(),
    });

    // Update quality score based on validation
    progressTracking.updateQualityScore(
      instructionFlow.currentStep,
      validationResult.professionalScore
    );

    // Provide haptic feedback based on validation
    if (!validationResult.canProceed) {
      if (validationResult.errors.length > 0) {
        hapticFeedback.validationError();
      } else if (validationResult.warnings.length > 0) {
        hapticFeedback.validationWarning();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [instructionFlow.currentStep, instructionFlow.checkedItems, instructionFlow.completedSteps]);

  // Enhanced navigation with validation
  const handleAdvanceStep = useCallback(async () => {
    // Validate before advancing
    const validationResult = stepValidation.validateStep(instructionFlow.currentStep, {
      checkedItems: instructionFlow.checkedItems,
      completedSteps: instructionFlow.completedSteps,
      elapsedTime: instructionFlow.getElapsedTime(),
    });

    if (validationResult.canProceed) {
      // Record step completion
      progressTracking.recordStepCompletion(instructionFlow.currentStep);

      // Mark step as complete
      instructionFlow.markStepComplete(instructionFlow.currentStep);

      // Advance with animation and haptics
      await animations.animateStepExit();
      await instructionFlow.advanceStep();
      await animations.animateStepEntrance();

      // Update progress
      progressTracking.updateStepProgress(instructionFlow.currentStep - 1, true);
    } else {
      // Show validation errors with haptic feedback
      if (validationResult.errors.length > 0) {
        hapticFeedback.validationError();
      } else {
        hapticFeedback.validationWarning();
      }

      // Could show validation modal here
      // Validation failed: validationResult.errors
    }
  }, [instructionFlow, stepValidation, progressTracking, animations, hapticFeedback]);

  const handleGoBack = useCallback(async () => {
    await animations.animateStepExit();
    await instructionFlow.goToPreviousStep();
    await animations.animateStepEntrance();
    hapticFeedback.navigationBack();
  }, [instructionFlow, animations, hapticFeedback]);

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleChecklistItemToggle = useCallback(
    async (itemId: string) => {
      await instructionFlow.toggleChecklistItem(itemId);
      hapticFeedback.checklistToggle();
    },
    [instructionFlow, hapticFeedback]
  );

  // Get current step data
  const currentStepData = instructionFlow.getCurrentStepData();
  const hasTimer = STEP_TIMER_PHASES[currentStepData.id];
  const metrics = progressTracking.metrics;

  // Animated styles
  const fadeStyle = animations.getAnimatedStyle('fade');
  const slideStyle = animations.getAnimatedStyle('slide');

  return (
    <View style={styles.container}>
      {/* Enhanced Header with Progress and Quality Indicators */}
      <View style={[styles.header, { backgroundColor: currentStepData.color }]}>
        <View style={styles.headerTop}>
          <button onPress={onClose} style={styles.closeButton}>
            <ChevronLeft size={28} color="white" />
          </button>

          <View style={styles.headerContent}>
            <Text style={styles.stepNumber}>
              Paso {instructionFlow.currentStep + 1} de {instructionFlow.totalSteps}
            </Text>
            <Text style={styles.stepTitle}>{currentStepData.title}</Text>

            {/* Progress and Quality Indicators */}
            <View style={styles.metricsRow}>
              <Text style={styles.progressText}>
                Progreso: {metrics.overallProgress.toFixed(0)}%
              </Text>
              <Text style={styles.qualityText}>
                Calidad: {metrics.averageQualityScore.toFixed(0)}%
              </Text>
            </View>
          </View>

          <View style={styles.timerContainer}>
            {hasTimer && stepTimer.hasStarted && (
              <View style={styles.timerDisplay}>
                <Text style={styles.timerText}>{stepTimer.formatCurrentPhaseTime()}</Text>
                <Text style={styles.timerPhase}>{stepTimer.currentPhase?.name}</Text>
              </View>
            )}
          </View>
        </View>

        {/* Enhanced Progress Indicators */}
        <View style={styles.progressContainer}>
          {ENHANCED_FLOW_STEPS.map((step, index) => {
            const isCompleted = instructionFlow.isStepCompleted(index);
            const isCurrent = index === instructionFlow.currentStep;

            return (
              <Animated.View
                key={step.id}
                style={[
                  styles.progressDot,
                  {
                    backgroundColor: isCompleted
                      ? Colors.light.success
                      : isCurrent
                        ? step.color
                        : Colors.light.border,
                    transform: [{ scale: animations.progressAnims[index] }],
                  },
                ]}
              />
            );
          })}
        </View>
      </View>

      {/* Main Content with Animations */}
      <Animated.View style={[styles.content, fadeStyle, slideStyle]}>
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Current Step Content would be rendered here */}
          {/* This is where you'd render the specific step components */}

          {/* Timer Controls (if applicable) */}
          {hasTimer && (
            <View style={styles.timerControls}>
              <button
                style={[styles.timerButton, styles.startButton]}
                onPress={stepTimer.start}
                disabled={stepTimer.isRunning}
              >
                <Text style={styles.timerButtonText}>
                  {stepTimer.hasStarted ? 'Reanudar' : 'Iniciar Timer'}
                </Text>
              </button>

              {stepTimer.hasStarted && (
                <>
                  <button
                    style={[styles.timerButton, styles.pauseButton]}
                    onPress={stepTimer.pause}
                    disabled={stepTimer.isPaused}
                  >
                    <Text style={styles.timerButtonText}>Pausar</Text>
                  </button>

                  <button
                    style={[styles.timerButton, styles.resetButton]}
                    onPress={stepTimer.reset}
                  >
                    <Text style={styles.timerButtonText}>Reiniciar</Text>
                  </button>
                </>
              )}
            </View>
          )}

          {/* Validation Status */}
          {(() => {
            const validation = stepValidation.validateStep(instructionFlow.currentStep, {
              checkedItems: instructionFlow.checkedItems,
              completedSteps: instructionFlow.completedSteps,
              elapsedTime: instructionFlow.getElapsedTime(),
            });

            if (validation.errors.length > 0 || validation.warnings.length > 0) {
              return (
                <View style={styles.validationContainer}>
                  {validation.errors.map((error, index) => (
                    <View key={`error-${index}`} style={styles.errorRow}>
                      <Text style={styles.errorText}>{error.message}</Text>
                      {error.actionRequired && (
                        <Text style={styles.actionText}>{error.actionRequired}</Text>
                      )}
                    </View>
                  ))}

                  {validation.warnings.map((warning, index) => (
                    <View key={`warning-${index}`} style={styles.warningRow}>
                      <Text style={styles.warningText}>{warning.message}</Text>
                    </View>
                  ))}
                </View>
              );
            }

            return null;
          })()}
        </ScrollView>
      </Animated.View>

      {/* Enhanced Navigation Footer */}
      <View style={styles.footer}>
        <View style={styles.navigationRow}>
          <button
            style={[styles.navButton, !instructionFlow.canGoBack && styles.navButtonDisabled]}
            onPress={handleGoBack}
            disabled={!instructionFlow.canGoBack}
          >
            <ChevronLeft size={24} color="white" />
            <Text style={styles.navButtonText}>Anterior</Text>
          </button>

          <View style={styles.progressInfo}>
            <Text style={styles.progressMainText}>
              {metrics.overallProgress.toFixed(0)}% Completado
            </Text>
            <Text style={styles.timeText}>
              Tiempo estimado: {Math.ceil(metrics.estimatedTimeRemaining / 60)} min
            </Text>
          </View>

          <button
            style={[styles.navButton, !instructionFlow.canAdvance && styles.navButtonDisabled]}
            onPress={handleAdvanceStep}
            disabled={!instructionFlow.canAdvance}
          >
            <Text style={styles.navButtonText}>
              {instructionFlow.currentStep === ENHANCED_FLOW_STEPS.length - 1
                ? 'Finalizar'
                : 'Siguiente'}
            </Text>
            <ChevronRight size={24} color="white" />
          </button>
        </View>
      </View>
    </View>
  );
}

// Styles remain similar but with enhanced metrics display
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  header: {
    paddingTop: 50,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  headerTop: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  closeButton: {
    padding: 8,
  },
  headerContent: {
    flex: 1,
    marginLeft: 15,
  },
  stepNumber: {
    color: 'white',
    fontSize: 14,
    opacity: 0.9,
  },
  stepTitle: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 2,
  },
  metricsRow: {
    flexDirection: 'row',
    marginTop: 8,
    gap: 15,
  },
  progressText: {
    color: 'white',
    fontSize: 12,
    opacity: 0.8,
  },
  qualityText: {
    color: 'white',
    fontSize: 12,
    opacity: 0.8,
  },
  timerContainer: {
    alignItems: 'flex-end',
  },
  timerDisplay: {
    alignItems: 'flex-end',
  },
  timerText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  timerPhase: {
    color: 'white',
    fontSize: 12,
    opacity: 0.8,
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
  },
  progressDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.light.border,
  },
  content: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    padding: 20,
  },
  timerControls: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 10,
    marginVertical: 20,
  },
  timerButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    minWidth: 100,
  },
  startButton: {
    backgroundColor: Colors.light.success,
  },
  pauseButton: {
    backgroundColor: Colors.light.warning,
  },
  resetButton: {
    backgroundColor: Colors.light.error,
  },
  timerButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  validationContainer: {
    marginVertical: 15,
    gap: 8,
  },
  errorRow: {
    backgroundColor: Colors.light.errorBackground,
    padding: 12,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: Colors.light.error,
  },
  errorText: {
    color: Colors.light.error,
    fontSize: 14,
    fontWeight: '500',
  },
  actionText: {
    color: Colors.light.error,
    fontSize: 12,
    marginTop: 4,
    opacity: 0.8,
  },
  warningRow: {
    backgroundColor: Colors.light.warningBackground,
    padding: 12,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: Colors.light.warning,
  },
  warningText: {
    color: Colors.light.warning,
    fontSize: 14,
  },
  footer: {
    padding: 20,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
  },
  navigationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  navButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.primary,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
    gap: 8,
  },
  navButtonDisabled: {
    backgroundColor: Colors.light.border,
    opacity: 0.5,
  },
  navButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  progressInfo: {
    alignItems: 'center',
    flex: 1,
  },
  progressMainText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.light.text,
  },
  timeText: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    marginTop: 2,
  },
});

/**
 * PHASE 3 BENEFITS ACHIEVED:
 *
 * ✅ Separation of Concerns:
 *    - Business logic completely separated from UI components
 *    - Each hook handles a specific domain (flow, validation, timing, etc.)
 *    - UI components are now pure presentation layer
 *
 * ✅ Testability:
 *    - Each hook can be tested independently
 *    - Business rules are isolated and unit-testable
 *    - UI logic is minimal and easy to test
 *
 * ✅ Reusability:
 *    - Hooks can be used across different components
 *    - Business logic is portable and component-agnostic
 *    - Animation and haptic patterns are shared
 *
 * ✅ Performance:
 *    - Proper memoization prevents unnecessary re-renders
 *    - Animation values are optimized with native drivers
 *    - Business logic runs only when necessary
 *
 * ✅ Maintainability:
 *    - Changes to business rules only affect hook files
 *    - UI changes don't impact business logic
 *    - Clear separation makes debugging easier
 *
 * ✅ Professional Features:
 *    - Advanced validation with professional scoring
 *    - Multi-phase timer with background tracking
 *    - Progress tracking with milestones
 *    - Rich haptic feedback patterns
 *    - Smooth animations with proper cleanup
 */
