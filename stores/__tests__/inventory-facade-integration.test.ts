/**
 * Comprehensive Integration Test Suite for Inventory Store Facade
 *
 * Tests cross-store coordination, backward compatibility, performance,
 * data consistency, and production-ready scenarios.
 */

import { useInventoryStore, InventoryMigrationHelper } from '../inventory-store-facade';
import { useProductStore } from '../product-store.new';
import { useBrandCategoryStore } from '../brand-category-store.new';
import { useStockStore } from '../stock-store.new';
import { useInventoryAnalyticsStore } from '../inventory-analytics-store';
import { useSyncQueueStore } from '../sync-queue-store';
import { getCurrentSalonId } from '@/lib/supabase';
import type { Product, StockMovement } from '@/types/inventory';
import { renderHook, act } from '@testing-library/react-native';

// Mock all dependencies
jest.mock('../product-store.new');
jest.mock('../brand-category-store.new');
jest.mock('../stock-store.new');
jest.mock('../inventory-analytics-store');
jest.mock('../sync-queue-store');
jest.mock('@/lib/supabase');
jest.mock('@/utils/logger');

// Mock data factories
const createMockProduct = (overrides: Partial<Product> = {}): Product => ({
  id: 'product-123',
  brand: 'Wella',
  line: 'Color Perfect',
  type: 'Tinte',
  shade: '7.1',
  displayName: 'Wella Color Perfect 7.1 Tinte',
  name: 'Wella Color Perfect 7.1',
  category: 'tinte',
  currentStock: 500,
  minStock: 100,
  maxStock: 1000,
  unitType: 'ml',
  unitSize: 60,
  purchasePrice: 15.5,
  costPerUnit: 0.26,
  barcode: '1234567890',
  supplier: 'Beauty Supply Co',
  notes: 'Popular shade',
  colorCode: '7.1',
  isActive: true,
  lastUpdated: '2024-01-01T00:00:00Z',
  _syncStatus: 'synced',
  ...overrides,
});

const createMockMovement = (overrides: Partial<StockMovement> = {}): StockMovement => ({
  id: 'movement-123',
  productId: 'product-123',
  quantity: -25,
  type: 'use',
  date: new Date().toISOString(),
  userId: 'user-123',
  notes: 'Service consumption',
  ...overrides,
});

describe('Inventory Facade Integration Tests', () => {
  let mockStores: any;

  beforeEach(() => {
    // Create comprehensive mock stores
    mockStores = {
      product: {
        products: [
          createMockProduct({ id: 'product-1', brand: 'Wella', currentStock: 500 }),
          createMockProduct({
            id: 'product-2',
            brand: "L'Oreal",
            currentStock: 250,
            minStock: 300,
          }),
          createMockProduct({ id: 'product-3', brand: 'Matrix', currentStock: 0 }),
        ],
        productMappings: [],
        isLoading: false,
        lastSync: '2024-01-01T00:00:00Z',
        loadProducts: jest.fn().mockResolvedValue(undefined),
        addProduct: jest.fn().mockImplementation(async product => {
          const id = `new-product-${Date.now()}`;
          mockStores.product.products.push({ ...product, id });
          return id;
        }),
        updateProduct: jest.fn().mockImplementation(async (id, updates) => {
          const index = mockStores.product.products.findIndex(p => p.id === id);
          if (index >= 0) {
            mockStores.product.products[index] = {
              ...mockStores.product.products[index],
              ...updates,
            };
          }
        }),
        deleteProduct: jest.fn().mockImplementation(async id => {
          mockStores.product.products = mockStores.product.products.filter(p => p.id !== id);
        }),
        getProduct: jest
          .fn()
          .mockImplementation(id => mockStores.product.products.find(p => p.id === id)),
        getProductByNameAndBrand: jest.fn(),
        searchProducts: jest
          .fn()
          .mockImplementation(query =>
            mockStores.product.products.filter(p =>
              p.displayName.toLowerCase().includes(query.toLowerCase())
            )
          ),
        getProductsByCategory: jest.fn(),
        syncWithSupabase: jest.fn().mockResolvedValue(undefined),
        clearAllData: jest.fn(),
        fixMalformedProducts: jest.fn().mockResolvedValue(undefined),
        migrateToUnitsSystem: jest.fn(),
        saveProductMapping: jest.fn().mockResolvedValue(undefined),
        getProductMapping: jest.fn(),
        incrementMappingUsage: jest.fn().mockResolvedValue(undefined),
        loadProductMappings: jest.fn().mockResolvedValue(undefined),
        getProductsMatchingFormula: jest.fn().mockResolvedValue([]),
      },

      brandCategory: {
        activeFilters: {
          stockStatus: 'all',
          categories: [],
          brands: [],
          searchQuery: '',
        },
        sortBy: 'name',
        groupBy: 'none',
        setFilter: jest.fn(),
        setSortBy: jest.fn(),
        setGroupBy: jest.fn(),
        resetFilters: jest.fn(),
        getFilteredAndSortedProducts: jest
          .fn()
          .mockImplementation(products =>
            [...products].sort((a, b) => a.displayName.localeCompare(b.displayName))
          ),
        getGroupedProducts: jest
          .fn()
          .mockImplementation(products => new Map([['Todos los productos', products]])),
        searchProducts: jest
          .fn()
          .mockImplementation((products, query) =>
            products.filter(p => p.displayName.toLowerCase().includes(query.toLowerCase()))
          ),
      },

      stock: {
        movements: [
          createMockMovement({ id: 'movement-1', productId: 'product-1' }),
          createMockMovement({ id: 'movement-2', productId: 'product-2' }),
        ],
        alerts: [],
        lowStockProducts: [
          {
            product_id: 'product-2',
            brand: "L'Oreal",
            name: "L'Oreal Product",
            category: 'tinte',
            stock_ml: 250,
            minimum_stock_ml: 300,
            percentage_remaining: 83,
          },
        ],
        currentStock: {
          'product-1': 500,
          'product-2': 250,
          'product-3': 0,
        },
        isLoadingLowStock: false,
        lastSync: '2024-01-01T00:00:00Z',
        updateStock: jest.fn().mockImplementation(async (productId, quantity) => {
          mockStores.stock.currentStock[productId] =
            (mockStores.stock.currentStock[productId] || 0) + quantity;
          mockStores.stock.movements.unshift(createMockMovement({ productId, quantity }));
        }),
        consumeProducts: jest.fn().mockImplementation(async consumptions => {
          consumptions.forEach(({ productId, quantity }) => {
            mockStores.stock.currentStock[productId] = Math.max(
              0,
              (mockStores.stock.currentStock[productId] || 0) - quantity
            );
          });
        }),
        getStockMovements: jest
          .fn()
          .mockImplementation(productId =>
            mockStores.stock.movements.filter(m => !productId || m.productId === productId)
          ),
        createAlert: jest.fn().mockImplementation((productId, type, message, severity) => {
          mockStores.stock.alerts.push({
            id: `alert-${Date.now()}`,
            productId,
            type,
            message,
            severity,
            isActive: true,
            createdAt: new Date().toISOString(),
          });
        }),
        acknowledgeAlert: jest.fn(),
        getActiveAlerts: jest
          .fn()
          .mockImplementation(() => mockStores.stock.alerts.filter(a => a.isActive)),
        checkLowStock: jest.fn(),
        loadLowStockProducts: jest.fn().mockResolvedValue(undefined),
        getLowStockProducts: jest.fn().mockImplementation(() => mockStores.stock.lowStockProducts),
        setCurrentStock: jest.fn().mockImplementation((productId, stock) => {
          mockStores.stock.currentStock[productId] = stock;
        }),
        getCurrentStock: jest
          .fn()
          .mockImplementation(productId => mockStores.stock.currentStock[productId] || 0),
        loadMovements: jest.fn().mockResolvedValue(undefined),
        syncWithSupabase: jest.fn().mockResolvedValue(undefined),
        generateMockMovements: jest.fn(),
        clearMovements: jest.fn(),
      },

      analytics: {
        currentReport: null,
        isLoadingReport: false,
        lowStockProducts: [],
        isLoadingLowStock: false,
        generateInventoryReport: jest.fn().mockImplementation((products, _movements) => ({
          totalValue: products.reduce((sum, p) => sum + p.currentStock * p.costPerUnit, 0),
          lowStockCount: products.filter(p => p.currentStock <= p.minStock && p.currentStock > 0)
            .length,
          outOfStockCount: products.filter(p => p.currentStock === 0).length,
          overstockCount: products.filter(p => p.maxStock && p.currentStock > p.maxStock).length,
          mostUsedProducts: [],
          leastUsedProducts: [],
          costByCategory: [],
          generatedAt: new Date().toISOString(),
        })),
        loadInventoryReport: jest.fn().mockImplementation((products, movements) => {
          mockStores.analytics.currentReport = mockStores.analytics.generateInventoryReport(
            products,
            movements
          );
        }),
        clearInventoryReport: jest.fn().mockImplementation(() => {
          mockStores.analytics.currentReport = null;
        }),
        loadLowStockProducts: jest.fn().mockResolvedValue(undefined),
        getLowStockProducts: jest
          .fn()
          .mockImplementation(() => mockStores.analytics.lowStockProducts),
        getConsumptionAnalysis: jest.fn(),
        getTotalInventoryValue: jest
          .fn()
          .mockImplementation(products =>
            products.reduce((sum, p) => sum + p.currentStock * p.costPerUnit, 0)
          ),
        getFrequentlyUsedProducts: jest.fn().mockImplementation(() => []),
        getProductsByCategory: jest
          .fn()
          .mockImplementation((products, category) =>
            products.filter(p => p.category === category)
          ),
        clearAnalyticsCache: jest.fn(),
      },

      syncQueue: {
        isOnline: true,
        addToQueue: jest.fn(),
      },
    };

    // Setup store mocks
    (useProductStore.getState as jest.Mock).mockReturnValue(mockStores.product);
    (useBrandCategoryStore.getState as jest.Mock).mockReturnValue(mockStores.brandCategory);
    (useStockStore.getState as jest.Mock).mockReturnValue(mockStores.stock);
    (useInventoryAnalyticsStore.getState as jest.Mock).mockReturnValue(mockStores.analytics);
    (useSyncQueueStore.getState as jest.Mock).mockReturnValue(mockStores.syncQueue);

    // Setup hook mocks
    (useProductStore as jest.Mock).mockReturnValue(mockStores.product);
    (useBrandCategoryStore as jest.Mock).mockReturnValue(mockStores.brandCategory);
    (useStockStore as jest.Mock).mockReturnValue(mockStores.stock);
    (useInventoryAnalyticsStore as jest.Mock).mockReturnValue(mockStores.analytics);

    (getCurrentSalonId as jest.Mock).mockResolvedValue('salon-123');

    jest.clearAllMocks();
  });

  describe('Cross-Store Coordination', () => {
    it('should coordinate product addition across all relevant stores', async () => {
      const { result } = renderHook(() => useInventoryStore());

      const newProduct = {
        brand: 'New Brand',
        displayName: 'New Product',
        category: 'tinte' as const,
        currentStock: 300,
        minStock: 100,
        costPerUnit: 0.35,
        unitType: 'ml' as const,
        unitSize: 50,
        isActive: true,
      };

      await act(async () => {
        await result.current.addProduct(newProduct);
      });

      // Should add to product store
      expect(mockStores.product.addProduct).toHaveBeenCalledWith(newProduct);

      // Should update stock tracking
      expect(mockStores.stock.setCurrentStock).toHaveBeenCalled();

      // Should check for low stock alerts
      expect(mockStores.stock.checkLowStock).toHaveBeenCalled();
    });

    it('should coordinate stock updates between product and stock stores', async () => {
      const { result } = renderHook(() => useInventoryStore());

      await act(async () => {
        await result.current.updateStock('product-1', -50, 'use', 'Client service');
      });

      // Should update stock store
      expect(mockStores.stock.updateStock).toHaveBeenCalledWith(
        'product-1',
        -50,
        'use',
        'Client service',
        undefined
      );

      // Should update product store with new stock level
      expect(mockStores.product.updateProduct).toHaveBeenCalledWith('product-1', {
        currentStock: expect.any(Number),
      });

      // Should trigger low stock check
      expect(mockStores.stock.checkLowStock).toHaveBeenCalled();
    });

    it('should coordinate consumption across multiple products and stores', async () => {
      const { result } = renderHook(() => useInventoryStore());

      const consumptions = [
        { productId: 'product-1', quantity: 30 },
        { productId: 'product-2', quantity: 20 },
      ];

      await act(async () => {
        await result.current.consumeProducts(consumptions, 'service-123', 'Maria Garcia');
      });

      // Should consume in stock store
      expect(mockStores.stock.consumeProducts).toHaveBeenCalledWith(
        consumptions,
        'service-123',
        'Maria Garcia'
      );

      // Should update each product's stock in product store
      expect(mockStores.product.updateProduct).toHaveBeenCalledTimes(2);

      // Should check for alerts after consumption
      expect(mockStores.stock.checkLowStock).toHaveBeenCalled();
    });

    it('should coordinate filtering and sorting across stores', () => {
      const { result } = renderHook(() => useInventoryStore());

      result.current.setFilter('stockStatus', 'low');
      result.current.setSortBy('usage');
      result.current.setGroupBy('brand');

      // Should delegate to brand category store
      expect(mockStores.brandCategory.setFilter).toHaveBeenCalledWith('stockStatus', 'low');
      expect(mockStores.brandCategory.setSortBy).toHaveBeenCalledWith('usage');
      expect(mockStores.brandCategory.setGroupBy).toHaveBeenCalledWith('brand');
    });

    it('should coordinate sync operations across all stores', async () => {
      const { result } = renderHook(() => useInventoryStore());

      await act(async () => {
        await result.current.syncWithSupabase();
      });

      // Should sync product and stock stores in parallel
      expect(mockStores.product.syncWithSupabase).toHaveBeenCalled();
      expect(mockStores.stock.syncWithSupabase).toHaveBeenCalled();
    });
  });

  describe('State Composition and Consistency', () => {
    it('should compose state correctly from all stores', () => {
      const { result } = renderHook(() => useInventoryStore());

      // Should aggregate products from product store
      expect(result.current.products).toEqual(mockStores.product.products);
      expect(result.current.products).toHaveLength(3);

      // Should aggregate movements from stock store
      expect(result.current.movements).toEqual(mockStores.stock.movements);

      // Should aggregate filters from brand category store
      expect(result.current.activeFilters).toEqual(mockStores.brandCategory.activeFilters);

      // Should calculate composite loading state
      expect(result.current.isLoading).toBe(false);

      // Should calculate initialization state
      expect(result.current.isInitialized).toBe(true);
    });

    it('should maintain consistency during concurrent operations', async () => {
      const { result } = renderHook(() => useInventoryStore());

      const operations = [
        result.current.updateStock('product-1', -25, 'use', 'Service 1'),
        result.current.updateStock('product-2', -15, 'use', 'Service 2'),
        result.current.addProduct({
          brand: 'Concurrent Brand',
          category: 'tinte' as const,
          currentStock: 100,
          minStock: 50,
          costPerUnit: 0.25,
          unitType: 'ml' as const,
          unitSize: 60,
          isActive: true,
        }),
      ];

      await act(async () => {
        await Promise.allSettled(operations);
      });

      // All operations should have completed
      expect(mockStores.stock.updateStock).toHaveBeenCalledTimes(2);
      expect(mockStores.product.addProduct).toHaveBeenCalledTimes(1);
      expect(mockStores.product.updateProduct).toHaveBeenCalled();
    });

    it('should handle store inconsistencies gracefully', () => {
      // Create inconsistent state
      mockStores.product.products = [createMockProduct({ id: 'product-1' })];
      mockStores.stock.movements = [
        createMockMovement({ productId: 'product-1' }),
        createMockMovement({ productId: 'product-nonexistent' }),
      ];

      const { result } = renderHook(() => useInventoryStore());

      // Should not crash and should provide access to available data
      expect(result.current.products).toHaveLength(1);
      expect(result.current.movements).toHaveLength(2);
    });
  });

  describe('Advanced Analytics Coordination', () => {
    it('should coordinate inventory report generation', () => {
      const { result } = renderHook(() => useInventoryStore());

      const report = result.current.generateInventoryReport();

      expect(mockStores.analytics.generateInventoryReport).toHaveBeenCalledWith(
        mockStores.product.products,
        mockStores.stock.movements
      );

      expect(report).toBeTruthy();
      expect(report.totalValue).toBeGreaterThan(0);
    });

    it('should coordinate consumption analysis', () => {
      const { result } = renderHook(() => useInventoryStore());

      result.current.getConsumptionAnalysis('product-1', 'daily');

      expect(mockStores.analytics.getConsumptionAnalysis).toHaveBeenCalledWith(
        'product-1',
        'daily',
        mockStores.stock.movements,
        expect.any(Function) // getProduct function
      );
    });

    it('should coordinate frequently used products analysis', () => {
      const { result } = renderHook(() => useInventoryStore());

      result.current.getFrequentlyUsedProducts(5);

      expect(mockStores.analytics.getFrequentlyUsedProducts).toHaveBeenCalledWith(
        mockStores.product.products,
        mockStores.stock.movements,
        5
      );
    });
  });

  describe('Low Stock Intelligence Coordination', () => {
    it('should coordinate low stock detection across stores', () => {
      const { result } = renderHook(() => useInventoryStore());

      result.current.checkLowStock();

      // Should create alerts for low stock products
      expect(mockStores.stock.createAlert).toHaveBeenCalled();

      // Should load low stock products from analytics
      expect(mockStores.stock.loadLowStockProducts).toHaveBeenCalled();
    });

    it('should coordinate low stock products retrieval with fallback', () => {
      const { result } = renderHook(() => useInventoryStore());

      // Test when analytics has data
      mockStores.analytics.lowStockProducts = [{ product_id: 'analytics-product' }];
      mockStores.stock.lowStockProducts = [{ product_id: 'stock-product' }];

      let lowStock = result.current.getLowStockProducts();
      expect(lowStock[0].product_id).toBe('analytics-product');

      // Test fallback to stock store when analytics is empty
      mockStores.analytics.lowStockProducts = [];

      lowStock = result.current.getLowStockProducts();
      expect(lowStock[0].product_id).toBe('stock-product');
    });
  });

  describe('Data Cleanup and Maintenance', () => {
    it('should coordinate data clearing across all stores', () => {
      const { result } = renderHook(() => useInventoryStore());

      result.current.clearAllData();

      expect(mockStores.product.clearAllData).toHaveBeenCalled();
      expect(mockStores.stock.clearMovements).toHaveBeenCalled();
      expect(mockStores.analytics.clearInventoryReport).toHaveBeenCalled();
      expect(mockStores.analytics.clearAnalyticsCache).toHaveBeenCalled();
      expect(mockStores.brandCategory.resetFilters).toHaveBeenCalled();
    });

    it('should coordinate product cleanup operations', async () => {
      const { result } = renderHook(() => useInventoryStore());

      await act(async () => {
        await result.current.fixMalformedProducts();
      });

      expect(mockStores.product.fixMalformedProducts).toHaveBeenCalled();
    });

    it('should coordinate migration operations', () => {
      const { result } = renderHook(() => useInventoryStore());

      result.current.migrateToUnitsSystem();

      expect(mockStores.product.migrateToUnitsSystem).toHaveBeenCalled();
    });
  });

  describe('Performance Optimization', () => {
    it('should handle large datasets efficiently through facade', () => {
      // Create large dataset
      const largeProductSet = Array.from({ length: 1000 }, (_, i) =>
        createMockProduct({ id: `product-${i}`, brand: `Brand-${i % 10}` })
      );
      mockStores.product.products = largeProductSet;

      const startTime = performance.now();
      const { result } = renderHook(() => useInventoryStore());

      // Access products through facade
      const products = result.current.products;
      const filtered = result.current.getFilteredAndSortedProducts();
      const report = result.current.generateInventoryReport();

      const endTime = performance.now();

      expect(products).toHaveLength(1000);
      expect(filtered).toBeDefined();
      expect(report).toBeTruthy();
      expect(endTime - startTime).toBeLessThan(100); // Should be reasonably fast
    });

    it('should minimize redundant calls through facade optimization', async () => {
      const { result } = renderHook(() => useInventoryStore());

      // Multiple calls that should be optimized
      await act(async () => {
        await result.current.updateStock('product-1', -10, 'use', 'Test 1');
        await result.current.updateStock('product-1', -10, 'use', 'Test 2');
      });

      // Should have made individual calls (facade doesn't batch by default)
      expect(mockStores.stock.updateStock).toHaveBeenCalledTimes(2);
      expect(mockStores.product.updateProduct).toHaveBeenCalledTimes(2);
    });
  });

  describe('Migration Helper Functionality', () => {
    describe('validateConsistency', () => {
      it('should detect consistent state between facade and stores', () => {
        const validation = InventoryMigrationHelper.validateConsistency();

        expect(validation.isConsistent).toBe(true);
        expect(validation.inconsistencies).toHaveLength(0);
      });

      it('should detect inconsistencies between facade and stores', () => {
        // Create inconsistency
        mockStores.product.products = [createMockProduct()];
        mockStores.stock.movements = [createMockMovement(), createMockMovement()];

        // Mock the facade getState to return different data
        const originalGetState = useInventoryStore.getState;
        (useInventoryStore.getState as jest.Mock).mockReturnValue({
          products: [],
          movements: mockStores.stock.movements,
          activeFilters: mockStores.brandCategory.activeFilters,
        });

        const validation = InventoryMigrationHelper.validateConsistency();

        expect(validation.isConsistent).toBe(false);
        expect(validation.inconsistencies.length).toBeGreaterThan(0);

        // Restore
        useInventoryStore.getState = originalGetState;
      });
    });

    describe('benchmarkAccess', () => {
      it('should benchmark facade vs direct access performance', () => {
        const benchmark = InventoryMigrationHelper.benchmarkAccess();

        expect(benchmark.facadeTime).toBeGreaterThan(0);
        expect(benchmark.directTime).toBeGreaterThan(0);
        expect(typeof benchmark.overhead).toBe('number');
        expect(benchmark.overhead).toBeGreaterThanOrEqual(0);
      });
    });

    describe('createTransitionHook', () => {
      it('should create transition hooks for gradual migration', () => {
        const TransitionHook = InventoryMigrationHelper.createTransitionHook('useProductStore');

        expect(typeof TransitionHook).toBe('function');

        const { result } = renderHook(TransitionHook);

        expect(result.current.facade).toBeDefined();
        expect(result.current.direct).toBeDefined();
        expect(result.current.__isTransitioning).toBe(true);
      });
    });
  });

  describe('Error Handling and Resilience', () => {
    it('should handle individual store failures gracefully', async () => {
      const { result } = renderHook(() => useInventoryStore());

      // Mock product store failure
      mockStores.product.addProduct.mockRejectedValue(new Error('Product store failed'));

      await act(async () => {
        try {
          await result.current.addProduct({
            brand: 'Test Brand',
            category: 'tinte' as const,
            currentStock: 100,
            minStock: 50,
            costPerUnit: 0.25,
            unitType: 'ml' as const,
            unitSize: 60,
            isActive: true,
          });
        } catch (error) {
          expect(error).toBeInstanceOf(Error);
        }
      });

      // Should still attempt stock operations even if product fails
      expect(mockStores.product.addProduct).toHaveBeenCalled();
    });

    it('should handle network connectivity changes', async () => {
      const { result } = renderHook(() => useInventoryStore());

      // Simulate offline state
      mockStores.syncQueue.isOnline = false;

      await act(async () => {
        await result.current.updateStock('product-1', -25, 'use', 'Offline test');
      });

      // Should still process locally
      expect(mockStores.stock.updateStock).toHaveBeenCalled();
    });
  });

  describe('Backward Compatibility', () => {
    it('should maintain exact API compatibility with original store', () => {
      const { result } = renderHook(() => useInventoryStore());
      const facade = result.current;

      // Test all original API methods exist and have correct signatures
      expect(typeof facade.loadProducts).toBe('function');
      expect(typeof facade.addProduct).toBe('function');
      expect(typeof facade.updateProduct).toBe('function');
      expect(typeof facade.deleteProduct).toBe('function');
      expect(typeof facade.getProduct).toBe('function');
      expect(typeof facade.searchProducts).toBe('function');
      expect(typeof facade.updateStock).toBe('function');
      expect(typeof facade.consumeProducts).toBe('function');
      expect(typeof facade.createAlert).toBe('function');
      expect(typeof facade.generateInventoryReport).toBe('function');
      expect(typeof facade.getTotalInventoryValue).toBe('function');
      expect(typeof facade.setFilter).toBe('function');
      expect(typeof facade.getFilteredAndSortedProducts).toBe('function');

      // Test state properties
      expect(Array.isArray(facade.products)).toBe(true);
      expect(Array.isArray(facade.movements)).toBe(true);
      expect(Array.isArray(facade.alerts)).toBe(true);
      expect(typeof facade.isLoading).toBe('boolean');
      expect(typeof facade.isInitialized).toBe('boolean');
      expect(typeof facade.activeFilters).toBe('object');
    });

    it('should handle legacy data formats', async () => {
      const { result } = renderHook(() => useInventoryStore());

      // Test with legacy product format
      const legacyProduct = {
        name: 'Legacy Product Name',
        brand: 'Legacy Brand',
        category: 'tinte' as const,
        stock: 100, // Old field name
        minimum: 50, // Old field name
        price: 15.5, // Old field name
        active: true, // Old field name
      };

      // Should handle legacy format gracefully
      await act(async () => {
        await expect(result.current.addProduct(legacyProduct as any)).resolves.toBeDefined();
      });
    });
  });

  describe('Real-world Scenarios', () => {
    it('should handle complete service workflow', async () => {
      const { result } = renderHook(() => useInventoryStore());

      // Simulate complete service workflow
      const clientName = 'Maria Garcia';
      const serviceId = 'service-123';

      // 1. Load initial data
      await act(async () => {
        await result.current.loadProducts();
      });

      // 2. Check stock availability
      const product1 = result.current.getProduct('product-1');
      expect(product1).toBeTruthy();
      expect(product1!.currentStock).toBeGreaterThan(0);

      // 3. Consume products for service
      const consumptions = [
        { productId: 'product-1', quantity: 30 },
        { productId: 'product-2', quantity: 20 },
      ];

      await act(async () => {
        await result.current.consumeProducts(consumptions, serviceId, clientName);
      });

      // 4. Generate post-service report
      const report = result.current.generateInventoryReport();
      expect(report).toBeTruthy();
      expect(report.totalValue).toBeGreaterThan(0);

      // 5. Check for low stock alerts
      const alerts = result.current.getActiveAlerts();
      expect(Array.isArray(alerts)).toBe(true);

      // Verify all operations completed
      expect(mockStores.product.loadProducts).toHaveBeenCalled();
      expect(mockStores.stock.consumeProducts).toHaveBeenCalled();
      expect(mockStores.analytics.generateInventoryReport).toHaveBeenCalled();
    });

    it('should handle inventory management workflow', async () => {
      const { result } = renderHook(() => useInventoryStore());

      // 1. Add new product
      const newProduct = {
        brand: 'Schwarzkopf',
        line: 'Igora',
        type: 'Oxidante',
        shade: '30 Vol',
        displayName: 'Schwarzkopf Igora 30 Vol',
        category: 'oxidante' as const,
        currentStock: 500,
        minStock: 100,
        costPerUnit: 0.2,
        unitType: 'ml' as const,
        unitSize: 1000,
        isActive: true,
      };

      await act(async () => {
        await result.current.addProduct(newProduct);
      });

      // 2. Update stock levels
      await act(async () => {
        await result.current.updateStock('product-1', 200, 'purchase', 'Inventory restock');
      });

      // 3. Apply filters and search
      result.current.setFilter('categories', ['oxidante']);
      result.current.setSortBy('stock');

      const filtered = result.current.getFilteredAndSortedProducts();
      expect(Array.isArray(filtered)).toBe(true);

      // 4. Generate analytics
      result.current.loadInventoryReport();
      const totalValue = result.current.getTotalInventoryValue();
      expect(typeof totalValue).toBe('number');

      // All operations should coordinate properly
      expect(mockStores.product.addProduct).toHaveBeenCalled();
      expect(mockStores.stock.updateStock).toHaveBeenCalled();
      expect(mockStores.brandCategory.setFilter).toHaveBeenCalled();
      expect(mockStores.analytics.loadInventoryReport).toHaveBeenCalled();
    });

    it('should handle bulk operations efficiently', async () => {
      const { result } = renderHook(() => useInventoryStore());

      const bulkConsumptions = Array.from({ length: 50 }, (_, i) => ({
        productId: i < 25 ? 'product-1' : 'product-2',
        quantity: 10 + (i % 5),
      }));

      const startTime = performance.now();

      await act(async () => {
        await result.current.consumeProducts(bulkConsumptions, 'bulk-service', 'Bulk Client');
      });

      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(1000); // Should handle bulk efficiently
      expect(mockStores.stock.consumeProducts).toHaveBeenCalledWith(
        bulkConsumptions,
        'bulk-service',
        'Bulk Client'
      );
    });
  });
});
