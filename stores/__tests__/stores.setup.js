/**
 * Setup file specifically for inventory stores testing
 *
 * Provides enhanced mocking, performance monitoring, and utilities
 * specific to the modular inventory architecture.
 */

/* eslint-env jest, node */
/* global performance, setTimeout, setImmediate */

// Import base setup
import '../../jest.setup.js';

// Enhanced faker configuration for consistent test data
import { faker } from '@faker-js/faker';
faker.seed(12345); // Consistent seed for reproducible tests

// Performance monitoring setup
let performanceStartTime;
let performanceMetrics = {};

// Mock enhanced Supabase client specifically for stores
const createEnhancedSupabaseMock = () => ({
  from: jest.fn(() => ({
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    upsert: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    single: jest.fn().mockResolvedValue({ data: null, error: null }),
  })),
  rpc: jest.fn().mockResolvedValue({ data: null, error: null }),
  auth: {
    getUser: jest.fn().mockResolvedValue({
      data: { user: { id: 'test-user-123' } },
      error: null,
    }),
    getSession: jest.fn().mockResolvedValue({
      data: { session: { user: { id: 'test-user-123' } } },
      error: null,
    }),
  },
});

// Mock Supabase for stores testing
jest.mock('@/lib/supabase', () => ({
  supabase: createEnhancedSupabaseMock(),
  getCurrentSalonId: jest.fn().mockResolvedValue('test-salon-123'),
}));

// Mock sync queue store
jest.mock('../sync-queue-store', () => ({
  useSyncQueueStore: {
    getState: jest.fn(() => ({
      isOnline: true,
      addToQueue: jest.fn(),
      processQueue: jest.fn(),
      clearQueue: jest.fn(),
    })),
  },
  generateLocalId: jest.fn(
    prefix => `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  ),
  isLocalId: jest.fn(id => typeof id === 'string' && id.includes('_')),
}));

// Mock logger with performance tracking
const createMockLogger = () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
  startTimer: jest.fn(operation => {
    performanceStartTime = performance.now();
    performanceMetrics[operation] = { start: performanceStartTime };
  }),
  endTimer: jest.fn((operation, metadata) => {
    const endTime = performance.now();
    if (performanceMetrics[operation]) {
      performanceMetrics[operation].end = endTime;
      performanceMetrics[operation].duration = endTime - performanceMetrics[operation].start;
      performanceMetrics[operation].metadata = metadata;
    }
  }),
  withContext: jest.fn(() => createMockLogger()),
});

jest.mock('@/utils/logger', () => ({
  logger: createMockLogger(),
}));

// Mock AsyncStorage with enhanced debugging
const mockAsyncStorage = {
  getItem: jest.fn().mockImplementation(async key => {
    const stored = global.__ASYNC_STORAGE__ || {};
    return stored[key] || null;
  }),
  setItem: jest.fn().mockImplementation(async (key, value) => {
    global.__ASYNC_STORAGE__ = global.__ASYNC_STORAGE__ || {};
    global.__ASYNC_STORAGE__[key] = value;
  }),
  removeItem: jest.fn().mockImplementation(async key => {
    global.__ASYNC_STORAGE__ = global.__ASYNC_STORAGE__ || {};
    delete global.__ASYNC_STORAGE__[key];
  }),
  clear: jest.fn().mockImplementation(async () => {
    global.__ASYNC_STORAGE__ = {};
  }),
};

jest.mock('@react-native-async-storage/async-storage', () => mockAsyncStorage);

// Mock services that stores might depend on
jest.mock('@/services/inventoryConsumptionService', () => ({
  InventoryConsumptionService: {
    findMatchingProducts: jest.fn().mockResolvedValue([
      {
        product: {
          id: 'matched-product-123',
          name: 'Matched Product',
          brand: 'Test Brand',
          category: 'tinte',
          currentStock: 100,
          minStock: 50,
          costPerUnit: 0.25,
          isActive: true,
        },
        matchScore: 85,
        confidence: 0.85,
      },
    ]),
    calculateConsumption: jest.fn().mockReturnValue(25),
  },
}));

// Mock product mappings constants
jest.mock('@/constants/product-mappings', () => ({
  mapCategoryToType: jest.fn().mockImplementation(category => {
    const mapping = {
      tinte: 'color',
      oxidante: 'developer',
      decolorante: 'bleach',
      tratamiento: 'treatment',
      matizador: 'toner',
      aditivo: 'additive',
      'pre-pigmentacion': 'pre_pigment',
      otro: 'other',
    };
    return mapping[category] || 'other';
  }),
}));

// Mock default products data
jest.mock('@/data/default-products', () => ({
  defaultProducts: [
    {
      brand: 'Wella',
      line: 'Color Perfect',
      type: 'Tinte',
      shade: '7.1',
      category: 'tinte',
      currentStock: 500,
      minStock: 100,
      costPerUnit: 0.3,
      unitType: 'ml',
      unitSize: 60,
      isActive: true,
    },
    {
      brand: 'Schwarzkopf',
      line: 'Igora',
      type: 'Oxidante',
      shade: '20 Vol',
      category: 'oxidante',
      currentStock: 1000,
      minStock: 200,
      costPerUnit: 0.15,
      unitType: 'ml',
      unitSize: 1000,
      isActive: true,
    },
  ],
}));

// Mock store creators - defined globally so they can be reused
const createMockProductStore = () => ({
  products: [],
  productMappings: [],
  isLoading: false,
  lastSync: null,
  loadProducts: jest.fn().mockResolvedValue(undefined),
  addProduct: jest.fn().mockResolvedValue('test-id-123'),
  updateProduct: jest.fn().mockResolvedValue(undefined),
  deleteProduct: jest.fn().mockResolvedValue(undefined),
  getProduct: jest.fn().mockReturnValue(undefined),
  getProductByNameAndBrand: jest.fn().mockReturnValue(undefined),
  searchProducts: jest.fn().mockReturnValue([]),
  getProductsByCategory: jest.fn().mockReturnValue([]),
  clearAllData: jest.fn(),
  syncWithSupabase: jest.fn().mockResolvedValue(undefined),
  migrateToUnitsSystem: jest.fn(),
  fixMalformedProducts: jest.fn().mockResolvedValue(undefined),
  saveProductMapping: jest.fn().mockResolvedValue(undefined),
  getProductMapping: jest.fn().mockReturnValue(undefined),
  incrementMappingUsage: jest.fn().mockResolvedValue(undefined),
  loadProductMappings: jest.fn().mockResolvedValue(undefined),
  getProductsMatchingFormula: jest.fn().mockResolvedValue([]),
  generateDisplayName: jest.fn().mockReturnValue('Test Product'),
  parseProductName: jest.fn().mockReturnValue({}),
});

const createMockStockStore = () => ({
  movements: [],
  alerts: [],
  lowStockProducts: [],
  isLoadingLowStock: false,
  lastSync: null,
  currentStock: {},
  updateStock: jest.fn().mockResolvedValue(undefined),
  consumeProducts: jest.fn().mockResolvedValue(undefined),
  loadMovements: jest.fn().mockResolvedValue(undefined),
  getStockMovements: jest.fn().mockReturnValue([]),
  createAlert: jest.fn(),
  acknowledgeAlert: jest.fn(),
  getActiveAlerts: jest.fn().mockReturnValue([]),
  setCurrentStock: jest.fn(),
  clearMovements: jest.fn(),
  generateMockMovements: jest.fn(),
  loadLowStockProducts: jest.fn().mockResolvedValue(undefined),
  getLowStockProducts: jest.fn().mockReturnValue([]),
  syncWithSupabase: jest.fn().mockResolvedValue(undefined),
});

const createMockBrandCategoryStore = () => ({
  activeFilters: {
    categories: [],
    brands: [],
    stockStatus: 'all',
    searchQuery: '',
  },
  sortBy: 'name',
  groupBy: 'none',
  setFilter: jest.fn(),
  setSortBy: jest.fn(),
  setGroupBy: jest.fn(),
  resetFilters: jest.fn(),
  searchProducts: jest.fn().mockReturnValue([]),
  getFilteredAndSortedProducts: jest.fn().mockReturnValue([]),
  getGroupedProducts: jest.fn().mockReturnValue(new Map()),
});

const createMockAnalyticsStore = () => ({
  isLoadingReport: false,
  inventoryReport: null,
  analyticsCache: {},
  loadLowStockProducts: jest.fn().mockResolvedValue(undefined),
  getLowStockProducts: jest.fn().mockReturnValue([]),
  getConsumptionAnalysis: jest.fn().mockReturnValue(null),
  generateInventoryReport: jest.fn().mockReturnValue({}),
  loadInventoryReport: jest.fn(),
  clearInventoryReport: jest.fn(),
  getProductsByCategory: jest.fn().mockReturnValue([]),
  getTotalInventoryValue: jest.fn().mockReturnValue(0),
  getFrequentlyUsedProducts: jest.fn().mockReturnValue([]),
  clearAnalyticsCache: jest.fn(),
});

// Mock the actual store hooks
jest.mock('../product-store.new', () => ({
  useProductStore: {
    getState: jest.fn(() => createMockProductStore()),
    setState: jest.fn(),
    subscribe: jest.fn(),
  },
}));

jest.mock('../brand-category-store.new', () => ({
  useBrandCategoryStore: {
    getState: jest.fn(() => createMockBrandCategoryStore()),
    setState: jest.fn(),
    subscribe: jest.fn(),
  },
}));

jest.mock('../stock-store.new', () => ({
  useStockStore: {
    getState: jest.fn(() => createMockStockStore()),
    setState: jest.fn(),
    subscribe: jest.fn(),
  },
}));

jest.mock('../inventory-analytics-store', () => ({
  useInventoryAnalyticsStore: {
    getState: jest.fn(() => createMockAnalyticsStore()),
    setState: jest.fn(),
    subscribe: jest.fn(),
  },
}));

// Enhanced console filtering for cleaner test output
const originalConsole = { ...console };

beforeEach(() => {
  // Reset performance metrics
  performanceMetrics = {};
  performanceStartTime = null;

  // Reset AsyncStorage
  global.__ASYNC_STORAGE__ = {};

  // Clean console output for tests
  console.warn = jest.fn(message => {
    // Only show warnings that are relevant to testing
    if (
      typeof message === 'string' &&
      (message.includes('performance') ||
        message.includes('memory') ||
        message.includes('store') ||
        message.includes('test'))
    ) {
      originalConsole.warn(message);
    }
  });

  console.error = jest.fn((message, ...args) => {
    // Always show errors but filter out known test noise
    if (
      typeof message === 'string' &&
      (message.includes('Warning: ReactDOM.render') ||
        message.includes('Warning: componentWillMount') ||
        message.includes('act() warnings'))
    ) {
      return;
    }
    originalConsole.error(message, ...args);
  });
});

afterEach(() => {
  // Restore console
  console.warn = originalConsole.warn;
  console.error = originalConsole.error;

  // Clear all mocks
  jest.clearAllMocks();

  // Report performance metrics if any were recorded
  if (Object.keys(performanceMetrics).length > 0) {
    const slowOperations = Object.entries(performanceMetrics)
      .filter(([, metrics]) => metrics.duration > 100) // More than 100ms
      .map(([operation, metrics]) => `${operation}: ${metrics.duration.toFixed(2)}ms`);

    if (slowOperations.length > 0) {
      console.info('Slow operations detected:', slowOperations.join(', '));
    }
  }

  // Memory cleanup
  if (global.gc) {
    global.gc();
  }
});

// Expose mock creators globally for reuse
global.createMockProductStore = createMockProductStore;
global.createMockStockStore = createMockStockStore;
global.createMockBrandCategoryStore = createMockBrandCategoryStore;
global.createMockAnalyticsStore = createMockAnalyticsStore;

// Global test helpers for stores
global.testHelpers = {
  // Reset all stores to clean state
  resetAllStores: () => {
    try {
      // Reset mock stores
      const { useProductStore } = require('../product-store.new');
      const { useBrandCategoryStore } = require('../brand-category-store.new');
      const { useStockStore } = require('../stock-store.new');
      const { useInventoryAnalyticsStore } = require('../inventory-analytics-store');

      // Reset each store's getState to return fresh mock data
      useProductStore.getState.mockReturnValue(createMockProductStore());
      useBrandCategoryStore.getState.mockReturnValue(createMockBrandCategoryStore());
      useStockStore.getState.mockReturnValue(createMockStockStore());
      useInventoryAnalyticsStore.getState.mockReturnValue(createMockAnalyticsStore());

      // Clear AsyncStorage
      global.__ASYNC_STORAGE__ = {};
    } catch (error) {
      console.warn('Could not reset stores:', error.message);
    }
  },

  // Wait for all pending async operations
  waitForAsync: async () => {
    await new Promise(resolve => setTimeout(resolve, 0));
    await new Promise(resolve => setImmediate(resolve));
  },

  // Performance assertion helper
  assertPerformance: (actualMs, maxMs, operation) => {
    if (actualMs > maxMs) {
      throw new Error(
        `Performance assertion failed: ${operation} took ${actualMs.toFixed(2)}ms, expected < ${maxMs}ms`
      );
    }
  },

  // Memory usage helper
  getMemoryUsage: () => {
    if (performance.memory) {
      return {
        used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
        limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024),
      };
    }
    return { used: 0, total: 0, limit: 0 };
  },

  // Simulate network conditions
  simulateNetworkCondition: (condition = 'online') => {
    const mockSyncQueue = require('../sync-queue-store');

    switch (condition) {
      case 'offline':
        mockSyncQueue.useSyncQueueStore.getState.mockReturnValue({
          isOnline: false,
          addToQueue: jest.fn(),
          processQueue: jest.fn(),
          clearQueue: jest.fn(),
        });
        break;
      case 'slow': {
        // Simulate slow network by adding delays to Supabase mocks

        const { supabase } = require('@/lib/supabase');
        const originalFrom = supabase.from;
        supabase.from = jest.fn(() => {
          const chain = originalFrom();
          const originalSingle = chain.single;
          chain.single = jest.fn(async () => {
            await new Promise(resolve => setTimeout(resolve, 1000)); // 1s delay
            return originalSingle();
          });
          return chain;
        });
        break;
      }
      case 'online':
      default:
        mockSyncQueue.useSyncQueueStore.getState.mockReturnValue({
          isOnline: true,
          addToQueue: jest.fn(),
          processQueue: jest.fn(),
          clearQueue: jest.fn(),
        });
        break;
    }
  },
};

// Export performance metrics for test reporters
global.getPerformanceMetrics = () => ({ ...performanceMetrics });

// Setup performance monitoring
if (typeof performance !== 'undefined' && performance.mark) {
  beforeEach(() => {
    performance.mark('test-start');
  });

  afterEach(() => {
    performance.mark('test-end');
    try {
      performance.measure('test-duration', 'test-start', 'test-end');
    } catch {
      // Ignore measurement errors
    }
  });
}

// Custom error handling for better debugging
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  // Don't exit the process during tests
});

// Test environment validation
if (typeof global === 'undefined') {
  throw new Error('Test environment not properly configured - global object not available');
}

if (typeof jest === 'undefined') {
  throw new Error('Test environment not properly configured - jest not available');
}

console.info('✅ Inventory stores test setup completed');

// Export setup completion flag
global.__STORES_SETUP_COMPLETE__ = true;
