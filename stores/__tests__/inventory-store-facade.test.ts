/**
 * Tests for the Inventory Store Facade
 *
 * This test suite verifies that the facade maintains 100% API compatibility
 * with the original inventory store while correctly delegating to modular stores.
 */

import { renderHook, act } from '@testing-library/react-native';
import { useInventoryStore, InventoryMigrationHelper } from '../inventory-store-facade';
import { useProductStore } from '../product-store.new';
import { useBrandCategoryStore } from '../brand-category-store.new';
import { useStockStore } from '../stock-store.new';
import { useInventoryAnalyticsStore } from '../inventory-analytics-store';

// Mock the individual stores
jest.mock('../product-store.new');
jest.mock('../brand-category-store.new');
jest.mock('../stock-store.new');
jest.mock('../inventory-analytics-store');
jest.mock('../sync-queue-store');
jest.mock('@/lib/supabase');

const mockProductStore = {
  products: [
    {
      id: 'product-1',
      name: 'Test Product',
      displayName: 'Test Product',
      brand: 'Test Brand',
      category: 'tinte',
      currentStock: 100,
      minStock: 10,
      maxStock: 200,
      costPerUnit: 5,
      isActive: true,
    },
  ],
  productMappings: [],
  isLoading: false,
  lastSync: '2024-01-01T00:00:00Z',
  loadProducts: jest.fn().mockResolvedValue(undefined),
  addProduct: jest.fn().mockResolvedValue('new-id'),
  updateProduct: jest.fn().mockResolvedValue(undefined),
  deleteProduct: jest.fn().mockResolvedValue(undefined),
  getProduct: jest.fn().mockImplementation(id => mockProductStore.products.find(p => p.id === id)),
  getProductByNameAndBrand: jest.fn(),
  searchProducts: jest.fn(),
  syncWithSupabase: jest.fn().mockResolvedValue(undefined),
  clearAllData: jest.fn(),
};

const mockBrandCategoryStore = {
  activeFilters: {
    stockStatus: 'all' as const,
    categories: [],
    brands: [],
    searchQuery: '',
  },
  sortBy: 'name' as const,
  groupBy: 'none' as const,
  setFilter: jest.fn(),
  setSortBy: jest.fn(),
  setGroupBy: jest.fn(),
  resetFilters: jest.fn(),
  getFilteredAndSortedProducts: jest.fn(),
  getGroupedProducts: jest.fn(),
  searchProducts: jest.fn(),
};

const mockStockStore = {
  movements: [
    {
      id: 'movement-1',
      productId: 'product-1',
      quantity: -10,
      type: 'use' as const,
      date: '2024-01-01T00:00:00Z',
      userId: 'user-1',
      notes: 'Test consumption',
    },
  ],
  alerts: [],
  lowStockProducts: [],
  currentStock: { 'product-1': 100 },
  isLoadingLowStock: false,
  lastSync: '2024-01-01T00:00:00Z',
  updateStock: jest.fn().mockResolvedValue(undefined),
  consumeProducts: jest.fn().mockResolvedValue(undefined),
  getStockMovements: jest.fn().mockReturnValue([]),
  createAlert: jest.fn(),
  acknowledgeAlert: jest.fn(),
  getActiveAlerts: jest.fn().mockReturnValue([]),
  checkLowStock: jest.fn(),
  loadLowStockProducts: jest.fn().mockResolvedValue(undefined),
  getLowStockProducts: jest.fn().mockReturnValue([]),
  setCurrentStock: jest.fn(),
  getCurrentStock: jest.fn(),
  loadMovements: jest.fn().mockResolvedValue(undefined),
  syncWithSupabase: jest.fn().mockResolvedValue(undefined),
  generateMockMovements: jest.fn(),
  clearMovements: jest.fn(),
  clearAllData: jest.fn(),
};

const mockAnalyticsStore = {
  currentReport: null,
  isLoadingReport: false,
  lowStockProducts: [],
  isLoadingLowStock: false,
  loadLowStockProducts: jest.fn().mockResolvedValue(undefined),
  getLowStockProducts: jest.fn().mockReturnValue([]),
  getConsumptionAnalysis: jest.fn(),
  generateInventoryReport: jest.fn(),
  loadInventoryReport: jest.fn(),
  clearInventoryReport: jest.fn(),
  getProductsByCategory: jest.fn(),
  getTotalInventoryValue: jest.fn(),
  getFrequentlyUsedProducts: jest.fn().mockReturnValue([]),
  getStockAlerts: jest.fn().mockReturnValue([]),
  clearAnalyticsCache: jest.fn(),
};

describe('InventoryStoreFacade', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Mock store implementations
    (useProductStore as jest.Mock).mockImplementation(selector => {
      if (typeof selector === 'function') {
        return selector(mockProductStore);
      }
      return mockProductStore;
    });

    (useProductStore.getState as jest.Mock) = jest.fn(() => mockProductStore);

    (useBrandCategoryStore as jest.Mock).mockImplementation(selector => {
      if (typeof selector === 'function') {
        return selector(mockBrandCategoryStore);
      }
      return mockBrandCategoryStore;
    });

    (useBrandCategoryStore.getState as jest.Mock) = jest.fn(() => mockBrandCategoryStore);

    (useStockStore as jest.Mock).mockImplementation(selector => {
      if (typeof selector === 'function') {
        return selector(mockStockStore);
      }
      return mockStockStore;
    });

    (useStockStore.getState as jest.Mock) = jest.fn(() => mockStockStore);

    (useInventoryAnalyticsStore as jest.Mock).mockImplementation(selector => {
      if (typeof selector === 'function') {
        return selector(mockAnalyticsStore);
      }
      return mockAnalyticsStore;
    });

    (useInventoryAnalyticsStore.getState as jest.Mock) = jest.fn(() => mockAnalyticsStore);

    // Sync facade state with mock data after setting up mocks
    act(() => {
      useInventoryStore.getState()._syncState();
    });
  });

  describe('State Composition', () => {
    it('should aggregate products from ProductStore', () => {
      const state = useInventoryStore.getState();
      expect(state.products).toEqual(mockProductStore.products);
    });

    it('should aggregate movements from StockStore', () => {
      const state = useInventoryStore.getState();
      expect(state.movements).toEqual(mockStockStore.movements);
    });

    it('should aggregate alerts from StockStore', () => {
      const state = useInventoryStore.getState();
      expect(state.alerts).toEqual(mockStockStore.alerts);
    });

    it('should aggregate filters from BrandCategoryStore', () => {
      const state = useInventoryStore.getState();
      expect(state.activeFilters).toEqual(mockBrandCategoryStore.activeFilters);
      expect(state.sortBy).toEqual(mockBrandCategoryStore.sortBy);
      expect(state.groupBy).toEqual(mockBrandCategoryStore.groupBy);
    });

    it('should calculate isLoading from all stores', () => {
      const state = useInventoryStore.getState();
      expect(state.isLoading).toBe(false);

      // Test when any store is loading
      act(() => {
        mockProductStore.isLoading = true;
        useInventoryStore.getState()._syncState();
      });

      const state2 = useInventoryStore.getState();
      expect(state2.isLoading).toBe(true);
    });

    it('should calculate isInitialized based on products', () => {
      const state = useInventoryStore.getState();
      expect(state.isInitialized).toBe(true);

      // Test when no products
      act(() => {
        mockProductStore.products = [];
        useInventoryStore.getState()._syncState();
      });

      const state2 = useInventoryStore.getState();
      expect(state2.isInitialized).toBe(false);
    });
  });

  describe('Product Actions Delegation', () => {
    it('should delegate loadProducts to ProductStore and StockStore', async () => {
      const { result } = renderHook(() => useInventoryStore());

      await act(async () => {
        await result.current.loadProducts();
      });

      expect(mockProductStore.loadProducts).toHaveBeenCalled();
      expect(mockStockStore.loadMovements).toHaveBeenCalled();
      expect(mockStockStore.setCurrentStock).toHaveBeenCalledWith('product-1', 100);
    });

    it('should delegate addProduct to ProductStore', async () => {
      const { result } = renderHook(() => useInventoryStore());
      const newProduct = {
        name: 'New Product',
        brand: 'New Brand',
        category: 'tinte' as const,
        currentStock: 50,
        minStock: 5,
        costPerUnit: 10,
        unitType: 'ml' as const,
        unitSize: 100,
        isActive: true,
      };

      mockProductStore.addProduct.mockResolvedValue('new-product-id');
      mockProductStore.getProduct.mockReturnValue({ ...newProduct, id: 'new-product-id' });

      await act(async () => {
        await result.current.addProduct(newProduct);
      });

      expect(mockProductStore.addProduct).toHaveBeenCalledWith(newProduct);
      expect(mockStockStore.setCurrentStock).toHaveBeenCalledWith('new-product-id', 50);
    });

    it('should delegate updateProduct to ProductStore', async () => {
      const { result } = renderHook(() => useInventoryStore());
      const updates = { currentStock: 150 };

      await act(async () => {
        await result.current.updateProduct('product-1', updates);
      });

      expect(mockProductStore.updateProduct).toHaveBeenCalledWith('product-1', updates);
      expect(mockStockStore.setCurrentStock).toHaveBeenCalledWith('product-1', 150);
    });

    it('should delegate deleteProduct to ProductStore', async () => {
      const { result } = renderHook(() => useInventoryStore());

      await act(async () => {
        await result.current.deleteProduct('product-1');
      });

      expect(mockProductStore.deleteProduct).toHaveBeenCalledWith('product-1');
    });

    it('should delegate search to BrandCategoryStore', () => {
      const { result } = renderHook(() => useInventoryStore());
      const query = 'test query';

      result.current.searchProducts(query);

      expect(mockBrandCategoryStore.searchProducts).toHaveBeenCalledWith(
        mockProductStore.products,
        query
      );
    });
  });

  describe('Stock Actions Delegation', () => {
    it('should delegate updateStock to both stores', async () => {
      const { result } = renderHook(() => useInventoryStore());
      mockProductStore.getProduct.mockReturnValue(mockProductStore.products[0]);

      await act(async () => {
        await result.current.updateStock('product-1', -10, 'use', 'Test consumption');
      });

      expect(mockStockStore.updateStock).toHaveBeenCalledWith(
        'product-1',
        -10,
        'use',
        'Test consumption',
        undefined
      );
      expect(mockProductStore.updateProduct).toHaveBeenCalledWith('product-1', {
        currentStock: 90,
      });
    });

    it('should delegate consumeProducts correctly', async () => {
      const { result } = renderHook(() => useInventoryStore());
      const consumptions = [{ productId: 'product-1', quantity: 20 }];
      mockProductStore.getProduct.mockReturnValue(mockProductStore.products[0]);

      await act(async () => {
        await result.current.consumeProducts(consumptions, 'service-123', 'John Doe');
      });

      expect(mockStockStore.consumeProducts).toHaveBeenCalledWith(
        consumptions,
        'service-123',
        'John Doe'
      );
      expect(mockProductStore.updateProduct).toHaveBeenCalledWith('product-1', {
        currentStock: 80,
      });
    });

    it('should delegate alert actions to StockStore', () => {
      const { result } = renderHook(() => useInventoryStore());

      result.current.createAlert('product-1', 'low_stock', 'Low stock alert', 'warning');
      expect(mockStockStore.createAlert).toHaveBeenCalledWith(
        'product-1',
        'low_stock',
        'Low stock alert',
        'warning'
      );

      result.current.acknowledgeAlert('alert-1', 'user-1');
      expect(mockStockStore.acknowledgeAlert).toHaveBeenCalledWith('alert-1', 'user-1');

      result.current.getActiveAlerts();
      expect(mockStockStore.getActiveAlerts).toHaveBeenCalled();
    });
  });

  describe('Analytics Delegation', () => {
    it('should delegate analytics methods to InventoryAnalyticsStore', () => {
      const { result } = renderHook(() => useInventoryStore());

      result.current.getTotalInventoryValue();
      expect(mockAnalyticsStore.getTotalInventoryValue).toHaveBeenCalledWith(
        mockProductStore.products
      );

      result.current.generateInventoryReport();
      expect(mockAnalyticsStore.generateInventoryReport).toHaveBeenCalledWith(
        mockProductStore.products,
        mockStockStore.movements
      );

      result.current.getProductsByCategory('tinte');
      expect(mockAnalyticsStore.getProductsByCategory).toHaveBeenCalledWith(
        mockProductStore.products,
        'tinte'
      );
    });

    it('should delegate low stock methods', async () => {
      const { result } = renderHook(() => useInventoryStore());

      await result.current.loadLowStockProducts();
      expect(mockAnalyticsStore.loadLowStockProducts).toHaveBeenCalled();

      result.current.getLowStockProducts();
      expect(mockAnalyticsStore.getLowStockProducts).toHaveBeenCalled();
    });
  });

  describe('Filter and Grouping Delegation', () => {
    it('should delegate filter actions to BrandCategoryStore', () => {
      const { result } = renderHook(() => useInventoryStore());

      result.current.setFilter('stockStatus', 'low');
      expect(mockBrandCategoryStore.setFilter).toHaveBeenCalledWith('stockStatus', 'low');

      result.current.setSortBy('price');
      expect(mockBrandCategoryStore.setSortBy).toHaveBeenCalledWith('price');

      result.current.setGroupBy('brand');
      expect(mockBrandCategoryStore.setGroupBy).toHaveBeenCalledWith('brand');

      result.current.resetFilters();
      expect(mockBrandCategoryStore.resetFilters).toHaveBeenCalled();
    });

    it('should delegate grouping methods with correct parameters', () => {
      const { result } = renderHook(() => useInventoryStore());

      result.current.getFilteredAndSortedProducts();
      expect(mockBrandCategoryStore.getFilteredAndSortedProducts).toHaveBeenCalledWith(
        mockProductStore.products,
        mockStockStore.movements
      );

      result.current.getGroupedProducts();
      expect(mockBrandCategoryStore.getGroupedProducts).toHaveBeenCalledWith(
        mockProductStore.products,
        mockStockStore.movements
      );
    });
  });

  describe('Migration Helper', () => {
    it('should provide access to individual stores', () => {
      const { result } = renderHook(() => useInventoryStore());
      const migration = result.current.__migration;

      expect(migration.useProductStore()).toBe(useProductStore);
      expect(migration.useBrandCategoryStore()).toBe(useBrandCategoryStore);
      expect(migration.useStockStore()).toBe(useStockStore);
      expect(migration.useInventoryAnalyticsStore()).toBe(useInventoryAnalyticsStore);
    });

    it('should validate consistency between facade and stores', () => {
      const validation = InventoryMigrationHelper.validateConsistency();

      expect(validation.isConsistent).toBe(true);
      expect(validation.inconsistencies).toHaveLength(0);
    });

    it('should create transition hooks', () => {
      const TransitionHook = InventoryMigrationHelper.createTransitionHook('useProductStore');
      const { result } = renderHook(TransitionHook);

      expect(result.current.facade).toBeDefined();
      expect(result.current.direct).toBeDefined();
      expect(result.current.__isTransitioning).toBe(true);
    });

    it('should benchmark performance', () => {
      const benchmark = InventoryMigrationHelper.benchmarkAccess();

      expect(benchmark).toHaveProperty('facadeTime');
      expect(benchmark).toHaveProperty('directTime');
      expect(benchmark).toHaveProperty('overhead');
      expect(typeof benchmark.overhead).toBe('number');
    });
  });

  describe('Sync Operations', () => {
    it('should coordinate sync across all stores', async () => {
      const { result } = renderHook(() => useInventoryStore());

      await act(async () => {
        await result.current.syncWithSupabase();
      });

      expect(mockProductStore.syncWithSupabase).toHaveBeenCalled();
      expect(mockStockStore.syncWithSupabase).toHaveBeenCalled();
      expect(mockStockStore.setCurrentStock).toHaveBeenCalledWith('product-1', 100);
    });

    it('should clear all data from all stores', () => {
      const { result } = renderHook(() => useInventoryStore());

      result.current.clearAllData();

      expect(mockProductStore.clearAllData).toHaveBeenCalled();
      expect(mockStockStore.clearMovements).toHaveBeenCalled();
      expect(mockAnalyticsStore.clearInventoryReport).toHaveBeenCalled();
      expect(mockAnalyticsStore.clearAnalyticsCache).toHaveBeenCalled();
      expect(mockBrandCategoryStore.resetFilters).toHaveBeenCalled();
    });
  });

  describe('Backward Compatibility', () => {
    it('should maintain exact same interface as original store', () => {
      const { result } = renderHook(() => useInventoryStore());
      const facade = result.current;

      // Check that all expected properties and methods exist
      expect(facade).toHaveProperty('products');
      expect(facade).toHaveProperty('movements');
      expect(facade).toHaveProperty('alerts');
      expect(facade).toHaveProperty('isLoading');
      expect(facade).toHaveProperty('isInitialized');
      expect(facade).toHaveProperty('activeFilters');

      // Check methods
      expect(typeof facade.loadProducts).toBe('function');
      expect(typeof facade.addProduct).toBe('function');
      expect(typeof facade.updateProduct).toBe('function');
      expect(typeof facade.deleteProduct).toBe('function');
      expect(typeof facade.updateStock).toBe('function');
      expect(typeof facade.consumeProducts).toBe('function');
      expect(typeof facade.generateInventoryReport).toBe('function');
      expect(typeof facade.getTotalInventoryValue).toBe('function');
      expect(typeof facade.setFilter).toBe('function');
      expect(typeof facade.getFilteredAndSortedProducts).toBe('function');
    });

    it('should handle cross-store consistency', () => {
      const { result } = renderHook(() => useInventoryStore());

      // Check that low stock products fallback works
      mockAnalyticsStore.getLowStockProducts.mockReturnValue([]);
      mockStockStore.getLowStockProducts.mockReturnValue([
        {
          product_id: 'product-1',
          brand: 'Test Brand',
          name: 'Test Product',
          category: 'tinte',
          stock_ml: 5,
          minimum_stock_ml: 10,
          percentage_remaining: 50,
        },
      ]);

      const lowStockProducts = result.current.getLowStockProducts();
      expect(lowStockProducts).toHaveLength(1);
      expect(lowStockProducts[0].product_id).toBe('product-1');
    });
  });
});
