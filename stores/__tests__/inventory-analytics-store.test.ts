/**
 * Comprehensive Test Suite for Inventory Analytics Store
 *
 * Tests reports generation, analytics, business intelligence,
 * low stock intelligence, consumption analysis, and caching.
 */

import { useInventoryAnalyticsStore, inventoryAnalytics } from '../inventory-analytics-store';
import { supabase, getCurrentSalonId } from '@/lib/supabase';
import type { Product, StockMovement } from '@/types/inventory';

// Mock dependencies
jest.mock('@/lib/supabase');
jest.mock('@/utils/logger');

// Mock data factories
const createMockProduct = (overrides: Partial<Product> = {}): Product => ({
  id: `product-${Math.random().toString(36).substr(2, 9)}`,
  brand: 'Wella',
  line: 'Color Perfect',
  type: 'Tinte',
  shade: '7.1',
  displayName: 'Wella Color Perfect 7.1 Tinte',
  name: 'Wella Color Perfect 7.1',
  category: 'tinte',
  currentStock: 500,
  minStock: 100,
  maxStock: 1000,
  unitType: 'ml',
  unitSize: 60,
  purchasePrice: 15.5,
  costPerUnit: 0.26,
  barcode: '1234567890',
  supplier: 'Beauty Supply Co',
  notes: 'Popular shade',
  colorCode: '7.1',
  isActive: true,
  lastUpdated: '2024-01-01T00:00:00Z',
  _syncStatus: 'synced',
  ...overrides,
});

const createMockMovement = (overrides: Partial<StockMovement> = {}): StockMovement => ({
  id: `movement-${Math.random().toString(36).substr(2, 9)}`,
  productId: 'product-123',
  quantity: -25,
  type: 'use',
  date: new Date().toISOString(),
  userId: 'user-123',
  notes: 'Service consumption',
  ...overrides,
});

const createMockLowStockProduct = (overrides: any = {}) => ({
  product_id: 'product-123',
  brand: 'Wella',
  name: 'Wella Color Perfect 7.1',
  category: 'tinte',
  stock_ml: 25,
  minimum_stock_ml: 100,
  percentage_remaining: 25,
  color_code: '7.1',
  ...overrides,
});

describe('InventoryAnalyticsStore', () => {
  let mockSupabase: any;
  let store: any;
  let testProducts: Product[];
  let testMovements: StockMovement[];

  beforeEach(() => {
    // Clear all mocks first
    jest.clearAllMocks();

    // Reset store state
    useInventoryAnalyticsStore.setState({
      currentReport: null,
      isLoadingReport: false,
      lowStockProducts: [],
      isLoadingLowStock: false,
      cachedConsumptionAnalysis: new Map(),
      cacheExpiryTime: new Map(),
      reportSettings: {
        includeLeastUsed: true,
        includeOverstock: true,
        mostUsedLimit: 5,
        leastUsedLimit: 5,
        categoryBreakdown: true,
        cacheDurationMinutes: 30,
      },
    });

    // Mock Supabase
    mockSupabase = {
      rpc: jest.fn(),
    };
    (supabase as any).rpc = mockSupabase.rpc;

    // Mock getCurrentSalonId
    (getCurrentSalonId as jest.Mock).mockResolvedValue('salon-123');

    store = useInventoryAnalyticsStore.getState();

    // Create diverse test data
    testProducts = [
      // High value, good stock
      createMockProduct({
        id: 'expensive-1',
        brand: 'Olaplex',
        displayName: 'Olaplex No.1 Bond Multiplier',
        category: 'tratamiento',
        currentStock: 500,
        minStock: 100,
        costPerUnit: 3.5,
        isActive: true,
      }),
      // Low stock
      createMockProduct({
        id: 'low-stock-1',
        brand: 'Wella',
        displayName: 'Wella Color Perfect 7.1',
        category: 'tinte',
        currentStock: 25,
        minStock: 100,
        costPerUnit: 0.3,
        isActive: true,
      }),
      // Out of stock
      createMockProduct({
        id: 'out-stock-1',
        brand: "L'Oreal",
        displayName: "L'Oreal Majirel 6.35",
        category: 'tinte',
        currentStock: 0,
        minStock: 50,
        costPerUnit: 0.32,
        isActive: true,
      }),
      // Overstock
      createMockProduct({
        id: 'over-stock-1',
        brand: 'Matrix',
        displayName: 'Matrix Light Master',
        category: 'decolorante',
        currentStock: 800,
        minStock: 100,
        maxStock: 500,
        costPerUnit: 0.45,
        isActive: true,
      }),
      // Inactive product (should be excluded from some calculations)
      createMockProduct({
        id: 'inactive-1',
        brand: 'Discontinued',
        displayName: 'Old Product',
        category: 'otro',
        currentStock: 200,
        minStock: 50,
        costPerUnit: 0.1,
        isActive: false,
      }),
    ];

    // Create test movements for usage analysis
    testMovements = [
      // Recent frequent usage
      ...Array.from({ length: 5 }, (_, i) =>
        createMockMovement({
          productId: 'expensive-1',
          type: 'use',
          quantity: -30,
          date: new Date(Date.now() - (i + 1) * 5 * 24 * 60 * 60 * 1000).toISOString(),
        })
      ),
      // Medium usage
      ...Array.from({ length: 2 }, (_, i) =>
        createMockMovement({
          productId: 'low-stock-1',
          type: 'use',
          quantity: -20,
          date: new Date(Date.now() - (i + 1) * 10 * 24 * 60 * 60 * 1000).toISOString(),
        })
      ),
      // Old usage (should not affect recent analysis)
      createMockMovement({
        productId: 'out-stock-1',
        type: 'use',
        quantity: -25,
        date: new Date(Date.now() - 40 * 24 * 60 * 60 * 1000).toISOString(),
      }),
      // Purchase movements (should not count as usage)
      createMockMovement({
        productId: 'expensive-1',
        type: 'purchase',
        quantity: 100,
        date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      }),
    ];
  });

  describe('Report Generation', () => {
    describe('generateInventoryReport', () => {
      it('should generate comprehensive inventory report', () => {
        const report = store.generateInventoryReport(testProducts, testMovements);

        expect(report).toBeTruthy();
        expect(report.generatedAt).toBeTruthy();
        expect(typeof report.totalValue).toBe('number');
        expect(typeof report.lowStockCount).toBe('number');
        expect(typeof report.outOfStockCount).toBe('number');
        expect(typeof report.overstockCount).toBe('number');
        expect(Array.isArray(report.mostUsedProducts)).toBe(true);
        expect(Array.isArray(report.costByCategory)).toBe(true);
      });

      it('should calculate correct stock counts', () => {
        const report = store.generateInventoryReport(testProducts, testMovements);

        expect(report.lowStockCount).toBe(1); // low-stock-1
        expect(report.outOfStockCount).toBe(1); // out-stock-1
        expect(report.overstockCount).toBe(1); // over-stock-1
      });

      it('should calculate total inventory value correctly', () => {
        const report = store.generateInventoryReport(testProducts, testMovements);

        const expectedValue = testProducts.reduce(
          (sum, p) => sum + p.currentStock * p.costPerUnit,
          0
        );
        expect(report.totalValue).toBeCloseTo(expectedValue, 2);
      });

      it('should identify most used products correctly', () => {
        const report = store.generateInventoryReport(testProducts, testMovements);

        expect(report.mostUsedProducts.length).toBeGreaterThan(0);

        // expensive-1 should be the most used (5 movements)
        const mostUsed = report.mostUsedProducts[0];
        expect(mostUsed.product.id).toBe('expensive-1');
        expect(mostUsed.usageCount).toBe(5);
        expect(mostUsed.totalConsumed).toBe(150); // 5 movements × 30ml each
      });

      it('should include least used products when enabled', () => {
        const report = store.generateInventoryReport(testProducts, testMovements);

        expect(report.leastUsedProducts).toBeDefined();
        expect(Array.isArray(report.leastUsedProducts)).toBe(true);
      });

      it('should exclude least used products when disabled', () => {
        store.updateReportSettings({ includeLeastUsed: false });

        const report = store.generateInventoryReport(testProducts, testMovements);

        expect(report.leastUsedProducts).toHaveLength(0);
      });

      it('should include category breakdown when enabled', () => {
        const report = store.generateInventoryReport(testProducts, testMovements);

        expect(report.costByCategory).toBeDefined();
        expect(report.costByCategory.length).toBeGreaterThan(0);

        // Should be sorted by value descending
        for (let i = 1; i < report.costByCategory.length; i++) {
          expect(report.costByCategory[i].totalCost).toBeLessThanOrEqual(
            report.costByCategory[i - 1].totalCost
          );
        }
      });

      it('should handle empty data gracefully', () => {
        const report = store.generateInventoryReport([], []);

        expect(report.totalValue).toBe(0);
        expect(report.lowStockCount).toBe(0);
        expect(report.outOfStockCount).toBe(0);
        expect(report.overstockCount).toBe(0);
        expect(report.mostUsedProducts).toHaveLength(0);
        expect(report.costByCategory).toHaveLength(0);
      });
    });

    describe('loadInventoryReport', () => {
      it('should load and cache inventory report', () => {
        // Ensure generateInventoryReport is working first
        const report = store.generateInventoryReport(testProducts, testMovements);
        expect(report).toBeTruthy();

        // Now test loadInventoryReport
        store.loadInventoryReport(testProducts, testMovements);

        expect(useInventoryAnalyticsStore.getState().currentReport).toBeTruthy();
        expect(useInventoryAnalyticsStore.getState().isLoadingReport).toBe(false);
      });

      it('should handle loading errors gracefully', () => {
        // Mock generateInventoryReport to throw
        const originalGenerate = store.generateInventoryReport;
        store.generateInventoryReport = jest.fn().mockImplementation(() => {
          throw new Error('Generation failed');
        });

        store.loadInventoryReport(testProducts, testMovements);

        expect(useInventoryAnalyticsStore.getState().currentReport).toBeNull();
        expect(useInventoryAnalyticsStore.getState().isLoadingReport).toBe(false);

        // Restore original method
        store.generateInventoryReport = originalGenerate;
      });
    });

    describe('clearInventoryReport', () => {
      it('should clear current report', () => {
        // Set a report first
        store.loadInventoryReport(testProducts, testMovements);
        expect(useInventoryAnalyticsStore.getState().currentReport).toBeTruthy();

        store.clearInventoryReport();

        expect(useInventoryAnalyticsStore.getState().currentReport).toBeNull();
        expect(useInventoryAnalyticsStore.getState().isLoadingReport).toBe(false);
      });
    });
  });

  describe('Low Stock Intelligence', () => {
    describe('loadLowStockProducts', () => {
      it('should load low stock products from RPC', async () => {
        const mockLowStockData = [
          createMockLowStockProduct(),
          createMockLowStockProduct({
            product_id: 'product-456',
            stock_ml: 10,
            minimum_stock_ml: 50,
            percentage_remaining: 20,
          }),
        ];

        mockSupabase.rpc.mockResolvedValue({
          data: mockLowStockData,
          error: null,
        });

        await store.loadLowStockProducts();

        expect(mockSupabase.rpc).toHaveBeenCalledWith('get_low_stock_products', {
          p_salon_id: 'salon-123',
        });

        const lowStockProducts = useInventoryAnalyticsStore.getState().lowStockProducts;
        expect(lowStockProducts).toHaveLength(2);
        expect(useInventoryAnalyticsStore.getState().isLoadingLowStock).toBe(false);
      });

      it('should handle RPC errors gracefully', async () => {
        mockSupabase.rpc.mockResolvedValue({
          data: null,
          error: new Error('RPC failed'),
        });

        await store.loadLowStockProducts();

        expect(useInventoryAnalyticsStore.getState().lowStockProducts).toHaveLength(0);
        expect(useInventoryAnalyticsStore.getState().isLoadingLowStock).toBe(false);
      });
    });

    describe('getProductStockStatus', () => {
      it('should correctly identify out of stock products', () => {
        const outOfStockProduct = createMockProduct({ currentStock: 0 });
        const status = store.getProductStockStatus(outOfStockProduct);
        expect(status).toBe('out');
      });

      it('should correctly identify low stock products', () => {
        const lowStockProduct = createMockProduct({
          currentStock: 50,
          minStock: 100,
        });
        const status = store.getProductStockStatus(lowStockProduct);
        expect(status).toBe('low');
      });

      it('should correctly identify overstock products', () => {
        const overstockProduct = createMockProduct({
          currentStock: 800,
          minStock: 100,
          maxStock: 500,
        });
        const status = store.getProductStockStatus(overstockProduct);
        expect(status).toBe('overstock');
      });

      it('should correctly identify ok stock products', () => {
        const okStockProduct = createMockProduct({
          currentStock: 300,
          minStock: 100,
        });
        const status = store.getProductStockStatus(okStockProduct);
        expect(status).toBe('ok');
      });
    });

    describe('getPredictedStockOut', () => {
      it('should predict stock out date based on consumption', () => {
        const product = createMockProduct({
          id: 'test-product',
          currentStock: 100,
        });

        // Create movements showing 30ml consumption over 30 days (1ml/day)
        const movements = Array.from({ length: 6 }, (_, i) =>
          createMockMovement({
            productId: 'test-product',
            type: 'use',
            quantity: -5,
            date: new Date(Date.now() - i * 5 * 24 * 60 * 60 * 1000).toISOString(),
          })
        );

        const predictedDate = store.getPredictedStockOut(product, movements);

        expect(predictedDate).toBeInstanceOf(Date);
        // With 100ml stock and 1ml/day consumption, should run out in ~100 days
        const daysUntilEmpty = Math.floor(
          (predictedDate!.getTime() - Date.now()) / (24 * 60 * 60 * 1000)
        );
        expect(daysUntilEmpty).toBeWithinRange(95, 105);
      });

      it('should return null for products with no consumption', () => {
        const product = createMockProduct({ currentStock: 100 });
        const movements: StockMovement[] = [];

        const predictedDate = store.getPredictedStockOut(product, movements);
        expect(predictedDate).toBeNull();
      });

      it('should only consider recent consumption (last 30 days)', () => {
        const product = createMockProduct({
          id: 'test-product',
          currentStock: 100,
        });

        const movements = [
          // Old consumption (should be ignored)
          createMockMovement({
            productId: 'test-product',
            type: 'use',
            quantity: -50,
            date: new Date(Date.now() - 40 * 24 * 60 * 60 * 1000).toISOString(),
          }),
          // Recent consumption
          createMockMovement({
            productId: 'test-product',
            type: 'use',
            quantity: -15,
            date: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
          }),
        ];

        const predictedDate = store.getPredictedStockOut(product, movements);

        expect(predictedDate).toBeInstanceOf(Date);
        // Should base calculation only on recent 15ml consumption
      });
    });
  });

  describe('Consumption Analysis', () => {
    describe('getConsumptionAnalysis', () => {
      const mockGetProduct = (id: string) => testProducts.find(p => p.id === id);

      it('should analyze daily consumption correctly', () => {
        const movements = [
          createMockMovement({
            productId: 'test-product',
            type: 'use',
            quantity: -25,
            date: new Date().toISOString(),
          }),
        ];

        const analysis = store.getConsumptionAnalysis(
          'test-product',
          'daily',
          movements,
          mockGetProduct
        );

        expect(analysis).toBeTruthy();
        expect(analysis!.productId).toBe('test-product');
        expect(analysis!.period).toBe('daily');
        expect(analysis!.totalConsumed).toBe(25);
        expect(analysis!.servicesCount).toBe(1);
      });

      it('should analyze weekly consumption correctly', () => {
        const weeklyMovements = Array.from({ length: 3 }, (_, i) =>
          createMockMovement({
            productId: 'test-product',
            type: 'use',
            quantity: -20,
            date: new Date(Date.now() - i * 2 * 24 * 60 * 60 * 1000).toISOString(),
          })
        );

        const analysis = store.getConsumptionAnalysis(
          'test-product',
          'weekly',
          weeklyMovements,
          mockGetProduct
        );

        expect(analysis).toBeTruthy();
        expect(analysis!.totalConsumed).toBe(60);
        expect(analysis!.servicesCount).toBe(3);
        expect(analysis!.averagePerService).toBe(20);
      });

      it('should calculate costs when product is available', () => {
        const product = createMockProduct({
          id: 'test-product',
          costPerUnit: 0.3,
        });
        const getProductMock = jest.fn().mockReturnValue(product);

        const movements = [
          createMockMovement({
            productId: 'test-product',
            type: 'use',
            quantity: -50,
          }),
        ];

        const analysis = store.getConsumptionAnalysis(
          'test-product',
          'daily',
          movements,
          getProductMock
        );

        expect(analysis!.totalCost).toBe(15); // 50ml * 0.30 per ml
        expect(analysis!.productName).toContain('Wella');
      });

      it('should return null for products with no consumption movements', () => {
        const analysis = store.getConsumptionAnalysis(
          'no-movements-product',
          'daily',
          [],
          mockGetProduct
        );

        expect(analysis).toBeNull();
      });

      it('should handle Spanish movement types', () => {
        const movements = [
          createMockMovement({
            productId: 'test-product',
            type: 'consumo' as any,
            quantity: -30,
          }),
        ];

        const analysis = store.getConsumptionAnalysis(
          'test-product',
          'daily',
          movements,
          mockGetProduct
        );

        expect(analysis).toBeTruthy();
        expect(analysis!.totalConsumed).toBe(30);
      });

      describe('Caching', () => {
        it('should cache analysis results', () => {
          const movements = [
            createMockMovement({
              productId: 'test-product',
              type: 'use',
              quantity: -25,
            }),
          ];

          // First call
          const analysis1 = store.getConsumptionAnalysis(
            'test-product',
            'daily',
            movements,
            mockGetProduct
          );

          // Second call should use cache
          const analysis2 = store.getConsumptionAnalysis(
            'test-product',
            'daily',
            movements,
            mockGetProduct
          );

          expect(analysis1).toEqual(analysis2);

          // Verify cache was used
          const state = useInventoryAnalyticsStore.getState();
          expect(state.cachedConsumptionAnalysis.size).toBeGreaterThan(0);
        });

        it('should respect cache expiry', () => {
          // Mock expired cache
          const expiredTime = Date.now() - 2000; // 2 seconds ago
          useInventoryAnalyticsStore.setState({
            cacheExpiryTime: new Map([['test-product_daily', expiredTime]]),
          });

          const isValid = store.isAnalyticsCacheValid('test-product_daily');
          expect(isValid).toBe(false);
        });

        it('should clear cache correctly', () => {
          // Add some cache data
          useInventoryAnalyticsStore.setState({
            cachedConsumptionAnalysis: new Map([['test', {} as any]]),
            cacheExpiryTime: new Map([['test', Date.now()]]),
          });

          store.clearAnalyticsCache();

          const state = useInventoryAnalyticsStore.getState();
          expect(state.cachedConsumptionAnalysis.size).toBe(0);
          expect(state.cacheExpiryTime.size).toBe(0);
        });
      });
    });
  });

  describe('Business Intelligence', () => {
    describe('getTotalInventoryValue', () => {
      it('should calculate total inventory value correctly', () => {
        const totalValue = store.getTotalInventoryValue(testProducts);

        const expectedValue = testProducts.reduce(
          (sum, p) => sum + p.currentStock * p.costPerUnit,
          0
        );
        expect(totalValue).toBeCloseTo(expectedValue, 2);
      });
    });

    describe('getFrequentlyUsedProducts', () => {
      it('should identify frequently used products', () => {
        const frequentlyUsed = store.getFrequentlyUsedProducts(testProducts, testMovements, 3);

        expect(frequentlyUsed.length).toBeLessThanOrEqual(3);

        if (frequentlyUsed.length > 0) {
          // Should be sorted by usage frequency
          expect(frequentlyUsed[0].id).toBe('expensive-1');
        }
      });

      it('should only consider recent movements (last 30 days)', () => {
        const recentMovements = testMovements.filter(m => {
          const thirtyDaysAgo = new Date();
          thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
          return new Date(m.date) > thirtyDaysAgo;
        });

        const frequentlyUsed = store.getFrequentlyUsedProducts(testProducts, recentMovements, 5);

        // Should only include products with recent usage
        expect(frequentlyUsed.every(p => recentMovements.some(m => m.productId === p.id))).toBe(
          true
        );
      });
    });

    describe('getProductsByCategory', () => {
      it('should filter products by category', () => {
        const tinteProducts = store.getProductsByCategory(testProducts, 'tinte');

        expect(tinteProducts.every(p => p.category === 'tinte')).toBe(true);
        expect(tinteProducts.length).toBeGreaterThan(0);
      });
    });

    describe('calculateCostByCategory', () => {
      it('should calculate cost distribution by category', () => {
        const costByCategory = store.calculateCostByCategory(testProducts);

        expect(Array.isArray(costByCategory)).toBe(true);
        expect(costByCategory.length).toBeGreaterThan(0);

        // Should only include active products
        const activeProducts = testProducts.filter(p => p.isActive);
        const totalCost = costByCategory.reduce((sum, c) => sum + c.totalCost, 0);
        const expectedCost = activeProducts.reduce(
          (sum, p) => sum + p.currentStock * p.costPerUnit,
          0
        );
        expect(totalCost).toBeCloseTo(expectedCost, 2);

        // Should calculate percentages correctly
        costByCategory.forEach(category => {
          expect(category.percentage).toBeGreaterThanOrEqual(0);
          expect(category.percentage).toBeLessThanOrEqual(100);
        });

        // Should be sorted by total cost descending
        for (let i = 1; i < costByCategory.length; i++) {
          expect(costByCategory[i].totalCost).toBeLessThanOrEqual(costByCategory[i - 1].totalCost);
        }
      });
    });

    describe('getInventoryTurnover', () => {
      it('should calculate inventory turnover rates', () => {
        const turnoverMap = store.getInventoryTurnover(testProducts, testMovements);

        expect(turnoverMap).toBeInstanceOf(Map);
        expect(turnoverMap.size).toBe(testProducts.length);

        // Check that all products have turnover calculated
        testProducts.forEach(product => {
          expect(turnoverMap.has(product.id)).toBe(true);
          expect(typeof turnoverMap.get(product.id)).toBe('number');
        });
      });

      it('should handle products with no consumption', () => {
        const productsWithoutMovements = [createMockProduct({ id: 'no-movements' })];

        const turnoverMap = store.getInventoryTurnover(productsWithoutMovements, []);

        expect(turnoverMap.get('no-movements')).toBe(0);
      });
    });

    describe('getSeasonalTrends', () => {
      it('should analyze seasonal consumption trends', () => {
        // Create movements across different months
        const seasonalMovements = [
          createMockMovement({
            type: 'use',
            quantity: -50,
            date: new Date('2024-01-15T10:00:00Z').toISOString(),
          }),
          createMockMovement({
            type: 'use',
            quantity: -30,
            date: new Date('2024-01-20T10:00:00Z').toISOString(),
          }),
          createMockMovement({
            type: 'use',
            quantity: -40,
            date: new Date('2024-02-10T10:00:00Z').toISOString(),
          }),
        ];

        const trends = store.getSeasonalTrends(seasonalMovements, 6);

        expect(Array.isArray(trends)).toBe(true);

        if (trends.length > 0) {
          const trend = trends[0];
          expect(trend).toHaveProperty('month');
          expect(trend).toHaveProperty('totalConsumption');
          expect(trend).toHaveProperty('averageOrderValue');
          expect(Array.isArray(trend.topCategories)).toBe(true);
        }

        // Should be sorted by month
        for (let i = 1; i < trends.length; i++) {
          expect(trends[i].month.localeCompare(trends[i - 1].month)).toBeGreaterThanOrEqual(0);
        }
      });

      it('should only include consumption movements', () => {
        const mixedMovements = [
          createMockMovement({ type: 'use', quantity: -30 }),
          createMockMovement({ type: 'purchase', quantity: 100 }),
          createMockMovement({ type: 'adjustment', quantity: 10 }),
        ];

        const trends = store.getSeasonalTrends(mixedMovements);

        // Should only process 'use' movements
        trends.forEach(trend => {
          expect(trend.totalConsumption).toBeGreaterThanOrEqual(0);
        });
      });
    });

    describe('getStockEfficiencyMetrics', () => {
      it('should calculate comprehensive efficiency metrics', () => {
        const metrics = store.getStockEfficiencyMetrics(testProducts, testMovements);

        expect(metrics).toHaveProperty('deadStockValue');
        expect(metrics).toHaveProperty('fastMovingProducts');
        expect(metrics).toHaveProperty('slowMovingProducts');
        expect(metrics).toHaveProperty('stockAccuracy');

        expect(typeof metrics.deadStockValue).toBe('number');
        expect(Array.isArray(metrics.fastMovingProducts)).toBe(true);
        expect(Array.isArray(metrics.slowMovingProducts)).toBe(true);
        expect(typeof metrics.stockAccuracy).toBe('number');

        // Stock accuracy should be a percentage (0-100)
        expect(metrics.stockAccuracy).toBeGreaterThanOrEqual(0);
        expect(metrics.stockAccuracy).toBeLessThanOrEqual(100);
      });
    });
  });

  describe('Stock Alerts', () => {
    describe('getStockAlerts', () => {
      it('should generate appropriate alerts for different stock conditions', () => {
        const alerts = store.getStockAlerts(testProducts);

        expect(Array.isArray(alerts)).toBe(true);
        expect(alerts.length).toBeGreaterThan(0);

        // Should have critical alert for out of stock
        const criticalAlerts = alerts.filter(a => a.severity === 'critical');
        expect(criticalAlerts.length).toBeGreaterThan(0);
        expect(criticalAlerts[0].alertType).toBe('out_of_stock');

        // Should have high priority alert for low stock
        const highAlerts = alerts.filter(a => a.severity === 'high');
        expect(highAlerts.length).toBeGreaterThan(0);
        expect(highAlerts.some(a => a.alertType === 'low_stock')).toBe(true);

        // Should have medium priority alert for overstock
        const mediumAlerts = alerts.filter(a => a.severity === 'medium');
        expect(mediumAlerts.length).toBeGreaterThan(0);
        expect(mediumAlerts.some(a => a.alertType === 'overstock')).toBe(true);

        // Should be sorted by severity (critical -> high -> medium -> low)
        for (let i = 1; i < alerts.length; i++) {
          const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
          const currentScore = severityOrder[alerts[i].severity];
          const previousScore = severityOrder[alerts[i - 1].severity];
          expect(currentScore).toBeLessThanOrEqual(previousScore);
        }
      });

      it('should only alert for active products', () => {
        const alerts = store.getStockAlerts(testProducts);

        alerts.forEach(alert => {
          const product = testProducts.find(p => p.id === alert.productId);
          expect(product!.isActive).toBe(true);
        });
      });

      it('should provide actionable recommendations', () => {
        const alerts = store.getStockAlerts(testProducts);

        alerts.forEach(alert => {
          expect(alert.message).toBeTruthy();
          expect(alert.recommendedAction).toBeTruthy();
          expect(typeof alert.message).toBe('string');
          expect(typeof alert.recommendedAction).toBe('string');
        });
      });
    });
  });

  describe('Settings Management', () => {
    describe('updateReportSettings', () => {
      it('should update report settings correctly', () => {
        const newSettings = {
          includeLeastUsed: false,
          mostUsedLimit: 10,
          cacheDurationMinutes: 60,
        };

        store.updateReportSettings(newSettings);

        const state = useInventoryAnalyticsStore.getState();
        expect(state.reportSettings.includeLeastUsed).toBe(false);
        expect(state.reportSettings.mostUsedLimit).toBe(10);
        expect(state.reportSettings.cacheDurationMinutes).toBe(60);

        // Should preserve existing settings not updated
        expect(state.reportSettings.includeOverstock).toBe(true);
        expect(state.reportSettings.categoryBreakdown).toBe(true);
      });
    });
  });

  describe('Helper Functions and Exports', () => {
    describe('inventoryAnalytics helper', () => {
      it('should provide convenient access to analytics functions', () => {
        expect(typeof inventoryAnalytics.generateReport).toBe('function');
        expect(typeof inventoryAnalytics.loadReport).toBe('function');
        expect(typeof inventoryAnalytics.getTotalValue).toBe('function');
        expect(typeof inventoryAnalytics.getFrequentlyUsed).toBe('function');
        expect(typeof inventoryAnalytics.getLowStock).toBe('function');
      });

      it('should execute analytics functions correctly', () => {
        const report = inventoryAnalytics.generateReport(testProducts, testMovements);
        expect(report).toBeTruthy();
        expect(report.totalValue).toBeGreaterThan(0);

        const totalValue = inventoryAnalytics.getTotalValue(testProducts);
        expect(typeof totalValue).toBe('number');

        const frequentlyUsed = inventoryAnalytics.getFrequentlyUsed(testProducts, testMovements, 3);
        expect(Array.isArray(frequentlyUsed)).toBe(true);
      });
    });
  });

  describe('Performance and Edge Cases', () => {
    it('should handle large datasets efficiently', () => {
      const largeProductSet = Array.from({ length: 1000 }, (_, i) =>
        createMockProduct({
          id: `product-${i}`,
          brand: `Brand-${i % 10}`,
          currentStock: Math.random() * 1000,
        })
      );

      const largeMovementSet = Array.from({ length: 5000 }, (_, i) =>
        createMockMovement({
          id: `movement-${i}`,
          productId: `product-${i % 1000}`,
          quantity: -Math.random() * 50,
        })
      );

      const startTime = performance.now();
      const report = store.generateInventoryReport(largeProductSet, largeMovementSet);
      const endTime = performance.now();

      expect(report).toBeTruthy();
      expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second
    });

    it('should handle null and undefined values gracefully', () => {
      const problematicProducts = [
        createMockProduct({ brand: null as any, currentStock: NaN }),
        createMockProduct({ costPerUnit: undefined as any }),
      ];

      const problematicMovements = [
        createMockMovement({ quantity: null as any }),
        createMockMovement({ date: undefined as any }),
      ];

      expect(() => {
        store.generateInventoryReport(problematicProducts, problematicMovements);
      }).not.toThrow();
    });

    it('should maintain consistency across multiple calls', () => {
      const report1 = store.generateInventoryReport(testProducts, testMovements);
      const report2 = store.generateInventoryReport(testProducts, testMovements);

      expect(report1.totalValue).toBe(report2.totalValue);
      expect(report1.lowStockCount).toBe(report2.lowStockCount);
      expect(report1.mostUsedProducts.length).toBe(report2.mostUsedProducts.length);
    });
  });

  describe('Integration with Other Stores', () => {
    it('should work correctly when called through facade pattern', () => {
      // Simulate facade usage
      const mockGetProduct = (id: string) => testProducts.find(p => p.id === id);

      const consumption = store.getConsumptionAnalysis(
        'expensive-1',
        'daily',
        testMovements,
        mockGetProduct
      );

      expect(consumption).toBeTruthy();
      expect(consumption!.productName).toContain('Olaplex');
    });
  });

  describe('Error Handling', () => {
    it('should handle Supabase RPC failures gracefully', async () => {
      mockSupabase.rpc.mockRejectedValue(new Error('Connection failed'));

      await store.loadLowStockProducts();

      expect(useInventoryAnalyticsStore.getState().lowStockProducts).toHaveLength(0);
      expect(useInventoryAnalyticsStore.getState().isLoadingLowStock).toBe(false);
    });

    it('should handle invalid salon ID gracefully', async () => {
      (getCurrentSalonId as jest.Mock).mockResolvedValue(null);

      await store.loadLowStockProducts();

      expect(mockSupabase.rpc).not.toHaveBeenCalled();
      expect(useInventoryAnalyticsStore.getState().isLoadingLowStock).toBe(false);
    });
  });
});
