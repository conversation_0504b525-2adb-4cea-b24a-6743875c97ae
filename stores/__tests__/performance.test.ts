/**
 * Performance and Stress Tests for Modular Inventory Stores
 *
 * Tests performance benchmarks, large dataset handling, concurrent operations,
 * memory usage, and optimization validation.
 */

import { useProductStore } from '../product-store.new';
import { useBrandCategoryStore } from '../brand-category-store.new';
import { useStockStore } from '../stock-store.new';
import { useInventoryAnalyticsStore } from '../inventory-analytics-store';
import { useInventoryStore } from '../inventory-store-facade';
import {
  InventoryMockFactory,
  InventoryTestHelpers,
  PerformanceTestHelper,
  MockStoreHelper,
} from './test-utils';
import type {} from '@/types/inventory';

// Mock dependencies for performance tests
jest.mock('../sync-queue-store');
jest.mock('@/lib/supabase');
jest.mock('@/utils/logger');

// Performance thresholds (in milliseconds)
const PERFORMANCE_THRESHOLDS = {
  small: {
    search: 10,
    filter: 15,
    sort: 20,
    report: 50,
    crud: 25,
  },
  medium: {
    search: 50,
    filter: 75,
    sort: 100,
    report: 200,
    crud: 100,
  },
  large: {
    search: 200,
    filter: 300,
    sort: 400,
    report: 800,
    crud: 500,
  },
  xlarge: {
    search: 1000,
    filter: 1500,
    sort: 2000,
    report: 3000,
    crud: 2000,
  },
};

describe('Inventory Stores Performance Tests', () => {
  beforeEach(() => {
    // Clean slate for each test
    jest.clearAllMocks();
  });

  describe('Product Store Performance', () => {
    describe('Search Operations', () => {
      it('should search small datasets efficiently', () => {
        const { products } = InventoryTestHelpers.createPerformanceTestData('small');
        useProductStore.setState({ products });

        const store = useProductStore.getState();

        const { duration } = PerformanceTestHelper.measure(() => {
          return store.searchProducts('Wella');
        });

        PerformanceTestHelper.assertPerformance(
          duration,
          PERFORMANCE_THRESHOLDS.small.search,
          'small dataset search'
        );
      });

      it('should search medium datasets efficiently', () => {
        const { products } = InventoryTestHelpers.createPerformanceTestData('medium');
        useProductStore.setState({ products });

        const store = useProductStore.getState();

        const { duration } = PerformanceTestHelper.measure(() => {
          return store.searchProducts('Color Perfect');
        });

        PerformanceTestHelper.assertPerformance(
          duration,
          PERFORMANCE_THRESHOLDS.medium.search,
          'medium dataset search'
        );
      });

      it('should search large datasets within acceptable limits', () => {
        const { products } = InventoryTestHelpers.createPerformanceTestData('large');
        useProductStore.setState({ products });

        const store = useProductStore.getState();

        const { duration } = PerformanceTestHelper.measure(() => {
          return store.searchProducts('Matrix');
        });

        PerformanceTestHelper.assertPerformance(
          duration,
          PERFORMANCE_THRESHOLDS.large.search,
          'large dataset search'
        );
      });

      it('should handle partial matches efficiently', () => {
        const { products } = InventoryTestHelpers.createPerformanceTestData('medium');
        useProductStore.setState({ products });

        const store = useProductStore.getState();

        const searchTerms = ['W', 'Co', 'Tin', '7.1', 'Perfect'];

        searchTerms.forEach(term => {
          const { duration } = PerformanceTestHelper.measure(() => {
            return store.searchProducts(term);
          });

          expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.medium.search * 1.5);
        });
      });
    });

    describe('CRUD Operations Performance', () => {
      it('should handle bulk product additions efficiently', async () => {
        const store = useProductStore.getState();
        useProductStore.setState({ products: [] });

        // Mock successful Supabase operations
        // eslint-disable-next-line @typescript-eslint/no-var-requires
        const mockSupabase = require('@/lib/supabase');
        mockSupabase.supabase.from.mockReturnValue({
          insert: jest.fn().mockReturnThis(),
          select: jest.fn().mockReturnThis(),
          single: jest.fn().mockResolvedValue({
            data: { id: 'new-id' },
            error: null,
          }),
        });

        const productsToAdd = InventoryMockFactory.createProductSet(50);

        const { duration } = await PerformanceTestHelper.measureAsync(async () => {
          const promises = productsToAdd.map(product => store.addProduct(product));
          return Promise.allSettled(promises);
        });

        PerformanceTestHelper.assertPerformance(
          duration,
          PERFORMANCE_THRESHOLDS.medium.crud * 2, // Allow more time for network operations
          'bulk product additions'
        );
      });

      it('should handle bulk updates efficiently', async () => {
        const { products } = InventoryTestHelpers.createPerformanceTestData('medium');
        useProductStore.setState({ products });

        const store = useProductStore.getState();

        // Mock successful updates
        // eslint-disable-next-line @typescript-eslint/no-var-requires
        const mockSupabase = require('@/lib/supabase');
        mockSupabase.supabase.from.mockReturnValue({
          update: jest.fn().mockReturnThis(),
          eq: jest.fn().mockResolvedValue({ error: null }),
        });

        const updates = products.slice(0, 25).map(p => ({
          id: p.id,
          updates: { currentStock: p.currentStock + 50 },
        }));

        const { duration } = await PerformanceTestHelper.measureAsync(async () => {
          const promises = updates.map(({ id, updates }) => store.updateProduct(id, updates));
          return Promise.allSettled(promises);
        });

        PerformanceTestHelper.assertPerformance(
          duration,
          PERFORMANCE_THRESHOLDS.medium.crud * 1.5,
          'bulk product updates'
        );
      });
    });

    describe('Display Name Generation', () => {
      it('should generate display names efficiently for large datasets', () => {
        const products = InventoryMockFactory.createProductSet(1000);
        const store = useProductStore.getState();

        const { duration } = PerformanceTestHelper.measure(() => {
          products.forEach(product => {
            store.generateDisplayName(product);
          });
        });

        PerformanceTestHelper.assertPerformance(
          duration,
          100, // Should be very fast for text operations
          'display name generation for 1000 products'
        );
      });
    });
  });

  describe('Brand Category Store Performance', () => {
    describe('Filtering Performance', () => {
      it('should filter large datasets efficiently', () => {
        const { products } = InventoryTestHelpers.createPerformanceTestData('large');
        const store = useBrandCategoryStore.getState();

        // Test different filter combinations
        const filterTests = [
          { type: 'categories', value: ['tinte', 'oxidante'] },
          { type: 'brands', value: ['Wella', "L'Oreal"] },
          { type: 'stockStatus', value: 'low' },
          { type: 'searchQuery', value: 'Color Perfect' },
        ];

        filterTests.forEach(({ type, value }) => {
          store.setFilter(type as any, value as any);

          const { duration } = PerformanceTestHelper.measure(() => {
            return store.getFilteredAndSortedProducts(products, movements);
          });

          PerformanceTestHelper.assertPerformance(
            duration,
            PERFORMANCE_THRESHOLDS.large.filter,
            `filtering by ${type}`
          );
        });
      });

      it('should handle complex filter combinations', () => {
        const { products } = InventoryTestHelpers.createPerformanceTestData('medium');
        const store = useBrandCategoryStore.getState();

        // Apply multiple filters
        store.setFilter('categories', ['tinte']);
        store.setFilter('brands', ['Wella', 'Matrix']);
        store.setFilter('stockStatus', 'ok');
        store.setFilter('searchQuery', 'Perfect');

        const { duration } = PerformanceTestHelper.measure(() => {
          return store.getFilteredAndSortedProducts(products, movements);
        });

        PerformanceTestHelper.assertPerformance(
          duration,
          PERFORMANCE_THRESHOLDS.medium.filter * 1.5, // Allow extra time for complex filtering
          'complex filter combination'
        );
      });
    });

    describe('Sorting Performance', () => {
      it('should sort large datasets efficiently', () => {
        const { products } = InventoryTestHelpers.createPerformanceTestData('large');
        const store = useBrandCategoryStore.getState();

        const sortMethods: Array<typeof store.sortBy> = [
          'name',
          'stock',
          'price',
          'brand',
          'usage',
        ];

        sortMethods.forEach(sortBy => {
          store.setSortBy(sortBy);

          const { duration } = PerformanceTestHelper.measure(() => {
            return store.getFilteredAndSortedProducts(products, movements);
          });

          PerformanceTestHelper.assertPerformance(
            duration,
            PERFORMANCE_THRESHOLDS.large.sort,
            `sorting by ${sortBy}`
          );
        });
      });

      it('should handle usage-based sorting efficiently', () => {
        const { products, movements } = InventoryTestHelpers.createPerformanceTestData('medium');
        const store = useBrandCategoryStore.getState();

        store.setSortBy('usage');

        const { duration } = PerformanceTestHelper.measure(() => {
          return store.getFilteredAndSortedProducts(products, movements);
        });

        PerformanceTestHelper.assertPerformance(
          duration,
          PERFORMANCE_THRESHOLDS.medium.sort * 2, // Usage sorting is more complex
          'usage-based sorting'
        );
      });
    });

    describe('Grouping Performance', () => {
      it('should group large datasets efficiently', () => {
        const { products } = InventoryTestHelpers.createPerformanceTestData('large');
        const store = useBrandCategoryStore.getState();

        const groupMethods: Array<typeof store.groupBy> = [
          'none',
          'brand',
          'line',
          'category',
          'type',
        ];

        groupMethods.forEach(groupBy => {
          store.setGroupBy(groupBy);

          const { duration } = PerformanceTestHelper.measure(() => {
            return store.getGroupedProducts(products, movements);
          });

          PerformanceTestHelper.assertPerformance(
            duration,
            PERFORMANCE_THRESHOLDS.large.sort, // Similar complexity to sorting
            `grouping by ${groupBy}`
          );
        });
      });

      it('should create hierarchical structure efficiently', () => {
        const { products } = InventoryTestHelpers.createPerformanceTestData('medium');
        const store = useBrandCategoryStore.getState();

        const { duration } = PerformanceTestHelper.measure(() => {
          return store.getProductHierarchy(products);
        });

        PerformanceTestHelper.assertPerformance(
          duration,
          PERFORMANCE_THRESHOLDS.medium.sort,
          'hierarchical structure creation'
        );
      });
    });

    describe('Analysis Performance', () => {
      it('should analyze brands efficiently', () => {
        const { products } = InventoryTestHelpers.createPerformanceTestData('large');
        const store = useBrandCategoryStore.getState();

        const { duration } = PerformanceTestHelper.measure(() => {
          return store.getAllBrands(products);
        });

        PerformanceTestHelper.assertPerformance(
          duration,
          PERFORMANCE_THRESHOLDS.large.filter,
          'brand analysis'
        );
      });

      it('should find frequently used products efficiently', () => {
        const { products, movements } = InventoryTestHelpers.createPerformanceTestData('large');
        const store = useBrandCategoryStore.getState();

        const { duration } = PerformanceTestHelper.measure(() => {
          return store.getFrequentlyUsedProducts(products, movements, 20);
        });

        PerformanceTestHelper.assertPerformance(
          duration,
          PERFORMANCE_THRESHOLDS.large.filter * 2, // More complex analysis
          'frequently used products analysis'
        );
      });
    });
  });

  describe('Stock Store Performance', () => {
    describe('Stock Updates', () => {
      it('should handle concurrent stock updates efficiently', async () => {
        const store = useStockStore.getState();

        // Mock Supabase operations
        // eslint-disable-next-line @typescript-eslint/no-var-requires
        const mockSupabase = require('@/lib/supabase');
        mockSupabase.supabase.from.mockReturnValue({
          insert: jest.fn().mockResolvedValue({ error: null }),
        });

        const productIds = Array.from({ length: 100 }, (_, i) => `product-${i}`);

        const { duration } = await PerformanceTestHelper.measureAsync(async () => {
          const promises = productIds.map((productId, i) =>
            store.updateStock(productId, -Math.random() * 50, 'use', `Service ${i}`)
          );
          return Promise.allSettled(promises);
        });

        PerformanceTestHelper.assertPerformance(
          duration,
          PERFORMANCE_THRESHOLDS.medium.crud,
          'concurrent stock updates'
        );
      });

      it('should process bulk consumption efficiently', async () => {
        const store = useStockStore.getState();
        const consumptions = Array.from({ length: 200 }, (_, i) => ({
          productId: `product-${i % 50}`,
          quantity: Math.random() * 30 + 10,
        }));

        const mockUpdateStock = jest.spyOn(store, 'updateStock').mockResolvedValue(undefined);

        const { duration } = await PerformanceTestHelper.measureAsync(async () => {
          return store.consumeProducts(consumptions, 'bulk-service', 'Bulk Client');
        });

        PerformanceTestHelper.assertPerformance(
          duration,
          PERFORMANCE_THRESHOLDS.medium.crud,
          'bulk product consumption'
        );

        mockUpdateStock.mockRestore();
      });
    });

    describe('Movement Analysis', () => {
      it('should calculate metrics efficiently for large movement sets', () => {
        const { products } = InventoryTestHelpers.createPerformanceTestData('large');
        const store = useStockStore.getState();

        const { duration } = PerformanceTestHelper.measure(() => {
          return store.calculateStockMetrics(products);
        });

        PerformanceTestHelper.assertPerformance(
          duration,
          PERFORMANCE_THRESHOLDS.large.filter,
          'stock metrics calculation'
        );
      });

      it('should identify most used products efficiently', () => {
        const { products, movements } = InventoryTestHelpers.createPerformanceTestData('large');
        const store = useStockStore.getState();
        useStockStore.setState({ movements });

        const { duration } = PerformanceTestHelper.measure(() => {
          return store.calculateMostUsedProducts(products);
        });

        PerformanceTestHelper.assertPerformance(
          duration,
          PERFORMANCE_THRESHOLDS.large.filter * 1.5,
          'most used products calculation'
        );
      });
    });

    describe('Mock Data Generation', () => {
      it('should generate mock movements efficiently', () => {
        const { products } = InventoryTestHelpers.createPerformanceTestData('medium');
        const store = useStockStore.getState();

        const { duration } = PerformanceTestHelper.measure(() => {
          return store.generateMockMovements(products);
        });

        PerformanceTestHelper.assertPerformance(
          duration,
          200, // Should be fast for data generation
          'mock movements generation'
        );
      });
    });
  });

  describe('Analytics Store Performance', () => {
    describe('Report Generation', () => {
      it('should generate comprehensive reports efficiently', () => {
        const { products, movements } = InventoryTestHelpers.createPerformanceTestData('large');
        const store = useInventoryAnalyticsStore.getState();

        const { duration } = PerformanceTestHelper.measure(() => {
          return store.generateInventoryReport(products, movements);
        });

        PerformanceTestHelper.assertPerformance(
          duration,
          PERFORMANCE_THRESHOLDS.large.report,
          'comprehensive inventory report generation'
        );
      });

      it('should handle multiple report generations efficiently', () => {
        const { products, movements } = InventoryTestHelpers.createPerformanceTestData('medium');
        const store = useInventoryAnalyticsStore.getState();

        const { duration } = PerformanceTestHelper.measure(() => {
          // Generate 5 reports in succession
          for (let i = 0; i < 5; i++) {
            store.generateInventoryReport(products, movements);
          }
        });

        PerformanceTestHelper.assertPerformance(
          duration,
          PERFORMANCE_THRESHOLDS.medium.report * 2, // Should benefit from some optimization
          'multiple report generations'
        );
      });
    });

    describe('Business Intelligence', () => {
      it('should calculate inventory turnover efficiently', () => {
        const { products, movements } = InventoryTestHelpers.createPerformanceTestData('large');
        const store = useInventoryAnalyticsStore.getState();

        const { duration } = PerformanceTestHelper.measure(() => {
          return store.getInventoryTurnover(products, movements);
        });

        PerformanceTestHelper.assertPerformance(
          duration,
          PERFORMANCE_THRESHOLDS.large.report * 0.5,
          'inventory turnover calculation'
        );
      });

      it('should analyze seasonal trends efficiently', () => {
        const { movements } = InventoryTestHelpers.createPerformanceTestData('large');
        const store = useInventoryAnalyticsStore.getState();

        const { duration } = PerformanceTestHelper.measure(() => {
          return store.getSeasonalTrends(movements, 12);
        });

        PerformanceTestHelper.assertPerformance(
          duration,
          PERFORMANCE_THRESHOLDS.large.report * 0.3,
          'seasonal trends analysis'
        );
      });

      it('should calculate efficiency metrics efficiently', () => {
        const { products, movements } = InventoryTestHelpers.createPerformanceTestData('large');
        const store = useInventoryAnalyticsStore.getState();

        const { duration } = PerformanceTestHelper.measure(() => {
          return store.getStockEfficiencyMetrics(products, movements);
        });

        PerformanceTestHelper.assertPerformance(
          duration,
          PERFORMANCE_THRESHOLDS.large.report * 0.7,
          'stock efficiency metrics'
        );
      });
    });

    describe('Alert Generation', () => {
      it('should generate stock alerts efficiently', () => {
        const { products } = InventoryTestHelpers.createPerformanceTestData('large');
        const store = useInventoryAnalyticsStore.getState();

        const { duration } = PerformanceTestHelper.measure(() => {
          return store.getStockAlerts(products);
        });

        PerformanceTestHelper.assertPerformance(
          duration,
          PERFORMANCE_THRESHOLDS.large.filter,
          'stock alerts generation'
        );
      });
    });
  });

  describe('Facade Performance', () => {
    describe('Cross-Store Coordination', () => {
      it('should coordinate operations efficiently', async () => {
        const scenario = InventoryTestHelpers.createTestScenario('busy_month');
        const _mockState = MockStoreHelper.createMockStoreState(scenario);

        // Mock all store getState methods
        const mockStores = {
          product: mockState,
          brandCategory: mockState,
          stock: mockState,
          analytics: {
            ...mockState,
            generateInventoryReport: jest.fn().mockReturnValue({
              totalValue: 1000,
              lowStockCount: 2,
              outOfStockCount: 1,
              overstockCount: 0,
              mostUsedProducts: [],
              leastUsedProducts: [],
              costByCategory: [],
              generatedAt: new Date().toISOString(),
            }),
          },
        };

        jest.doMock('../product-store.new', () => ({
          useProductStore: { getState: () => mockStores.product },
        }));
        jest.doMock('../brand-category-store.new', () => ({
          useBrandCategoryStore: { getState: () => mockStores.brandCategory },
        }));
        jest.doMock('../stock-store.new', () => ({
          useStockStore: { getState: () => mockStores.stock },
        }));
        jest.doMock('../inventory-analytics-store', () => ({
          useInventoryAnalyticsStore: { getState: () => mockStores.analytics },
        }));

        // eslint-disable-next-line @typescript-eslint/no-var-requires
        const { useInventoryStore: TestInventoryStore } = require('../inventory-store-facade');
        const store = TestInventoryStore.getState();

        const { duration } = PerformanceTestHelper.measure(() => {
          // Perform multiple facade operations
          store.generateInventoryReport();
          store.getFilteredAndSortedProducts();
          store.getTotalInventoryValue();
          store.getActiveAlerts();
        });

        PerformanceTestHelper.assertPerformance(
          duration,
          PERFORMANCE_THRESHOLDS.medium.report,
          'facade cross-store coordination'
        );
      });
    });

    describe('State Composition', () => {
      it('should compose state efficiently from multiple stores', () => {
        const scenario = InventoryTestHelpers.createTestScenario('salon_startup');
        const _mockState = MockStoreHelper.createMockStoreState(scenario);

        // Create larger datasets to test composition performance
        const largeScenario = {
          ...scenario,
          products: InventoryMockFactory.createProductSet(1000),
          movements: scenario.movements.concat(
            InventoryMockFactory.createMovementsForProducts(
              scenario.products.map(p => p.id),
              { movementsPerProduct: 20 }
            )
          ),
        };

        const largeMockState = MockStoreHelper.createMockStoreState(largeScenario);

        const { duration } = PerformanceTestHelper.measure(() => {
          // Access composed state multiple times
          for (let i = 0; i < 100; i++) {
            const products = largeMockState.products;
            const movements = largeMockState.movements;
            const alerts = largeMockState.alerts;
            const isLoading = largeMockState.isLoading;

            // Ensure the access actually happens
            expect(products).toBeDefined();
            expect(movements).toBeDefined();
            expect(alerts).toBeDefined();
            expect(typeof isLoading).toBe('boolean');
          }
        });

        PerformanceTestHelper.assertPerformance(
          duration,
          50, // State composition should be very fast
          'state composition from multiple stores'
        );
      });
    });
  });

  describe('Memory Performance', () => {
    it('should handle large datasets without excessive memory usage', () => {
      const initialHeap = (performance as any).memory?.usedJSHeapSize || 0;

      // Create and process large dataset
      const { products, movements } = InventoryTestHelpers.createPerformanceTestData('xlarge');

      // Perform various operations that might cause memory issues
      const _productStore = useProductStore.getState();
      useProductStore.setState({ products });

      const brandStore = useBrandCategoryStore.getState();
      brandStore.getFilteredAndSortedProducts(products, movements);
      brandStore.getGroupedProducts(products, movements);

      const analyticsStore = useInventoryAnalyticsStore.getState();
      analyticsStore.generateInventoryReport(products, movements);

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      const finalHeap = (performance as any).memory?.usedJSHeapSize || 0;
      const memoryIncrease = finalHeap - initialHeap;

      // Memory increase should be reasonable (less than 100MB for test data)
      if (memoryIncrease > 0) {
        expect(memoryIncrease).toBeLessThan(100 * 1024 * 1024); // 100MB
      }
    });

    it('should clean up memory when clearing data', () => {
      const initialHeap = (performance as any).memory?.usedJSHeapSize || 0;

      // Create large dataset
      const { products, movements } = InventoryTestHelpers.createPerformanceTestData('large');

      // Store data in all stores
      useProductStore.setState({
        products,
        productMappings: InventoryMockFactory.createProductSet(100).map(p =>
          InventoryMockFactory.createProductMapping({ inventoryProductId: p.id })
        ),
      });
      useStockStore.setState({ movements });

      const midHeap = (performance as any).memory?.usedJSHeapSize || 0;

      // Clear all data
      useProductStore.getState().clearAllData();
      useStockStore.getState().clearMovements();
      useInventoryAnalyticsStore.getState().clearAnalyticsCache();

      // Force garbage collection
      if (global.gc) {
        global.gc();
      }

      const finalHeap = (performance as any).memory?.usedJSHeapSize || 0;

      // Memory should decrease after clearing (or at least not continue growing)
      if (midHeap > initialHeap) {
        expect(finalHeap).toBeLessThanOrEqual(midHeap * 1.1); // Allow 10% tolerance
      }
    });
  });

  describe('Real-world Stress Tests', () => {
    it('should handle peak salon hours simulation', async () => {
      // Simulate peak hours: multiple concurrent services, stock updates, reports
      const { products } = InventoryTestHelpers.createPerformanceTestData('medium');
      const store = useInventoryStore.getState();

      // Mock store states
      // eslint-disable-next-line @typescript-eslint/no-var-requires
      const mockStores = require('../inventory-store-facade');
      jest.spyOn(mockStores.useProductStore, 'getState').mockReturnValue({
        products,
        updateProduct: jest.fn().mockResolvedValue(undefined),
        getProduct: jest.fn().mockImplementation(id => products.find(p => p.id === id)),
      });
      jest.spyOn(mockStores.useStockStore, 'getState').mockReturnValue({
        updateStock: jest.fn().mockResolvedValue(undefined),
        consumeProducts: jest.fn().mockResolvedValue(undefined),
        movements: [],
        checkLowStock: jest.fn(),
      });

      const { duration } = await PerformanceTestHelper.measureAsync(async () => {
        // Simulate 10 concurrent services
        const servicePromises = Array.from({ length: 10 }, async (_, i) => {
          const consumptions = Array.from({ length: 5 }, (_, j) => ({
            productId: products[j].id,
            quantity: Math.random() * 30 + 10,
          }));

          return store.consumeProducts(consumptions, `service-${i}`, `Client ${i}`);
        });

        // Simulate concurrent reporting
        const reportPromises = Array.from({ length: 3 }, () => store.generateInventoryReport());

        await Promise.allSettled([...servicePromises, ...reportPromises]);
      });

      PerformanceTestHelper.assertPerformance(
        duration,
        3000, // Allow 3 seconds for complex concurrent operations
        'peak salon hours simulation'
      );
    });

    it('should handle inventory audit workflow', () => {
      const { products, movements } = InventoryTestHelpers.createPerformanceTestData('large');

      const { duration } = PerformanceTestHelper.measure(() => {
        const brandStore = useBrandCategoryStore.getState();
        const analyticsStore = useInventoryAnalyticsStore.getState();

        // Full inventory audit operations
        brandStore.getAllBrands(products);
        brandStore.getAllCategories(products);
        brandStore.getFrequentlyUsedProducts(products, movements, 50);

        analyticsStore.generateInventoryReport(products, movements);
        analyticsStore.getInventoryTurnover(products, movements);
        analyticsStore.getStockEfficiencyMetrics(products, movements);
        analyticsStore.getStockAlerts(products);
      });

      PerformanceTestHelper.assertPerformance(
        duration,
        2000, // Allow 2 seconds for comprehensive audit
        'inventory audit workflow'
      );
    });
  });
});
