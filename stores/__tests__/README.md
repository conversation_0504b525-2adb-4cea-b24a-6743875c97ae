# Inventory Stores Test Suite

Comprehensive test coverage for the modular inventory store architecture, ensuring production readiness and maintaining backward compatibility with the original monolithic store.

## 🏗️ Architecture Overview

The test suite covers the new modular inventory architecture:

```
┌─────────────────────────────────────┐
│        Inventory Store Facade       │ ← 100% API Compatibility
├─────────────────────────────────────┤
│  Product Store  │  Brand Category   │
│  - CRUD Ops     │  - Filtering      │
│  - AI Mappings  │  - Sorting        │
│  - Sync         │  - Grouping       │
├─────────────────┼───────────────────┤
│   Stock Store   │ Analytics Store   │
│  - Movements    │  - Reports        │
│  - Consumption  │  - Business Intel │
│  - Alerts       │  - Metrics        │
└─────────────────────────────────────┘
```

## 📋 Test Structure

### Core Store Tests

- **`product-store.test.ts`** - Product CRUD, search, AI mappings, sync
- **`brand-category-store.test.ts`** - Filtering, sorting, grouping, hierarchical organization
- **`stock-store.test.ts`** - Stock movements, consumption, alerts, analytics
- **`inventory-analytics-store.test.ts`** - Reports, metrics, business intelligence

### Integration Tests

- **`inventory-facade-integration.test.ts`** - Cross-store coordination and API compatibility
- **`performance.test.ts`** - Performance benchmarks and stress testing

### Utilities

- **`test-utils.ts`** - Mock factories, custom matchers, test helpers
- **`stores.setup.js`** - Enhanced test environment configuration

## 🚀 Running Tests

### Quick Start

```bash
# Run all inventory store tests
npm run test:stores

# Run specific test suite
npm test stores/__tests__/product-store.test.ts

# Run with coverage
npm test stores/__tests__ -- --coverage

# Run integration tests only
node scripts/test-inventory-stores.js --suite=integration
```

### Advanced Usage

```bash
# Run comprehensive test suite with performance monitoring
node scripts/test-inventory-stores.js

# Run specific test categories
node scripts/test-inventory-stores.js --suite=unit
node scripts/test-inventory-stores.js --suite=facade
node scripts/test-inventory-stores.js --suite=performance

# Watch mode for development
npm test stores/__tests__ -- --watch

# Debug mode
npm test stores/__tests__ -- --verbose --no-coverage
```

## 📊 Coverage Targets

### Minimum Thresholds

| Metric     | Target | Critical Files Target |
|------------|--------|-----------------------|
| Statements | 90%    | 95%                  |
| Branches   | 85%    | 90%                  |
| Functions  | 90%    | 95%                  |
| Lines      | 90%    | 95%                  |

### Critical Files

- `product-store.new.ts`
- `inventory-store-facade.ts`
- All store integration points

## ⚡ Performance Benchmarks

### Performance Thresholds

| Dataset Size | Search | Filter | Sort | Reports | CRUD |
|--------------|--------|--------|------|---------|------|
| Small (10)   | 10ms   | 15ms   | 20ms | 50ms    | 25ms |
| Medium (100) | 50ms   | 75ms   | 100ms| 200ms   | 100ms|
| Large (1K)   | 200ms  | 300ms  | 400ms| 800ms   | 500ms|
| XLarge (10K) | 1000ms | 1500ms | 2000ms| 3000ms | 2000ms|

### Stress Testing

- **Concurrent Operations**: 100 simultaneous updates
- **Bulk Operations**: 1000+ products processed
- **Memory Efficiency**: <500MB peak usage
- **Cache Performance**: >40% hit rate

## 🧪 Test Categories

### 1. Unit Tests

Testing individual store functionality in isolation:

- **Product Store**: CRUD operations, search algorithms, display name generation
- **Brand Category Store**: Filtering logic, sorting algorithms, grouping methods
- **Stock Store**: Movement tracking, consumption calculation, alert generation
- **Analytics Store**: Report generation, metrics calculation, caching

### 2. Integration Tests

Testing cross-store coordination:

- **Facade Coordination**: Operations spanning multiple stores
- **Data Consistency**: State synchronization across stores
- **Event Coordination**: Alerts and updates propagation
- **Backward Compatibility**: Original API compliance

### 3. Performance Tests

Validating performance under various conditions:

- **Load Testing**: Large dataset processing
- **Concurrency Testing**: Simultaneous operations
- **Memory Testing**: Resource usage optimization
- **Real-world Scenarios**: Salon workflows simulation

### 4. Edge Case Tests

Handling exceptional conditions:

- **Offline Behavior**: Network unavailable scenarios
- **Data Corruption**: Invalid or malformed data
- **Concurrent Modifications**: Race condition handling
- **Error Recovery**: Graceful failure management

## 🛠️ Test Utilities

### Mock Factories

```typescript
import { InventoryMockFactory } from './test-utils';

// Create realistic test data
const products = InventoryMockFactory.createProductSet(50, {
  diverseStock: true,
  sameBrand: false,
});

const movements = InventoryMockFactory.createMovementsForProducts(
  products.map(p => p.id),
  { daysBack: 30, includeUsage: true }
);
```

### Custom Matchers

```typescript
// Validate product structure
expect(product).toBeValidProduct();

// Check stock status
expect(product).toHaveStockStatus('low');

// Performance assertions
expect(duration).toBeWithinRange(0, 100);

// Date validations
expect(product.lastUpdated).toBeRecentDate(7);
```

### Test Scenarios

```typescript
import { InventoryTestHelpers } from './test-utils';

// Pre-built realistic scenarios
const scenario = InventoryTestHelpers.createTestScenario('busy_month');
const { products, movements, alerts } = scenario;
```

## 📈 Performance Monitoring

### Automatic Performance Tracking

Tests automatically track:

- **Execution Time**: Individual test and suite durations
- **Memory Usage**: Heap utilization during tests
- **Slow Tests**: Operations exceeding thresholds
- **Resource Cleanup**: Memory leaks detection

### Performance Reports

Generated automatically:

- **JSON Report**: Detailed metrics for CI/CD
- **Markdown Summary**: Human-readable overview
- **Coverage Reports**: HTML and LCOV formats
- **Performance Graphs**: Trend analysis over time

## 🔧 Configuration

### Jest Configuration

Custom configuration in `jest.config.stores.js`:

- **Enhanced coverage reporting**
- **Performance monitoring**
- **Memory leak detection**
- **Custom test environment**

### Environment Variables

```bash
# Test environment
NODE_ENV=test

# Performance thresholds
PERFORMANCE_THRESHOLD_MS=100
MEMORY_THRESHOLD_MB=500

# Coverage requirements
COVERAGE_STATEMENTS=90
COVERAGE_BRANCHES=85
```

## 🚨 Quality Gates

### Pre-commit Checks

- All tests must pass
- Coverage thresholds must be met
- Performance benchmarks must pass
- No memory leaks detected

### CI/CD Integration

```yaml
# GitHub Actions example
- name: Run Inventory Store Tests
  run: node scripts/test-inventory-stores.js
  
- name: Upload Coverage
  uses: codecov/codecov-action@v3
  with:
    files: ./coverage/stores/lcov.info
```

## 🐛 Debugging

### Common Issues

1. **Test Timeouts**
   ```bash
   # Increase timeout for performance tests
   npm test -- --testTimeout=60000
   ```

2. **Memory Issues**
   ```bash
   # Enable garbage collection
   node --expose-gc node_modules/.bin/jest
   ```

3. **Mock Problems**
   ```bash
   # Clear all mocks between tests
   global.testHelpers.resetAllStores();
   ```

### Debug Mode

```bash
# Verbose output with debug information
npm test stores/__tests__ -- --verbose --detectOpenHandles

# Single test with full debugging
npm test stores/__tests__/product-store.test.ts -- --verbose --no-coverage
```

## 📊 Reporting

### Test Reports Location

```
test-results/stores/
├── junit.xml                    # CI/CD compatible results
├── report.html                  # Interactive HTML report  
├── inventory-stores-summary.md  # Human-readable summary
└── inventory-stores-test-report.json # Detailed JSON data
```

### Coverage Reports Location

```
coverage/stores/
├── lcov.info           # LCOV format for tools
├── coverage-final.json # Raw coverage data
└── lcov-report/       # HTML coverage browser
    └── index.html
```

## 🔄 Continuous Integration

### Recommended Workflow

1. **Pre-push**: Run unit tests locally
2. **PR Validation**: Full test suite with coverage
3. **Merge Protection**: Require passing tests
4. **Post-merge**: Performance regression detection
5. **Nightly**: Extended stress testing

### Performance Regression Detection

Automated monitoring for:

- Test execution time increases >20%
- Memory usage increases >15%
- Coverage decreases below thresholds
- New performance anti-patterns

## 🎯 Best Practices

### Writing Tests

1. **Use descriptive test names** that explain the behavior
2. **Test behavior, not implementation** details
3. **Use realistic mock data** from factories
4. **Group related tests** with clear describe blocks
5. **Clean up after tests** to prevent interference

### Performance Considerations

1. **Batch similar operations** to reduce setup overhead
2. **Use appropriate dataset sizes** for different test types
3. **Mock external dependencies** to focus on store logic
4. **Monitor memory usage** in long-running tests
5. **Profile slow tests** to identify bottlenecks

### Maintenance

1. **Update test data** when business logic changes
2. **Review performance thresholds** quarterly
3. **Refactor tests** when they become brittle
4. **Document test scenarios** for future maintainers
5. **Keep mocks synchronized** with real implementations

## 🔮 Future Enhancements

### Planned Improvements

- **Visual regression testing** for UI components
- **API contract testing** for backend integration
- **Cross-browser compatibility** testing
- **Accessibility testing** integration
- **Security vulnerability** scanning

### Monitoring Integration

- **Real-time performance** dashboards
- **Test flakiness** detection and reporting
- **Coverage trend** analysis
- **Performance regression** alerts
- **Test maintenance** recommendations

---

## 📞 Support

For questions or issues with the test suite:

1. **Check the console output** for detailed error messages
2. **Review the generated reports** for insights
3. **Run individual test files** to isolate problems
4. **Use debug mode** for detailed execution traces
5. **Check the GitHub issues** for known problems

Remember: These tests are the safety net ensuring the reliability and performance of Salonier's inventory system. Maintain them with the same care as the production code! 🎯