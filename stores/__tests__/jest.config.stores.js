/**
 * Jest Configuration for Inventory Stores Testing
 *
 * Specialized configuration for comprehensive store testing with
 * enhanced performance monitoring and coverage reporting.
 */

const baseConfig = require('../../jest.config.js');

module.exports = {
  ...baseConfig,

  // Focus on stores directory for this configuration
  testMatch: ['<rootDir>/stores/__tests__/**/*.test.(ts|tsx|js|jsx)'],

  // Collect coverage specifically for stores
  collectCoverageFrom: [
    'stores/**/*.{ts,tsx}',
    '!stores/**/*.d.ts',
    '!stores/__tests__/**',
    '!stores/**/index.ts',
  ],

  // Enhanced coverage thresholds for production readiness
  coverageThreshold: {
    global: {
      branches: 85,
      functions: 90,
      lines: 90,
      statements: 90,
    },
    // Specific thresholds for critical files
    'stores/product-store.new.ts': {
      branches: 90,
      functions: 95,
      lines: 95,
      statements: 95,
    },
    'stores/inventory-store-facade.ts': {
      branches: 85,
      functions: 90,
      lines: 90,
      statements: 90,
    },
  },

  // Enhanced reporting for stores
  coverageReporters: [
    'text',
    'text-summary',
    'lcov',
    'html',
    'json-summary',
    'cobertura', // For CI/CD integration
  ],

  // Custom coverage directory for stores
  coverageDirectory: '<rootDir>/coverage/stores',

  // Setup files specific to store testing
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js', '<rootDir>/stores/__tests__/stores.setup.js'],

  // Performance testing configuration
  testTimeout: 30000, // 30 seconds for performance tests

  // Custom test environment for stores
  testEnvironment: 'jsdom',

  // Memory and performance monitoring
  logHeapUsage: true,
  detectOpenHandles: true,
  detectLeaks: true,

  // Enhanced error handling
  verbose: true,
  bail: false, // Continue running tests even if some fail

  // Custom reporters for better visibility
  reporters: [
    'default',
    [
      'jest-junit',
      {
        outputDirectory: '<rootDir>/test-results/stores',
        outputName: 'junit.xml',
        ancestorSeparator: ' › ',
        uniqueOutputName: 'false',
        suiteNameTemplate: '{filepath}',
        classNameTemplate: '{classname}',
        titleTemplate: '{title}',
      },
    ],
    [
      'jest-html-reporters',
      {
        publicPath: '<rootDir>/test-results/stores',
        filename: 'report.html',
        expand: true,
      },
    ],
  ],

  // Module name mapping for stores testing
  moduleNameMapper: {
    ...baseConfig.moduleNameMapper,
    '^@/stores/(.*)$': '<rootDir>/stores/$1',
    '^@/types/(.*)$': '<rootDir>/types/$1',
    '^@/lib/(.*)$': '<rootDir>/lib/$1',
    '^@/utils/(.*)$': '<rootDir>/utils/$1',
    '^@/constants/(.*)$': '<rootDir>/constants/$1',
    '^@/data/(.*)$': '<rootDir>/data/$1',
    '^@/services/(.*)$': '<rootDir>/services/$1',
  },

  // Transform ignore patterns for stores
  transformIgnorePatterns: ['node_modules/(?!(@faker-js|zustand|react-native|@react-native|expo))'],

  // Global setup for performance monitoring
  globalSetup: '<rootDir>/stores/__tests__/performance.setup.js',
  globalTeardown: '<rootDir>/stores/__tests__/performance.teardown.js',

  // Test categories via runner configuration
  runner: 'jest-runner',

  // Custom matchers and utilities
  testEnvironmentOptions: {
    url: 'https://localhost',
  },

  // Error handling
  errorOnDeprecated: true,

  // Custom test sequencer for optimal performance
  testSequencer: '<rootDir>/stores/__tests__/test-sequencer.js',

  // Watch mode configuration
  watchPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/coverage/',
    '<rootDir>/test-results/',
  ],

  // Cache configuration
  cacheDirectory: '<rootDir>/.jest-cache/stores',

  // Memory optimization
  maxWorkers: '50%',
  workerIdleMemoryLimit: '1GB',
};
