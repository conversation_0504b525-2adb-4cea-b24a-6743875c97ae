/**
 * Comprehensive Test Suite for Product Store
 *
 * Tests CRUD operations, search, AI mappings, sync functionality,
 * offline-first behavior, and migration utilities.
 */

import { useProductStore } from '../product-store.new';
import { useSyncQueueStore } from '../sync-queue-store';
import { supabase, getCurrentSalonId } from '@/lib/supabase';
import type { Product, ProductMapping } from '@/types/inventory';

// Mock dependencies
jest.mock('../sync-queue-store');
jest.mock('@/lib/supabase');
jest.mock('@/utils/logger');
jest.mock('@react-native-async-storage/async-storage');

// Mock data factories
const createMockProduct = (overrides: Partial<Product> = {}): Product => ({
  id: 'product-123',
  brand: 'Wella',
  line: 'Color Perfect',
  type: 'Tinte',
  shade: '7.1',
  displayName: 'Wella Color Perfect 7.1 Tinte',
  name: 'Wella Color Perfect 7.1',
  category: 'tinte',
  currentStock: 500,
  minStock: 100,
  maxStock: 1000,
  unitType: 'ml',
  unitSize: 60,
  purchasePrice: 15.5,
  costPerUnit: 0.26,
  barcode: '1234567890',
  supplier: 'Beauty Supply Co',
  notes: 'Popular shade',
  colorCode: '7.1',
  isActive: true,
  lastUpdated: '2024-01-01T00:00:00Z',
  _syncStatus: 'synced',
  ...overrides,
});

const createMockProductMapping = (overrides: Partial<ProductMapping> = {}): ProductMapping => ({
  id: 'mapping-123',
  salonId: 'salon-123',
  aiProductName: 'Rubio Medio Ceniza',
  inventoryProductId: 'product-123',
  confidence: 0.95,
  usageCount: 5,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  ...overrides,
});

const mockSupabaseProduct = {
  id: 'supabase-product-123',
  name: 'Wella Color Perfect 7.1',
  brand: 'Wella',
  category: 'tinte',
  type: 'color',
  shade: '7.1',
  stock_ml: 500,
  minimum_stock_ml: 100,
  max_stock: 1000,
  size_ml: 60,
  cost_per_unit: 0.26,
  barcode: '1234567890',
  supplier: 'Beauty Supply Co',
  notes: 'Popular shade',
  line: 'Color Perfect',
  color_code: '7.1',
  is_active: true,
  updated_at: '2024-01-01T00:00:00Z',
  salon_id: 'salon-123',
};

describe('ProductStore', () => {
  let mockSyncQueue: any;
  let mockSupabase: any;
  let store: any;

  beforeEach(() => {
    // Reset store state
    useProductStore.setState({
      products: [],
      productMappings: [],
      isLoading: false,
      lastSync: null,
    });

    // Mock sync queue
    mockSyncQueue = {
      isOnline: true,
      addToQueue: jest.fn(),
    };
    (useSyncQueueStore.getState as jest.Mock).mockReturnValue(mockSyncQueue);

    // Mock Supabase
    mockSupabase = {
      from: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnThis(),
        insert: jest.fn().mockReturnThis(),
        update: jest.fn().mockReturnThis(),
        delete: jest.fn().mockReturnThis(),
        upsert: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
        single: jest.fn(),
      }),
      rpc: jest.fn(),
    };
    (supabase as any).from = mockSupabase.from;
    (supabase as any).rpc = mockSupabase.rpc;

    // Mock getCurrentSalonId
    (getCurrentSalonId as jest.Mock).mockResolvedValue('salon-123');

    store = useProductStore.getState();

    jest.clearAllMocks();
  });

  describe('Product CRUD Operations', () => {
    describe('loadProducts', () => {
      it('should load products from Supabase successfully', async () => {
        mockSupabase.from().single.mockResolvedValue({
          data: [mockSupabaseProduct],
          error: null,
        });

        await store.loadProducts();

        expect(mockSupabase.from).toHaveBeenCalledWith('products');
        expect(useProductStore.getState().products).toHaveLength(1);
        expect(useProductStore.getState().isLoading).toBe(false);
        expect(useProductStore.getState().lastSync).toBeTruthy();
      });

      it('should handle load errors gracefully', async () => {
        mockSupabase.from().single.mockResolvedValue({
          data: null,
          error: new Error('Database error'),
        });

        await store.loadProducts();

        expect(useProductStore.getState().products).toHaveLength(0);
        expect(useProductStore.getState().isLoading).toBe(false);
      });

      it('should skip loading when no salon ID available', async () => {
        (getCurrentSalonId as jest.Mock).mockResolvedValue(null);

        await store.loadProducts();

        expect(mockSupabase.from).not.toHaveBeenCalled();
        expect(useProductStore.getState().isLoading).toBe(false);
      });

      it('should convert Supabase data to local format correctly', async () => {
        mockSupabase.from().single.mockResolvedValue({
          data: [mockSupabaseProduct],
          error: null,
        });

        await store.loadProducts();

        const product = useProductStore.getState().products[0];
        expect(product.displayName).toBe('Wella Color Perfect 7.1 (Tinte)');
        expect(product.unitType).toBe('ml');
        expect(product.category).toBe('tinte');
        expect(product._syncStatus).toBe('synced');
      });
    });

    describe('addProduct', () => {
      const newProductData = {
        brand: "L'Oreal",
        line: 'Majirel',
        type: 'Tinte',
        shade: '8.31',
        category: 'tinte' as const,
        currentStock: 300,
        minStock: 50,
        costPerUnit: 0.3,
        unitType: 'ml' as const,
        unitSize: 50,
        isActive: true,
      };

      it('should add product optimistically when online', async () => {
        mockSupabase
          .from()
          .select()
          .single.mockResolvedValue({
            data: { ...mockSupabaseProduct, id: 'new-product-id' },
            error: null,
          });

        const productId = await store.addProduct(newProductData);

        // Check optimistic update
        expect(useProductStore.getState().products).toHaveLength(1);

        // Check sync attempt
        expect(mockSupabase.from).toHaveBeenCalledWith('products');

        // Check final state after sync
        const finalProducts = useProductStore.getState().products;
        expect(finalProducts[0]._syncStatus).toBe('synced');
        expect(productId).toBe('new-product-id');
      });

      it('should add product offline with pending sync status', async () => {
        mockSyncQueue.isOnline = false;

        const _productId = await store.addProduct(newProductData);

        // Check product was added locally
        const products = useProductStore.getState().products;
        expect(products).toHaveLength(1);
        expect(products[0]._syncStatus).toBe('pending');
        expect(products[0].displayName).toContain("L'Oreal");

        // Check that sync was queued
        expect(mockSyncQueue.addToQueue).toHaveBeenCalled();
      });

      it('should generate correct displayName', async () => {
        mockSyncQueue.isOnline = false;

        await store.addProduct(newProductData);

        const product = useProductStore.getState().products[0];
        expect(product.displayName).toBe("L'Oreal Majirel 8.31 (Tinte)");
      });

      it('should handle sync errors gracefully', async () => {
        mockSupabase
          .from()
          .select()
          .single.mockResolvedValue({
            data: null,
            error: new Error('Sync failed'),
          });

        const _productId = await store.addProduct(newProductData);

        const products = useProductStore.getState().products;
        expect(products).toHaveLength(1);
        expect(products[0]._syncStatus).toBe('error');
        expect(mockSyncQueue.addToQueue).toHaveBeenCalled();
      });
    });

    describe('updateProduct', () => {
      beforeEach(() => {
        // Add a product to update
        useProductStore.setState({
          products: [createMockProduct()],
        });
      });

      it('should update product optimistically when online', async () => {
        mockSupabase.from().update.mockResolvedValue({
          error: null,
        });

        const updates = { currentStock: 250, minStock: 50 };
        await store.updateProduct('product-123', updates);

        // Check optimistic update
        const product = useProductStore.getState().products[0];
        expect(product.currentStock).toBe(250);
        expect(product.minStock).toBe(50);
        expect(product._syncStatus).toBe('synced');

        // Check sync call
        expect(mockSupabase.from).toHaveBeenCalledWith('products');
      });

      it('should regenerate displayName when relevant fields change', async () => {
        mockSupabase.from().update.mockResolvedValue({ error: null });

        const updates = { brand: 'Matrix', line: 'SoColor' };
        await store.updateProduct('product-123', updates);

        const product = useProductStore.getState().products[0];
        expect(product.displayName).toContain('Matrix SoColor');
      });

      it('should queue update when offline', async () => {
        mockSyncQueue.isOnline = false;

        const updates = { currentStock: 200 };
        await store.updateProduct('product-123', updates);

        const product = useProductStore.getState().products[0];
        expect(product.currentStock).toBe(200);
        expect(product._syncStatus).toBe('pending');
        expect(mockSyncQueue.addToQueue).toHaveBeenCalled();
      });

      it('should handle local IDs correctly', async () => {
        const localProduct = createMockProduct({
          id: 'local_123456',
          _localId: 'local_123456',
        });
        useProductStore.setState({ products: [localProduct] });

        const updates = { currentStock: 300 };
        await store.updateProduct('local_123456', updates);

        expect(mockSupabase.from).not.toHaveBeenCalled();
        expect(mockSyncQueue.addToQueue).toHaveBeenCalled();
      });
    });

    describe('deleteProduct', () => {
      beforeEach(() => {
        useProductStore.setState({
          products: [createMockProduct()],
        });
      });

      it('should delete product optimistically when online', async () => {
        mockSupabase.from().delete.mockResolvedValue({ error: null });

        await store.deleteProduct('product-123');

        expect(useProductStore.getState().products).toHaveLength(0);
        expect(mockSupabase.from).toHaveBeenCalledWith('products');
      });

      it('should restore product if delete fails', async () => {
        mockSupabase.from().delete.mockResolvedValue({
          error: new Error('Delete failed'),
        });

        await store.deleteProduct('product-123');

        // Product should be restored with error status
        const products = useProductStore.getState().products;
        expect(products).toHaveLength(1);
        expect(products[0]._syncStatus).toBe('error');
      });

      it('should queue deletion when offline', async () => {
        mockSyncQueue.isOnline = false;

        await store.deleteProduct('product-123');

        expect(useProductStore.getState().products).toHaveLength(0);
        expect(mockSyncQueue.addToQueue).toHaveBeenCalled();
      });
    });
  });

  describe('Product Search and Retrieval', () => {
    beforeEach(() => {
      const products = [
        createMockProduct({
          id: 'product-1',
          brand: 'Wella',
          name: 'Wella Color Perfect 7.1',
          displayName: 'Wella Color Perfect 7.1 Tinte',
          category: 'tinte',
          line: 'Color Perfect',
          shade: '7.1',
        }),
        createMockProduct({
          id: 'product-2',
          brand: "L'Oreal",
          name: "L'Oreal Majirel 8.31",
          displayName: "L'Oreal Majirel 8.31 Tinte",
          category: 'tinte',
          line: 'Majirel',
          shade: '8.31',
        }),
        createMockProduct({
          id: 'product-3',
          brand: 'Schwarzkopf',
          name: 'Igora 20 Vol',
          displayName: 'Schwarzkopf Igora 20 Vol Oxidante',
          category: 'oxidante',
          type: 'Oxidante',
        }),
      ];
      useProductStore.setState({ products });
    });

    describe('getProduct', () => {
      it('should retrieve product by ID', () => {
        const product = store.getProduct('product-1');
        expect(product).toBeTruthy();
        expect(product.brand).toBe('Wella');
      });

      it('should return undefined for non-existent ID', () => {
        const product = store.getProduct('non-existent');
        expect(product).toBeUndefined();
      });
    });

    describe('getProductByNameAndBrand', () => {
      it('should find product by name and brand', () => {
        const product = store.getProductByNameAndBrand('Wella Color Perfect 7.1', 'Wella');
        expect(product).toBeTruthy();
        expect(product.id).toBe('product-1');
      });

      it('should be case insensitive', () => {
        const product = store.getProductByNameAndBrand('wella color perfect 7.1', 'WELLA');
        expect(product).toBeTruthy();
        expect(product.id).toBe('product-1');
      });

      it('should return undefined when not found', () => {
        const product = store.getProductByNameAndBrand('Non Existent', 'Unknown');
        expect(product).toBeUndefined();
      });
    });

    describe('searchProducts', () => {
      it('should search across multiple fields', () => {
        const results = store.searchProducts('Wella');
        expect(results).toHaveLength(1);
        expect(results[0].brand).toBe('Wella');
      });

      it('should search by shade', () => {
        const results = store.searchProducts('7.1');
        expect(results).toHaveLength(1);
        expect(results[0].shade).toBe('7.1');
      });

      it('should search by category', () => {
        const results = store.searchProducts('oxidante');
        expect(results).toHaveLength(1);
        expect(results[0].category).toBe('oxidante');
      });

      it('should handle multiple search terms', () => {
        const results = store.searchProducts("L'Oreal Majirel");
        expect(results).toHaveLength(1);
        expect(results[0].brand).toBe("L'Oreal");
      });

      it('should return empty array for no matches', () => {
        const results = store.searchProducts('xyz123');
        expect(results).toHaveLength(0);
      });
    });

    describe('getProductsByCategory', () => {
      it('should filter products by category', () => {
        const tintes = store.getProductsByCategory('tinte');
        expect(tintes).toHaveLength(2);
        tintes.forEach(product => {
          expect(product.category).toBe('tinte');
        });
      });

      it('should return empty array for non-existent category', () => {
        const results = store.getProductsByCategory('non-existent');
        expect(results).toHaveLength(0);
      });
    });
  });

  describe('Product Mappings', () => {
    describe('saveProductMapping', () => {
      it('should save new mapping to Supabase', async () => {
        mockSupabase
          .from()
          .upsert()
          .select()
          .single.mockResolvedValue({
            data: {
              id: 'mapping-123',
              salon_id: 'salon-123',
              ai_product_name: 'Rubio Medio Ceniza',
              inventory_product_id: 'product-123',
              confidence: 0.95,
              usage_count: 1,
              created_at: '2024-01-01T00:00:00Z',
              updated_at: '2024-01-01T00:00:00Z',
            },
            error: null,
          });

        await store.saveProductMapping('Rubio Medio Ceniza', 'product-123', 0.95);

        expect(mockSupabase.from).toHaveBeenCalledWith('product_mappings');

        const mappings = useProductStore.getState().productMappings;
        expect(mappings).toHaveLength(1);
        expect(mappings[0].aiProductName).toBe('Rubio Medio Ceniza');
        expect(mappings[0].confidence).toBe(0.95);
      });

      it('should update existing mapping', async () => {
        // Set existing mapping
        useProductStore.setState({
          productMappings: [createMockProductMapping()],
        });

        mockSupabase
          .from()
          .upsert()
          .select()
          .single.mockResolvedValue({
            data: {
              id: 'mapping-123',
              salon_id: 'salon-123',
              ai_product_name: 'Rubio Medio Ceniza',
              inventory_product_id: 'product-456',
              confidence: 0.98,
              usage_count: 6,
              created_at: '2024-01-01T00:00:00Z',
              updated_at: '2024-01-02T00:00:00Z',
            },
            error: null,
          });

        await store.saveProductMapping('Rubio Medio Ceniza', 'product-456', 0.98);

        const mappings = useProductStore.getState().productMappings;
        expect(mappings).toHaveLength(1);
        expect(mappings[0].inventoryProductId).toBe('product-456');
        expect(mappings[0].confidence).toBe(0.98);
      });

      it('should handle save errors', async () => {
        mockSupabase
          .from()
          .upsert()
          .select()
          .single.mockResolvedValue({
            data: null,
            error: new Error('Save failed'),
          });

        await expect(store.saveProductMapping('Test', 'product-123', 0.5)).rejects.toThrow(
          'Save failed'
        );
      });
    });

    describe('getProductMapping', () => {
      beforeEach(() => {
        useProductStore.setState({
          productMappings: [createMockProductMapping()],
        });
      });

      it('should retrieve mapping by AI product name', () => {
        const mapping = store.getProductMapping('Rubio Medio Ceniza');
        expect(mapping).toBeTruthy();
        expect(mapping.inventoryProductId).toBe('product-123');
      });

      it('should return undefined for non-existent mapping', () => {
        const mapping = store.getProductMapping('Non Existent');
        expect(mapping).toBeUndefined();
      });
    });

    describe('incrementMappingUsage', () => {
      beforeEach(() => {
        useProductStore.setState({
          productMappings: [createMockProductMapping({ usageCount: 5 })],
        });
      });

      it('should increment usage count via RPC', async () => {
        mockSupabase.rpc.mockResolvedValue({ error: null });

        await store.incrementMappingUsage('Rubio Medio Ceniza');

        expect(mockSupabase.rpc).toHaveBeenCalledWith('increment_mapping_usage', {
          p_salon_id: 'salon-123',
          p_ai_product_name: 'Rubio Medio Ceniza',
        });

        const mapping = useProductStore.getState().productMappings[0];
        expect(mapping.usageCount).toBe(6);
      });

      it('should handle increment errors gracefully', async () => {
        mockSupabase.rpc.mockResolvedValue({
          error: new Error('RPC failed'),
        });

        await store.incrementMappingUsage('Rubio Medio Ceniza');

        // Should not throw, should handle error gracefully
        const mapping = useProductStore.getState().productMappings[0];
        expect(mapping.usageCount).toBe(5); // Unchanged
      });
    });

    describe('loadProductMappings', () => {
      it('should load all mappings from Supabase', async () => {
        mockSupabase.from().order.mockResolvedValue({
          data: [
            {
              id: 'mapping-1',
              salon_id: 'salon-123',
              ai_product_name: 'Rubio Claro',
              inventory_product_id: 'product-1',
              confidence: 0.9,
              usage_count: 3,
              created_at: '2024-01-01T00:00:00Z',
              updated_at: '2024-01-01T00:00:00Z',
            },
            {
              id: 'mapping-2',
              salon_id: 'salon-123',
              ai_product_name: 'Castaño Medio',
              inventory_product_id: 'product-2',
              confidence: 0.85,
              usage_count: 7,
              created_at: '2024-01-01T00:00:00Z',
              updated_at: '2024-01-01T00:00:00Z',
            },
          ],
          error: null,
        });

        await store.loadProductMappings();

        const mappings = useProductStore.getState().productMappings;
        expect(mappings).toHaveLength(2);
        expect(mappings[0].aiProductName).toBe('Rubio Claro');
        expect(mappings[1].aiProductName).toBe('Castaño Medio');
      });
    });
  });

  describe('Helper Functions', () => {
    describe('generateDisplayName', () => {
      it('should generate display name with all components', () => {
        const product = {
          brand: 'Wella',
          line: 'Color Perfect',
          shade: '7.1',
          type: 'Tinte',
        };

        const displayName = store.generateDisplayName(product);
        expect(displayName).toBe('Wella Color Perfect 7.1 (Tinte)');
      });

      it('should handle missing components', () => {
        const product = {
          brand: "L'Oreal",
          shade: '8.31',
        };

        const displayName = store.generateDisplayName(product);
        expect(displayName).toBe("L'Oreal 8.31");
      });

      it('should fallback to name if no components', () => {
        const product = {
          name: 'Legacy Product Name',
        };

        const displayName = store.generateDisplayName(product);
        expect(displayName).toBe('Legacy Product Name');
      });
    });

    describe('parseProductName', () => {
      it('should extract type from name', () => {
        const result = store.parseProductName('Wella Color Perfect Tinte 7.1');
        expect(result.type).toBe('Tinte');
      });

      it('should extract shade patterns', () => {
        const result1 = store.parseProductName('Color 7.1 Perfect');
        expect(result1.shade).toBe('7.1');

        const result2 = store.parseProductName('Developer 20 vol');
        expect(result2.shade).toBe('20 vol');

        const result3 = store.parseProductName('Tone N7 Ash');
        expect(result3.shade).toBe('N7');
      });

      it('should return empty object for unparseable name', () => {
        const result = store.parseProductName('Generic Product');
        expect(result).toEqual({});
      });
    });
  });

  describe('Sync and Migration', () => {
    describe('syncWithSupabase', () => {
      it('should trigger loadProducts', async () => {
        const loadProductsSpy = jest.spyOn(store, 'loadProducts');
        mockSupabase.from().single.mockResolvedValue({ data: [], error: null });

        await store.syncWithSupabase();

        expect(loadProductsSpy).toHaveBeenCalled();
      });
    });

    describe('fixMalformedProducts', () => {
      beforeEach(() => {
        const malformedProducts = [
          createMockProduct({
            id: 'product-1',
            brand: 'Wella',
            displayName: 'Wrong Display Name',
          }),
          createMockProduct({
            id: 'product-2',
            brand: "L'Oreal",
            displayName: "L'Oreal Correct Name",
          }),
        ];
        useProductStore.setState({ products: malformedProducts });
      });

      it('should fix malformed display names', async () => {
        const updateProductSpy = jest.spyOn(store, 'updateProduct').mockResolvedValue(undefined);

        await store.fixMalformedProducts();

        expect(updateProductSpy).toHaveBeenCalledWith('product-1', {
          displayName: expect.stringContaining('Wella'),
        });

        // Should not update product-2 as it already contains the brand
        expect(updateProductSpy).not.toHaveBeenCalledWith('product-2', expect.any(Object));
      });
    });

    describe('migrateToUnitsSystem', () => {
      it('should set unitType for products without it', () => {
        const products = [
          createMockProduct({ unitType: undefined, category: 'tinte' }),
          createMockProduct({ unitType: undefined, category: 'otro' }),
        ];
        useProductStore.setState({ products });

        store.migrateToUnitsSystem();

        const updatedProducts = useProductStore.getState().products;
        expect(updatedProducts[0].unitType).toBe('ml');
        expect(updatedProducts[1].unitType).toBe('g');
      });
    });

    describe('clearAllData', () => {
      it('should clear all product data', () => {
        useProductStore.setState({
          products: [createMockProduct()],
          productMappings: [createMockProductMapping()],
          lastSync: '2024-01-01T00:00:00Z',
        });

        store.clearAllData();

        const state = useProductStore.getState();
        expect(state.products).toHaveLength(0);
        expect(state.productMappings).toHaveLength(0);
        expect(state.lastSync).toBeNull();
      });
    });
  });

  describe('Formula Matching', () => {
    it('should find matching products for formula', async () => {
      // Mock the InventoryConsumptionService
      const mockMatches = [
        { product: createMockProduct(), matchScore: 85 },
        { product: createMockProduct({ id: 'product-2' }), matchScore: 70 },
      ];

      // Mock dynamic import
      jest.doMock('@/services/inventoryConsumptionService', () => ({
        InventoryConsumptionService: {
          findMatchingProducts: jest.fn().mockResolvedValue(mockMatches),
        },
      }));

      const formulaProducts = ['Wella Color Perfect 7.1', "L'Oreal Majirel 8.31"];
      const results = await store.getProductsMatchingFormula(formulaProducts);

      expect(results).toHaveLength(2);
    });
  });

  describe('Performance and Error Handling', () => {
    it('should handle concurrent operations safely', async () => {
      const promises = [
        store.addProduct(createMockProduct({ brand: 'Brand1' })),
        store.addProduct(createMockProduct({ brand: 'Brand2' })),
        store.addProduct(createMockProduct({ brand: 'Brand3' })),
      ];

      await Promise.allSettled(promises);

      // Should handle concurrent adds without conflicts
      expect(useProductStore.getState().products.length).toBeGreaterThan(0);
    });

    it('should handle network timeouts gracefully', async () => {
      const timeoutError = new Error('Network timeout');
      mockSupabase.from().single.mockRejectedValue(timeoutError);

      await store.loadProducts();

      expect(useProductStore.getState().isLoading).toBe(false);
    });
  });

  describe('Offline Behavior', () => {
    beforeEach(() => {
      mockSyncQueue.isOnline = false;
    });

    it('should work completely offline for all operations', async () => {
      // Add product offline
      await store.addProduct(createMockProduct({ brand: 'Offline Brand' }));
      expect(useProductStore.getState().products).toHaveLength(1);

      // Update product offline
      const productId = useProductStore.getState().products[0].id;
      await store.updateProduct(productId, { currentStock: 999 });
      expect(useProductStore.getState().products[0].currentStock).toBe(999);

      // Search works offline
      const results = store.searchProducts('Offline');
      expect(results).toHaveLength(1);

      // All operations should queue for sync
      expect(mockSyncQueue.addToQueue).toHaveBeenCalledTimes(2);
    });
  });

  describe('Type Safety and Validation', () => {
    it('should maintain type safety for all operations', () => {
      // TypeScript should catch these at compile time, but we can test runtime behavior
      expect(() => store.getProduct('valid-id')).not.toThrow();
      expect(() => store.searchProducts('')).not.toThrow();
      expect(() => store.getProductsByCategory('tinte')).not.toThrow();
    });

    it('should handle invalid data gracefully', async () => {
      // Try to add product with missing required fields
      const invalidProduct = {};

      // Should not crash, might use defaults
      await expect(store.addProduct(invalidProduct)).resolves.toBeDefined();
    });
  });
});
