/**
 * Comprehensive Test Suite for Brand Category Store
 *
 * Tests filtering, sorting, grouping, hierarchical organization,
 * brand analysis, and category management functionality.
 */

import { useBrandCategoryStore, ProductOrganizer } from '../brand-category-store.new';
import type { Product, StockMovement } from '@/types/inventory';

// Mock data factories
const createMockProduct = (overrides: Partial<Product> = {}): Product => {
  const base = {
    id: `product-${Math.random().toString(36).substr(2, 9)}`,
    brand: 'Wella',
    line: 'Color Perfect',
    type: 'Tinte',
    shade: '7.1',
    displayName: 'Wella Color Perfect 7.1 Tinte',
    name: 'Wella Color Perfect 7.1',
    category: 'tinte',
    currentStock: 500,
    minStock: 100,
    maxStock: 1000,
    unitType: 'ml',
    unitSize: 60,
    purchasePrice: 15.5,
    costPerUnit: 0.26,
    barcode: '1234567890',
    supplier: 'Beauty Supply Co',
    notes: 'Popular shade',
    colorCode: '7.1',
    isActive: true,
    lastUpdated: '2024-01-01T00:00:00Z',
    _syncStatus: 'synced',
    ...overrides,
  };

  // Generate unique name based on provided overrides if not explicitly set
  if (!overrides.name && overrides.displayName) {
    base.name = overrides.displayName;
  } else if (!overrides.name && (overrides.brand || overrides.line || overrides.shade)) {
    base.name =
      `${overrides.brand || base.brand} ${overrides.line || base.line} ${overrides.shade || base.shade}`.trim();
  }

  return base;
};

const createMockMovement = (overrides: Partial<StockMovement> = {}): StockMovement => ({
  id: `movement-${Math.random().toString(36).substr(2, 9)}`,
  productId: 'product-123',
  quantity: -25,
  type: 'use',
  date: new Date().toISOString(),
  userId: 'user-123',
  notes: 'Service consumption',
  ...overrides,
});

describe('BrandCategoryStore', () => {
  let store: any;
  let testProducts: Product[];
  let testMovements: StockMovement[];

  beforeEach(() => {
    // Reset store state
    useBrandCategoryStore.setState({
      activeFilters: {
        stockStatus: 'all',
        categories: [],
        brands: [],
        searchQuery: '',
      },
      sortBy: 'name',
      groupBy: 'none',
    });

    store = useBrandCategoryStore.getState();

    // Create diverse test data
    testProducts = [
      // Wella products
      createMockProduct({
        id: 'wella-1',
        brand: 'Wella',
        line: 'Color Perfect',
        type: 'Tinte',
        shade: '7.1',
        displayName: 'Wella Color Perfect 7.1 Tinte',
        name: 'Wella Color Perfect 7.1',
        category: 'tinte',
        currentStock: 50,
        minStock: 100,
        costPerUnit: 0.3,
      }),
      createMockProduct({
        id: 'wella-2',
        brand: 'Wella',
        line: 'Koleston',
        type: 'Tinte',
        shade: '8.0',
        displayName: 'Wella Koleston 8.0 Tinte',
        name: 'Wella Koleston 8.0',
        category: 'tinte',
        currentStock: 200,
        minStock: 100,
        costPerUnit: 0.28,
      }),
      // L'Oreal products
      createMockProduct({
        id: 'loreal-1',
        brand: "L'Oreal",
        line: 'Majirel',
        type: 'Tinte',
        shade: '6.35',
        displayName: "L'Oreal Majirel 6.35 Tinte",
        name: "L'Oreal Majirel 6.35",
        category: 'tinte',
        currentStock: 0,
        minStock: 50,
        costPerUnit: 0.32,
      }),
      // Schwarzkopf products
      createMockProduct({
        id: 'schwarzkopf-1',
        brand: 'Schwarzkopf',
        line: 'Igora',
        type: 'Oxidante',
        shade: '20 Vol',
        displayName: 'Schwarzkopf Igora 20 Vol Oxidante',
        name: 'Schwarzkopf Igora 20 Vol',
        category: 'oxidante',
        currentStock: 800,
        minStock: 200,
        costPerUnit: 0.15,
      }),
      // Decolorante
      createMockProduct({
        id: 'decolor-1',
        brand: 'Matrix',
        line: 'Light Master',
        type: 'Decolorante',
        shade: undefined,
        displayName: 'Matrix Light Master Decolorante',
        name: 'Matrix Light Master',
        category: 'decolorante',
        currentStock: 150,
        minStock: 100,
        costPerUnit: 0.45,
      }),
      // Tratamiento
      createMockProduct({
        id: 'treatment-1',
        brand: 'Olaplex',
        line: undefined,
        type: 'Tratamiento',
        shade: undefined,
        displayName: 'Olaplex No.1 Tratamiento',
        name: 'Olaplex No.1 Tratamiento',
        category: 'tratamiento',
        currentStock: 2000,
        minStock: 100,
        maxStock: 500,
        costPerUnit: 2.5,
      }),
    ];

    // Create test movements for usage analysis
    testMovements = [
      // Recent movements (last 30 days)
      createMockMovement({
        productId: 'wella-1',
        date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        type: 'use',
        quantity: -30,
      }),
      createMockMovement({
        productId: 'wella-1',
        date: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
        type: 'use',
        quantity: -25,
      }),
      createMockMovement({
        productId: 'schwarzkopf-1',
        date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        type: 'use',
        quantity: -50,
      }),
      // Older movements (should not affect usage sorting)
      createMockMovement({
        productId: 'loreal-1',
        date: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString(),
        type: 'use',
        quantity: -40,
      }),
    ];
  });

  describe('Filter Management', () => {
    describe('setFilter', () => {
      it('should update stock status filter', () => {
        store.setFilter('stockStatus', 'low');

        const state = useBrandCategoryStore.getState();
        expect(state.activeFilters.stockStatus).toBe('low');
      });

      it('should update categories filter', () => {
        store.setFilter('categories', ['tinte', 'oxidante']);

        const state = useBrandCategoryStore.getState();
        expect(state.activeFilters.categories).toEqual(['tinte', 'oxidante']);
      });

      it('should update brands filter', () => {
        store.setFilter('brands', ['Wella', "L'Oreal"]);

        const state = useBrandCategoryStore.getState();
        expect(state.activeFilters.brands).toEqual(['Wella', "L'Oreal"]);
      });

      it('should update search query filter', () => {
        store.setFilter('searchQuery', 'color perfect');

        const state = useBrandCategoryStore.getState();
        expect(state.activeFilters.searchQuery).toBe('color perfect');
      });
    });

    describe('setSortBy', () => {
      it('should update sort criteria', () => {
        store.setSortBy('price');

        const state = useBrandCategoryStore.getState();
        expect(state.sortBy).toBe('price');
      });
    });

    describe('setGroupBy', () => {
      it('should update grouping criteria', () => {
        store.setGroupBy('brand');

        const state = useBrandCategoryStore.getState();
        expect(state.groupBy).toBe('brand');
      });
    });

    describe('resetFilters', () => {
      it('should reset all filters to defaults', () => {
        // Set some filters
        store.setFilter('stockStatus', 'low');
        store.setFilter('categories', ['tinte']);
        store.setSortBy('price');
        store.setGroupBy('brand');

        store.resetFilters();

        const state = useBrandCategoryStore.getState();
        expect(state.activeFilters).toEqual({
          stockStatus: 'all',
          categories: [],
          brands: [],
          searchQuery: '',
        });
        expect(state.sortBy).toBe('name');
        expect(state.groupBy).toBe('none');
      });
    });
  });

  describe('Product Filtering and Sorting', () => {
    describe('getFilteredAndSortedProducts', () => {
      it('should filter by search query across multiple fields', () => {
        store.setFilter('searchQuery', 'wella color');

        const filtered = store.getFilteredAndSortedProducts(testProducts);
        expect(filtered).toHaveLength(1);
        expect(filtered[0].brand).toBe('Wella');
        expect(filtered[0].line).toBe('Color Perfect');
      });

      it('should filter by stock status - low stock', () => {
        store.setFilter('stockStatus', 'low');

        const filtered = store.getFilteredAndSortedProducts(testProducts);
        const lowStockProducts = filtered.filter(
          p => p.currentStock <= p.minStock && p.currentStock > 0
        );
        expect(lowStockProducts.length).toBe(filtered.length);
      });

      it('should filter by stock status - out of stock', () => {
        store.setFilter('stockStatus', 'out');

        const filtered = store.getFilteredAndSortedProducts(testProducts);
        expect(filtered).toHaveLength(1);
        expect(filtered[0].currentStock).toBe(0);
      });

      it('should filter by stock status - ok stock', () => {
        store.setFilter('stockStatus', 'ok');

        const filtered = store.getFilteredAndSortedProducts(testProducts);
        const okStockProducts = filtered.filter(p => p.currentStock > p.minStock);
        expect(okStockProducts.length).toBe(filtered.length);
      });

      it('should filter by categories', () => {
        store.setFilter('categories', ['tinte', 'oxidante']);

        const filtered = store.getFilteredAndSortedProducts(testProducts);
        filtered.forEach(product => {
          expect(['tinte', 'oxidante']).toContain(product.category);
        });
      });

      it('should filter by brands', () => {
        store.setFilter('brands', ['Wella']);

        const filtered = store.getFilteredAndSortedProducts(testProducts);
        expect(filtered).toHaveLength(2);
        filtered.forEach(product => {
          expect(product.brand).toBe('Wella');
        });
      });

      it('should combine multiple filters', () => {
        store.setFilter('categories', ['tinte']);
        store.setFilter('brands', ['Wella']);
        store.setFilter('stockStatus', 'ok');

        const filtered = store.getFilteredAndSortedProducts(testProducts);
        expect(filtered).toHaveLength(1);
        expect(filtered[0].brand).toBe('Wella');
        expect(filtered[0].category).toBe('tinte');
        expect(filtered[0].currentStock).toBeGreaterThan(filtered[0].minStock);
      });
    });

    describe('Sorting', () => {
      it('should sort by name alphabetically', () => {
        store.setSortBy('name');

        const sorted = store.getFilteredAndSortedProducts(testProducts);
        for (let i = 1; i < sorted.length; i++) {
          const current = sorted[i].displayName || sorted[i].name || '';
          const previous = sorted[i - 1].displayName || sorted[i - 1].name || '';
          expect(current.localeCompare(previous)).toBeGreaterThanOrEqual(0);
        }
      });

      it('should sort by stock level', () => {
        store.setSortBy('stock');

        const sorted = store.getFilteredAndSortedProducts(testProducts);
        for (let i = 1; i < sorted.length; i++) {
          expect(sorted[i].currentStock).toBeGreaterThanOrEqual(sorted[i - 1].currentStock);
        }
      });

      it('should sort by price', () => {
        store.setSortBy('price');

        const sorted = store.getFilteredAndSortedProducts(testProducts);
        for (let i = 1; i < sorted.length; i++) {
          expect(sorted[i].costPerUnit).toBeGreaterThanOrEqual(sorted[i - 1].costPerUnit);
        }
      });

      it('should sort by brand', () => {
        store.setSortBy('brand');

        const sorted = store.getFilteredAndSortedProducts(testProducts);
        for (let i = 1; i < sorted.length; i++) {
          const current = sorted[i].brand || '';
          const previous = sorted[i - 1].brand || '';
          expect(current.localeCompare(previous)).toBeGreaterThanOrEqual(0);
        }
      });

      it('should sort by usage frequency', () => {
        store.setSortBy('usage');

        const sorted = store.getFilteredAndSortedProducts(testProducts, testMovements);

        // wella-1 should be first (2 recent movements)
        expect(sorted[0].id).toBe('wella-1');
        // schwarzkopf-1 should be second (1 recent movement)
        expect(sorted[1].id).toBe('schwarzkopf-1');
      });
    });
  });

  describe('Product Grouping', () => {
    describe('getGroupedProducts', () => {
      it('should group by brand', () => {
        store.setGroupBy('brand');

        const grouped = store.getGroupedProducts(testProducts);

        expect(grouped.has('Wella')).toBe(true);
        expect(grouped.has("L'Oreal")).toBe(true);
        expect(grouped.has('Schwarzkopf')).toBe(true);
        expect(grouped.has('Matrix')).toBe(true);
        expect(grouped.has('Olaplex')).toBe(true);

        expect(grouped.get('Wella')).toHaveLength(2);
        expect(grouped.get("L'Oreal")).toHaveLength(1);
      });

      it('should group by line', () => {
        store.setGroupBy('line');

        const grouped = store.getGroupedProducts(testProducts);

        expect(grouped.has('Color Perfect')).toBe(true);
        expect(grouped.has('Koleston')).toBe(true);
        expect(grouped.has('Majirel')).toBe(true);
        expect(grouped.has('Igora')).toBe(true);
        expect(grouped.has('Light Master')).toBe(true);
        expect(grouped.has('Sin línea')).toBe(true); // Olaplex product has no line
      });

      it('should group by category', () => {
        store.setGroupBy('category');

        const grouped = store.getGroupedProducts(testProducts);

        expect(grouped.has('Tintes')).toBe(true);
        expect(grouped.has('Oxidantes')).toBe(true);
        expect(grouped.has('Decolorantes')).toBe(true);
        expect(grouped.has('Tratamientos')).toBe(true);

        expect(grouped.get('Tintes')).toHaveLength(3);
        expect(grouped.get('Oxidantes')).toHaveLength(1);
      });

      it('should group by type', () => {
        store.setGroupBy('type');

        const grouped = store.getGroupedProducts(testProducts);

        expect(grouped.has('Tinte')).toBe(true);
        expect(grouped.has('Oxidante')).toBe(true);
        expect(grouped.has('Decolorante')).toBe(true);
        expect(grouped.has('Tratamiento')).toBe(true);
      });

      it('should return single group when groupBy is none', () => {
        store.setGroupBy('none');

        const grouped = store.getGroupedProducts(testProducts);

        expect(grouped.size).toBe(1);
        expect(grouped.has('Todos los productos')).toBe(true);
        expect(grouped.get('Todos los productos')).toHaveLength(testProducts.length);
      });

      it('should sort groups alphabetically', () => {
        store.setGroupBy('brand');

        const grouped = store.getGroupedProducts(testProducts);
        const groupKeys = Array.from(grouped.keys());

        for (let i = 1; i < groupKeys.length; i++) {
          expect(groupKeys[i].localeCompare(groupKeys[i - 1])).toBeGreaterThanOrEqual(0);
        }
      });
    });
  });

  describe('Brand Analysis', () => {
    describe('getAllBrands', () => {
      it('should analyze all brands with correct metrics', () => {
        const brands = store.getAllBrands(testProducts);

        const wellaBrand = brands.find(b => b.name === 'Wella');
        expect(wellaBrand).toBeTruthy();
        expect(wellaBrand!.productCount).toBe(2);
        expect(wellaBrand!.lines).toEqual(['Color Perfect', 'Koleston']);
        expect(wellaBrand!.totalValue).toBeGreaterThan(0);
      });

      it('should calculate total value correctly', () => {
        const brands = store.getAllBrands(testProducts);

        const olaplex = brands.find(b => b.name === 'Olaplex');
        expect(olaplex!.totalValue).toBe(2000 * 2.5); // currentStock * costPerUnit
      });

      it('should sort brands alphabetically', () => {
        const brands = store.getAllBrands(testProducts);

        for (let i = 1; i < brands.length; i++) {
          expect(brands[i].name.localeCompare(brands[i - 1].name)).toBeGreaterThanOrEqual(0);
        }
      });

      it('should handle products without brands gracefully', () => {
        const productsWithNoBrand = [
          ...testProducts,
          createMockProduct({ brand: '', id: 'no-brand' }),
        ];

        const brands = store.getAllBrands(productsWithNoBrand);

        // Should still process all products with brands
        expect(brands.length).toBe(5); // Original unique brands
      });
    });

    describe('getAllCategories', () => {
      it('should analyze all categories with correct metrics', () => {
        const categories = store.getAllCategories(testProducts);

        const tinteCategory = categories.find(c => c.name === 'tinte');
        expect(tinteCategory).toBeTruthy();
        expect(tinteCategory!.displayName).toBe('Tintes');
        expect(tinteCategory!.productCount).toBe(3);
        expect(tinteCategory!.totalValue).toBeGreaterThan(0);
      });

      it('should sort categories by display name', () => {
        const categories = store.getAllCategories(testProducts);

        for (let i = 1; i < categories.length; i++) {
          expect(
            categories[i].displayName.localeCompare(categories[i - 1].displayName)
          ).toBeGreaterThanOrEqual(0);
        }
      });
    });

    describe('Brand-specific methods', () => {
      it('should get brand lines correctly', () => {
        const wellaLines = store.getBrandLines(testProducts, 'Wella');
        expect(wellaLines).toEqual(['Color Perfect', 'Koleston']);
      });

      it('should get products by brand', () => {
        const wellaProducts = store.getProductsByBrand(testProducts, 'Wella');
        expect(wellaProducts).toHaveLength(2);
        wellaProducts.forEach(p => expect(p.brand).toBe('Wella'));
      });

      it('should get products by category', () => {
        const tinteProducts = store.getProductsByCategory(testProducts, 'tinte');
        expect(tinteProducts).toHaveLength(3);
        tinteProducts.forEach(p => expect(p.category).toBe('tinte'));
      });

      it('should get products by brand and line', () => {
        const colorPerfectProducts = store.getProductsByLine(
          testProducts,
          'Wella',
          'Color Perfect'
        );
        expect(colorPerfectProducts).toHaveLength(1);
        expect(colorPerfectProducts[0].line).toBe('Color Perfect');
      });
    });
  });

  describe('Usage Analysis', () => {
    describe('getFrequentlyUsedProducts', () => {
      it('should find frequently used products based on recent movements', () => {
        const frequentlyUsed = store.getFrequentlyUsedProducts(testProducts, testMovements, 3);

        // wella-1 should be first (2 movements)
        expect(frequentlyUsed[0].id).toBe('wella-1');
        // schwarzkopf-1 should be second (1 movement)
        expect(frequentlyUsed[1].id).toBe('schwarzkopf-1');
        // loreal-1 should not appear (old movement)
        expect(frequentlyUsed.find(p => p.id === 'loreal-1')).toBeUndefined();
      });

      it('should respect the limit parameter', () => {
        const frequentlyUsed = store.getFrequentlyUsedProducts(testProducts, testMovements, 1);
        expect(frequentlyUsed).toHaveLength(1);
      });

      it('should filter out products with no recent usage', () => {
        const noRecentMovements: StockMovement[] = [];
        const frequentlyUsed = store.getFrequentlyUsedProducts(testProducts, noRecentMovements);
        expect(frequentlyUsed).toHaveLength(0);
      });

      it('should handle both Spanish and English movement types', () => {
        const mixedMovements = [
          createMockMovement({ productId: 'wella-1', type: 'consumo' }),
          createMockMovement({ productId: 'wella-2', type: 'use' }),
        ];

        const frequentlyUsed = store.getFrequentlyUsedProducts(testProducts, mixedMovements);
        expect(frequentlyUsed).toHaveLength(2);
      });
    });
  });

  describe('Search Functionality', () => {
    describe('searchProducts', () => {
      it('should search across all product fields', () => {
        const results = store.searchProducts(testProducts, 'wella color');
        expect(results).toHaveLength(1);
        expect(results[0].brand).toBe('Wella');
        expect(results[0].line).toBe('Color Perfect');
      });

      it('should handle multiple search terms', () => {
        const results = store.searchProducts(testProducts, 'tinte 7.1');
        expect(results).toHaveLength(1);
        expect(results[0].shade).toBe('7.1');
      });

      it('should be case insensitive', () => {
        const results = store.searchProducts(testProducts, 'WELLA color');
        expect(results).toHaveLength(1);
      });

      it('should return empty array for no matches', () => {
        const results = store.searchProducts(testProducts, 'nonexistent');
        expect(results).toHaveLength(0);
      });
    });
  });

  describe('Hierarchical Structure', () => {
    describe('getProductHierarchy', () => {
      it('should create brand -> line -> type hierarchy', () => {
        const hierarchy = store.getProductHierarchy(testProducts);

        // Check brand level
        expect(hierarchy.has('Wella')).toBe(true);
        expect(hierarchy.has("L'Oreal")).toBe(true);

        // Check line level
        const wellaLines = hierarchy.get('Wella');
        expect(wellaLines!.has('Color Perfect')).toBe(true);
        expect(wellaLines!.has('Koleston')).toBe(true);

        // Check type level
        const colorPerfectTypes = wellaLines!.get('Color Perfect');
        expect(colorPerfectTypes!.has('Tinte')).toBe(true);

        // Check products at leaf level
        const tinteProducts = colorPerfectTypes!.get('Tinte');
        expect(tinteProducts).toHaveLength(1);
        expect(tinteProducts![0].shade).toBe('7.1');
      });

      it('should handle missing hierarchy levels', () => {
        const productWithoutLine = createMockProduct({
          brand: 'Generic',
          line: undefined,
          type: 'Product',
        });
        const productsWithMissing = [...testProducts, productWithoutLine];

        const hierarchy = store.getProductHierarchy(productsWithMissing);

        expect(hierarchy.has('Generic')).toBe(true);
        const genericBrand = hierarchy.get('Generic');
        expect(genericBrand!.has('Sin línea')).toBe(true);
      });
    });
  });

  describe('ProductOrganizer Utility Class', () => {
    describe('getProductsWithStockStatus', () => {
      it('should add stock status indicators', () => {
        const withStatus = ProductOrganizer.getProductsWithStockStatus(testProducts);

        const outOfStock = withStatus.find(p => p.id === 'loreal-1');
        expect(outOfStock!.stockStatus).toBe('out');

        const lowStock = withStatus.find(p => p.id === 'wella-1');
        expect(lowStock!.stockStatus).toBe('low');

        const overStock = withStatus.find(p => p.id === 'treatment-1');
        expect(overStock!.stockStatus).toBe('overstock');

        const okStock = withStatus.find(p => p.id === 'schwarzkopf-1');
        expect(okStock!.stockStatus).toBe('ok');
      });
    });

    describe('getInventoryValueByBrand', () => {
      it('should calculate value by brand correctly', () => {
        const brandValues = ProductOrganizer.getInventoryValueByBrand(testProducts);

        expect(brandValues.length).toBeGreaterThan(0);

        // Should be sorted by value descending
        for (let i = 1; i < brandValues.length; i++) {
          expect(brandValues[i].value).toBeLessThanOrEqual(brandValues[i - 1].value);
        }

        // Olaplex should have highest value (2000 * 2.50 = 5000)
        expect(brandValues[0].brand).toBe('Olaplex');
      });
    });

    describe('getInventoryValueByCategory', () => {
      it('should calculate value by category correctly', () => {
        const categoryValues = ProductOrganizer.getInventoryValueByCategory(testProducts);

        const tratamientoValue = categoryValues.find(c => c.category === 'tratamiento');
        expect(tratamientoValue!.value).toBe(2000 * 2.5);
        expect(tratamientoValue!.displayName).toBe('Tratamientos');

        // Should be sorted by value descending
        for (let i = 1; i < categoryValues.length; i++) {
          expect(categoryValues[i].value).toBeLessThanOrEqual(categoryValues[i - 1].value);
        }
      });
    });

    describe('getProductsNeedingAttention', () => {
      it('should categorize products by attention needed', () => {
        const needingAttention = ProductOrganizer.getProductsNeedingAttention(testProducts);

        expect(needingAttention.outOfStock).toHaveLength(1);
        expect(needingAttention.outOfStock[0].id).toBe('loreal-1');

        expect(needingAttention.lowStock).toHaveLength(1);
        expect(needingAttention.lowStock[0].id).toBe('wella-1');

        expect(needingAttention.overstock).toHaveLength(1);
        expect(needingAttention.overstock[0].id).toBe('treatment-1');
      });
    });

    describe('getProductSuggestions', () => {
      it('should generate intelligent product suggestions', () => {
        const suggestions = ProductOrganizer.getProductSuggestions(testProducts, testMovements);

        // Should have high priority suggestion for high usage + low stock
        const highPriority = suggestions.filter(s => s.priority === 'high');
        expect(highPriority.length).toBeGreaterThan(0);

        // Should be sorted by priority
        let lastPriorityScore = 3; // high = 3
        suggestions.forEach(suggestion => {
          const currentScore =
            suggestion.priority === 'high' ? 3 : suggestion.priority === 'medium' ? 2 : 1;
          expect(currentScore).toBeLessThanOrEqual(lastPriorityScore);
          lastPriorityScore = currentScore;
        });
      });

      it('should identify different suggestion types', () => {
        const suggestions = ProductOrganizer.getProductSuggestions(testProducts, testMovements);

        const reasons = suggestions.map(s => s.reason);
        expect(reasons).toContain('high_usage_low_stock');
        // Might also contain 'frequently_used' or 'alternative_needed' depending on data
      });
    });
  });

  describe('Performance and Edge Cases', () => {
    it('should handle empty product arrays gracefully', () => {
      const emptyResults = store.getFilteredAndSortedProducts([]);
      expect(emptyResults).toHaveLength(0);

      const emptyGrouped = store.getGroupedProducts([]);
      expect(emptyGrouped.get('Todos los productos')).toHaveLength(0);

      const emptyBrands = store.getAllBrands([]);
      expect(emptyBrands).toHaveLength(0);
    });

    it('should handle large product datasets efficiently', () => {
      const largeDataset = Array.from({ length: 1000 }, (_, i) =>
        createMockProduct({
          id: `product-${i}`,
          brand: `Brand-${i % 10}`,
          currentStock: Math.random() * 1000,
        })
      );

      const startTime = performance.now();
      const filtered = store.getFilteredAndSortedProducts(largeDataset);
      const endTime = performance.now();

      expect(filtered).toHaveLength(1000);
      expect(endTime - startTime).toBeLessThan(100); // Should be fast
    });

    it('should handle products with missing or null fields', () => {
      const problematicProducts = [
        createMockProduct({ brand: null as any, line: undefined }),
        createMockProduct({ displayName: '', name: null as any }),
        createMockProduct({ currentStock: NaN, minStock: null as any }),
      ];

      // Should not throw errors
      expect(() => store.getFilteredAndSortedProducts(problematicProducts)).not.toThrow();
      expect(() => store.getGroupedProducts(problematicProducts)).not.toThrow();
      expect(() => store.getAllBrands(problematicProducts)).not.toThrow();
    });

    it('should maintain consistent sorting across multiple calls', () => {
      store.setSortBy('name');

      const sorted1 = store.getFilteredAndSortedProducts(testProducts);
      const sorted2 = store.getFilteredAndSortedProducts(testProducts);

      expect(sorted1.map(p => p.id)).toEqual(sorted2.map(p => p.id));
    });
  });

  describe('State Persistence', () => {
    it('should persist filter preferences correctly', () => {
      store.setFilter('stockStatus', 'low');
      store.setFilter('categories', ['tinte']);
      store.setSortBy('usage');
      store.setGroupBy('brand');

      const state = useBrandCategoryStore.getState();

      expect(state.activeFilters.stockStatus).toBe('low');
      expect(state.activeFilters.categories).toEqual(['tinte']);
      expect(state.sortBy).toBe('usage');
      expect(state.groupBy).toBe('brand');
    });
  });

  describe('Type Translations', () => {
    it('should handle Spanish to English type mapping', () => {
      const movements = [
        createMockMovement({ type: 'consumo' }),
        createMockMovement({ type: 'compra' }),
      ];

      const frequentlyUsed = store.getFrequentlyUsedProducts(testProducts, movements);

      // Should process Spanish 'consumo' as consumption
      expect(frequentlyUsed.length).toBeGreaterThanOrEqual(0);
    });
  });
});
