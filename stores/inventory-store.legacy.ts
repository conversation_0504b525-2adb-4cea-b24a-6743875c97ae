/**
 * LEGACY BACKUP - Monolithic Inventory Store (v2.2.0)
 *
 * This is a backup of the original monolithic inventory-store.ts before
 * migration to modular facade-based architecture.
 *
 * Created: $(date)
 * Original Size: 1,601 lines
 * Purpose: Emergency rollback capability
 *
 * CRITICAL: DO NOT MODIFY THIS FILE
 * This backup ensures we can always revert to the working monolithic architecture
 * if the modular facade deployment encounters issues.
 */

import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase, getCurrentSalonId } from '@/lib/supabase';
import { generateLocalId, isLocalId, useSyncQueueStore } from './sync-queue-store';
import { useInventoryAnalyticsStore } from './inventory-analytics-store';
import { Database } from '@/types/database';
import {
  Product,
  StockMovement,
  InventoryAlert,
  ConsumptionAnalysis,
  InventoryReport,
  ProductMapping,
} from '@/types/inventory';
import { logger } from '@/utils/logger';
import { defaultProducts } from '@/data/default-products';
import { mapCategoryToType } from '@/constants/product-mappings';
// Remove circular dependency - import service at runtime when needed

type SupabaseProduct = Database['public']['Tables']['products']['Row'];
type SupabaseProductInsert = Database['public']['Tables']['products']['Insert'];
type SupabaseProductUpdate = Database['public']['Tables']['products']['Update'];
type SupabaseStockMovement = Database['public']['Tables']['stock_movements']['Row'];
type _SupabaseStockMovementInsert = Database['public']['Tables']['stock_movements']['Insert'];

// Type for low stock products from RPC function (for backward compatibility)
interface LowStockProduct {
  product_id: string;
  brand: string;
  name: string;
  category: string;
  stock_ml: number;
  minimum_stock_ml: number;
  percentage_remaining: number;
  color_code?: string;
}

interface InventoryStore {
  products: Product[];
  movements: StockMovement[];
  alerts: InventoryAlert[];
  lastSync: string | null;
  isLoading: boolean;
  isInitialized: boolean;

  // Analytics state (delegated to analytics store)
  // Note: currentReport and isLoadingReport are now managed by inventory-analytics-store
  // Low stock products are also managed by inventory-analytics-store

  // Filter and Grouping State
  activeFilters: {
    stockStatus: 'all' | 'low' | 'out' | 'ok';
    categories: string[];
    brands: string[];
    searchQuery: string;
  };
  sortBy: 'name' | 'stock' | 'usage' | 'price' | 'brand';
  groupBy: 'none' | 'brand' | 'line' | 'category' | 'type';

  // Product Actions
  loadProducts: () => Promise<void>;
  addProduct: (product: Omit<Product, 'id' | 'lastUpdated'>) => Promise<string>;
  updateProduct: (id: string, updates: Partial<Product>) => Promise<void>;
  deleteProduct: (id: string) => Promise<void>;
  getProduct: (id: string) => Product | undefined;
  getProductByNameAndBrand: (name: string, brand: string) => Product | undefined;
  searchProducts: (query: string) => Product[];

  // Stock Actions
  updateStock: (
    productId: string,
    quantity: number,
    type: StockMovement['type'],
    reason: string,
    referenceId?: string
  ) => Promise<void>;
  consumeProducts: (
    consumptions: Array<{ productId: string; quantity: number }>,
    referenceId: string,
    clientName: string
  ) => Promise<void>;
  getStockMovements: (productId?: string, limit?: number) => StockMovement[];

  // Alert Actions
  createAlert: (
    productId: string,
    type: InventoryAlert['type'],
    message: string,
    severity: InventoryAlert['severity']
  ) => void;
  acknowledgeAlert: (alertId: string, userId: string) => void;
  getActiveAlerts: () => InventoryAlert[];
  checkLowStock: () => void;

  // Analytics & Reports (delegated to analytics store for backward compatibility)
  loadLowStockProducts: () => Promise<void>;
  getLowStockProducts: () => LowStockProduct[];
  getConsumptionAnalysis: (
    productId: string,
    period: 'daily' | 'weekly' | 'monthly'
  ) => ConsumptionAnalysis | null;
  generateInventoryReport: () => InventoryReport;
  loadInventoryReport: () => void;
  clearInventoryReport: () => void;
  getProductsByCategory: (category: Product['category']) => Product[];
  getTotalInventoryValue: () => number;

  // Sync & Initialize
  initializeWithDefaults: () => void;
  generateMockMovements: () => void;
  clearAllData: () => void;
  syncWithSupabase: () => Promise<void>;

  // Migration
  migrateToUnitsSystem: () => void;
  fixMalformedProducts: () => Promise<void>;

  // Product Mappings
  productMappings: ProductMapping[];
  saveProductMapping: (
    aiProductName: string,
    inventoryProductId: string,
    confidence: number
  ) => Promise<void>;
  getProductMapping: (aiProductName: string) => ProductMapping | undefined;
  incrementMappingUsage: (aiProductName: string) => Promise<void>;
  loadProductMappings: () => Promise<void>;

  // Filter and Grouping Actions
  setFilter: (
    filterType: 'stockStatus' | 'categories' | 'brands' | 'searchQuery',
    value: string | string[] | 'all' | 'low' | 'out' | 'ok'
  ) => void;
  setSortBy: (sortBy: InventoryStore['sortBy']) => void;
  setGroupBy: (groupBy: InventoryStore['groupBy']) => void;
  resetFilters: () => void;
  getFilteredAndSortedProducts: () => Product[];
  getGroupedProducts: () => Map<string, Product[]>;
  getFrequentlyUsedProducts: (limit?: number) => Product[];
  getProductsMatchingFormula: (formulaProducts: string[]) => Promise<Product[]>;
}

const inventoryLogger = logger.withContext('InventoryStore');

// Helper function to generate displayName from structured fields
function generateDisplayName(product: Partial<Product>): string {
  const parts = [];

  // Mantener marca completa como viene de BD
  const brand = product.brand || '';

  if (brand) parts.push(brand);
  if (product.line) parts.push(product.line);
  if (product.shade) parts.push(product.shade);

  let displayName = parts.join(' ').trim();

  // Add type in parentheses (opcional, para mantener compatibilidad)
  if (product.type && !displayName.includes('(')) {
    const typeLabel =
      {
        color: 'Tinte',
        tinte: 'Tinte',
        developer: 'Oxidante',
        oxidante: 'Oxidante',
        bleach: 'Decolorante',
        decolorante: 'Decolorante',
        treatment: 'Tratamiento',
        tratamiento: 'Tratamiento',
        toner: 'Matizador',
        matizador: 'Matizador',
        additive: 'Aditivo',
        aditivo: 'Aditivo',
        pre_pigment: 'Pre-pigmentación',
        shampoo: 'Champú',
        champú: 'Champú',
        conditioner: 'Acondicionador',
        acondicionador: 'Acondicionador',
        styling: 'Styling',
      }[product.type] || product.type;

    displayName += ` (${typeLabel})`;
  }

  return displayName.trim() || product.name || 'Producto sin nombre';
}

// Helper function to parse legacy name into structured fields
function parseProductName(name: string): { type?: string; shade?: string } {
  const result: { type?: string; shade?: string } = {};

  // Common product types
  const types = [
    'tinte',
    'oxidante',
    'decolorante',
    'tratamiento',
    'matizador',
    'champú',
    'acondicionador',
  ];
  const typesRegex = new RegExp(`\\b(${types.join('|')})\\b`, 'i');
  const typeMatch = name.match(typesRegex);
  if (typeMatch) {
    result.type = typeMatch[1].charAt(0).toUpperCase() + typeMatch[1].slice(1).toLowerCase();
  }

  // Extract shade/tone patterns
  const shadePatterns = [
    /\b(\d+(?:[/.]\d+)?)\b/, // Matches: 7, 7.1, 7/1
    /\b(\d+\s*vol)\b/i, // Matches: 20 vol, 30vol
    /\b([A-Z]\d+)\b/, // Matches: N7, C4
  ];

  for (const pattern of shadePatterns) {
    const match = name.match(pattern);
    if (match) {
      result.shade = match[1];
      break;
    }
  }

  return result;
}

// Helper functions to convert between local and Supabase formats
function convertSupabaseToLocal(supabaseProduct: SupabaseProduct): Product {
  // Handle backward compatibility: map old 'type' to new 'category' if needed
  let category: Product['category'] = 'otro';
  if (supabaseProduct.category) {
    category = supabaseProduct.category as Product['category'];
  } else if (supabaseProduct.type) {
    // Migration from old type field
    const typeMapping: Record<string, Product['category']> = {
      color: 'tinte',
      developer: 'oxidante',
      treatment: 'tratamiento',
      shampoo: 'tratamiento',
      conditioner: 'tratamiento',
      styling: 'tratamiento',
      other: 'otro',
    };
    category = typeMapping[supabaseProduct.type] || 'otro';
  }

  // Extract structured fields from name if new fields are missing (migration)
  let productType = supabaseProduct.type;
  let shade = supabaseProduct.shade;

  if (!productType || !shade) {
    const parsed = parseProductName(supabaseProduct.name);
    productType = productType || parsed.type || 'Producto';
    shade = shade || parsed.shade;
  }

  const product: Product = {
    id: supabaseProduct.id,
    brand: supabaseProduct.brand,
    line: supabaseProduct.line || undefined,
    type: productType || 'Producto',
    shade: shade || undefined,
    displayName: '', // Will be generated below
    name: supabaseProduct.name, // Keep for backward compatibility
    category,
    currentStock: Number(supabaseProduct.stock_ml || 0),
    minStock: Number(supabaseProduct.minimum_stock_ml || 0),
    maxStock: supabaseProduct.max_stock ? Number(supabaseProduct.max_stock) : undefined,
    unitType: 'ml', // Always ml in database
    unitSize: Number(supabaseProduct.size_ml || 0),
    purchasePrice:
      Number(supabaseProduct.cost_per_unit || 0) * Number(supabaseProduct.size_ml || 0),
    costPerUnit: Number(supabaseProduct.cost_per_unit || 0),
    barcode: supabaseProduct.barcode || undefined,
    supplier: supabaseProduct.supplier || undefined,
    notes: supabaseProduct.notes || undefined,
    colorCode: supabaseProduct.color_code || undefined,
    isActive: supabaseProduct.is_active,
    lastUpdated: supabaseProduct.updated_at,
    _syncStatus: 'synced',
  };

  // Generate displayName
  product.displayName = generateDisplayName(product);

  return product;
}

function convertLocalToSupabase(
  product: Partial<Product>
): SupabaseProductInsert & SupabaseProductUpdate {
  // Generate name from structured fields if not provided (for backward compatibility)
  const name = product.name || product.displayName || generateDisplayName(product);

  // Map category to proper database type
  const databaseType = product.type
    ? mapCategoryToType(product.type)
    : mapCategoryToType(product.category || 'otro');

  return {
    name,
    brand: product.brand!,
    category: product.category,
    type: databaseType, // Properly mapped database type
    shade: product.shade || null, // New shade field
    stock_ml: product.currentStock || 0,
    minimum_stock_ml: product.minStock || 0,
    max_stock: product.maxStock || null,
    size_ml: product.unitSize || 0,
    cost_per_unit: product.costPerUnit || 0,
    barcode: product.barcode || null,
    supplier: product.supplier || null,
    notes: product.notes || null,
    line: product.line || null,
    color_code: product.colorCode || product.shade || null, // Use shade as color_code if not provided
    is_active: product.isActive ?? true,
  };
}

// Mapeo de tipos español → inglés para insertar en BD
const typeSpanishToEnglish: Record<string, string> = {
  compra: 'purchase',
  consumo: 'use',
  ajuste: 'adjustment',
  devolución: 'return',
  pérdida: 'waste',
  inventario_inicial: 'purchase', // Se mapea a purchase para inventario inicial
  entrada: 'purchase', // Para StockMovementsModal
  salida: 'use', // Para StockMovementsModal
};

// Mapeo de tipos inglés → español para UI (mantener compatibilidad)
export const typeEnglishToSpanish: Record<string, string> = {
  purchase: 'compra',
  use: 'consumo',
  adjustment: 'ajuste',
  return: 'devolución',
  waste: 'pérdida',
};

function convertSupabaseMovementToLocal(movement: SupabaseStockMovement): StockMovement {
  // La BD ahora devuelve tipos en inglés, validamos que sea un tipo válido
  const validTypes: StockMovement['type'][] = ['purchase', 'use', 'adjustment', 'return', 'waste'];
  const type = validTypes.includes(movement.type as StockMovement['type'])
    ? (movement.type as StockMovement['type'])
    : 'adjustment';

  return {
    id: movement.id,
    productId: movement.product_id,
    quantity: movement.quantity_ml,
    type,
    date: movement.created_at,
    userId: movement.created_by || 'system',
    referenceId: movement.reference_id || undefined,
    notes: movement.notes || null,
  };
}

/**
 * Handle sync errors consistently across all operations
 * @param operation - The operation that failed
 * @param error - The error that occurred
 * @param data - Optional data to add to sync queue
 */
async function handleSyncError(
  operation: 'create' | 'update' | 'delete',
  error: unknown,
  data?: Record<string, unknown>
): Promise<void> {
  inventoryLogger.error(`Error during ${operation} sync:`, error);

  if (data) {
    useSyncQueueStore.getState().addToQueue({
      type: operation,
      table: 'products',
      data,
    });
  }
}

// Analytics helper functions moved to inventory-analytics-store.ts
// Keeping backward compatibility through delegation

export const useInventoryStore = create<InventoryStore>()(
  persist(
    (set, get) => ({
      products: [],
      movements: [],
      alerts: [],
      lastSync: null,
      isLoading: false,
      isInitialized: false,
      productMappings: [],

      // Initialize filter and grouping state
      activeFilters: {
        stockStatus: 'all',
        categories: [],
        brands: [],
        searchQuery: '',
      },
      sortBy: 'name',
      groupBy: 'none',

      loadProducts: async () => {
        inventoryLogger.startTimer('loadProducts');
        set({ isLoading: true });

        try {
          const salonId = await getCurrentSalonId();
          if (!salonId) {
            // Silently return if no salon ID (user not authenticated yet)
            set({ isLoading: false });
            return;
          }

          // Load products
          const { data: productsData, error: productsError } = await supabase
            .from('products')
            .select('*')
            .eq('salon_id', salonId)
            .order('name', { ascending: true });

          if (productsError) throw productsError;

          // Load recent movements
          const { data: movementsData, error: movementsError } = await supabase
            .from('stock_movements')
            .select('*')
            .eq('salon_id', salonId)
            .order('created_at', { ascending: false })
            .limit(100);

          if (movementsError) throw movementsError;

          const localProducts = productsData?.map(convertSupabaseToLocal) || [];
          const localMovements = movementsData?.map(m => convertSupabaseMovementToLocal(m)) || [];

          set({
            products: localProducts,
            movements: localMovements,
            isLoading: false,
            isInitialized: true,
            lastSync: new Date().toISOString(),
          });

          inventoryLogger.info('Products loaded', {
            productCount: localProducts.length,
            movementCount: localMovements.length,
          });

          // Check for low stock after loading
          get().checkLowStock();

          inventoryLogger.endTimer('loadProducts');
        } catch (error) {
          inventoryLogger.error('Error loading inventory:', error);
          set({ isLoading: false });
        }
      },

      addProduct: async productData => {
        inventoryLogger.startTimer('addProduct');
        const tempId = generateLocalId('product');

        // Generate displayName if not provided
        const displayName = productData.displayName || generateDisplayName(productData);

        const newProduct: Product = {
          ...productData,
          id: tempId,
          displayName,
          lastUpdated: new Date().toISOString(),
          _syncStatus: 'pending',
          _localId: tempId,
        };

        // 1. UI Optimista - Actualizar inmediatamente
        set(state => ({
          products: [...state.products, newProduct],
        }));

        // Check stock levels
        get().checkLowStock();

        // 2. Intentar sincronizar con Supabase
        const { isOnline } = useSyncQueueStore.getState();

        if (isOnline) {
          try {
            const salonId = await getCurrentSalonId();
            if (!salonId) throw new Error('Usuario no autenticado');

            const supabaseData = convertLocalToSupabase(productData);
            const { data, error } = await supabase
              .from('products')
              .insert({
                ...supabaseData,
                salon_id: salonId,
              })
              .select()
              .single();

            if (error) throw error;

            // 3. Actualizar con datos reales de Supabase
            set(state => ({
              products: state.products.map(p =>
                p.id === tempId ? convertSupabaseToLocal(data) : p
              ),
            }));

            inventoryLogger.endTimer('addProduct');
            return data.id;
          } catch (error) {
            await handleSyncError('create', error, {
              ...convertLocalToSupabase(productData),
              _tempId: tempId,
            });

            // Marcar como error
            set(state => ({
              products: state.products.map(p =>
                p.id === tempId ? { ...p, _syncStatus: 'error' } : p
              ),
            }));
          }
        } else {
          // Sin conexión, agregar a la cola
          await handleSyncError('create', null, {
            ...convertLocalToSupabase(productData),
            _tempId: tempId,
          });
        }

        return tempId;
      },

      updateProduct: async (id, updates) => {
        inventoryLogger.startTimer('updateProduct');

        // Regenerar displayName si se actualizan campos relevantes
        const finalUpdates = { ...updates };
        if (updates.brand || updates.line || updates.shade || updates.type) {
          const currentProduct = get().products.find(p => p.id === id);
          if (currentProduct) {
            const updatedProduct = { ...currentProduct, ...updates };
            finalUpdates.displayName = generateDisplayName(updatedProduct);
          }
        }

        // 1. Actualizar UI inmediatamente
        set(state => ({
          products: state.products.map(p =>
            p.id === id
              ? {
                  ...p,
                  ...finalUpdates,
                  lastUpdated: new Date().toISOString(),
                  _syncStatus: 'pending',
                }
              : p
          ),
        }));

        // Check stock levels after update
        get().checkLowStock();

        // 2. Intentar sincronizar
        const { isOnline } = useSyncQueueStore.getState();

        if (isOnline && !isLocalId(id)) {
          try {
            const product = get().products.find(p => p.id === id);
            if (!product) return;

            const supabaseUpdates = convertLocalToSupabase({
              ...product,
              ...updates,
            });
            const { error } = await supabase.from('products').update(supabaseUpdates).eq('id', id);

            if (error) throw error;

            // Marcar como sincronizado
            set(state => ({
              products: state.products.map(p =>
                p.id === id ? { ...p, _syncStatus: 'synced' } : p
              ),
            }));

            inventoryLogger.endTimer('updateProduct');
          } catch (error) {
            await handleSyncError('update', error, { id, ...updates });

            // Marcar como error
            set(state => ({
              products: state.products.map(p => (p.id === id ? { ...p, _syncStatus: 'error' } : p)),
            }));
          }
        } else {
          // Sin conexión o ID local, agregar a la cola
          await handleSyncError('update', null, { id, ...updates });
        }
      },

      deleteProduct: async id => {
        inventoryLogger.startTimer('deleteProduct');

        // 1. Actualizar UI inmediatamente
        const deletedProduct = get().products.find(p => p.id === id);
        set(state => ({
          products: state.products.filter(p => p.id !== id),
          movements: state.movements.filter(m => m.productId !== id),
          alerts: state.alerts.filter(a => a.productId !== id),
        }));

        // 2. Intentar sincronizar
        const { isOnline } = useSyncQueueStore.getState();

        if (isOnline && !isLocalId(id)) {
          try {
            const { error } = await supabase.from('products').delete().eq('id', id);

            if (error) throw error;

            inventoryLogger.info('Product deleted', { id });
            inventoryLogger.endTimer('deleteProduct');
          } catch (error) {
            await handleSyncError('delete', error, { id });

            // Restaurar el producto si falló
            if (deletedProduct) {
              set(state => ({
                products: [...state.products, { ...deletedProduct, _syncStatus: 'error' }],
              }));
            }
          }
        } else {
          // Sin conexión o ID local, agregar a la cola
          await handleSyncError('delete', null, { id });
        }
      },

      getProduct: id => {
        return get().products.find(p => p.id === id);
      },

      getProductByNameAndBrand: (name, brand) => {
        return get().products.find(
          p =>
            p.name.toLowerCase() === name.toLowerCase() &&
            p.brand.toLowerCase() === brand.toLowerCase()
        );
      },

      searchProducts: query => {
        const searchTerms = query.toLowerCase().split(' ');
        return get().products.filter(product => {
          const searchableText =
            `${product.name} ${product.brand} ${product.category || ''}`.toLowerCase();
          return searchTerms.every(term => searchableText.includes(term));
        });
      },

      updateStock: async (productId, quantity, type, reason, referenceId) => {
        inventoryLogger.startTimer('updateStock');

        const product = get().getProduct(productId);
        if (!product) return;

        const newStock = product.currentStock + quantity;

        // Update stock immediately
        await get().updateProduct(productId, { currentStock: newStock });

        // Create movement record
        const movement: StockMovement = {
          id: generateLocalId('movement'),
          productId,
          quantity,
          type,
          date: new Date().toISOString(),
          userId: 'current-user', // TODO: Get from auth
          referenceId,
          notes: reason,
        };

        set(state => ({
          movements: [movement, ...state.movements],
        }));

        // Sync to Supabase if online
        const { isOnline } = useSyncQueueStore.getState();
        if (isOnline) {
          try {
            const salonId = await getCurrentSalonId();
            if (!salonId) throw new Error('No salon ID');

            const { error } = await supabase.from('stock_movements').insert({
              salon_id: salonId,
              product_id: productId,
              quantity_ml: quantity,
              type: typeSpanishToEnglish[type] || type,
              notes: reason,
              reference_id: referenceId,
            });

            if (error) throw error;

            inventoryLogger.endTimer('updateStock');
          } catch (error) {
            inventoryLogger.error('Error syncing stock movement:', error);
          }
        }
      },

      consumeProducts: async (consumptions, referenceId, clientName) => {
        inventoryLogger.startTimer('consumeProducts');

        // Process each consumption in parallel for better performance
        const consumptionPromises = consumptions.map(({ productId, quantity }) =>
          get().updateStock(
            productId,
            -quantity, // Negative for consumption
            'consumo',
            `Servicio para ${clientName}`,
            referenceId
          )
        );

        await Promise.all(consumptionPromises);

        inventoryLogger.info('Products consumed', {
          count: consumptions.length,
          referenceId,
        });
        inventoryLogger.endTimer('consumeProducts');
      },

      getStockMovements: (productId, limit = 50) => {
        const movements = get().movements;
        const filtered = productId ? movements.filter(m => m.productId === productId) : movements;
        return filtered.slice(0, limit);
      },

      createAlert: (productId, type, message, severity = 'warning') => {
        const alert: InventoryAlert = {
          id: generateLocalId('alert'),
          productId,
          type,
          message,
          severity,
          createdAt: new Date().toISOString(),
          isActive: true,
        };

        set(state => ({
          alerts: [...state.alerts, alert],
        }));

        inventoryLogger.info('Alert created', { type, severity, productId });
      },

      acknowledgeAlert: (alertId, userId) => {
        set(state => ({
          alerts: state.alerts.map(a =>
            a.id === alertId
              ? {
                  ...a,
                  isActive: false,
                  acknowledgedBy: userId,
                  acknowledgedAt: new Date().toISOString(),
                }
              : a
          ),
        }));
      },

      getActiveAlerts: () => {
        return get().alerts.filter(a => a.isActive);
      },

      checkLowStock: () => {
        const products = get().products;
        const existingAlerts = get().alerts;

        products.forEach(product => {
          if (!product.isActive) return;

          const hasActiveAlert = existingAlerts.some(
            a => a.productId === product.id && a.isActive && a.type === 'low_stock'
          );

          if (product.currentStock <= product.minStock && !hasActiveAlert) {
            get().createAlert(
              product.id,
              'low_stock',
              `${product.name} (${product.brand}) está por debajo del stock mínimo`,
              product.currentStock === 0 ? 'error' : 'warning'
            );
          }
        });

        // Load low stock products through analytics store
        useInventoryAnalyticsStore.getState().loadLowStockProducts();
      },

      // Analytics methods delegated to analytics store for backward compatibility
      loadLowStockProducts: async () => {
        return useInventoryAnalyticsStore.getState().loadLowStockProducts();
      },

      getLowStockProducts: () => {
        return useInventoryAnalyticsStore.getState().getLowStockProducts();
      },

      getConsumptionAnalysis: (productId, period) => {
        return useInventoryAnalyticsStore
          .getState()
          .getConsumptionAnalysis(productId, period, get().movements, get().getProduct);
      },

      generateInventoryReport: () => {
        return useInventoryAnalyticsStore
          .getState()
          .generateInventoryReport(get().products, get().movements);
      },

      getProductsByCategory: category => {
        return useInventoryAnalyticsStore
          .getState()
          .getProductsByCategory(get().products, category);
      },

      getTotalInventoryValue: () => {
        return useInventoryAnalyticsStore.getState().getTotalInventoryValue(get().products);
      },

      loadInventoryReport: () => {
        useInventoryAnalyticsStore.getState().loadInventoryReport(get().products, get().movements);
      },

      clearInventoryReport: () => {
        useInventoryAnalyticsStore.getState().clearInventoryReport();
      },

      initializeWithDefaults: () => {
        const productsWithIds = defaultProducts.map(p => ({
          ...p,
          id: generateLocalId('product'),
          lastUpdated: new Date().toISOString(),
          _syncStatus: 'pending' as const,
        }));

        set({ products: productsWithIds });
        get().checkLowStock();

        inventoryLogger.info('Initialized with default products', {
          count: productsWithIds.length,
        });
      },

      generateMockMovements: () => {
        const products = get().products;
        const movements: StockMovement[] = [];

        products.forEach(product => {
          // Generate 5-10 random movements per product
          const movementCount = Math.floor(Math.random() * 6) + 5;

          for (let i = 0; i < movementCount; i++) {
            const daysAgo = Math.floor(Math.random() * 90); // Last 90 days
            const date = new Date();
            date.setDate(date.getDate() - daysAgo);

            const types: StockMovement['type'][] = ['consumo', 'compra', 'ajuste'];
            const type = types[Math.floor(Math.random() * types.length)];

            let quantity: number;
            if (type === 'consumo') {
              quantity = -(Math.floor(Math.random() * 50) + 10); // -10 to -60
            } else if (type === 'compra') {
              quantity = Math.floor(Math.random() * 5) * product.unitSize; // Buy in units
            } else {
              quantity = Math.floor(Math.random() * 40) - 20; // -20 to +20
            }

            movements.push({
              id: generateLocalId('movement'),
              productId: product.id,
              quantity,
              type,
              date: date.toISOString(),
              userId: 'demo-user',
              notes: `Demo ${type}`,
            });
          }
        });

        set(state => ({
          movements: [...movements, ...state.movements].sort(
            (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
          ),
        }));

        inventoryLogger.info('Generated mock movements', {
          count: movements.length,
        });
      },

      clearAllData: () => {
        set({
          products: [],
          movements: [],
          alerts: [],
          lastSync: null,
          isInitialized: false,
          productMappings: [],
        });
        inventoryLogger.info('All inventory data cleared');
      },

      syncWithSupabase: async () => {
        inventoryLogger.startTimer('syncWithSupabase');

        const salonId = await getCurrentSalonId();
        if (!salonId) {
          inventoryLogger.warn('No salon ID for sync');
          return;
        }

        await get().loadProducts();

        inventoryLogger.endTimer('syncWithSupabase');
      },

      migrateToUnitsSystem: () => {
        inventoryLogger.info('Starting units system migration');

        set(state => ({
          products: state.products.map(product => {
            // Ensure unitType is set
            if (!product.unitType) {
              const isLiquid = ['tinte', 'oxidante', 'tratamiento'].includes(
                product.category || ''
              );
              return {
                ...product,
                unitType: isLiquid ? 'ml' : 'g',
                unitSize: product.unitSize || (isLiquid ? 1000 : 500),
              };
            }
            return product;
          }),
        }));

        inventoryLogger.info('Units system migration completed');
      },

      fixMalformedProducts: async () => {
        inventoryLogger.info('Fixing malformed products');

        const { products } = get();
        let fixedCount = 0;

        // Process product fixes in parallel for better performance
        const fixPromises = products.map(async product => {
          let needsUpdate = false;
          const updates: Partial<Product> = {};

          // Solo regenerar displayName si tiene formato incorrecto
          // Ya NO cambiamos "Salerm Cosmetics" porque es correcto
          if (
            product.displayName &&
            !product.displayName.includes(product.brand || '') &&
            product.brand
          ) {
            needsUpdate = true;
          }

          if (needsUpdate) {
            // Regenerar displayName con los datos actuales
            updates.displayName = generateDisplayName(product);

            // Actualizar el producto
            await get().updateProduct(product.id, updates);

            inventoryLogger.info('Fixed malformed product:', {
              id: product.id,
              original: product.displayName,
              fixed: updates.displayName,
            });

            return 1; // Fixed one product
          }
          return 0; // Nothing fixed
        });

        const results = await Promise.allSettled(fixPromises);
        fixedCount = results
          .filter(result => result.status === 'fulfilled')
          .reduce((sum, result) => sum + result.value, 0);

        inventoryLogger.info(`Fixed ${fixedCount} malformed products`);
      },

      // Product Mapping functions
      saveProductMapping: async (
        aiProductName: string,
        inventoryProductId: string,
        confidence: number
      ) => {
        inventoryLogger.info('Saving product mapping', {
          aiProductName,
          inventoryProductId,
          confidence,
        });

        const salonId = await getCurrentSalonId();
        if (!salonId) {
          inventoryLogger.warn('No salon ID for saving mapping');
          return;
        }

        try {
          const { data, error } = await supabase
            .from('product_mappings')
            .upsert(
              {
                salon_id: salonId,
                ai_product_name: aiProductName,
                inventory_product_id: inventoryProductId,
                confidence,
              },
              {
                onConflict: 'salon_id,ai_product_name',
              }
            )
            .select()
            .single();

          if (error) throw error;

          // Add to local store
          set(state => {
            const existing = state.productMappings.findIndex(
              m => m.aiProductName === aiProductName
            );

            const newMapping: ProductMapping = {
              id: data.id,
              salonId: data.salon_id,
              aiProductName: data.ai_product_name,
              inventoryProductId: data.inventory_product_id,
              confidence: data.confidence,
              usageCount: data.usage_count,
              createdAt: data.created_at,
              updatedAt: data.updated_at,
            };

            if (existing >= 0) {
              const mappings = [...state.productMappings];
              mappings[existing] = newMapping;
              return { productMappings: mappings };
            } else {
              return {
                productMappings: [...state.productMappings, newMapping],
              };
            }
          });

          inventoryLogger.info('Product mapping saved successfully');
        } catch (error) {
          inventoryLogger.error('Error saving product mapping:', error);
          throw error;
        }
      },

      getProductMapping: (aiProductName: string) => {
        const state = get();
        return state.productMappings.find(m => m.aiProductName === aiProductName);
      },

      incrementMappingUsage: async (aiProductName: string) => {
        inventoryLogger.info('Incrementing mapping usage', { aiProductName });

        const salonId = await getCurrentSalonId();
        if (!salonId) {
          inventoryLogger.warn('No salon ID for incrementing usage');
          return;
        }

        try {
          const { error } = await supabase.rpc('increment_mapping_usage', {
            p_salon_id: salonId,
            p_ai_product_name: aiProductName,
          });

          if (error) throw error;

          // Update local store
          set(state => ({
            productMappings: state.productMappings.map(m =>
              m.aiProductName === aiProductName
                ? {
                    ...m,
                    usageCount: m.usageCount + 1,
                    updatedAt: new Date().toISOString(),
                  }
                : m
            ),
          }));

          inventoryLogger.info('Mapping usage incremented successfully');
        } catch (error) {
          inventoryLogger.error('Error incrementing mapping usage:', error);
        }
      },

      loadProductMappings: async () => {
        inventoryLogger.startTimer('loadProductMappings');

        const salonId = await getCurrentSalonId();
        if (!salonId) {
          inventoryLogger.warn('No salon ID for loading mappings');
          return;
        }

        try {
          const { data, error } = await supabase
            .from('product_mappings')
            .select('*')
            .eq('salon_id', salonId)
            .order('usage_count', { ascending: false });

          if (error) throw error;

          const mappings: ProductMapping[] = (data || []).map(m => ({
            id: m.id,
            salonId: m.salon_id,
            aiProductName: m.ai_product_name,
            inventoryProductId: m.inventory_product_id,
            confidence: m.confidence,
            usageCount: m.usage_count,
            createdAt: m.created_at,
            updatedAt: m.updated_at,
          }));

          set({ productMappings: mappings });

          inventoryLogger.endTimer('loadProductMappings', {
            count: mappings.length,
          });
        } catch (error) {
          inventoryLogger.error('Error loading product mappings:', error);
        }
      },

      // Filter and Grouping Actions Implementation
      setFilter: (filterType, value) => {
        set(state => ({
          activeFilters: {
            ...state.activeFilters,
            [filterType]: value,
          },
        }));
      },

      setSortBy: sortBy => {
        set({ sortBy });
      },

      setGroupBy: groupBy => {
        set({ groupBy });
      },

      resetFilters: () => {
        set({
          activeFilters: {
            stockStatus: 'all',
            categories: [],
            brands: [],
            searchQuery: '',
          },
          sortBy: 'name',
          groupBy: 'none',
        });
      },

      getFilteredAndSortedProducts: () => {
        const state = get();
        let filtered = [...state.products];

        // Apply search filter
        if (state.activeFilters.searchQuery) {
          const query = state.activeFilters.searchQuery.toLowerCase();
          filtered = filtered.filter(
            p =>
              p.name?.toLowerCase().includes(query) ||
              p.displayName?.toLowerCase().includes(query) ||
              p.brand?.toLowerCase().includes(query) ||
              p.line?.toLowerCase().includes(query) ||
              p.shade?.toLowerCase().includes(query) ||
              p.type?.toLowerCase().includes(query)
          );
        }

        // Apply stock status filter
        if (state.activeFilters.stockStatus !== 'all') {
          filtered = filtered.filter(p => {
            switch (state.activeFilters.stockStatus) {
              case 'low':
                return p.currentStock <= p.minStock && p.currentStock > 0;
              case 'out':
                return p.currentStock === 0;
              case 'ok':
                return p.currentStock > p.minStock;
              default:
                return true;
            }
          });
        }

        // Apply category filter
        if (state.activeFilters.categories.length > 0) {
          filtered = filtered.filter(p => state.activeFilters.categories.includes(p.category));
        }

        // Apply brand filter
        if (state.activeFilters.brands.length > 0) {
          filtered = filtered.filter(p => p.brand && state.activeFilters.brands.includes(p.brand));
        }

        // Sort products
        filtered.sort((a, b) => {
          switch (state.sortBy) {
            case 'name':
              return (a.displayName || a.name || '').localeCompare(b.displayName || b.name || '');
            case 'stock':
              return a.currentStock - b.currentStock;
            case 'price':
              return a.costPerUnit - b.costPerUnit;
            case 'brand':
              return (a.brand || '').localeCompare(b.brand || '');
            case 'usage':
              // Count movements for each product in the last 30 days
              const thirtyDaysAgo = new Date();
              thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
              const aUsage = state.movements.filter(
                m => m.productId === a.id && new Date(m.date) > thirtyDaysAgo
              ).length;
              const bUsage = state.movements.filter(
                m => m.productId === b.id && new Date(m.date) > thirtyDaysAgo
              ).length;
              return bUsage - aUsage;
            default:
              return 0;
          }
        });

        return filtered;
      },

      getGroupedProducts: () => {
        const state = get();
        const filtered = get().getFilteredAndSortedProducts();
        const grouped = new Map<string, Product[]>();

        if (state.groupBy === 'none') {
          grouped.set('Todos los productos', filtered);
          return grouped;
        }

        filtered.forEach(product => {
          let groupKey = 'Sin categoría';

          switch (state.groupBy) {
            case 'brand':
              groupKey = product.brand || 'Sin marca';
              break;
            case 'line':
              groupKey = product.line || 'Sin línea';
              break;
            case 'category':
              groupKey = product.category || 'Otro';
              break;
            case 'type':
              groupKey = product.type || 'Sin tipo';
              break;
          }

          if (!grouped.has(groupKey)) {
            grouped.set(groupKey, []);
          }
          grouped.get(groupKey)!.push(product);
        });

        // Sort groups alphabetically
        return new Map([...grouped.entries()].sort((a, b) => a[0].localeCompare(b[0])));
      },

      getFrequentlyUsedProducts: (limit = 10) => {
        return useInventoryAnalyticsStore
          .getState()
          .getFrequentlyUsedProducts(get().products, get().movements, limit);
      },

      getProductsMatchingFormula: async formulaProducts => {
        const _state = get();
        const matching: Product[] = [];

        // Import at runtime to avoid circular dependency
        const { InventoryConsumptionService } = await import(
          '@/services/inventoryConsumptionService'
        );

        // Process all products in parallel for better performance
        const matchingPromises = formulaProducts.map(async formulaProduct => {
          const matches = await InventoryConsumptionService.findMatchingProducts(formulaProduct);
          return matches.length > 0 && matches[0].matchScore > 60 ? matches[0].product : null;
        });

        const results = await Promise.all(matchingPromises);
        matching.push(
          ...results.filter((product): product is typeof product & object => product !== null)
        );

        return matching;
      },
    }),
    {
      name: 'inventory-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: state => ({
        products: state.products,
        movements: state.movements.slice(0, 100), // Only keep recent movements
        alerts: state.alerts.filter(a => a.isActive), // Only keep active alerts
        lastSync: state.lastSync,
        isInitialized: state.isInitialized,
      }),
    }
  )
);
