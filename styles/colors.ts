/**
 * CENTRALIZED COLOR CONSTANTS
 *
 * Extracted color literals to comply with ESLint rules.
 * All inline color values are centralized here for maintainability.
 * These constants work alongside the main Colors system.
 */

export const ColorConstants = {
  // Pure colors
  TRANSPARENT: 'transparent',
  BLACK: '#000000',
  WHITE: '#FFFFFF',
  RED: 'red',
  GREEN: 'green',
  ORANGE: 'orange',

  // Alpha variations for overlays
  TRANSPARENT_BLACK_05: 'rgba(0, 0, 0, 0.05)',
  TRANSPARENT_BLACK_40: 'rgba(0, 0, 0, 0.4)',
  TRANSPARENT_BLACK_60: 'rgba(0, 0, 0, 0.6)',
  TRANSPARENT_BLACK_70: 'rgba(0, 0, 0, 0.7)',
  TRANSPARENT_BLACK_80: 'rgba(0, 0, 0, 0.8)',
  TRANSPARENT_WHITE_20: 'rgba(255, 255, 255, 0.2)',
  TRANSPARENT_WHITE_60: 'rgba(255, 255, 255, 0.6)',
  TRANSPARENT_WHITE_90: 'rgba(255, 255, 255, 0.9)',

  // Hair color palette
  HAIR_AMMONIA: '#E6F3FF',
  HAIR_BASE: '#8B6B3D',
  HAIR_TONER: '#DDA0DD',
  HAIR_BLEACH: '#F5F3F0',
  HAIR_DYE: '#4A148C',
} as const;
