# 🏗️ SALONIER MASSIVE REFACTORING ROADMAP

**Project**: Salonier Hair Coloration AI Assistant  
**Objective**: Transform monolithic files into Clean Architecture  
**Timeline**: 14-day sprint (September 7-21, 2025)  
**Success Criteria**: All files <300 lines, SOLID principles, >95% test coverage

---

## 🎯 STRATEGIC OVERVIEW

### Critical Refactoring Targets

1. **salonier-assistant/index.ts** (4,377 lines) - HIGHEST RISK ⚠️
   - Central AI hub serving all Edge Functions
   - Single point of failure for entire AI system
   - Security-critical with authentication handling

2. **inventory-store.ts** (1,601 lines) - HIGH BUSINESS IMPACT 📈
   - Core business logic for product management
   - Complex state management with offline sync
   - Multiple responsibilities violating SRP

3. **chat-store.ts** (~800 lines) - USER EXPERIENCE CRITICAL 💬
   - Real-time messaging state management
   - Complex conversation threading
   - AI integration points

4. **client-history-store.ts** (~600 lines) - DATA INTEGRITY CRITICAL 📊
   - Service history and analytics
   - Formula tracking and feedback system
   - Multi-tenant data isolation

---

## 📋 SPRINT ORGANIZATION (4 x 3.5-day Sprints)

### 🚀 SPRINT 1: EDGE FUNCTION DECOMPOSITION [Sep 7-10]
**Risk**: Critical - Touch point for all AI operations  
**Focus**: Safety-first approach with zero downtime

### 🏪 SPRINT 2: INVENTORY DOMAIN EXTRACTION [Sep 11-14]
**Risk**: High - Core business functionality  
**Focus**: Domain-driven design patterns

### 💬 SPRINT 3: COMMUNICATION LAYER SEPARATION [Sep 15-18]
**Risk**: Medium - User experience dependent  
**Focus**: Clean interfaces and state management

### 📊 SPRINT 4: DATA LAYER OPTIMIZATION [Sep 19-21]
**Risk**: Medium - Historical data integrity  
**Focus**: Performance and maintainability

---

# 🚀 SPRINT 1: EDGE FUNCTION DECOMPOSITION [Sep 7-10]

## 🎯 Sprint Objective
Decompose the massive salonier-assistant Edge Function (4,377 lines) into specialized, maintainable microservices following Single Responsibility Principle.

**Capacity**: 3.5 days effective  
**Risk Level**: ⚠️ CRITICAL - Central AI system  
**Success Metrics**: Zero downtime, performance maintained

---

## 📋 SPRINT 1 TASKS

### 🔴 PHASE 1A: SAFETY INFRASTRUCTURE [Day 1 - Morning] (P0 - Critical)

**[EDGE-SAFETY-001] 🛡️ Create Rollback System**
- **Agent**: deployment-engineer
- **MCP Tools**: Supabase MCP (branching, deployment)
- **Time**: 2 hours
- **Files**:
  - `.github/workflows/edge-function-rollback.yml` - CREATE
  - `scripts/rollback-edge-function.sh` - CREATE
  - `supabase/functions/health-check/` - CREATE monitoring endpoint
- **Deliverables**:
  - One-click rollback to current consolidated version
  - Health monitoring with alerts
  - Automated testing pipeline
- **Risk Mitigation**:
  - Blue-green deployment strategy
  - Function versioning with semantic tags
  - Real-time monitoring dashboard

**[EDGE-SAFETY-002] 🧪 Integration Test Suite**
- **Agent**: test-runner
- **MCP Tools**: IDE MCP (diagnostics)
- **Time**: 1.5 hours
- **Files**:
  - `tests/edge-functions/integration/` - CREATE directory
  - `salonier-assistant.integration.test.ts` - CREATE comprehensive tests
  - `test-fixtures/ai-responses/` - CREATE realistic test data
- **Deliverables**:
  - End-to-end test coverage for all AI endpoints
  - Performance benchmarks (latency <3s target)
  - Error scenario testing (timeout, auth, rate limits)
- **Success Criteria**:
  - 100% endpoint coverage
  - Realistic test scenarios with actual hair analysis data
  - Automated CI/CD validation

---

### 🟡 PHASE 1B: DOMAIN ANALYSIS & ARCHITECTURE [Day 1 - Afternoon] (P1 - High)

**[EDGE-ANALYSIS-001] 🔍 Function Responsibility Mapping**
- **Agent**: database-architect
- **MCP Tools**: Serena MCP (code analysis)
- **Time**: 2 hours
- **Analysis Scope**:
  - Map all 15+ AI operations in current consolidated function
  - Identify shared dependencies and utilities
  - Document authentication and caching patterns
- **Deliverables**:
  - Responsibility matrix with clear boundaries
  - Dependency graph visualization
  - Performance bottleneck identification
- **Output**:
  ```
  supabase/functions/analysis/
  ├── responsibility-matrix.md
  ├── dependency-graph.mmd
  └── performance-analysis.json
  ```

**[EDGE-ARCHITECTURE-001] 🏗️ Clean Architecture Design**
- **Agent**: ai-integration-specialist
- **MCP Tools**: Context7 MCP (architecture patterns)
- **Time**: 1.5 hours
- **Design Focus**:
  - Hexagonal Architecture for AI services
  - Shared kernel for common utilities
  - Event-driven communication patterns
- **Files to Create**:
  - `supabase/functions/shared/` - Common utilities extraction
  - `docs/architecture/edge-functions-v2.md` - Architecture documentation
- **Principles**:
  - Single Responsibility per function
  - Dependency Inversion for AI providers
  - Interface Segregation for different AI operations

---

### 🔴 PHASE 1C: CORE AI SERVICES EXTRACTION [Day 2] (P0 - Critical)

**[EDGE-CORE-001] 🤖 Hair Analysis Service**
- **Agent**: ai-integration-specialist
- **Time**: 3 hours
- **New Function**: `supabase/functions/hair-analysis/`
- **Extracted Functionality**:
  - GPT-4 Vision image analysis
  - Hair level and tone detection
  - Confidence scoring and validation
- **Clean Architecture**:
  ```typescript
  // Domain Layer
  export interface HairAnalysisResult {
    level: number;
    tone: string;
    confidence: number;
    zones: ZoneAnalysis[];
  }
  
  // Application Layer  
  export class HairAnalysisService {
    constructor(
      private aiProvider: AIProvider,
      private validator: AnalysisValidator
    ) {}
  }
  
  // Infrastructure Layer
  export class OpenAIVisionProvider implements AIProvider
  ```
- **Performance Target**: <2s analysis time
- **Error Handling**: Graceful degradation with fallback responses

**[EDGE-CORE-002] ⚗️ Formula Generation Service**
- **Agent**: colorimetry-expert + ai-integration-specialist
- **Time**: 4 hours
- **New Function**: `supabase/functions/formula-generation/`
- **Extracted Functionality**:
  - Chemical validation and safety checks
  - Brand-specific formula optimization
  - Cost calculation and product matching
- **Clean Architecture**:
  ```typescript
  // Domain Layer
  export interface FormulationRequest {
    hairAnalysis: HairAnalysisResult;
    desiredResult: DesiredLook;
    availableProducts: Product[];
  }
  
  // Application Layer
  export class FormulaGenerationService {
    constructor(
      private chemicalValidator: ChemicalValidator,
      private brandExpert: BrandExpertise,
      private costCalculator: CostCalculator
    ) {}
  }
  ```
- **Safety**: Multi-layer chemical validation
- **Business Logic**: Brand expertise integration

---

### 🟡 PHASE 1D: SUPPORT SERVICES EXTRACTION [Day 3] (P1 - High)

**[EDGE-SUPPORT-001] 💬 Chat Assistant Service**
- **Agent**: ai-integration-specialist
- **Time**: 2 hours  
- **New Function**: `supabase/functions/chat-assistant/`
- **Extracted Functionality**:
  - Conversational AI with salon context
  - Token usage tracking and cost management
  - Multi-tenant conversation isolation
- **Architecture**:
  ```typescript
  // Application Layer
  export class ChatAssistantService {
    constructor(
      private conversationManager: ConversationManager,
      private tokenTracker: TokenUsageTracker,
      private contextProvider: SalonContextProvider
    ) {}
  }
  ```

**[EDGE-SUPPORT-002] 📷 Image Upload Service**
- **Agent**: security-privacy-auditor
- **Time**: 1.5 hours
- **New Function**: `supabase/functions/image-upload/`
- **Security Focus**:
  - GDPR-compliant image processing
  - Automatic PII detection and masking
  - Secure storage with TTL cleanup
- **Features**:
  - Image compression and optimization
  - Format validation and sanitization
  - Metadata extraction for analysis

**[EDGE-SUPPORT-003] 🔄 Shared Utilities Library**
- **Agent**: debug-specialist
- **Time**: 2 hours
- **New Module**: `supabase/functions/shared/`
- **Extracted Utilities**:
  - Security utilities (JWT masking, sanitization)
  - Logger with structured logging
  - AI router with model selection
  - Chemical validator with safety rules
- **Architecture**:
  ```typescript
  // Shared kernel exports
  export { SecurityUtils } from './security/index.ts';
  export { Logger } from './logging/index.ts';
  export { AIRouter } from './ai/router.ts';
  export { ChemicalValidator } from './chemistry/validator.ts';
  ```

---

### 🟢 PHASE 1E: INTEGRATION & VALIDATION [Day 3.5] (P2 - Medium)

**[EDGE-INTEGRATION-001] 🔗 Service Integration Testing**
- **Agent**: test-runner
- **Time**: 2 hours
- **Integration Points**:
  - Cross-service communication validation
  - Authentication flow end-to-end
  - Error propagation and handling
- **Test Scenarios**:
  - Happy path: Image → Analysis → Formula → Chat
  - Error scenarios: Timeout, auth failure, invalid input
  - Performance testing: Concurrent requests, rate limiting

**[EDGE-DEPLOYMENT-001] 🚀 Phased Deployment Strategy**
- **Agent**: deployment-engineer
- **MCP Tools**: Supabase MCP (deployment, monitoring)
- **Time**: 1.5 hours
- **Strategy**:
  - Phase 1: Deploy new services alongside existing (parallel)
  - Phase 2: Route 10% traffic to new services
  - Phase 3: Gradual rollout with monitoring
  - Phase 4: Sunset consolidated function
- **Monitoring**:
  - Real-time latency and error rate dashboards
  - Automated rollback triggers
  - Success metrics tracking

---

## 📊 SPRINT 1 SUCCESS CRITERIA

### 🎯 Functional Requirements ✅
- [ ] All AI operations maintain <3s latency
- [ ] Zero downtime during transition
- [ ] 100% feature parity with consolidated version
- [ ] Error rates <0.1% in production

### 🏗️ Architecture Requirements ✅
- [ ] Each service <300 lines of code
- [ ] Clear separation of concerns (Domain/App/Infra)
- [ ] Shared utilities properly extracted
- [ ] Comprehensive test coverage >95%

### 🛡️ Security & Compliance ✅
- [ ] GDPR compliance maintained
- [ ] Authentication flows validated
- [ ] Security logging preserved
- [ ] Data isolation verified

### 📈 Performance Metrics ✅
- [ ] Function cold start time <500ms
- [ ] Memory usage optimized per service
- [ ] Concurrent request handling verified
- [ ] Cost impact analysis completed

---

## 🚨 RISK MITIGATION STRATEGIES

### Risk 1: AI Service Failures
- **Impact**: High - Core functionality breakdown
- **Mitigation**: 
  - Fallback responses for each service
  - Circuit breaker pattern implementation
  - Multiple AI provider support (OpenAI + backup)
- **Rollback Plan**: Instant revert to consolidated function

### Risk 2: Authentication Complexity
- **Impact**: Critical - Security breach potential  
- **Mitigation**:
  - Shared authentication middleware
  - Centralized JWT validation
  - Row Level Security preservation
- **Testing**: Extensive auth flow validation

### Risk 3: Performance Degradation
- **Impact**: High - User experience impact
- **Mitigation**:
  - Performance benchmarking before/after
  - Intelligent caching strategies
  - Connection pooling optimization
- **Monitoring**: Real-time performance dashboards

### Risk 4: Integration Failures  
- **Impact**: Medium - Feature incompatibilities
- **Mitigation**:
  - Comprehensive integration test suite
  - Contract testing between services
  - Gradual rollout with feature flags
- **Recovery**: Service-level rollback capabilities

---

## 🔄 ROLLBACK PLAN

### Immediate Rollback (0-5 minutes)
1. Switch DNS/routing to consolidated function
2. Pause new service deployments
3. Alert engineering team

### Investigation Phase (5-30 minutes)  
1. Analyze error logs and metrics
2. Identify root cause
3. Assess fix complexity

### Resolution Strategies
- **Quick Fix**: Hotfix and redeploy (<1 hour)
- **Temporary Revert**: Use consolidated function until fix
- **Gradual Recovery**: Selective service restoration

---

# 🏪 SPRINT 2: INVENTORY DOMAIN EXTRACTION [Sep 11-14]

## 🎯 Sprint Objective
Transform the monolithic inventory-store.ts (1,601 lines) into a domain-driven architecture with clear business boundaries, offline-first capabilities, and optimal performance.

**Capacity**: 3.5 days effective  
**Risk Level**: 📈 HIGH - Core business functionality  
**Success Metrics**: Maintain offline-first, improve performance 30%

---

## 📋 SPRINT 2 TASKS

### 🔴 PHASE 2A: DOMAIN MODELING & BOUNDARIES [Day 1 - Morning] (P0 - Critical)

**[INVENTORY-DOMAIN-001] 🎯 Domain Boundary Analysis**
- **Agent**: database-architect
- **MCP Tools**: Serena MCP (symbol analysis), Supabase MCP (table analysis)
- **Time**: 2 hours
- **Analysis Scope**:
  - Map business capabilities vs technical responsibilities
  - Identify aggregate roots and entities
  - Define bounded contexts with clear interfaces
- **Current Responsibilities Analysis**:
  ```
  inventory-store.ts (1,601 lines) contains:
  - Product catalog management (CRUD operations)
  - Stock movement tracking and history
  - Inventory alerts and low stock monitoring  
  - Consumption analytics and reporting
  - Product matching and search functionality
  - Offline synchronization logic
  - Filter and grouping state management
  ```
- **Domain Boundaries**:
  ```
  📦 Product Catalog Domain
  ├── Product entities and value objects
  ├── Brand/Line/Type hierarchy
  └── Product search and matching

  📊 Stock Management Domain  
  ├── Stock movements and transactions
  ├── Consumption tracking
  └── Inventory level monitoring

  🔔 Alert Management Domain
  ├── Low stock alerts
  ├── Inventory notifications  
  └── Alert acknowledgment workflow

  📈 Analytics Domain
  ├── Consumption analysis
  ├── Inventory reports
  └── Usage pattern insights
  
  🔄 Sync Infrastructure Domain
  ├── Offline state management
  ├── Conflict resolution
  └── Queue management
  ```

**[INVENTORY-ARCHITECTURE-001] 🏗️ Clean Architecture Design**
- **Agent**: frontend-developer + database-architect  
- **Time**: 1.5 hours
- **Architecture Pattern**: Domain-Driven Design + Clean Architecture
- **Structure**:
  ```typescript
  stores/inventory/
  ├── domain/
  │   ├── entities/
  │   │   ├── Product.ts
  │   │   ├── StockMovement.ts
  │   │   └── InventoryAlert.ts
  │   ├── value-objects/
  │   │   ├── StockLevel.ts
  │   │   ├── ProductCode.ts
  │   │   └── MoneyAmount.ts
  │   └── repositories/
  │       └── IInventoryRepository.ts
  ├── application/
  │   ├── services/
  │   │   ├── ProductCatalogService.ts
  │   │   ├── StockManagementService.ts
  │   │   └── AlertManagementService.ts
  │   └── use-cases/
  │       ├── AddProductUseCase.ts
  │       ├── ConsumeProductsUseCase.ts
  │       └── GenerateInventoryReportUseCase.ts
  └── infrastructure/
      ├── repositories/
      │   └── SupabaseInventoryRepository.ts
      ├── sync/
      │   └── OfflineSyncManager.ts
      └── storage/
          └── LocalStorageAdapter.ts
  ```

---

### 🟡 PHASE 2B: DOMAIN ENTITIES & VALUE OBJECTS [Day 1 - Afternoon] (P1 - High)

**[INVENTORY-ENTITIES-001] 🏛️ Core Domain Entities**
- **Agent**: frontend-developer
- **Time**: 2.5 hours
- **Entities to Extract**:

```typescript
// domain/entities/Product.ts
export class Product {
  constructor(
    private readonly id: ProductId,
    private name: ProductName,
    private brand: Brand,
    private line: ProductLine,
    private stockLevel: StockLevel,
    private pricing: ProductPricing
  ) {}
  
  updateStock(movement: StockMovement): void {
    this.stockLevel = this.stockLevel.apply(movement);
    this.recordDomainEvent(new StockUpdatedEvent(this.id, movement));
  }
  
  isLowStock(): boolean {
    return this.stockLevel.isBelowMinimum();
  }
}

// domain/value-objects/StockLevel.ts  
export class StockLevel {
  constructor(
    private readonly current: number,
    private readonly minimum: number,
    private readonly unit: StockUnit
  ) {
    if (current < 0) throw new Error('Stock cannot be negative');
  }
  
  apply(movement: StockMovement): StockLevel {
    const newAmount = this.current + movement.quantity;
    return new StockLevel(newAmount, this.minimum, this.unit);
  }
  
  isBelowMinimum(): boolean {
    return this.current <= this.minimum;
  }
}
```

**[INVENTORY-AGGREGATES-001] 🎯 Aggregate Root Definition**
- **Agent**: database-architect
- **Time**: 1 hour  
- **Aggregates**:
  - **Product Aggregate**: Product + Stock movements
  - **Alert Aggregate**: Alert + Acknowledgments
  - **Report Aggregate**: Report + Analytics data

---

### 🔴 PHASE 2C: APPLICATION SERVICES EXTRACTION [Day 2] (P0 - Critical)

**[INVENTORY-SERVICES-001] 📦 Product Catalog Service**
- **Agent**: frontend-developer
- **Time**: 3 hours
- **Service Responsibilities**:
  - Product CRUD operations
  - Product search and filtering
  - Brand/line hierarchy management
- **Clean Implementation**:
```typescript
// application/services/ProductCatalogService.ts
export class ProductCatalogService {
  constructor(
    private repository: IInventoryRepository,
    private eventDispatcher: IEventDispatcher,
    private logger: ILogger
  ) {}
  
  async addProduct(command: AddProductCommand): Promise<ProductId> {
    const product = Product.create(command);
    await this.repository.save(product);
    
    this.eventDispatcher.dispatch(
      new ProductAddedEvent(product.id, product.name)
    );
    
    return product.id;
  }
  
  async searchProducts(query: SearchQuery): Promise<Product[]> {
    return this.repository.findByQuery(query);
  }
}
```

**[INVENTORY-SERVICES-002] 📊 Stock Management Service**
- **Agent**: frontend-developer  
- **Time**: 3 hours
- **Service Responsibilities**:
  - Stock movement tracking
  - Consumption recording
  - Stock level updates
- **Complex Operations**:
```typescript  
// application/services/StockManagementService.ts
export class StockManagementService {
  async consumeProducts(
    command: ConsumeProductsCommand
  ): Promise<ConsumptionResult> {
    const products = await this.repository.findByIds(command.productIds);
    
    // Domain validation
    const insufficientStock = products.filter(p => 
      !p.hasAvailableStock(command.getQuantityFor(p.id))
    );
    
    if (insufficientStock.length > 0) {
      throw new InsufficientStockError(insufficientStock);
    }
    
    // Apply consumption
    const movements = products.map(product => {
      const quantity = command.getQuantityFor(product.id);
      return product.consume(quantity, command.referenceId);
    });
    
    await this.repository.saveMovements(movements);
    
    return ConsumptionResult.success(movements);
  }
}
```

---

### 🟡 PHASE 2D: INFRASTRUCTURE LAYER [Day 2.5-3] (P1 - High)

**[INVENTORY-INFRA-001] 🗄️ Repository Implementation**
- **Agent**: database-architect + offline-sync-specialist
- **Time**: 2 hours
- **Repository Pattern**:
```typescript
// infrastructure/repositories/SupabaseInventoryRepository.ts
export class SupabaseInventoryRepository implements IInventoryRepository {
  constructor(
    private supabase: SupabaseClient,
    private offlineCache: IOfflineCache,
    private syncQueue: ISyncQueue
  ) {}
  
  async save(product: Product): Promise<void> {
    try {
      await this.supabase.from('products').upsert(product.toDatabase());
    } catch (error) {
      // Offline-first: Cache locally and queue for sync
      await this.offlineCache.store(product);
      await this.syncQueue.enqueue(new SaveProductOperation(product));
    }
  }
  
  async findByIds(ids: ProductId[]): Promise<Product[]> {
    // Try cache first, then database
    const cached = await this.offlineCache.findByIds(ids);
    const missingIds = ids.filter(id => !cached.has(id));
    
    if (missingIds.length === 0) {
      return Array.from(cached.values());
    }
    
    const fromDb = await this.supabase
      .from('products')
      .select('*')
      .in('id', missingIds);
    
    return [...cached.values(), ...fromDb.data.map(Product.fromDatabase)];
  }
}
```

**[INVENTORY-INFRA-002] 🔄 Offline Sync Manager**
- **Agent**: offline-sync-specialist
- **Time**: 2 hours
- **Sync Strategy**:
```typescript
// infrastructure/sync/OfflineSyncManager.ts
export class InventoryOfflineSyncManager {
  async syncPendingOperations(): Promise<SyncResult> {
    const operations = await this.syncQueue.getPending();
    const results = [];
    
    for (const operation of operations) {
      try {
        await this.executeOperation(operation);
        await this.syncQueue.markCompleted(operation.id);
        results.push({ operation: operation.id, status: 'success' });
      } catch (error) {
        if (this.isConflictError(error)) {
          await this.resolveConflict(operation, error);
        } else {
          await this.syncQueue.markFailed(operation.id, error);
        }
        results.push({ operation: operation.id, status: 'failed', error });
      }
    }
    
    return new SyncResult(results);
  }
  
  private async resolveConflict(
    operation: SyncOperation, 
    conflict: ConflictError
  ): Promise<void> {
    // Last-write-wins strategy for inventory operations
    const serverVersion = await this.repository.findById(operation.entityId);
    const localVersion = operation.data;
    
    if (serverVersion.lastUpdated > localVersion.lastUpdated) {
      // Server wins - update local cache
      await this.offlineCache.update(serverVersion);
    } else {
      // Local wins - retry operation
      await this.executeOperation(operation);
    }
  }
}
```

---

### 🟢 PHASE 2E: STORE COMPOSITION & INTEGRATION [Day 3.5] (P2 - Medium)

**[INVENTORY-COMPOSITION-001] 🔧 Zustand Store Facade**
- **Agent**: frontend-developer
- **Time**: 2 hours
- **Facade Pattern**: Maintain existing API while using new architecture
```typescript
// stores/inventory-store.ts (NEW - Facade)
export const useInventoryStore = create<InventoryStore>()(
  persist(
    (set, get) => {
      // Inject dependencies
      const repository = new SupabaseInventoryRepository(supabase, cache, queue);
      const productService = new ProductCatalogService(repository, dispatcher, logger);
      const stockService = new StockManagementService(repository, dispatcher, logger);
      const alertService = new AlertManagementService(repository, dispatcher, logger);
      
      return {
        // State
        products: [],
        movements: [],
        alerts: [],
        isLoading: false,
        
        // Actions (delegate to services)
        addProduct: async (productData) => {
          set({ isLoading: true });
          try {
            const command = AddProductCommand.from(productData);
            const productId = await productService.addProduct(command);
            
            // Update local state optimistically
            const product = Product.fromCommand(command, productId);
            set(state => ({ 
              products: [...state.products, product.toDTO()],
              isLoading: false 
            }));
            
            return productId;
          } catch (error) {
            set({ isLoading: false });
            throw error;
          }
        },
        
        consumeProducts: async (consumptions, referenceId, clientName) => {
          const command = new ConsumeProductsCommand(consumptions, referenceId, clientName);
          await stockService.consumeProducts(command);
          
          // Update local state
          await get().loadProducts();
        }
      };
    },
    { name: 'inventory-store' }
  )
);
```

**[INVENTORY-TESTING-001] 🧪 Comprehensive Test Suite**
- **Agent**: test-runner
- **Time**: 1.5 hours
- **Test Coverage**:
  - Domain entity tests (business logic)
  - Service integration tests
  - Repository contract tests
  - Offline sync scenario tests

---

## 📊 SPRINT 2 SUCCESS CRITERIA

### 🎯 Business Requirements ✅
- [ ] All inventory operations maintain functionality
- [ ] Offline-first behavior preserved
- [ ] Performance improvement >30% (measured via load times)
- [ ] Data integrity maintained across refactor

### 🏗️ Architecture Requirements ✅  
- [ ] Each file <300 lines of code
- [ ] Clear domain boundaries established
- [ ] Single Responsibility Principle applied
- [ ] Dependency Inversion implemented

### 📱 User Experience Requirements ✅
- [ ] No UX changes or regressions
- [ ] Existing store API maintained
- [ ] Response times improved or maintained
- [ ] Error handling enhanced with better messages

---

# 💬 SPRINT 3: COMMUNICATION LAYER SEPARATION [Sep 15-18]

## 🎯 Sprint Objective
Refactor chat-store.ts (~800 lines) into clean communication architecture with real-time capabilities, optimal performance, and maintainable conversation management.

**Capacity**: 3.5 days effective  
**Risk Level**: 💬 MEDIUM - User experience dependent  
**Success Metrics**: Real-time performance, conversation reliability

---

## 📋 SPRINT 3 TASKS

### 🔴 PHASE 3A: COMMUNICATION DOMAIN ANALYSIS [Day 1] (P0 - Critical)

**[CHAT-DOMAIN-001] 🗨️ Conversation Domain Modeling**
- **Agent**: ux-researcher + frontend-developer
- **Time**: 2.5 hours
- **Domain Analysis**:
```typescript
// Current chat-store.ts responsibilities:
// 1. Conversation management (create, archive, retrieve)
// 2. Message handling (send, receive, status tracking)
// 3. Attachment management (upload, download, display)
// 4. AI integration (token tracking, cost calculation)
// 5. Real-time synchronization
// 6. Offline message queuing
// 7. Context management (salon, client, service)
// 8. Search and filtering
```

- **Domain Boundaries**:
```typescript
🗨️ Conversation Management Domain
├── Conversation lifecycle
├── Participant management
└── Context binding (salon/client/service)

💬 Message Processing Domain
├── Message creation and validation
├── Status tracking (sent/delivered/read)
└── Message threading and ordering

📎 Attachment Handling Domain
├── File upload and validation
├── Image processing and thumbnails
└── Storage management

🤖 AI Integration Domain
├── AI request/response handling
├── Token usage and cost tracking
└── Context enrichment

🔄 Real-time Sync Domain
├── WebSocket connection management
├── Conflict resolution
└── Offline queue management
```

**[CHAT-ARCHITECTURE-001] 🏗️ Event-Driven Architecture Design**  
- **Agent**: ai-integration-specialist + frontend-developer
- **Time**: 1.5 hours
- **Architecture**: Event-Driven + CQRS pattern
```typescript
stores/communication/
├── domain/
│   ├── aggregates/
│   │   ├── Conversation.ts
│   │   ├── Message.ts
│   │   └── Attachment.ts
│   ├── events/
│   │   ├── MessageSentEvent.ts
│   │   ├── ConversationCreatedEvent.ts
│   │   └── AttachmentUploadedEvent.ts
│   └── services/
│       ├── IMessageService.ts
│       └── IConversationService.ts
├── application/
│   ├── commands/
│   │   ├── SendMessageCommand.ts
│   │   └── CreateConversationCommand.ts
│   ├── queries/
│   │   ├── GetConversationQuery.ts
│   │   └── SearchMessagesQuery.ts
│   └── handlers/
│       ├── SendMessageHandler.ts
│       └── CreateConversationHandler.ts
└── infrastructure/
    ├── websocket/
    │   └── RealtimeMessageBridge.ts
    ├── ai/
    │   └── ChatAssistantIntegration.ts
    └── storage/
        └── ConversationRepository.ts
```

---

### 🟡 PHASE 3B: CORE COMMUNICATION ENTITIES [Day 1.5] (P1 - High)

**[CHAT-ENTITIES-001] 🗨️ Conversation Aggregate**
- **Agent**: frontend-developer
- **Time**: 2 hours
```typescript
// domain/aggregates/Conversation.ts
export class Conversation {
  private messages: Map<MessageId, Message> = new Map();
  private participants: Set<UserId> = new Set();
  
  constructor(
    private readonly id: ConversationId,
    private title: ConversationTitle,
    private context: ConversationContext,
    private status: ConversationStatus
  ) {}
  
  sendMessage(
    content: MessageContent, 
    senderId: UserId,
    attachments?: Attachment[]
  ): Message {
    const message = Message.create({
      conversationId: this.id,
      content,
      senderId,
      attachments
    });
    
    this.messages.set(message.id, message);
    this.recordDomainEvent(new MessageSentEvent(this.id, message.id));
    
    return message;
  }
  
  addAIResponse(response: AIResponse, tokens: TokenUsage): void {
    const assistantMessage = Message.createAssistantMessage({
      conversationId: this.id,
      content: response.content,
      tokenUsage: tokens,
      costUsd: tokens.calculateCost()
    });
    
    this.messages.set(assistantMessage.id, assistantMessage);
    this.recordDomainEvent(new AIResponseReceivedEvent(this.id, assistantMessage.id));
  }
  
  getMessages(limit?: number, offset?: number): Message[] {
    return Array.from(this.messages.values())
      .sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime())
      .slice(offset || 0, limit ? (offset || 0) + limit : undefined);
  }
}
```

**[CHAT-ENTITIES-002] 💬 Message Entity**
- **Agent**: frontend-developer  
- **Time**: 1.5 hours
```typescript
// domain/aggregates/Message.ts
export class Message {
  constructor(
    private readonly id: MessageId,
    private readonly conversationId: ConversationId,
    private role: MessageRole,
    private content: MessageContent,
    private status: MessageStatus,
    private attachments: Attachment[],
    private tokenUsage?: TokenUsage,
    private readonly createdAt: Date
  ) {}
  
  markAsDelivered(): void {
    if (this.status === MessageStatus.SENT) {
      this.status = MessageStatus.DELIVERED;
      this.recordDomainEvent(new MessageDeliveredEvent(this.id));
    }
  }
  
  markAsRead(): void {
    if (this.status === MessageStatus.DELIVERED) {
      this.status = MessageStatus.READ;
      this.recordDomainEvent(new MessageReadEvent(this.id));
    }
  }
  
  addAttachment(attachment: Attachment): void {
    if (this.attachments.length >= MAX_ATTACHMENTS_PER_MESSAGE) {
      throw new TooManyAttachmentsError();
    }
    
    this.attachments.push(attachment);
    this.recordDomainEvent(new AttachmentAddedEvent(this.id, attachment.id));
  }
  
  calculateCost(): number {
    return this.tokenUsage?.calculateCost() || 0;
  }
}
```

---

### 🔴 PHASE 3C: APPLICATION SERVICES [Day 2] (P0 - Critical)

**[CHAT-SERVICES-001] 💬 Message Service**
- **Agent**: ai-integration-specialist
- **Time**: 3 hours
```typescript
// application/services/MessageService.ts
export class MessageService {
  constructor(
    private conversationRepo: IConversationRepository,
    private aiAssistant: IChatAssistant,
    private realtimeService: IRealtimeService,
    private eventBus: IEventBus
  ) {}
  
  async sendMessage(command: SendMessageCommand): Promise<Message> {
    const conversation = await this.conversationRepo.findById(command.conversationId);
    if (!conversation) {
      throw new ConversationNotFoundError(command.conversationId);
    }
    
    // Create and send user message
    const userMessage = conversation.sendMessage(
      command.content,
      command.senderId,
      command.attachments
    );
    
    await this.conversationRepo.save(conversation);
    
    // Broadcast real-time update
    await this.realtimeService.broadcast(
      conversation.id,
      new MessageSentEvent(conversation.id, userMessage.id)
    );
    
    // Trigger AI response if needed
    if (command.expectsAIResponse) {
      await this.requestAIResponse(conversation, userMessage);
    }
    
    return userMessage;
  }
  
  private async requestAIResponse(
    conversation: Conversation,
    userMessage: Message
  ): Promise<void> {
    try {
      const context = await this.buildAIContext(conversation);
      const response = await this.aiAssistant.generateResponse({
        conversationId: conversation.id,
        userMessage: userMessage.content,
        context
      });
      
      conversation.addAIResponse(response, response.tokenUsage);
      await this.conversationRepo.save(conversation);
      
      // Broadcast AI response
      await this.realtimeService.broadcast(
        conversation.id,
        new AIResponseReceivedEvent(conversation.id, response.messageId)
      );
      
    } catch (error) {
      // Handle AI service failures gracefully
      const fallbackMessage = this.createFallbackResponse(error);
      conversation.addAIResponse(fallbackMessage, TokenUsage.zero());
      await this.conversationRepo.save(conversation);
    }
  }
}
```

**[CHAT-SERVICES-002] 📎 Attachment Service**
- **Agent**: security-privacy-auditor
- **Time**: 2 hours
```typescript
// application/services/AttachmentService.ts
export class AttachmentService {
  constructor(
    private storageProvider: IStorageProvider,
    private imageProcessor: IImageProcessor,
    private virusScanner: IVirusScanner
  ) {}
  
  async uploadAttachment(command: UploadAttachmentCommand): Promise<Attachment> {
    // Security validations
    await this.validateFile(command.file);
    await this.virusScanner.scan(command.file);
    
    // Process image if applicable
    let processedFile = command.file;
    if (this.isImage(command.file)) {
      processedFile = await this.imageProcessor.process({
        file: command.file,
        maxWidth: 1920,
        maxHeight: 1080,
        quality: 0.85,
        generateThumbnail: true
      });
    }
    
    // Upload to storage
    const storageResult = await this.storageProvider.upload({
      file: processedFile,
      path: this.generateStoragePath(command),
      metadata: {
        originalName: command.file.name,
        uploadedBy: command.userId,
        conversationId: command.conversationId
      }
    });
    
    return Attachment.create({
      id: AttachmentId.generate(),
      type: this.detectFileType(command.file),
      url: storageResult.url,
      fileName: command.file.name,
      fileSize: command.file.size,
      thumbnailUrl: storageResult.thumbnailUrl
    });
  }
}
```

---

### 🟡 PHASE 3D: REAL-TIME INFRASTRUCTURE [Day 2.5-3] (P1 - High)

**[CHAT-REALTIME-001] 🔄 WebSocket Integration**
- **Agent**: frontend-developer + ai-integration-specialist  
- **Time**: 2.5 hours
```typescript
// infrastructure/websocket/RealtimeMessageBridge.ts
export class RealtimeMessageBridge {
  private connection: RealtimeConnection;
  private subscriptions: Map<ConversationId, RealtimeSubscription> = new Map();
  
  constructor(
    private supabaseClient: SupabaseClient,
    private eventBus: IEventBus
  ) {
    this.connection = new RealtimeConnection(supabaseClient);
  }
  
  async subscribeToConversation(conversationId: ConversationId): Promise<void> {
    if (this.subscriptions.has(conversationId)) {
      return; // Already subscribed
    }
    
    const subscription = this.connection
      .channel(`conversation:${conversationId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'chat_messages',
          filter: `conversation_id=eq.${conversationId}`
        },
        (payload) => this.handleMessageReceived(payload)
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'chat_messages',
          filter: `conversation_id=eq.${conversationId}`
        },
        (payload) => this.handleMessageUpdated(payload)
      )
      .subscribe();
    
    this.subscriptions.set(conversationId, subscription);
  }
  
  private handleMessageReceived(payload: RealtimePayload): void {
    const message = Message.fromDatabase(payload.new);
    this.eventBus.publish(new MessageReceivedEvent(message.conversationId, message));
  }
  
  async broadcastTypingIndicator(
    conversationId: ConversationId,
    userId: UserId,
    isTyping: boolean
  ): Promise<void> {
    const channel = this.connection.channel(`conversation:${conversationId}`);
    await channel.send({
      type: 'broadcast',
      event: 'typing',
      payload: { userId, isTyping, timestamp: new Date() }
    });
  }
}
```

**[CHAT-OFFLINE-001] 📱 Offline Queue Management**
- **Agent**: offline-sync-specialist
- **Time**: 2 hours
```typescript
// infrastructure/offline/MessageOfflineQueue.ts
export class MessageOfflineQueue {
  private queue: PendingMessage[] = [];
  private isProcessing = false;
  
  constructor(
    private messageService: IMessageService,
    private storage: ILocalStorage
  ) {
    this.loadQueueFromStorage();
  }
  
  async enqueue(message: PendingMessage): Promise<void> {
    this.queue.push(message);
    await this.persistQueue();
    
    // Try to process immediately if online
    if (navigator.onLine && !this.isProcessing) {
      await this.processQueue();
    }
  }
  
  async processQueue(): Promise<ProcessResult> {
    if (this.isProcessing || this.queue.length === 0) {
      return ProcessResult.skipped();
    }
    
    this.isProcessing = true;
    const results: ProcessResult[] = [];
    
    try {
      while (this.queue.length > 0) {
        const pendingMessage = this.queue[0];
        
        try {
          await this.messageService.sendMessage(pendingMessage.toCommand());
          this.queue.shift(); // Remove successfully sent message
          results.push(ProcessResult.success(pendingMessage.id));
        } catch (error) {
          if (this.isPermanentError(error)) {
            // Remove permanently failed messages
            this.queue.shift();
            results.push(ProcessResult.permanentFailure(pendingMessage.id, error));
          } else {
            // Keep for retry
            results.push(ProcessResult.temporaryFailure(pendingMessage.id, error));
            break; // Stop processing on temporary failure
          }
        }
      }
      
      await this.persistQueue();
      return ProcessResult.batch(results);
      
    } finally {
      this.isProcessing = false;
    }
  }
}
```

---

### 🟢 PHASE 3E: STORE INTEGRATION & TESTING [Day 3.5] (P2 - Medium)

**[CHAT-INTEGRATION-001] 🔧 Zustand Store Facade**
- **Agent**: frontend-developer
- **Time**: 2 hours
```typescript
// stores/chat-store.ts (NEW - Clean facade)
export const useChatStore = create<ChatStore>()(
  persist(
    (set, get) => {
      // Dependency injection
      const conversationRepo = new SupabaseConversationRepository(supabase);
      const messageService = new MessageService(conversationRepo, aiAssistant, realtime, eventBus);
      const attachmentService = new AttachmentService(storage, imageProcessor, scanner);
      const realtimeBridge = new RealtimeMessageBridge(supabase, eventBus);
      const offlineQueue = new MessageOfflineQueue(messageService, localStorage);
      
      return {
        // State
        conversations: [],
        activeConversationId: null,
        isLoading: false,
        
        // Actions
        sendMessage: async (conversationId, content, attachments) => {
          set({ isLoading: true });
          
          try {
            const command = new SendMessageCommand({
              conversationId,
              content,
              senderId: get().currentUserId,
              attachments,
              expectsAIResponse: true
            });
            
            if (navigator.onLine) {
              await messageService.sendMessage(command);
            } else {
              await offlineQueue.enqueue(PendingMessage.fromCommand(command));
            }
            
            // Optimistic UI update
            set(state => ({
              conversations: state.conversations.map(conv =>
                conv.id === conversationId
                  ? { ...conv, messages: [...conv.messages, optimisticMessage] }
                  : conv
              ),
              isLoading: false
            }));
            
          } catch (error) {
            set({ isLoading: false });
            throw error;
          }
        },
        
        subscribeToConversation: async (conversationId) => {
          await realtimeBridge.subscribeToConversation(conversationId);
          set({ activeConversationId: conversationId });
        }
      };
    },
    { name: 'chat-store' }
  )
);
```

---

# 📊 SPRINT 4: DATA LAYER OPTIMIZATION [Sep 19-21]

## 🎯 Sprint Objective
Optimize client-history-store.ts (~600 lines) and other remaining stores into performant, maintainable data access layer with analytics capabilities.

**Capacity**: 3 days effective  
**Risk Level**: 📊 MEDIUM - Historical data integrity  
**Success Metrics**: Query performance, data accuracy, maintainability

---

## 📋 SPRINT 4 TASKS

### 🔴 PHASE 4A: DATA ARCHITECTURE ANALYSIS [Day 1 - Morning] (P0 - Critical)

**[DATA-ANALYSIS-001] 📊 Service History Domain Analysis**
- **Agent**: database-architect
- **MCP Tools**: Supabase MCP (table analysis, query optimization)
- **Time**: 2 hours
- **Analysis Scope**:
```typescript
// client-history-store.ts current responsibilities:
// 1. Service history tracking (CRUD operations)
// 2. Client service analytics and reporting
// 3. Formula performance tracking
// 4. Satisfaction scoring and feedback management
// 5. Photo management (before/after galleries)
// 6. AI analysis result caching
// 7. Cross-client service comparison
// 8. Revenue and cost analysis
// 9. Service template generation
// 10. Offline synchronization
```

- **Data Domain Boundaries**:
```typescript
🏥 Service History Domain
├── Service records and details
├── Service completion tracking
└── Service categorization

📈 Analytics Domain
├── Performance metrics calculation
├── Revenue/cost analysis
└── Trend analysis and forecasting

📷 Media Management Domain
├── Photo storage and organization
├── Before/after comparison
└── Image analysis integration

🎯 Formula Tracking Domain
├── Formula effectiveness tracking
├── Client-specific adjustments
└── Success rate analytics

📊 Reporting Domain
├── Business intelligence queries
├── Export capabilities
└── Dashboard data aggregation
```

**[DATA-OPTIMIZATION-001] ⚡ Query Performance Analysis**
- **Agent**: database-architect
- **MCP Tools**: Supabase MCP (query execution plans)
- **Time**: 1.5 hours
- **Performance Issues to Address**:
  - Complex aggregation queries for analytics
  - N+1 query problems in service loading
  - Missing indexes for frequently filtered data
  - Inefficient pagination for large datasets
- **Optimization Strategy**:
  - Implement query result caching
  - Add strategic database indexes
  - Create materialized views for complex analytics
  - Implement efficient pagination patterns

---

### 🟡 PHASE 4B: SERVICE HISTORY DOMAIN [Day 1 - Afternoon] (P1 - High)

**[DATA-ENTITIES-001] 🏥 Service History Aggregate**
- **Agent**: frontend-developer + database-architect
- **Time**: 2.5 hours
```typescript
// domain/aggregates/ServiceHistory.ts
export class ServiceHistory {
  private services: Map<ServiceId, ServiceRecord> = new Map();
  private analytics: ServiceAnalytics;
  
  constructor(
    private readonly clientId: ClientId,
    private clientInfo: ClientInfo
  ) {
    this.analytics = new ServiceAnalytics(clientId);
  }
  
  addService(serviceData: CreateServiceData): ServiceRecord {
    const service = ServiceRecord.create({
      ...serviceData,
      clientId: this.clientId,
      status: ServiceStatus.COMPLETED
    });
    
    this.services.set(service.id, service);
    this.analytics.recordService(service);
    
    this.recordDomainEvent(new ServiceAddedEvent(this.clientId, service.id));
    
    return service;
  }
  
  updateServiceSatisfaction(
    serviceId: ServiceId, 
    rating: SatisfactionRating,
    feedback?: FeedbackText
  ): void {
    const service = this.services.get(serviceId);
    if (!service) {
      throw new ServiceNotFoundError(serviceId);
    }
    
    service.updateSatisfaction(rating, feedback);
    this.analytics.updateSatisfactionMetrics();
    
    this.recordDomainEvent(
      new ServiceSatisfactionUpdatedEvent(this.clientId, serviceId, rating)
    );
  }
  
  getServicesByDateRange(start: Date, end: Date): ServiceRecord[] {
    return Array.from(this.services.values())
      .filter(service => service.isInDateRange(start, end))
      .sort((a, b) => b.completedAt.getTime() - a.completedAt.getTime());
  }
  
  calculateAverageRating(): number {
    return this.analytics.getAverageRating();
  }
  
  getFormulaSuccessRate(formulaId: FormulaId): number {
    return this.analytics.getFormulaSuccessRate(formulaId);
  }
}
```

**[DATA-ANALYTICS-001] 📈 Service Analytics Value Object**
- **Agent**: database-architect
- **Time**: 1.5 hours
```typescript
// domain/value-objects/ServiceAnalytics.ts
export class ServiceAnalytics {
  constructor(
    private readonly clientId: ClientId,
    private satisfactionScores: SatisfactionScore[] = [],
    private revenueData: RevenueRecord[] = [],
    private formulaPerformance: Map<FormulaId, PerformanceMetrics> = new Map()
  ) {}
  
  recordService(service: ServiceRecord): void {
    if (service.satisfactionRating) {
      this.satisfactionScores.push(
        new SatisfactionScore(service.id, service.satisfactionRating, service.completedAt)
      );
    }
    
    this.revenueData.push(
      new RevenueRecord(service.id, service.totalCost, service.completedAt)
    );
    
    if (service.formulaId) {
      this.updateFormulaPerformance(service);
    }
  }
  
  getAverageRating(): number {
    if (this.satisfactionScores.length === 0) return 0;
    
    const total = this.satisfactionScores.reduce((sum, score) => sum + score.rating, 0);
    return total / this.satisfactionScores.length;
  }
  
  getTrendAnalysis(months: number = 6): TrendAnalysis {
    const cutoffDate = new Date();
    cutoffDate.setMonth(cutoffDate.getMonth() - months);
    
    const recentScores = this.satisfactionScores
      .filter(score => score.date >= cutoffDate)
      .sort((a, b) => a.date.getTime() - b.date.getTime());
    
    return new TrendAnalysis({
      direction: this.calculateTrendDirection(recentScores),
      strength: this.calculateTrendStrength(recentScores),
      dataPoints: recentScores.length,
      timeframe: months
    });
  }
}
```

---

### 🔴 PHASE 4C: QUERY OPTIMIZATION & REPOSITORY PATTERN [Day 2] (P0 - Critical)

**[DATA-REPOSITORY-001] 🗄️ Service History Repository**
- **Agent**: database-architect
- **MCP Tools**: Supabase MCP (query optimization, indexing)
- **Time**: 3 hours
```typescript
// infrastructure/repositories/ServiceHistoryRepository.ts
export class SupabaseServiceHistoryRepository implements IServiceHistoryRepository {
  constructor(
    private supabase: SupabaseClient,
    private queryCache: IQueryCache,
    private logger: ILogger
  ) {}
  
  async findByClientId(clientId: ClientId): Promise<ServiceHistory | null> {
    const cacheKey = `service-history:${clientId}`;
    const cached = await this.queryCache.get(cacheKey);
    
    if (cached) {
      this.logger.debug('ServiceHistory cache hit', { clientId });
      return ServiceHistory.fromCache(cached);
    }
    
    // Optimized query with proper joins and indexing
    const { data, error } = await this.supabase
      .from('services')
      .select(`
        id,
        client_id,
        service_type,
        formula_data,
        technique,
        processing_time,
        satisfaction_rating,
        feedback_text,
        total_cost,
        created_at,
        completed_at,
        before_photos,
        after_photos,
        ai_analysis,
        clients!inner (
          id,
          name,
          email
        )
      `)
      .eq('client_id', clientId)
      .order('completed_at', { ascending: false });
    
    if (error) {
      throw new DatabaseError('Failed to load service history', error);
    }
    
    const serviceHistory = ServiceHistory.fromDatabase(data);
    
    // Cache for 10 minutes
    await this.queryCache.set(cacheKey, serviceHistory.toCache(), 600);
    
    return serviceHistory;
  }
  
  async getServiceAnalytics(
    clientId: ClientId,
    dateRange?: DateRange
  ): Promise<ServiceAnalytics> {
    // Use materialized view for complex analytics
    const query = this.supabase
      .from('service_analytics_view')
      .select('*')
      .eq('client_id', clientId);
    
    if (dateRange) {
      query
        .gte('completed_at', dateRange.start.toISOString())
        .lte('completed_at', dateRange.end.toISOString());
    }
    
    const { data, error } = await query;
    
    if (error) {
      throw new DatabaseError('Failed to load analytics', error);
    }
    
    return ServiceAnalytics.fromAnalyticsView(data);
  }
  
  async findTopPerformingFormulas(
    salonId: SalonId,
    limit: number = 10
  ): Promise<FormulaPerformance[]> {
    // Complex aggregation query optimized with proper indexing
    const { data, error } = await this.supabase
      .rpc('get_top_performing_formulas', {
        salon_id: salonId,
        result_limit: limit
      });
    
    if (error) {
      throw new DatabaseError('Failed to load formula performance', error);
    }
    
    return data.map(FormulaPerformance.fromDatabase);
  }
}
```

**[DATA-CACHE-001] ⚡ Intelligent Query Caching**
- **Agent**: performance-benchmarker
- **Time**: 2 hours
```typescript
// infrastructure/cache/ServiceQueryCache.ts
export class ServiceQueryCache implements IQueryCache {
  private cache: Map<string, CacheEntry> = new Map();
  private readonly TTL_SECONDS = {
    SERVICE_HISTORY: 600,      // 10 minutes
    ANALYTICS: 1800,           // 30 minutes
    FORMULA_PERFORMANCE: 3600  // 1 hour
  };
  
  constructor(
    private storage: AsyncStorage,
    private evictionPolicy: IEvictionPolicy = new LRUEvictionPolicy()
  ) {}
  
  async get<T>(key: string): Promise<T | null> {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }
    
    if (this.isExpired(entry)) {
      this.cache.delete(key);
      await this.storage.removeItem(key);
      return null;
    }
    
    // Update access time for LRU
    entry.lastAccessed = new Date();
    
    return entry.data as T;
  }
  
  async set<T>(key: string, data: T, ttlSeconds?: number): Promise<void> {
    const ttl = ttlSeconds || this.getTTLForKey(key);
    const entry: CacheEntry = {
      data,
      expiresAt: new Date(Date.now() + ttl * 1000),
      lastAccessed: new Date()
    };
    
    this.cache.set(key, entry);
    await this.storage.setItem(key, JSON.stringify(entry));
    
    // Check if eviction is needed
    if (this.cache.size > MAX_CACHE_SIZE) {
      await this.evictLeastRecentlyUsed();
    }
  }
  
  private getTTLForKey(key: string): number {
    if (key.includes('analytics')) return this.TTL_SECONDS.ANALYTICS;
    if (key.includes('formula-performance')) return this.TTL_SECONDS.FORMULA_PERFORMANCE;
    return this.TTL_SECONDS.SERVICE_HISTORY;
  }
}
```

---

### 🟡 PHASE 4D: PERFORMANCE OPTIMIZATION [Day 2.5-3] (P1 - High)

**[DATA-INDEXES-001] 🔍 Database Index Optimization**
- **Agent**: database-architect
- **MCP Tools**: Supabase MCP (migration creation)
- **Time**: 1.5 hours
```sql
-- Migration: optimize_service_history_queries.sql
-- Create strategic indexes for service history queries

-- Composite index for client services with date filtering
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_services_client_date 
ON services (client_id, completed_at DESC) 
WHERE completed_at IS NOT NULL;

-- Index for satisfaction rating queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_services_satisfaction 
ON services (satisfaction_rating) 
WHERE satisfaction_rating IS NOT NULL;

-- Composite index for formula performance analysis
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_services_formula_performance 
ON services (formula_data->>'selectedBrand', satisfaction_rating, completed_at)
WHERE formula_data IS NOT NULL AND satisfaction_rating IS NOT NULL;

-- Full-text search index for service notes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_services_notes_fts 
ON services USING gin(to_tsvector('english', notes))
WHERE notes IS NOT NULL;

-- Materialized view for complex analytics
CREATE MATERIALIZED VIEW service_analytics_view AS
SELECT 
  client_id,
  COUNT(*) as total_services,
  AVG(satisfaction_rating) as avg_satisfaction,
  SUM(total_cost) as total_revenue,
  COUNT(CASE WHEN satisfaction_rating >= 4 THEN 1 END) as satisfied_services,
  DATE_TRUNC('month', completed_at) as month,
  formula_data->>'selectedBrand' as brand
FROM services 
WHERE completed_at IS NOT NULL
GROUP BY client_id, DATE_TRUNC('month', completed_at), formula_data->>'selectedBrand';

-- Index the materialized view
CREATE INDEX idx_service_analytics_client 
ON service_analytics_view (client_id);

-- Refresh function for the materialized view
CREATE OR REPLACE FUNCTION refresh_service_analytics()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY service_analytics_view;
END;
$$ LANGUAGE plpgsql;
```

**[DATA-PAGINATION-001] 📄 Efficient Pagination System**
- **Agent**: performance-benchmarker
- **Time**: 1.5 hours
```typescript
// application/queries/PaginatedServiceQuery.ts
export class PaginatedServiceQuery {
  constructor(
    private repository: IServiceHistoryRepository,
    private cacheManager: ICacheManager
  ) {}
  
  async execute(request: PaginationRequest): Promise<PaginatedResult<ServiceRecord>> {
    const cacheKey = this.generateCacheKey(request);
    const cached = await this.cacheManager.get<PaginatedResult<ServiceRecord>>(cacheKey);
    
    if (cached) {
      return cached;
    }
    
    // Use cursor-based pagination for better performance
    const result = await this.repository.findPaginated({
      clientId: request.clientId,
      cursor: request.cursor,
      limit: request.limit,
      sortBy: request.sortBy,
      filters: request.filters
    });
    
    await this.cacheManager.set(cacheKey, result, 300); // 5 minute cache
    
    return result;
  }
  
  private generateCacheKey(request: PaginationRequest): string {
    return `paginated-services:${request.clientId}:${request.cursor}:${request.limit}:${JSON.stringify(request.filters)}`;
  }
}

// infrastructure/repositories/PaginatedServiceRepository.ts
export class PaginatedServiceRepository {
  async findPaginated(request: PaginatedServiceRequest): Promise<PaginatedResult<ServiceRecord>> {
    let query = this.supabase
      .from('services')
      .select('*', { count: 'exact' })
      .eq('client_id', request.clientId);
    
    // Apply cursor-based pagination
    if (request.cursor) {
      query = query.lt('completed_at', request.cursor);
    }
    
    // Apply filters efficiently
    if (request.filters.satisfactionRating) {
      query = query.gte('satisfaction_rating', request.filters.satisfactionRating);
    }
    
    if (request.filters.dateRange) {
      query = query
        .gte('completed_at', request.filters.dateRange.start.toISOString())
        .lte('completed_at', request.filters.dateRange.end.toISOString());
    }
    
    // Sort and limit
    query = query
      .order('completed_at', { ascending: false })
      .limit(request.limit + 1); // +1 to check for next page
    
    const { data, error, count } = await query;
    
    if (error) {
      throw new DatabaseError('Pagination query failed', error);
    }
    
    const hasNextPage = data.length > request.limit;
    const items = hasNextPage ? data.slice(0, request.limit) : data;
    const nextCursor = hasNextPage ? items[items.length - 1].completed_at : null;
    
    return new PaginatedResult({
      items: items.map(ServiceRecord.fromDatabase),
      hasNextPage,
      nextCursor,
      totalCount: count || 0
    });
  }
}
```

---

### 🟢 PHASE 4E: FINAL INTEGRATION & TESTING [Day 3] (P2 - Medium)

**[DATA-INTEGRATION-001] 🔧 Store Facade Integration**
- **Agent**: frontend-developer
- **Time**: 2 hours
```typescript
// stores/client-history-store.ts (NEW - Clean architecture)
export const useClientHistoryStore = create<ClientHistoryStore>()(
  persist(
    (set, get) => {
      // Dependency injection
      const repository = new SupabaseServiceHistoryRepository(supabase, queryCache, logger);
      const analyticsService = new ServiceAnalyticsService(repository);
      const paginationQuery = new PaginatedServiceQuery(repository, cacheManager);
      
      return {
        // State
        serviceHistories: new Map(),
        currentAnalytics: null,
        pagination: { hasNextPage: false, nextCursor: null },
        isLoading: false,
        
        // Actions
        loadClientHistory: async (clientId: ClientId) => {
          set({ isLoading: true });
          
          try {
            const serviceHistory = await repository.findByClientId(clientId);
            const analytics = await analyticsService.calculateAnalytics(clientId);
            
            set(state => ({
              serviceHistories: new Map(state.serviceHistories.set(clientId, serviceHistory)),
              currentAnalytics: analytics,
              isLoading: false
            }));
            
          } catch (error) {
            set({ isLoading: false });
            throw error;
          }
        },
        
        addServiceRecord: async (clientId: ClientId, serviceData: CreateServiceData) => {
          const serviceHistory = get().serviceHistories.get(clientId);
          if (!serviceHistory) {
            throw new Error('Service history not loaded');
          }
          
          const newService = serviceHistory.addService(serviceData);
          await repository.save(serviceHistory);
          
          // Update local state optimistically
          set(state => ({
            serviceHistories: new Map(state.serviceHistories.set(clientId, serviceHistory))
          }));
          
          return newService;
        },
        
        getServicesPaginated: async (request: PaginationRequest) => {
          const result = await paginationQuery.execute(request);
          
          set({
            pagination: {
              hasNextPage: result.hasNextPage,
              nextCursor: result.nextCursor
            }
          });
          
          return result;
        }
      };
    },
    { name: 'client-history-store' }
  )
);
```

**[DATA-TESTING-001] 🧪 Performance & Integration Testing**
- **Agent**: test-runner + performance-benchmarker
- **Time**: 2 hours
```typescript
// tests/stores/client-history-performance.test.ts
describe('Client History Performance Tests', () => {
  let repository: SupabaseServiceHistoryRepository;
  let queryCache: ServiceQueryCache;
  
  beforeEach(() => {
    repository = new SupabaseServiceHistoryRepository(supabase, queryCache, logger);
  });
  
  describe('Query Performance', () => {
    it('should load client history under 500ms', async () => {
      const startTime = performance.now();
      
      await repository.findByClientId('test-client-id');
      
      const duration = performance.now() - startTime;
      expect(duration).toBeLessThan(500);
    });
    
    it('should benefit from caching on subsequent loads', async () => {
      const clientId = 'test-client-id';
      
      // First load (cache miss)
      const start1 = performance.now();
      await repository.findByClientId(clientId);
      const duration1 = performance.now() - start1;
      
      // Second load (cache hit)
      const start2 = performance.now();
      await repository.findByClientId(clientId);
      const duration2 = performance.now() - start2;
      
      expect(duration2).toBeLessThan(duration1 * 0.1); // 90% improvement
    });
    
    it('should handle pagination efficiently with large datasets', async () => {
      const paginationQuery = new PaginatedServiceQuery(repository, cacheManager);
      
      const startTime = performance.now();
      
      const result = await paginationQuery.execute({
        clientId: 'test-client-with-many-services',
        limit: 20,
        cursor: null,
        sortBy: 'completedAt'
      });
      
      const duration = performance.now() - startTime;
      
      expect(duration).toBeLessThan(200); // Sub-200ms pagination
      expect(result.items.length).toBeLessThanOrEqual(20);
    });
  });
  
  describe('Analytics Performance', () => {
    it('should calculate analytics efficiently', async () => {
      const analyticsService = new ServiceAnalyticsService(repository);
      
      const startTime = performance.now();
      
      await analyticsService.calculateAnalytics('test-client-id');
      
      const duration = performance.now() - startTime;
      expect(duration).toBeLessThan(300);
    });
  });
});
```

---

# 📊 OVERALL SUCCESS METRICS & ROLLBACK PLANS

## 🎯 Success Criteria Summary

### Code Quality Metrics ✅
- [ ] **All files <300 lines** (Target: 100% compliance)
- [ ] **SOLID principles applied** (Verified via code review)
- [ ] **Test coverage >95%** (Measured via Jest coverage reports)
- [ ] **Zero circular dependencies** (Enforced via ESLint rules)

### Performance Metrics ✅  
- [ ] **AI latency <3s** (Edge functions optimization)
- [ ] **Query performance improved 30%** (Database optimization)
- [ ] **Bundle size reduction** (Lazy loading implementation)
- [ ] **Memory usage optimized** (Proper cleanup implemented)

### Business Continuity ✅
- [ ] **Zero downtime deployment** (Blue-green deployment strategy)
- [ ] **Feature parity maintained** (100% existing functionality)
- [ ] **Data integrity preserved** (Comprehensive migration testing)
- [ ] **User experience unchanged** (No breaking UI changes)

## 🚨 Master Rollback Plan

### Phase 1: Immediate Response (0-5 minutes)
1. **Deploy Rollback Scripts**
   ```bash
   # Automated rollback to pre-refactoring state
   ./scripts/emergency-rollback.sh --phase=[1|2|3|4]
   ```
2. **Switch Traffic Routing** (Blue-green deployment)
3. **Activate Monitoring Alerts**

### Phase 2: Assessment (5-30 minutes)  
1. **Error Analysis** using structured logging
2. **Performance Impact Assessment**
3. **Data Integrity Verification**

### Phase 3: Recovery Strategies
- **Quick Fix**: Hotfix specific issues (<1 hour)
- **Selective Rollback**: Revert problematic components only
- **Full Rollback**: Return to consolidated architecture
- **Gradual Re-deployment**: Fix and re-deploy incrementally

## 🛡️ Risk Mitigation Matrix

| Risk Category | Probability | Impact | Mitigation Strategy |
|---------------|-------------|---------|-------------------|
| AI Service Failure | Medium | Critical | Fallback responses, circuit breakers |
| Database Performance | Low | High | Query optimization, caching layers |
| Authentication Issues | Low | Critical | Shared auth middleware, extensive testing |
| Memory Leaks | Medium | Medium | Automated cleanup, monitoring |
| Bundle Size Increase | Low | Low | Lazy loading, tree shaking |

## 🤖 Agent Assignments Summary

### Primary Agents by Sprint
- **Sprint 1**: deployment-engineer, ai-integration-specialist, security-privacy-auditor
- **Sprint 2**: database-architect, frontend-developer, offline-sync-specialist  
- **Sprint 3**: ai-integration-specialist, frontend-developer, ux-researcher
- **Sprint 4**: database-architect, performance-benchmarker, test-runner

### Specialized Agents by Domain
- **Security**: security-privacy-auditor (GDPR compliance, auth flows)
- **Performance**: performance-benchmarker (metrics, optimization)
- **Testing**: test-runner (comprehensive test coverage)
- **Architecture**: database-architect (schema optimization, query performance)

## 📈 Expected Outcomes

### Technical Debt Reduction
- **Monolithic files eliminated**: 4,377 + 1,601 + 800 + 600 = 7,978 lines → ~40 focused files
- **Maintainability score**: Improved from C grade to A grade
- **Developer velocity**: 40% improvement in feature development speed
- **Bug reduction**: 60% fewer production issues

### Business Value
- **Reduced time-to-market**: New features can be developed 40% faster
- **Improved scalability**: System can handle 10x current load
- **Enhanced reliability**: 99.9% uptime target achievable
- **Better team productivity**: Clear domain boundaries enable parallel development

---

**📝 Document Version**: 1.0  
**Created**: September 7, 2025  
**Timeline**: 14-day sprint (Sep 7-21, 2025)  
**Success Definition**: Clean architecture with <300 lines per file, >95% test coverage, zero performance regression