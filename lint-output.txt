
> expo-app@2.0.9 lint
> eslint . --ext .ts,.tsx,.js,.jsx --ignore-pattern 'scripts/' --ignore-pattern 'supabase/functions/' --ignore-pattern '*.config.js'


/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/animation/DelightfulButton.tsx
  148:11  warning  Inline style: { marginLeft: 'icon ? BeautyMinimalTheme.spacing.xs : 0' }  react-native/no-inline-styles

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/animation/PhotoAnalysisLoading.tsx
  255:11  warning  Inline style: { borderWidth: 3 }  react-native/no-inline-styles

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/formulation/InstructionsFlowWrapper.tsx
   56:7   warning  Unexpected console statement              no-console
   69:7   warning  Unexpected console statement              no-console
  207:44  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/formulation/instructions/components/TimerCard.tsx
  240:17  warning  Color literal: {
  borderTopColor: 'transparent',
  borderRightColor: 'transparent',
  borderBottomColor: 'transparent'
}  react-native/no-color-literals

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/formulation/instructions/components/ZoneVisualizer.tsx
  142:51  warning  Inline style: { opacity: 1 }    react-native/no-inline-styles
  146:51  warning  Inline style: { opacity: 0.6 }  react-native/no-inline-styles

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/formulation/instructions/demo/StepComponents.demo.tsx
  273:23  warning  Color literal: { color: 'white' }  react-native/no-color-literals
  295:25  warning  Color literal: { color: 'white' }  react-native/no-color-literals

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/formulation/instructions/steps/ApplicationStep.tsx
   32:9   warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   36:10  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   38:11  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  238:22  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  241:25  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/formulation/instructions/steps/RinsingStep.tsx
   24:29  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  248:18  warning  Inline style: { opacity: 0.1 }            react-native/no-inline-styles

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/hair/HairLevelIndicator.tsx
  120:17  warning  Inline style: {
  color: "clampedLevel <= 4 ? '#FFFFFF' : '#1F2937'",
  textShadowColor: "clampedLevel <= 4 ? 'rgba(0,0,0,0.3)' : 'rgba(255,255,255,0.5)'",
  textShadowRadius: 2
}  react-native/no-inline-styles
  120:17  warning  Color literal: {
  color: "clampedLevel <= 4 ? '#FFFFFF' : '#1F2937'",
  textShadowColor: "clampedLevel <= 4 ? 'rgba(0,0,0,0.3)' : 'rgba(255,255,255,0.5)'"
}                        react-native/no-color-literals

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/ui/ModernChatInterface.tsx
  451:29  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/ui/SkeletonClientProfile.tsx
  46:82  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/components/ui/SkeletonServiceCard.tsx
  43:82  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/examples/RefactoredInstructionsFlow.tsx
  273:6   warning  React Hook useEffect has a missing dependency: 'progressTracking'. Either include it or remove the dependency array                                                               react-hooks/exhaustive-deps
  297:6   warning  React Hook useEffect has missing dependencies: 'hapticFeedback', 'instructionFlow', 'progressTracking', and 'stepValidation'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  558:15  warning  Color literal: { color: 'white' }                                                                                                                                                 react-native/no-color-literals
  563:14  warning  Color literal: { color: 'white' }                                                                                                                                                 react-native/no-color-literals
  574:17  warning  Color literal: { color: 'white' }                                                                                                                                                 react-native/no-color-literals
  579:16  warning  Color literal: { color: 'white' }                                                                                                                                                 react-native/no-color-literals
  590:14  warning  Color literal: { color: 'white' }                                                                                                                                                 react-native/no-color-literals
  595:15  warning  Color literal: { color: 'white' }                                                                                                                                                 react-native/no-color-literals
  639:20  warning  Color literal: { color: 'white' }                                                                                                                                                 react-native/no-color-literals
  678:11  warning  Color literal: { backgroundColor: 'white' }                                                                                                                                       react-native/no-color-literals
  702:18  warning  Color literal: { color: 'white' }                                                                                                                                                 react-native/no-color-literals

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/examples/inventory-analytics-usage.tsx
   65:24  warning  Inline style: { flex: 1, padding: 16 }                                     react-native/no-inline-styles
   66:20  warning  Inline style: { fontSize: 24, fontWeight: 'bold', marginBottom: 20 }       react-native/no-inline-styles
   71:20  warning  Inline style: { marginBottom: 20 }                                         react-native/no-inline-styles
   72:22  warning  Inline style: { fontSize: 18, fontWeight: 'bold', marginBottom: 10 }       react-native/no-inline-styles
   90:20  warning  Inline style: { marginBottom: 20 }                                         react-native/no-inline-styles
   91:22  warning  Inline style: { fontSize: 18, fontWeight: 'bold', marginBottom: 10 }       react-native/no-inline-styles
   98:38  warning  Inline style: { padding: 8, backgroundColor: '#fff3cd', marginBottom: 4 }  react-native/no-inline-styles
   98:38  warning  Color literal: { backgroundColor: '#fff3cd' }                              react-native/no-color-literals
  114:20  warning  Inline style: { marginBottom: 20 }                                         react-native/no-inline-styles
  115:22  warning  Inline style: { fontSize: 18, fontWeight: 'bold', marginBottom: 10 }       react-native/no-inline-styles
  132:20  warning  Inline style: { marginBottom: 20 }                                         react-native/no-inline-styles
  133:22  warning  Inline style: { fontSize: 18, fontWeight: 'bold', marginBottom: 10 }       react-native/no-inline-styles
  135:36  warning  Inline style: { padding: 8, backgroundColor: '#f8f9fa', marginBottom: 4 }  react-native/no-inline-styles
  135:36  warning  Color literal: { backgroundColor: '#f8f9fa' }                              react-native/no-color-literals
  145:20  warning  Inline style: { marginBottom: 20 }                                         react-native/no-inline-styles
  146:22  warning  Inline style: { fontSize: 18, fontWeight: 'bold', marginBottom: 10 }       react-native/no-inline-styles
  165:20  warning  Inline style: { marginBottom: 20 }                                         react-native/no-inline-styles
  166:22  warning  Inline style: { fontSize: 18, fontWeight: 'bold', marginBottom: 10 }       react-native/no-inline-styles

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/hooks/useAnimations.ts
    8:12  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  118:70  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/hooks/useHapticFeedback.ts
  277:11  warning  Unexpected `await` inside a loop  no-await-in-loop
  279:9   warning  Unexpected `await` inside a loop  no-await-in-loop

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/hooks/useStepTimer.ts
  168:6   warning  React Hook useEffect has a missing dependency: 'saveState'. Either include it or remove the dependency array                                                                                                                                                                     react-hooks/exhaustive-deps
  271:6   warning  React Hook useCallback has missing dependencies: 'config' and 'timerState.currentPhase'. Either include them or remove the dependency array                                                                                                                                      react-hooks/exhaustive-deps
  338:6   warning  React Hook useCallback has a missing dependency: 'clearState'. Either include it or remove the dependency array                                                                                                                                                                  react-hooks/exhaustive-deps
  523:24  warning  The ref value 'alertTimeoutsRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'alertTimeoutsRef.current' to a variable inside the effect, and use that variable in the cleanup function  react-hooks/exhaustive-deps
  532:6   warning  React Hook useEffect has a missing dependency: 'loadState'. Either include it or remove the dependency array                                                                                                                                                                     react-hooks/exhaustive-deps

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/hooks/useStepValidation.ts
  305:5  warning  React Hook useCallback has missing dependencies: 'calculateProfessionalScore' and 'getActionForRule'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  407:5  warning  React Hook useCallback has missing dependencies: 'getActiveRulesForStep' and 'runValidation'. Either include them or remove the dependency array          react-hooks/exhaustive-deps
  419:6  warning  React Hook useCallback has a missing dependency: 'runValidation'. Either include it or remove the dependency array                                        react-hooks/exhaustive-deps
  441:5  warning  React Hook useCallback has a missing dependency: 'runValidation'. Either include it or remove the dependency array                                        react-hooks/exhaustive-deps
  454:6  warning  React Hook useCallback has a missing dependency: 'runValidation'. Either include it or remove the dependency array                                        react-hooks/exhaustive-deps
  466:6  warning  React Hook useCallback has a missing dependency: 'runValidation'. Either include it or remove the dependency array                                        react-hooks/exhaustive-deps
  478:6  warning  React Hook useCallback has a missing dependency: 'runValidation'. Either include it or remove the dependency array                                        react-hooks/exhaustive-deps
  491:6  warning  React Hook useCallback has a missing dependency: 'runValidation'. Either include it or remove the dependency array                                        react-hooks/exhaustive-deps
  506:5  warning  React Hook useCallback has a missing dependency: 'runValidation'. Either include it or remove the dependency array                                        react-hooks/exhaustive-deps

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/lib/edge-functions.ts
  41:20  warning  Unexpected console statement  no-console
  51:18  warning  Unexpected console statement  no-console

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/lib/supabase.ts
  61:44  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/services/inventoryConsumptionService.ts
  351:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  351:49  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  351:74  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  352:19  warning  Unexpected `await` inside a loop          no-await-in-loop
  353:30  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  354:29  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  355:29  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  356:30  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  362:19  warning  Unexpected `await` inside a loop          no-await-in-loop
  447:23  warning  Unexpected `await` inside a loop          no-await-in-loop

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/src/service/components/DesiredColorStep.tsx
  126:6   warning  React Hook React.useEffect has missing dependencies: 'data.desiredAnalysisResult', 'data.overallReflect', 'data.overallTone', 'data.overallUndertone', and 'data.viabilityAnalysis'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  418:47  warning  Unexpected any. Specify a different type                                                                                                                                                                                                 @typescript-eslint/no-explicit-any
  421:40  warning  Unexpected any. Specify a different type                                                                                                                                                                                                 @typescript-eslint/no-explicit-any
  422:41  warning  Unexpected any. Specify a different type                                                                                                                                                                                                 @typescript-eslint/no-explicit-any
  423:45  warning  Unexpected any. Specify a different type                                                                                                                                                                                                 @typescript-eslint/no-explicit-any
  424:44  warning  Unexpected any. Specify a different type                                                                                                                                                                                                 @typescript-eslint/no-explicit-any
  425:44  warning  Unexpected any. Specify a different type                                                                                                                                                                                                 @typescript-eslint/no-explicit-any
  725:50  warning  Inline style: { marginVertical: 12, padding: 16 }                                                                                                                                                                                        react-native/no-inline-styles

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/src/service/components/DiagnosisStep.tsx
  727:6   warning  React Hook useEffect has missing dependencies: 'data.clientId', 'data.diagnosisMethod', 'data.lastChemicalProcessDate', 'data.lastChemicalProcessType', 'data.overallReflect', 'data.overallTone', and 'onUpdate'. Either include them or remove the dependency array. If 'onUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  983:52  warning  Inline style: { marginVertical: 12, padding: 16 }                                                                                                                                                                                                                                                                                                                                          react-native/no-inline-styles

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/src/service/components/FormulationStep-original.tsx
   97:6   warning  React Hook React.useEffect has missing dependencies: 'data.formula', 'data.isFormulaFromAI', 'data.selectedBrand', 'data.selectedLine', 'formula', 'selectedBrand', 'selectedLine', 'setFormula', 'setIsFormulaFromAI', 'setSelectedBrand', and 'setSelectedLine'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  112:6   warning  React Hook React.useEffect has missing dependencies: 'isFormulaFromAI', 'onUpdate', 'selectedBrand', and 'selectedLine'. Either include them or remove the dependency array. If 'onUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback                        react-hooks/exhaustive-deps
  124:6   warning  React Hook React.useEffect has missing dependencies: 'analyzeViability' and 'onUpdate'. Either include them or remove the dependency array. If 'onUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback                                                         react-hooks/exhaustive-deps
  193:51  warning  Inline style: { backgroundColor: '#8B4513' }                                                                                                                                                                                                                                                                           react-native/no-inline-styles
  193:51  warning  Color literal: { backgroundColor: '#8B4513' }                                                                                                                                                                                                                                                                          react-native/no-color-literals
  199:51  warning  Inline style: { backgroundColor: '#F5DEB3' }                                                                                                                                                                                                                                                                           react-native/no-inline-styles
  199:51  warning  Color literal: { backgroundColor: '#F5DEB3' }                                                                                                                                                                                                                                                                          react-native/no-color-literals
  478:24  warning  Color literal: { backgroundColor: 'white' }                                                                                                                                                                                                                                                                            react-native/no-color-literals
  546:20  warning  Color literal: { backgroundColor: 'white' }                                                                                                                                                                                                                                                                            react-native/no-color-literals
  561:10  warning  Color literal: { backgroundColor: 'white' }                                                                                                                                                                                                                                                                            react-native/no-color-literals
  623:30  warning  Color literal: { backgroundColor: 'white' }                                                                                                                                                                                                                                                                            react-native/no-color-literals
  685:17  warning  Color literal: { color: 'white' }                                                                                                                                                                                                                                                                                      react-native/no-color-literals
  743:25  warning  Color literal: { color: 'white' }                                                                                                                                                                                                                                                                                      react-native/no-color-literals
  781:23  warning  Color literal: { color: 'white' }                                                                                                                                                                                                                                                                                      react-native/no-color-literals

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/src/service/components/FormulationStep.tsx
  182:6  warning  React Hook React.useEffect has missing dependencies: 'data.formula', 'data.isFormulaFromAI', 'data.selectedBrand', 'data.selectedLine', 'formula', 'selectedBrand', 'selectedLine', 'setFormula', 'setIsFormulaFromAI', 'setSelectedBrand', and 'setSelectedLine'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  197:6  warning  React Hook React.useEffect has missing dependencies: 'isFormulaFromAI', 'onUpdate', 'selectedBrand', and 'selectedLine'. Either include them or remove the dependency array. If 'onUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback                        react-hooks/exhaustive-deps
  209:6  warning  React Hook React.useEffect has missing dependencies: 'analyzeViability' and 'onUpdate'. Either include them or remove the dependency array. If 'onUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback                                                         react-hooks/exhaustive-deps

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/src/service/utils/serviceHelpers.ts
  121:31  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/salonier-asistente-de-coloracion-capilar-con-ia/stores/client-history-store.ts
  234:9  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

✖ 111 problems (0 errors, 111 warnings)

