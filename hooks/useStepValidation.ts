import { useCallback, useMemo } from 'react';
import { VisualFormulationData } from '@/types/visual-formulation';
import { FlowStep } from './useInstructionFlow';
import { logger } from '@/utils/logger';

// Validation rule types
type ValidationRule = {
  id: string;
  name: string;
  description: string;
  validate: (context: ValidationContext) => boolean;
  severity: 'error' | 'warning' | 'info';
  category: 'safety' | 'professional' | 'quality' | 'materials';
};

// Validation context
interface ValidationContext {
  formulaData: VisualFormulationData;
  checkedItems: Set<string>;
  currentStep: number;
  completedSteps: Set<number>;
  elapsedTime: number;
}

// Validation result
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationIssue[];
  warnings: ValidationIssue[];
  infos: ValidationIssue[];
  completionPercentage: number;
  canProceed: boolean;
  professionalScore: number; // 0-100
}

export interface ValidationIssue {
  ruleId: string;
  message: string;
  severity: 'error' | 'warning' | 'info';
  category: 'safety' | 'professional' | 'quality' | 'materials';
  actionRequired?: string;
  autoFix?: () => void;
}

// Hook options
interface UseStepValidationOptions {
  steps: FlowStep[];
  formulaData: VisualFormulationData;
  strictMode?: boolean; // Whether warnings should block progression
  enableProfessionalScoring?: boolean;
}

// Hook return interface
export interface UseStepValidationReturn {
  // Current validation state
  validateCurrentStep: () => ValidationResult;
  validateStep: (stepIndex: number, context: Partial<ValidationContext>) => ValidationResult;
  validateAllSteps: () => ValidationResult;

  // Specific validations
  validateChecklist: (checkedItems: Set<string>) => ValidationResult;
  validateSafetyRequirements: () => ValidationResult;
  validateProfessionalStandards: () => ValidationResult;
  validateMaterialsAvailability: () => ValidationResult;

  // Quality checks
  checkFormulationQuality: () => ValidationResult;
  checkTimingCompliance: (elapsedTime: number) => ValidationResult;
  checkProcessOrder: (completedSteps: Set<number>) => ValidationResult;

  // Professional scoring
  calculateProfessionalScore: (context: ValidationContext) => number;
  getProfessionalRecommendations: () => string[];

  // Auto-fix capabilities
  getAutoFixSuggestions: (issues: ValidationIssue[]) => ValidationIssue[];
  applyAutoFixes: (issues: ValidationIssue[]) => Promise<void>;

  // Validation rules management
  getActiveRules: () => ValidationRule[];
  toggleRule: (ruleId: string, enabled: boolean) => void;
  addCustomRule: (rule: ValidationRule) => void;
}

// Predefined validation rules
const SAFETY_RULES: ValidationRule[] = [
  {
    id: 'gloves_required',
    name: 'Guantes de protección',
    description: 'Se requieren guantes de nitrilo o látex para la aplicación',
    category: 'safety',
    severity: 'error',
    validate: context => context.checkedItems.has('protective_gloves'),
  },
  {
    id: 'eye_protection',
    name: 'Protección ocular',
    description: 'Protección ocular requerida para químicos fuertes',
    category: 'safety',
    severity: 'warning',
    validate: context => {
      const hasStrongChemicals = context.formulaData.products?.some(
        product => product.type === 'bleach' || product.name?.toLowerCase().includes('decolorant')
      );
      return !hasStrongChemicals || context.checkedItems.has('eye_protection');
    },
  },
  {
    id: 'ventilation_check',
    name: 'Ventilación adecuada',
    description: 'Área bien ventilada para evitar inhalación de vapores',
    category: 'safety',
    severity: 'warning',
    validate: context => context.checkedItems.has('proper_ventilation'),
  },
  {
    id: 'skin_test',
    name: 'Prueba de sensibilidad',
    description: 'Prueba de parche realizada 48h antes del servicio',
    category: 'safety',
    severity: 'error',
    validate: context => context.checkedItems.has('patch_test_completed'),
  },
];

const PROFESSIONAL_RULES: ValidationRule[] = [
  {
    id: 'hair_analysis_complete',
    name: 'Análisis capilar completo',
    description: 'Diagnóstico profesional del cabello completado',
    category: 'professional',
    severity: 'error',
    validate: context => {
      return (
        context.formulaData.hairAnalysis?.overallHealth !== undefined &&
        context.formulaData.hairAnalysis?.porosity !== undefined
      );
    },
  },
  {
    id: 'client_consultation',
    name: 'Consulta con cliente',
    description: 'Expectativas y resultado final acordados con el cliente',
    category: 'professional',
    severity: 'warning',
    validate: context => context.checkedItems.has('client_consultation_complete'),
  },
  {
    id: 'color_wheel_reference',
    name: 'Teoría del color aplicada',
    description: 'Decisiones basadas en principios colorimétricos',
    category: 'professional',
    severity: 'info',
    validate: context => context.checkedItems.has('color_theory_applied'),
  },
  {
    id: 'timing_precision',
    name: 'Precisión en tiempos',
    description: 'Tiempo de procesamiento dentro del rango recomendado',
    category: 'professional',
    severity: 'warning',
    validate: context => {
      // Validate timing is within professional standards (not too rushed)
      const minimumTime = 300; // 5 minutes minimum for professional work
      return context.elapsedTime >= minimumTime;
    },
  },
];

const QUALITY_RULES: ValidationRule[] = [
  {
    id: 'formulation_precision',
    name: 'Precisión en formulación',
    description: 'Proporciones exactas según recomendaciones AI',
    category: 'quality',
    severity: 'error',
    validate: context => {
      // Check if formulation ratios are within acceptable ranges
      return (
        context.formulaData.mixingRatios?.every(ratio => ratio.amount > 0 && ratio.amount <= 100) ||
        false
      );
    },
  },
  {
    id: 'strand_test',
    name: 'Prueba de mechón',
    description: 'Prueba en mechón oculto antes de aplicación completa',
    category: 'quality',
    severity: 'warning',
    validate: context => context.checkedItems.has('strand_test_performed'),
  },
  {
    id: 'product_compatibility',
    name: 'Compatibilidad de productos',
    description: 'Todos los productos son de marcas compatibles',
    category: 'quality',
    severity: 'warning',
    validate: context => {
      const brands = context.formulaData.products?.map(p => p.brand).filter(Boolean);
      return !brands || brands.length <= 2; // Max 2 different brands
    },
  },
];

const MATERIALS_RULES: ValidationRule[] = [
  {
    id: 'all_products_available',
    name: 'Productos disponibles',
    description: 'Todos los productos necesarios están en inventario',
    category: 'materials',
    severity: 'error',
    validate: context => {
      return (
        context.formulaData.products?.every(product =>
          context.checkedItems.has(`product_${product.id}`)
        ) || false
      );
    },
  },
  {
    id: 'tools_sanitized',
    name: 'Herramientas sanitizadas',
    description: 'Todas las herramientas están limpias y desinfectadas',
    category: 'materials',
    severity: 'error',
    validate: context => context.checkedItems.has('tools_sanitized'),
  },
  {
    id: 'mixing_bowls_ready',
    name: 'Bowls de mezcla preparados',
    description: 'Recipientes limpios y secos para la mezcla',
    category: 'materials',
    severity: 'warning',
    validate: context => context.checkedItems.has('mixing_bowls_ready'),
  },
];

export function useStepValidation({
  steps,
  formulaData,
  strictMode = false,
  enableProfessionalScoring = true,
}: UseStepValidationOptions): UseStepValidationReturn {
  // Combine all validation rules
  const allRules = useMemo(
    () => [...SAFETY_RULES, ...PROFESSIONAL_RULES, ...QUALITY_RULES, ...MATERIALS_RULES],
    []
  );

  // Helper function to run validation rules
  const runValidation = useCallback(
    (rules: ValidationRule[], context: ValidationContext): ValidationResult => {
      const issues: ValidationIssue[] = [];

      rules.forEach(rule => {
        try {
          const isValid = rule.validate(context);
          if (!isValid) {
            issues.push({
              ruleId: rule.id,
              message: rule.description,
              severity: rule.severity,
              category: rule.category,
              actionRequired: getActionForRule(rule.id),
            });
          }
        } catch (error) {
          logger.warn(`Validation rule ${rule.id} failed`, 'useStepValidation', error);
          issues.push({
            ruleId: rule.id,
            message: `Error validating ${rule.name}`,
            severity: 'warning',
            category: rule.category,
          });
        }
      });

      const errors = issues.filter(i => i.severity === 'error');
      const warnings = issues.filter(i => i.severity === 'warning');
      const infos = issues.filter(i => i.severity === 'info');

      const totalChecks = rules.length;
      const passedChecks = totalChecks - errors.length - warnings.length;
      const completionPercentage = totalChecks > 0 ? (passedChecks / totalChecks) * 100 : 100;

      const canProceed = strictMode
        ? errors.length === 0 && warnings.length === 0
        : errors.length === 0;

      const professionalScore = enableProfessionalScoring
        ? calculateProfessionalScore(context)
        : 100;

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        infos,
        completionPercentage,
        canProceed,
        professionalScore,
      };
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [strictMode, enableProfessionalScoring, calculateProfessionalScore]
  );

  // Get action recommendation for a rule
  const getActionForRule = useCallback((ruleId: string): string | undefined => {
    const actionMap: Record<string, string> = {
      gloves_required: 'Marcar "Guantes protectores" en la lista de verificación',
      patch_test_completed: 'Confirmar que la prueba de parche fue realizada hace 48 horas',
      all_products_available: 'Verificar disponibilidad de todos los productos en inventario',
      tools_sanitized: 'Limpiar y desinfectar todas las herramientas',
      hair_analysis_complete: 'Completar el diagnóstico AI del cabello en pasos anteriores',
      strand_test_performed: 'Realizar prueba en mechón oculto antes de proceder',
    };

    return actionMap[ruleId];
  }, []);

  // Calculate professional score based on context
  const calculateProfessionalScore = useCallback(
    (context: ValidationContext): number => {
      let score = 100;

      // Deduct points for safety violations
      const safetyIssues = SAFETY_RULES.filter(rule => !rule.validate(context));
      score -= safetyIssues.length * 20; // 20 points per safety issue

      // Deduct points for professional standard violations
      const professionalIssues = PROFESSIONAL_RULES.filter(rule => !rule.validate(context));
      score -= professionalIssues.length * 15; // 15 points per professional issue

      // Deduct points for quality issues
      const qualityIssues = QUALITY_RULES.filter(rule => !rule.validate(context));
      score -= qualityIssues.length * 10; // 10 points per quality issue

      // Deduct points for materials issues
      const materialIssues = MATERIALS_RULES.filter(rule => !rule.validate(context));
      score -= materialIssues.length * 5; // 5 points per material issue

      // Bonus for completing all steps in reasonable time
      if (context.completedSteps.size === steps.length) {
        const averageTimePerStep = context.elapsedTime / steps.length;
        if (averageTimePerStep >= 300 && averageTimePerStep <= 600) {
          // 5-10 minutes per step
          score += 10;
        }
      }

      return Math.max(0, Math.min(100, score));
    },
    [steps.length]
  );

  // Main validation functions
  const validateCurrentStep = useCallback(() => {
    // This would need to be called with current context from the consuming component
    // For now, return a basic validation
    return {
      isValid: true,
      errors: [],
      warnings: [],
      infos: [],
      completionPercentage: 100,
      canProceed: true,
      professionalScore: 100,
    };
  }, []);

  const validateStep = useCallback(
    (stepIndex: number, context: Partial<ValidationContext>): ValidationResult => {
      const step = steps[stepIndex];
      if (!step) {
        return {
          isValid: false,
          errors: [
            {
              ruleId: 'invalid_step',
              message: 'Paso inválido',
              severity: 'error',
              category: 'professional',
            },
          ],
          warnings: [],
          infos: [],
          completionPercentage: 0,
          canProceed: false,
          professionalScore: 0,
        };
      }

      // Get relevant rules for this step
      const stepRules = getActiveRulesForStep(stepIndex);

      const fullContext: ValidationContext = {
        formulaData,
        checkedItems: context.checkedItems || new Set(),
        currentStep: stepIndex,
        completedSteps: context.completedSteps || new Set(),
        elapsedTime: context.elapsedTime || 0,
      };

      return runValidation(stepRules, fullContext);
    },
    [steps, formulaData, runValidation, getActiveRulesForStep]
  );

  const validateAllSteps = useCallback((): ValidationResult => {
    // For comprehensive validation, we'd need full context
    return runValidation(allRules, {
      formulaData,
      checkedItems: new Set(),
      currentStep: 0,
      completedSteps: new Set(),
      elapsedTime: 0,
    });
  }, [allRules, formulaData, runValidation]);

  // Specific validation functions
  const validateChecklist = useCallback(
    (checkedItems: Set<string>): ValidationResult => {
      const context: ValidationContext = {
        formulaData,
        checkedItems,
        currentStep: 0,
        completedSteps: new Set(),
        elapsedTime: 0,
      };

      const checklistRules = allRules.filter(
        rule =>
          rule.id.includes('available') ||
          rule.id.includes('ready') ||
          rule.id.includes('sanitized')
      );

      return runValidation(checklistRules, context);
    },
    [allRules, formulaData, runValidation]
  );

  const validateSafetyRequirements = useCallback((): ValidationResult => {
    const context: ValidationContext = {
      formulaData,
      checkedItems: new Set(),
      currentStep: 0,
      completedSteps: new Set(),
      elapsedTime: 0,
    };

    return runValidation(SAFETY_RULES, context);
  }, [formulaData, runValidation]);

  const validateProfessionalStandards = useCallback((): ValidationResult => {
    const context: ValidationContext = {
      formulaData,
      checkedItems: new Set(),
      currentStep: 0,
      completedSteps: new Set(),
      elapsedTime: 0,
    };

    return runValidation(PROFESSIONAL_RULES, context);
  }, [formulaData, runValidation]);

  const validateMaterialsAvailability = useCallback((): ValidationResult => {
    const context: ValidationContext = {
      formulaData,
      checkedItems: new Set(),
      currentStep: 0,
      completedSteps: new Set(),
      elapsedTime: 0,
    };

    return runValidation(MATERIALS_RULES, context);
  }, [formulaData, runValidation]);

  // Quality and timing checks
  const checkFormulationQuality = useCallback((): ValidationResult => {
    const context: ValidationContext = {
      formulaData,
      checkedItems: new Set(),
      currentStep: 0,
      completedSteps: new Set(),
      elapsedTime: 0,
    };

    return runValidation(QUALITY_RULES, context);
  }, [formulaData, runValidation]);

  const checkTimingCompliance = useCallback(
    (elapsedTime: number): ValidationResult => {
      const context: ValidationContext = {
        formulaData,
        checkedItems: new Set(),
        currentStep: 0,
        completedSteps: new Set(),
        elapsedTime,
      };

      const timingRules = PROFESSIONAL_RULES.filter(rule => rule.id.includes('timing'));
      return runValidation(timingRules, context);
    },
    [formulaData, runValidation]
  );

  const checkProcessOrder = useCallback((completedSteps: Set<number>): ValidationResult => {
    // Check if steps are completed in proper order
    const stepNumbers = Array.from(completedSteps).sort((a, b) => a - b);
    const isProperOrder = stepNumbers.every(
      (step, index) => index === 0 || step === stepNumbers[index - 1] + 1
    );

    if (!isProperOrder) {
      return {
        isValid: false,
        errors: [
          {
            ruleId: 'process_order',
            message: 'Los pasos deben completarse en orden secuencial',
            severity: 'error',
            category: 'professional',
            actionRequired: 'Regresar al paso anterior incompleto',
          },
        ],
        warnings: [],
        infos: [],
        completionPercentage: 0,
        canProceed: false,
        professionalScore: 0,
      };
    }

    return {
      isValid: true,
      errors: [],
      warnings: [],
      infos: [],
      completionPercentage: 100,
      canProceed: true,
      professionalScore: 100,
    };
  }, []);

  // Professional scoring
  const getProfessionalRecommendations = useCallback((): string[] => {
    return [
      'Realiza siempre una prueba de mechón antes de la aplicación completa',
      'Documenta el proceso con fotos para referencia futura',
      'Mantén comunicación constante con el cliente durante el proceso',
      'Verifica la compatibilidad de productos antes de mezclar',
      'Usa guantes protectores durante todo el proceso químico',
    ];
  }, []);

  // Auto-fix capabilities
  const getAutoFixSuggestions = useCallback((issues: ValidationIssue[]): ValidationIssue[] => {
    return issues.filter(issue => issue.autoFix !== undefined);
  }, []);

  const applyAutoFixes = useCallback(async (issues: ValidationIssue[]): Promise<void> => {
    for (const issue of issues) {
      if (issue.autoFix) {
        try {
          issue.autoFix();
        } catch (error) {
          logger.warn(`Auto-fix failed for ${issue.ruleId}`, 'useStepValidation', error);
        }
      }
    }
  }, []);

  // Rule management
  const getActiveRules = useCallback((): ValidationRule[] => {
    return allRules;
  }, [allRules]);

  const getActiveRulesForStep = useCallback(
    (stepIndex: number): ValidationRule[] => {
      const step = steps[stepIndex];
      if (!step) return [];

      // Return relevant rules based on step type
      switch (step.id) {
        case 'checklist':
          return [...SAFETY_RULES, ...MATERIALS_RULES];
        case 'formulas':
          return [...QUALITY_RULES, ...PROFESSIONAL_RULES];
        case 'mixing':
          return [...SAFETY_RULES, ...QUALITY_RULES];
        case 'application':
          return [...SAFETY_RULES, ...PROFESSIONAL_RULES];
        default:
          return PROFESSIONAL_RULES;
      }
    },
    [steps]
  );

  const toggleRule = useCallback((ruleId: string, enabled: boolean): void => {
    // In a real implementation, this would update rule state
    logger.debug(`Rule ${ruleId} ${enabled ? 'enabled' : 'disabled'}`, 'useStepValidation');
  }, []);

  const addCustomRule = useCallback((rule: ValidationRule): void => {
    // In a real implementation, this would add to custom rules list
    logger.debug('Custom rule added', 'useStepValidation', { ruleName: rule.name });
  }, []);

  return {
    // Current validation state
    validateCurrentStep,
    validateStep,
    validateAllSteps,

    // Specific validations
    validateChecklist,
    validateSafetyRequirements,
    validateProfessionalStandards,
    validateMaterialsAvailability,

    // Quality checks
    checkFormulationQuality,
    checkTimingCompliance,
    checkProcessOrder,

    // Professional scoring
    calculateProfessionalScore,
    getProfessionalRecommendations,

    // Auto-fix capabilities
    getAutoFixSuggestions,
    applyAutoFixes,

    // Validation rules management
    getActiveRules,
    toggleRule,
    addCustomRule,
  };
}
