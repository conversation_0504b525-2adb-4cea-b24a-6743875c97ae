import { useState, useCallback, useEffect, useMemo } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { FlowStep } from './useInstructionFlow';
import { logger } from '@/utils/logger';

// Progress weight types for different completion factors
interface ProgressWeights {
  stepCompletion: number; // Weight for completing steps (0-1)
  timeSpent: number; // Weight for time investment (0-1)
  qualityScore: number; // Weight for professional quality (0-1)
  checklistItems: number; // Weight for checklist completion (0-1)
}

// Progress tracking state
interface ProgressState {
  completedSteps: Set<number>;
  checkedItems: Set<string>;
  stepStartTimes: Record<number, number>;
  stepCompletionTimes: Record<number, number>;
  totalStartTime: number;
  qualityScores: Record<number, number>;
  milestones: Set<string>;
  lastCalculatedAt: number;
}

// Progress metrics
export interface ProgressMetrics {
  // Overall progress
  overallProgress: number; // 0-100
  weightedProgress: number; // 0-100 (considering quality)

  // Step-based metrics
  stepsCompleted: number;
  totalSteps: number;
  stepProgress: number; // 0-100

  // Time-based metrics
  totalElapsedTime: number; // seconds
  averageTimePerStep: number; // seconds
  estimatedTimeRemaining: number; // seconds
  estimatedCompletionTime: Date;

  // Quality metrics
  averageQualityScore: number; // 0-100
  professionalCompliance: number; // 0-100

  // Checklist metrics
  checklistCompletion: number; // 0-100
  criticalItemsCompleted: number; // 0-100

  // Milestone tracking
  milestonesAchieved: number;
  totalMilestones: number;

  // Performance insights
  efficiency: number; // 0-100 (speed vs quality balance)
  consistency: number; // 0-100 (time variance between steps)
  focus: number; // 0-100 (based on pause/resume patterns)
}

// Progress milestone definitions
interface ProgressMilestone {
  id: string;
  name: string;
  description: string;
  condition: (metrics: ProgressMetrics, state: ProgressState) => boolean;
  reward?: string;
  icon?: string;
}

// Hook options
interface UseProgressTrackingOptions {
  steps: FlowStep[];
  weights?: Partial<ProgressWeights>;
  milestones?: ProgressMilestone[];
  persistProgress?: boolean;
  autoCalculate?: boolean;
  qualityThreshold?: number; // Minimum quality score to consider "good" completion
  onMilestoneAchieved?: (milestone: ProgressMilestone) => void;
  onProgressUpdate?: (metrics: ProgressMetrics) => void;
}

// Hook return interface
export interface UseProgressTrackingReturn {
  // Current metrics
  metrics: ProgressMetrics;

  // Progress updates
  updateStepProgress: (stepIndex: number, completed: boolean) => void;
  updateChecklistProgress: (items: Set<string>) => void;
  updateQualityScore: (stepIndex: number, score: number) => void;
  recordStepStart: (stepIndex: number) => void;
  recordStepCompletion: (stepIndex: number) => void;

  // Milestone management
  checkMilestones: () => string[]; // Returns newly achieved milestone IDs
  getMilestoneProgress: () => { achieved: number; total: number };
  getNextMilestone: () => ProgressMilestone | null;

  // Time estimations
  getStepEstimate: (stepIndex: number) => number; // seconds
  getEstimatedCompletion: () => Date;
  getTimeToNextMilestone: () => number; // seconds

  // Performance analysis
  getEfficiencyTrend: () => number[]; // Efficiency over time
  getQualityTrend: () => number[]; // Quality scores over time
  getSpeedTrend: () => number[]; // Time per step trend

  // Progress visualization data
  getProgressBreakdown: () => {
    steps: number;
    checklist: number;
    quality: number;
    time: number;
  };
  getCompletionForecast: () => {
    optimistic: Date;
    realistic: Date;
    conservative: Date;
  };

  // Persistence
  saveProgress: () => Promise<void>;
  loadProgress: () => Promise<void>;
  clearProgress: () => Promise<void>;

  // Reset and utilities
  reset: () => void;
  recalculateMetrics: () => void;
}

const DEFAULT_WEIGHTS: ProgressWeights = {
  stepCompletion: 0.4,
  timeSpent: 0.2,
  qualityScore: 0.3,
  checklistItems: 0.1,
};

const DEFAULT_MILESTONES: ProgressMilestone[] = [
  {
    id: 'first_step',
    name: 'Primer Paso',
    description: 'Has completado tu primer paso del proceso',
    condition: metrics => metrics.stepsCompleted >= 1,
    reward: 'Desbloqueado: Guía de consejos profesionales',
    icon: '🎯',
  },
  {
    id: 'quarter_complete',
    name: 'Un Cuarto del Camino',
    description: 'Has completado el 25% del proceso',
    condition: metrics => metrics.stepProgress >= 25,
    reward: 'Desbloqueado: Calculadora avanzada',
    icon: '📈',
  },
  {
    id: 'halfway_hero',
    name: 'Héroe de Medio Camino',
    description: 'Has alcanzado el 50% de completitud',
    condition: metrics => metrics.stepProgress >= 50,
    reward: 'Desbloqueado: Vista previa del resultado',
    icon: '⭐',
  },
  {
    id: 'quality_master',
    name: 'Maestro de Calidad',
    description: 'Has mantenido un puntaje de calidad superior a 90',
    condition: metrics => metrics.averageQualityScore >= 90,
    reward: 'Desbloqueado: Certificado profesional',
    icon: '👑',
  },
  {
    id: 'speed_demon',
    name: 'Demonio de la Velocidad',
    description: 'Has completado el proceso en tiempo récord',
    condition: metrics => metrics.efficiency >= 95,
    reward: 'Desbloqueado: Modo experto',
    icon: '⚡',
  },
  {
    id: 'perfectionist',
    name: 'Perfeccionista',
    description: 'Has completado todos los elementos de verificación',
    condition: metrics => metrics.checklistCompletion >= 100,
    reward: 'Desbloqueado: Plantillas avanzadas',
    icon: '💎',
  },
];

const STORAGE_PREFIX = 'progress_tracking_';

export function useProgressTracking({
  steps,
  weights = {},
  milestones = DEFAULT_MILESTONES,
  persistProgress = true,
  autoCalculate = true,
  qualityThreshold = 80,
  onMilestoneAchieved,
  onProgressUpdate,
}: UseProgressTrackingOptions): UseProgressTrackingReturn {
  // Merge weights with defaults
  const finalWeights = useMemo<ProgressWeights>(
    () => ({ ...DEFAULT_WEIGHTS, ...weights }),
    [weights]
  );

  // Progress state
  const [progressState, setProgressState] = useState<ProgressState>({
    completedSteps: new Set<number>(),
    checkedItems: new Set<string>(),
    stepStartTimes: {},
    stepCompletionTimes: {},
    totalStartTime: Date.now(),
    qualityScores: {},
    milestones: new Set<string>(),
    lastCalculatedAt: Date.now(),
  });

  // Storage key
  const storageKey = `${STORAGE_PREFIX}default`;

  // Calculate current metrics
  const metrics = useMemo((): ProgressMetrics => {
    const now = Date.now();
    const totalElapsedTime = Math.floor((now - progressState.totalStartTime) / 1000);
    const stepsCompleted = progressState.completedSteps.size;
    const totalSteps = steps.length;
    const stepProgress = totalSteps > 0 ? (stepsCompleted / totalSteps) * 100 : 0;

    // Calculate average time per step
    const completedStepTimes = Array.from(progressState.completedSteps)
      .map(stepIndex => {
        const startTime = progressState.stepStartTimes[stepIndex];
        const endTime = progressState.stepCompletionTimes[stepIndex];
        return startTime && endTime ? (endTime - startTime) / 1000 : 0;
      })
      .filter(time => time > 0);

    const averageTimePerStep =
      completedStepTimes.length > 0
        ? completedStepTimes.reduce((sum, time) => sum + time, 0) / completedStepTimes.length
        : 300; // Default 5 minutes per step

    // Calculate remaining time estimate
    const remainingSteps = totalSteps - stepsCompleted;
    const estimatedTimeRemaining = remainingSteps * averageTimePerStep;
    const estimatedCompletionTime = new Date(now + estimatedTimeRemaining * 1000);

    // Calculate quality metrics
    const qualityScores = Object.values(progressState.qualityScores);
    const averageQualityScore =
      qualityScores.length > 0
        ? qualityScores.reduce((sum, score) => sum + score, 0) / qualityScores.length
        : 100;

    const professionalCompliance =
      (qualityScores.filter(score => score >= qualityThreshold).length /
        Math.max(1, qualityScores.length)) *
      100;

    // Calculate checklist completion
    const totalChecklistItems = steps.reduce(
      (total, step) => total + (step.requirements?.length || 0),
      0
    );
    const checklistCompletion =
      totalChecklistItems > 0 ? (progressState.checkedItems.size / totalChecklistItems) * 100 : 100;

    // Calculate critical items (safety requirements)
    const criticalItems = steps
      .flatMap(step => step.requirements || [])
      .filter(req => req.includes('safety') || req.includes('protection') || req.includes('test'));
    const criticalItemsCompleted = criticalItems.filter(item =>
      progressState.checkedItems.has(item)
    ).length;
    const criticalItemsCompletion =
      criticalItems.length > 0 ? (criticalItemsCompleted / criticalItems.length) * 100 : 100;

    // Calculate weighted progress
    const weightedProgress =
      stepProgress * finalWeights.stepCompletion +
      Math.min(100, (totalElapsedTime / (averageTimePerStep * totalSteps)) * 100) *
        finalWeights.timeSpent +
      averageQualityScore * finalWeights.qualityScore +
      checklistCompletion * finalWeights.checklistItems;

    // Calculate performance metrics
    const timeVariance =
      completedStepTimes.length > 1
        ? Math.sqrt(
            completedStepTimes.reduce(
              (sum, time) => sum + Math.pow(time - averageTimePerStep, 2),
              0
            ) / completedStepTimes.length
          )
        : 0;
    const consistency = Math.max(0, 100 - (timeVariance / averageTimePerStep) * 100);

    const expectedTime = averageTimePerStep * stepsCompleted;
    const actualTime = completedStepTimes.reduce((sum, time) => sum + time, 0);
    const efficiency = expectedTime > 0 ? Math.min(100, (expectedTime / actualTime) * 100) : 100;

    // Focus score (placeholder - would need pause/resume data)
    const focus = 95; // High default, would be calculated from actual interaction patterns

    return {
      overallProgress: Math.min(100, stepProgress),
      weightedProgress: Math.min(100, weightedProgress),
      stepsCompleted,
      totalSteps,
      stepProgress,
      totalElapsedTime,
      averageTimePerStep,
      estimatedTimeRemaining,
      estimatedCompletionTime,
      averageQualityScore,
      professionalCompliance,
      checklistCompletion,
      criticalItemsCompleted: criticalItemsCompletion,
      milestonesAchieved: progressState.milestones.size,
      totalMilestones: milestones.length,
      efficiency,
      consistency,
      focus,
    };
  }, [progressState, steps, finalWeights, qualityThreshold, milestones.length]);

  // Auto-calculate metrics when state changes
  useEffect(() => {
    if (autoCalculate) {
      setProgressState(prev => ({
        ...prev,
        lastCalculatedAt: Date.now(),
      }));

      onProgressUpdate?.(metrics);
    }
  }, [autoCalculate, metrics, onProgressUpdate]);

  // Persist progress on state changes
  useEffect(() => {
    if (persistProgress) {
      const debounceTimer = setTimeout(() => {
        saveProgress();
      }, 1000);

      return () => clearTimeout(debounceTimer);
    }
  }, [progressState, persistProgress, saveProgress]);

  // Progress update functions
  const updateStepProgress = useCallback((stepIndex: number, completed: boolean): void => {
    setProgressState(prev => {
      const newCompletedSteps = new Set(prev.completedSteps);
      if (completed) {
        newCompletedSteps.add(stepIndex);
      } else {
        newCompletedSteps.delete(stepIndex);
      }

      return {
        ...prev,
        completedSteps: newCompletedSteps,
      };
    });
  }, []);

  const updateChecklistProgress = useCallback((items: Set<string>): void => {
    setProgressState(prev => ({
      ...prev,
      checkedItems: new Set(items),
    }));
  }, []);

  const updateQualityScore = useCallback((stepIndex: number, score: number): void => {
    setProgressState(prev => ({
      ...prev,
      qualityScores: {
        ...prev.qualityScores,
        [stepIndex]: Math.max(0, Math.min(100, score)),
      },
    }));
  }, []);

  const recordStepStart = useCallback((stepIndex: number): void => {
    setProgressState(prev => ({
      ...prev,
      stepStartTimes: {
        ...prev.stepStartTimes,
        [stepIndex]: Date.now(),
      },
    }));
  }, []);

  const recordStepCompletion = useCallback((stepIndex: number): void => {
    setProgressState(prev => ({
      ...prev,
      stepCompletionTimes: {
        ...prev.stepCompletionTimes,
        [stepIndex]: Date.now(),
      },
    }));
  }, []);

  // Milestone management
  const checkMilestones = useCallback((): string[] => {
    const newMilestones: string[] = [];

    milestones.forEach(milestone => {
      if (
        !progressState.milestones.has(milestone.id) &&
        milestone.condition(metrics, progressState)
      ) {
        newMilestones.push(milestone.id);
        setProgressState(prev => ({
          ...prev,
          milestones: new Set([...prev.milestones, milestone.id]),
        }));

        onMilestoneAchieved?.(milestone);
      }
    });

    return newMilestones;
  }, [milestones, metrics, progressState, onMilestoneAchieved]);

  const getMilestoneProgress = useCallback((): { achieved: number; total: number } => {
    return {
      achieved: progressState.milestones.size,
      total: milestones.length,
    };
  }, [progressState.milestones.size, milestones.length]);

  const getNextMilestone = useCallback((): ProgressMilestone | null => {
    return milestones.find(milestone => !progressState.milestones.has(milestone.id)) || null;
  }, [milestones, progressState.milestones]);

  // Time estimation functions
  const getStepEstimate = useCallback(
    (stepIndex: number): number => {
      // Use step-specific estimate if available, otherwise use average
      const step = steps[stepIndex];
      return step?.estimatedDuration ? step.estimatedDuration * 60 : metrics.averageTimePerStep;
    },
    [steps, metrics.averageTimePerStep]
  );

  const getEstimatedCompletion = useCallback((): Date => {
    return metrics.estimatedCompletionTime;
  }, [metrics.estimatedCompletionTime]);

  const getTimeToNextMilestone = useCallback((): number => {
    const nextMilestone = getNextMilestone();
    if (!nextMilestone) return 0;

    // This is a simplified calculation
    // In practice, you'd analyze the milestone condition to estimate time needed
    const remainingProgress = 100 - metrics.stepProgress;
    return (remainingProgress / 100) * metrics.estimatedTimeRemaining;
  }, [getNextMilestone, metrics.stepProgress, metrics.estimatedTimeRemaining]);

  // Performance analysis
  const getEfficiencyTrend = useCallback((): number[] => {
    // Return efficiency trend over completed steps
    const trend = Array.from(progressState.completedSteps)
      .sort((a, b) => a - b)
      .map(stepIndex => {
        const startTime = progressState.stepStartTimes[stepIndex];
        const endTime = progressState.stepCompletionTimes[stepIndex];
        const actualTime = startTime && endTime ? (endTime - startTime) / 1000 : 0;
        const expectedTime = getStepEstimate(stepIndex);

        return expectedTime > 0 ? Math.min(100, (expectedTime / actualTime) * 100) : 100;
      });

    return trend;
  }, [progressState, getStepEstimate]);

  const getQualityTrend = useCallback((): number[] => {
    return Array.from(progressState.completedSteps)
      .sort((a, b) => a - b)
      .map(stepIndex => progressState.qualityScores[stepIndex] || 100);
  }, [progressState]);

  const getSpeedTrend = useCallback((): number[] => {
    return Array.from(progressState.completedSteps)
      .sort((a, b) => a - b)
      .map(stepIndex => {
        const startTime = progressState.stepStartTimes[stepIndex];
        const endTime = progressState.stepCompletionTimes[stepIndex];
        return startTime && endTime ? (endTime - startTime) / 1000 : 0;
      })
      .filter(time => time > 0);
  }, [progressState]);

  // Progress visualization data
  const getProgressBreakdown = useCallback(() => {
    return {
      steps: metrics.stepProgress,
      checklist: metrics.checklistCompletion,
      quality: metrics.averageQualityScore,
      time: Math.min(
        100,
        (metrics.totalElapsedTime / (metrics.averageTimePerStep * metrics.totalSteps)) * 100
      ),
    };
  }, [metrics]);

  const getCompletionForecast = useCallback(() => {
    const now = Date.now();
    const baseEstimate = metrics.estimatedTimeRemaining;

    return {
      optimistic: new Date(now + baseEstimate * 0.8 * 1000),
      realistic: new Date(now + baseEstimate * 1000),
      conservative: new Date(now + baseEstimate * 1.3 * 1000),
    };
  }, [metrics.estimatedTimeRemaining]);

  // Persistence methods
  const saveProgress = useCallback(async (): Promise<void> => {
    try {
      const dataToSave = {
        ...progressState,
        completedSteps: Array.from(progressState.completedSteps),
        checkedItems: Array.from(progressState.checkedItems),
        milestones: Array.from(progressState.milestones),
        savedAt: Date.now(),
      };

      await AsyncStorage.setItem(storageKey, JSON.stringify(dataToSave));
    } catch (error) {
      logger.warn('Failed to save progress', 'useProgressTracking', error);
    }
  }, [progressState, storageKey]);

  const loadProgress = useCallback(async (): Promise<void> => {
    try {
      const savedData = await AsyncStorage.getItem(storageKey);
      if (savedData) {
        const parsedData = JSON.parse(savedData);

        setProgressState({
          ...parsedData,
          completedSteps: new Set(parsedData.completedSteps || []),
          checkedItems: new Set(parsedData.checkedItems || []),
          milestones: new Set(parsedData.milestones || []),
        });
      }
    } catch (error) {
      logger.warn('Failed to load progress', 'useProgressTracking', error);
    }
  }, [storageKey]);

  const clearProgress = useCallback(async (): Promise<void> => {
    try {
      await AsyncStorage.removeItem(storageKey);
    } catch (error) {
      logger.warn('Failed to clear progress', 'useProgressTracking', error);
    }
  }, [storageKey]);

  // Reset and utilities
  const reset = useCallback((): void => {
    setProgressState({
      completedSteps: new Set<number>(),
      checkedItems: new Set<string>(),
      stepStartTimes: {},
      stepCompletionTimes: {},
      totalStartTime: Date.now(),
      qualityScores: {},
      milestones: new Set<string>(),
      lastCalculatedAt: Date.now(),
    });
  }, []);

  const recalculateMetrics = useCallback((): void => {
    setProgressState(prev => ({
      ...prev,
      lastCalculatedAt: Date.now(),
    }));
  }, []);

  // Load persisted progress on mount
  useEffect(() => {
    if (persistProgress) {
      loadProgress();
    }
  }, [persistProgress, loadProgress]);

  // Check for new milestones when metrics change
  useEffect(() => {
    if (autoCalculate) {
      checkMilestones();
    }
  }, [metrics, autoCalculate, checkMilestones]);

  return {
    // Current metrics
    metrics,

    // Progress updates
    updateStepProgress,
    updateChecklistProgress,
    updateQualityScore,
    recordStepStart,
    recordStepCompletion,

    // Milestone management
    checkMilestones,
    getMilestoneProgress,
    getNextMilestone,

    // Time estimations
    getStepEstimate,
    getEstimatedCompletion,
    getTimeToNextMilestone,

    // Performance analysis
    getEfficiencyTrend,
    getQualityTrend,
    getSpeedTrend,

    // Progress visualization data
    getProgressBreakdown,
    getCompletionForecast,

    // Persistence
    saveProgress,
    loadProgress,
    clearProgress,

    // Reset and utilities
    reset,
    recalculateMetrics,
  };
}
