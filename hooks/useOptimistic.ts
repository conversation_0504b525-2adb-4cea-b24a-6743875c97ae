import { useState, useCallback, useRef } from 'react';
import { Alert } from 'react-native';
import * as Haptics from 'expo-haptics';
import { logger } from '@/utils/logger';

export interface OptimisticState<T> {
  data: T;
  isPending: boolean;
  error: Error | null;
}

export interface OptimisticOptions<T> {
  onError?: (error: Error, rollbackData: T) => void;
  onSuccess?: (data: T) => void;
  showErrorAlert?: boolean;
  errorTitle?: string;
  errorMessage?: string;
  hapticFeedback?: boolean;
}

/**
 * Claude-style Optimistic UI Hook
 * Actualiza inmediatamente la UI y revierte si hay error
 */
export function useOptimistic<T>(initialData: T, options: OptimisticOptions<T> = {}) {
  const [state, setState] = useState<OptimisticState<T>>({
    data: initialData,
    isPending: false,
    error: null,
  });

  const rollbackRef = useRef<T>(initialData);
  const {
    onError,
    onSuccess,
    showErrorAlert = true,
    errorTitle = 'Error',
    errorMessage = 'No se pudo completar la acción. Se ha revertido.',
    hapticFeedback = true,
  } = options;

  const updateOptimistic = useCallback(
    async <R>(
      optimisticUpdate: (currentData: T) => T,
      asyncOperation: (updatedData: T) => Promise<R>,
      options: {
        successUpdate?: (result: R, optimisticData: T) => T;
        skipSuccessUpdate?: boolean;
      } = {}
    ): Promise<R | null> => {
      // Store current data for potential rollback
      rollbackRef.current = state.data;

      // Apply optimistic update IMMEDIATELY
      const optimisticData = optimisticUpdate(state.data);
      setState(prev => ({
        ...prev,
        data: optimisticData,
        isPending: true,
        error: null,
      }));

      // Immediate haptic feedback
      if (hapticFeedback) {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }

      try {
        // Execute async operation
        const result = await asyncOperation(optimisticData);

        // Apply success update if provided
        const finalData = options.successUpdate
          ? options.successUpdate(result, optimisticData)
          : options.skipSuccessUpdate
            ? optimisticData
            : optimisticData;

        setState(prev => ({
          ...prev,
          data: finalData,
          isPending: false,
          error: null,
        }));

        // Success callback
        onSuccess?.(finalData);

        // Success haptic feedback
        if (hapticFeedback) {
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        }

        logger.debug('Optimistic operation completed successfully');
        return result;
      } catch (error) {
        logger.error('Optimistic operation failed, rolling back', error);

        // ROLLBACK: Revert to previous state
        setState(prev => ({
          ...prev,
          data: rollbackRef.current,
          isPending: false,
          error: error as Error,
        }));

        // Error haptic feedback
        if (hapticFeedback) {
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
        }

        // Error handling
        onError?.(error as Error, rollbackRef.current);

        // Show error alert if enabled
        if (showErrorAlert) {
          setTimeout(() => {
            Alert.alert(errorTitle, errorMessage);
          }, 100);
        }

        return null;
      }
    },
    [state.data, onError, onSuccess, showErrorAlert, errorTitle, errorMessage, hapticFeedback]
  );

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  const updateData = useCallback((newData: T) => {
    setState(prev => ({ ...prev, data: newData }));
  }, []);

  return {
    data: state.data,
    isPending: state.isPending,
    error: state.error,
    updateOptimistic,
    clearError,
    updateData,
  };
}

/**
 * Optimistic UI para operaciones booleanas (toggle states)
 */
export function useOptimisticToggle(
  initialState: boolean,
  asyncOperation: (newState: boolean) => Promise<void>,
  options: OptimisticOptions<boolean> = {}
) {
  const { updateOptimistic, ...rest } = useOptimistic(initialState, options);

  const toggle = useCallback(async () => {
    await updateOptimistic(
      currentState => !currentState,
      async newState => {
        await asyncOperation(newState);
        return newState;
      },
      { skipSuccessUpdate: true }
    );
  }, [updateOptimistic, asyncOperation]);

  return {
    ...rest,
    toggle,
  };
}

/**
 * Optimistic UI para arrays (add/remove/update items)
 */
export function useOptimisticArray<T extends { id: string }>(
  initialArray: T[],
  options: OptimisticOptions<T[]> = {}
) {
  const { updateOptimistic, ...rest } = useOptimistic(initialArray, options);

  const addItem = useCallback(
    async (newItem: T, asyncOperation: (item: T) => Promise<T>): Promise<T | null> => {
      return updateOptimistic(
        currentArray => [newItem, ...currentArray],
        async _optimisticArray => {
          const result = await asyncOperation(newItem);
          return result;
        },
        {
          successUpdate: (result, optimisticArray) =>
            optimisticArray.map(item => (item.id === newItem.id ? result : item)),
        }
      );
    },
    [updateOptimistic]
  );

  const removeItem = useCallback(
    async (itemId: string, asyncOperation: (id: string) => Promise<void>): Promise<void> => {
      await updateOptimistic(
        currentArray => currentArray.filter(item => item.id !== itemId),
        async () => {
          await asyncOperation(itemId);
        },
        { skipSuccessUpdate: true }
      );
    },
    [updateOptimistic]
  );

  const updateItem = useCallback(
    async (
      itemId: string,
      updates: Partial<T>,
      asyncOperation: (id: string, updates: Partial<T>) => Promise<T>
    ): Promise<T | null> => {
      return updateOptimistic(
        currentArray =>
          currentArray.map(item => (item.id === itemId ? { ...item, ...updates } : item)),
        async _optimisticArray => {
          const result = await asyncOperation(itemId, updates);
          return result;
        },
        {
          successUpdate: (result, optimisticArray) =>
            optimisticArray.map(item => (item.id === itemId ? result : item)),
        }
      );
    },
    [updateOptimistic]
  );

  return {
    ...rest,
    addItem,
    removeItem,
    updateItem,
  };
}
