import { useCallback } from 'react';
import * as Haptics from 'expo-haptics';
import { Platform } from 'react-native';
import { useHaptics } from './useHaptics';
import { logger } from '@/utils/logger';

// Extended haptic patterns for instruction flow
type InstructionHapticPattern =
  | 'stepAdvance'
  | 'stepComplete'
  | 'flowComplete'
  | 'validationSuccess'
  | 'validationWarning'
  | 'validationError'
  | 'timerAlert'
  | 'phaseTransition'
  | 'milestone'
  | 'progressUpdate'
  | 'checklistToggle'
  | 'navigationBack'
  | 'navigationForward'
  | 'modalOpen'
  | 'modalClose'
  | 'buttonHover'
  | 'cardFlip'
  | 'listReorder'
  | 'confirmAction'
  | 'cancelAction';

// Complex haptic sequence definition
interface HapticSequence {
  pattern: Array<{
    type: Haptics.ImpactFeedbackStyle | Haptics.NotificationFeedbackType | 'selection';
    delay?: number;
    intensity?: 'light' | 'medium' | 'heavy';
  }>;
  description: string;
}

// Professional haptic patterns for salon workflow
const INSTRUCTION_HAPTIC_PATTERNS: Record<InstructionHapticPattern, HapticSequence> = {
  stepAdvance: {
    pattern: [{ type: Haptics.ImpactFeedbackStyle.Light }, { type: 'selection', delay: 100 }],
    description: 'Gentle confirmation when advancing to next step',
  },

  stepComplete: {
    pattern: [
      { type: Haptics.ImpactFeedbackStyle.Medium },
      { type: Haptics.NotificationFeedbackType.Success, delay: 150 },
    ],
    description: 'Satisfying completion feedback for finishing a step',
  },

  flowComplete: {
    pattern: [
      { type: Haptics.ImpactFeedbackStyle.Heavy },
      { type: Haptics.NotificationFeedbackType.Success, delay: 100 },
      { type: Haptics.ImpactFeedbackStyle.Light, delay: 200 },
      { type: Haptics.ImpactFeedbackStyle.Light, delay: 300 },
    ],
    description: 'Celebratory pattern for completing entire instruction flow',
  },

  validationSuccess: {
    pattern: [{ type: Haptics.NotificationFeedbackType.Success }],
    description: 'Clean success notification for validation passes',
  },

  validationWarning: {
    pattern: [
      { type: Haptics.NotificationFeedbackType.Warning },
      { type: Haptics.ImpactFeedbackStyle.Light, delay: 200 },
    ],
    description: 'Attention-grabbing pattern for validation warnings',
  },

  validationError: {
    pattern: [
      { type: Haptics.NotificationFeedbackType.Error },
      { type: Haptics.ImpactFeedbackStyle.Heavy, delay: 100 },
    ],
    description: 'Strong error feedback for validation failures',
  },

  timerAlert: {
    pattern: [
      { type: Haptics.ImpactFeedbackStyle.Medium },
      { type: Haptics.ImpactFeedbackStyle.Medium, delay: 100 },
      { type: Haptics.NotificationFeedbackType.Warning, delay: 200 },
    ],
    description: 'Rhythmic alert for timer notifications',
  },

  phaseTransition: {
    pattern: [
      { type: 'selection' },
      { type: Haptics.ImpactFeedbackStyle.Light, delay: 50 },
      { type: 'selection', delay: 100 },
    ],
    description: 'Smooth transition feel between timer phases',
  },

  milestone: {
    pattern: [
      { type: Haptics.ImpactFeedbackStyle.Light },
      { type: Haptics.ImpactFeedbackStyle.Medium, delay: 80 },
      { type: Haptics.NotificationFeedbackType.Success, delay: 160 },
      { type: Haptics.ImpactFeedbackStyle.Light, delay: 300 },
    ],
    description: 'Special celebration for achievement milestones',
  },

  progressUpdate: {
    pattern: [{ type: 'selection' }],
    description: 'Subtle feedback for progress updates',
  },

  checklistToggle: {
    pattern: [{ type: Haptics.ImpactFeedbackStyle.Light }],
    description: 'Light tap for checklist item toggles',
  },

  navigationBack: {
    pattern: [{ type: 'selection' }, { type: Haptics.ImpactFeedbackStyle.Light, delay: 50 }],
    description: 'Directional feedback for going back',
  },

  navigationForward: {
    pattern: [{ type: Haptics.ImpactFeedbackStyle.Light }, { type: 'selection', delay: 50 }],
    description: 'Directional feedback for going forward',
  },

  modalOpen: {
    pattern: [{ type: Haptics.ImpactFeedbackStyle.Medium }],
    description: 'Announcement for modal opening',
  },

  modalClose: {
    pattern: [{ type: Haptics.ImpactFeedbackStyle.Light }],
    description: 'Gentle confirmation for modal closing',
  },

  buttonHover: {
    pattern: [{ type: 'selection' }],
    description: 'Subtle hover feedback for buttons',
  },

  cardFlip: {
    pattern: [{ type: Haptics.ImpactFeedbackStyle.Light }, { type: 'selection', delay: 200 }],
    description: 'Tactile feedback for card flip animations',
  },

  listReorder: {
    pattern: [{ type: Haptics.ImpactFeedbackStyle.Medium }, { type: 'selection', delay: 100 }],
    description: 'Feedback for drag and drop reordering',
  },

  confirmAction: {
    pattern: [
      { type: Haptics.ImpactFeedbackStyle.Medium },
      { type: Haptics.NotificationFeedbackType.Success, delay: 100 },
    ],
    description: 'Strong confirmation for important actions',
  },

  cancelAction: {
    pattern: [{ type: Haptics.ImpactFeedbackStyle.Light }],
    description: 'Gentle feedback for canceling actions',
  },
};

// Hook return interface
export interface UseHapticFeedbackReturn {
  // Instruction flow patterns
  stepAdvance: () => Promise<void>;
  stepComplete: () => Promise<void>;
  flowComplete: () => Promise<void>;

  // Validation patterns
  validationSuccess: () => Promise<void>;
  validationWarning: () => Promise<void>;
  validationError: () => Promise<void>;

  // Timer patterns
  timerAlert: () => Promise<void>;
  phaseTransition: () => Promise<void>;

  // Achievement patterns
  milestone: () => Promise<void>;
  progressUpdate: () => Promise<void>;

  // Interaction patterns
  checklistToggle: () => Promise<void>;
  navigationBack: () => Promise<void>;
  navigationForward: () => Promise<void>;

  // UI patterns
  modalOpen: () => Promise<void>;
  modalClose: () => Promise<void>;
  buttonHover: () => Promise<void>;
  cardFlip: () => Promise<void>;
  listReorder: () => Promise<void>;

  // Action patterns
  confirmAction: () => Promise<void>;
  cancelAction: () => Promise<void>;

  // Custom pattern execution
  playPattern: (pattern: InstructionHapticPattern) => Promise<void>;
  playCustomSequence: (sequence: HapticSequence) => Promise<void>;

  // Conditional patterns (based on context)
  qualityFeedback: (score: number) => Promise<void>;
  progressFeedback: (percentage: number) => Promise<void>;
  timingFeedback: (isOnTime: boolean, isLate: boolean) => Promise<void>;

  // Accessibility and preferences
  isHapticsEnabled: () => boolean;
  setHapticsEnabled: (enabled: boolean) => void;
  getPatternDescription: (pattern: InstructionHapticPattern) => string;

  // Advanced patterns
  createProgressSequence: (steps: number, currentStep: number) => Promise<void>;
  createQualitySequence: (previousScore: number, newScore: number) => Promise<void>;
  createTimerSequence: (timeRemaining: number, totalTime: number) => Promise<void>;
}

// Internal haptics state (would be managed by a context in real implementation)
let hapticsEnabled = true;

export function useHapticFeedback(): UseHapticFeedbackReturn {
  const { impact: _impact } = useHaptics();

  // Check if haptics are supported
  const isHapticsSupported = Platform.OS === 'ios' || Platform.OS === 'android';

  // Execute a single haptic feedback
  const executeSingleHaptic = useCallback(
    async (
      type: Haptics.ImpactFeedbackStyle | Haptics.NotificationFeedbackType | 'selection'
    ): Promise<void> => {
      if (!isHapticsSupported || !hapticsEnabled) return;

      try {
        if (type === 'selection') {
          await Haptics.selectionAsync();
        } else if (
          Object.values(Haptics.ImpactFeedbackStyle).includes(type as Haptics.ImpactFeedbackStyle)
        ) {
          await Haptics.impactAsync(type as Haptics.ImpactFeedbackStyle);
        } else if (
          Object.values(Haptics.NotificationFeedbackType).includes(
            type as Haptics.NotificationFeedbackType
          )
        ) {
          await Haptics.notificationAsync(type as Haptics.NotificationFeedbackType);
        }
      } catch (error) {
        // Silently handle haptics errors
        // Silently handle haptics errors in production
        if (__DEV__) {
          logger.debug('Haptic feedback failed', 'useHapticFeedback', error);
        }
      }
    },
    [isHapticsSupported]
  );

  // Execute a haptic sequence
  const executeHapticSequence = useCallback(
    async (sequence: HapticSequence): Promise<void> => {
      if (!isHapticsSupported || !hapticsEnabled) return;

      for (const haptic of sequence.pattern) {
        if (haptic.delay) {
          await new Promise(resolve => setTimeout(resolve, haptic.delay));
        }
        await executeSingleHaptic(haptic.type);
      }
    },
    [isHapticsSupported, executeSingleHaptic]
  );

  // Play predefined patterns
  const playPattern = useCallback(
    async (pattern: InstructionHapticPattern): Promise<void> => {
      const sequence = INSTRUCTION_HAPTIC_PATTERNS[pattern];
      if (sequence) {
        await executeHapticSequence(sequence);
      }
    },
    [executeHapticSequence]
  );

  const playCustomSequence = useCallback(
    async (sequence: HapticSequence): Promise<void> => {
      await executeHapticSequence(sequence);
    },
    [executeHapticSequence]
  );

  // Instruction flow patterns
  const stepAdvance = useCallback(() => playPattern('stepAdvance'), [playPattern]);
  const stepComplete = useCallback(() => playPattern('stepComplete'), [playPattern]);
  const flowComplete = useCallback(() => playPattern('flowComplete'), [playPattern]);

  // Validation patterns
  const validationSuccess = useCallback(() => playPattern('validationSuccess'), [playPattern]);
  const validationWarning = useCallback(() => playPattern('validationWarning'), [playPattern]);
  const validationError = useCallback(() => playPattern('validationError'), [playPattern]);

  // Timer patterns
  const timerAlert = useCallback(() => playPattern('timerAlert'), [playPattern]);
  const phaseTransition = useCallback(() => playPattern('phaseTransition'), [playPattern]);

  // Achievement patterns
  const milestone = useCallback(() => playPattern('milestone'), [playPattern]);
  const progressUpdate = useCallback(() => playPattern('progressUpdate'), [playPattern]);

  // Interaction patterns
  const checklistToggle = useCallback(() => playPattern('checklistToggle'), [playPattern]);
  const navigationBack = useCallback(() => playPattern('navigationBack'), [playPattern]);
  const navigationForward = useCallback(() => playPattern('navigationForward'), [playPattern]);

  // UI patterns
  const modalOpen = useCallback(() => playPattern('modalOpen'), [playPattern]);
  const modalClose = useCallback(() => playPattern('modalClose'), [playPattern]);
  const buttonHover = useCallback(() => playPattern('buttonHover'), [playPattern]);
  const cardFlip = useCallback(() => playPattern('cardFlip'), [playPattern]);
  const listReorder = useCallback(() => playPattern('listReorder'), [playPattern]);

  // Action patterns
  const confirmAction = useCallback(() => playPattern('confirmAction'), [playPattern]);
  const cancelAction = useCallback(() => playPattern('cancelAction'), [playPattern]);

  // Conditional patterns based on context
  const qualityFeedback = useCallback(
    async (score: number): Promise<void> => {
      if (score >= 90) {
        await playPattern('validationSuccess');
      } else if (score >= 70) {
        await playPattern('progressUpdate');
      } else if (score >= 50) {
        await playPattern('validationWarning');
      } else {
        await playPattern('validationError');
      }
    },
    [playPattern]
  );

  const progressFeedback = useCallback(
    async (percentage: number): Promise<void> => {
      if (percentage >= 100) {
        await playPattern('flowComplete');
      } else if (percentage >= 75) {
        await playPattern('milestone');
      } else if (percentage >= 50) {
        await playPattern('stepComplete');
      } else if (percentage >= 25) {
        await playPattern('stepAdvance');
      } else {
        await playPattern('progressUpdate');
      }
    },
    [playPattern]
  );

  const timingFeedback = useCallback(
    async (isOnTime: boolean, isLate: boolean): Promise<void> => {
      if (isLate) {
        await playPattern('validationWarning');
      } else if (isOnTime) {
        await playPattern('validationSuccess');
      } else {
        await playPattern('timerAlert');
      }
    },
    [playPattern]
  );

  // Accessibility and preferences
  const isHapticsEnabled = useCallback((): boolean => {
    return hapticsEnabled && isHapticsSupported;
  }, [isHapticsSupported]);

  const setHapticsEnabled = useCallback((enabled: boolean): void => {
    hapticsEnabled = enabled;
  }, []);

  const getPatternDescription = useCallback((pattern: InstructionHapticPattern): string => {
    return INSTRUCTION_HAPTIC_PATTERNS[pattern]?.description || 'Unknown pattern';
  }, []);

  // Advanced pattern generators
  const createProgressSequence = useCallback(
    async (steps: number, currentStep: number): Promise<void> => {
      // Create a sequence that represents progress through steps
      const progressRatio = currentStep / steps;
      const intensity = progressRatio < 0.33 ? 'light' : progressRatio < 0.66 ? 'medium' : 'heavy';

      const sequence: HapticSequence = {
        pattern: [],
        description: `Progress sequence for step ${currentStep} of ${steps}`,
      };

      // Add base progress feedback
      sequence.pattern.push({ type: Haptics.ImpactFeedbackStyle.Light });

      // Add intensity based on progress
      if (intensity === 'medium') {
        sequence.pattern.push({
          type: Haptics.ImpactFeedbackStyle.Medium,
          delay: 100,
        });
      } else if (intensity === 'heavy') {
        sequence.pattern.push({
          type: Haptics.ImpactFeedbackStyle.Medium,
          delay: 100,
        });
        sequence.pattern.push({
          type: Haptics.ImpactFeedbackStyle.Heavy,
          delay: 200,
        });
      }

      // Add milestone feedback at quarter points
      if (
        currentStep === Math.floor(steps * 0.25) ||
        currentStep === Math.floor(steps * 0.5) ||
        currentStep === Math.floor(steps * 0.75)
      ) {
        sequence.pattern.push({
          type: Haptics.NotificationFeedbackType.Success,
          delay: 300,
        });
      }

      await executeHapticSequence(sequence);
    },
    [executeHapticSequence]
  );

  const createQualitySequence = useCallback(
    async (previousScore: number, newScore: number): Promise<void> => {
      const improvement = newScore - previousScore;

      if (improvement > 10) {
        // Significant improvement
        await playPattern('validationSuccess');
      } else if (improvement > 0) {
        // Small improvement
        await playPattern('progressUpdate');
      } else if (improvement === 0) {
        // No change
        await executeSingleHaptic('selection');
      } else if (improvement > -10) {
        // Small decrease
        await playPattern('validationWarning');
      } else {
        // Significant decrease
        await playPattern('validationError');
      }
    },
    [playPattern, executeSingleHaptic]
  );

  const createTimerSequence = useCallback(
    async (timeRemaining: number, totalTime: number): Promise<void> => {
      const ratio = timeRemaining / totalTime;

      if (ratio <= 0.1) {
        // Critical time remaining
        await playPattern('validationError');
      } else if (ratio <= 0.25) {
        // Warning time
        await playPattern('timerAlert');
      } else if (ratio <= 0.5) {
        // Mid-point
        await playPattern('phaseTransition');
      } else {
        // Plenty of time
        await playPattern('progressUpdate');
      }
    },
    [playPattern]
  );

  return {
    // Instruction flow patterns
    stepAdvance,
    stepComplete,
    flowComplete,

    // Validation patterns
    validationSuccess,
    validationWarning,
    validationError,

    // Timer patterns
    timerAlert,
    phaseTransition,

    // Achievement patterns
    milestone,
    progressUpdate,

    // Interaction patterns
    checklistToggle,
    navigationBack,
    navigationForward,

    // UI patterns
    modalOpen,
    modalClose,
    buttonHover,
    cardFlip,
    listReorder,

    // Action patterns
    confirmAction,
    cancelAction,

    // Custom pattern execution
    playPattern,
    playCustomSequence,

    // Conditional patterns
    qualityFeedback,
    progressFeedback,
    timingFeedback,

    // Accessibility and preferences
    isHapticsEnabled,
    setHapticsEnabled,
    getPatternDescription,

    // Advanced patterns
    createProgressSequence,
    createQualitySequence,
    createTimerSequence,
  };
}
