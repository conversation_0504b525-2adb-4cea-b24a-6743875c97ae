import { useState, useCallback, useRef, useEffect } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Haptics from 'expo-haptics';
import { useHaptics } from './useHaptics';
import { logger } from '@/utils/logger';

// Timer phase interface
export interface TimerPhase {
  id: string;
  name: string;
  duration: number; // in seconds
  description?: string;
  color?: string;
  hapticPattern?: 'light' | 'medium' | 'heavy' | 'warning' | 'success';
  alertIntervals?: number[]; // Alert at specific intervals (in seconds before end)
}

// Timer state
interface TimerState {
  currentPhase: number;
  currentPhaseTime: number; // elapsed time in current phase
  totalElapsedTime: number;
  totalRemainingTime: number;
  isRunning: boolean;
  isPaused: boolean;
  hasStarted: boolean;
  isCompleted: boolean;
  backgroundTime: number; // time spent in background
  lastActiveTime: number;
}

// Timer configuration
interface TimerConfig {
  phases: TimerPhase[];
  autoAdvance?: boolean; // Automatically advance to next phase
  allowPause?: boolean;
  backgroundTracking?: boolean;
  persistState?: boolean;
  warningThreshold?: number; // seconds before end to show warning
  onPhaseChange?: (phase: TimerPhase, phaseIndex: number) => void;
  onPhaseComplete?: (phase: TimerPhase, phaseIndex: number) => void;
  onTimerComplete?: () => void;
  onWarning?: (secondsRemaining: number) => void;
}

// Hook return interface
export interface UseStepTimerReturn {
  // Timer state
  currentPhase: TimerPhase | null;
  currentPhaseIndex: number;
  currentPhaseTime: number;
  currentPhaseRemaining: number;
  totalElapsedTime: number;
  totalRemainingTime: number;
  totalDuration: number;

  // Status flags
  isRunning: boolean;
  isPaused: boolean;
  hasStarted: boolean;
  isCompleted: boolean;
  isLastPhase: boolean;

  // Progress calculations
  currentPhaseProgress: number; // 0-100
  totalProgress: number; // 0-100

  // Control actions
  start: () => Promise<void>;
  pause: () => Promise<void>;
  resume: () => Promise<void>;
  stop: () => Promise<void>;
  reset: () => Promise<void>;

  // Phase navigation
  nextPhase: () => Promise<void>;
  previousPhase: () => Promise<void>;
  goToPhase: (phaseIndex: number) => Promise<void>;
  skipPhase: () => Promise<void>;
  addExtraTime: (seconds: number) => Promise<void>;

  // Time formatting
  formatTime: (seconds: number) => string;
  formatCurrentPhaseTime: () => string;
  formatTotalTime: () => string;
  formatRemainingTime: () => string;

  // Persistence
  saveState: () => Promise<void>;
  loadState: () => Promise<void>;
  clearState: () => Promise<void>;
}

const STORAGE_PREFIX = 'step_timer_';

export function useStepTimer(config: TimerConfig, persistenceKey = 'default'): UseStepTimerReturn {
  const { impact } = useHaptics();

  // Core timer state
  const [timerState, setTimerState] = useState<TimerState>({
    currentPhase: 0,
    currentPhaseTime: 0,
    totalElapsedTime: 0,
    totalRemainingTime: config.phases.reduce((total, phase) => total + phase.duration, 0),
    isRunning: false,
    isPaused: false,
    hasStarted: false,
    isCompleted: false,
    backgroundTime: 0,
    lastActiveTime: Date.now(),
  });

  // Timer interval ref
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const alertTimeoutsRef = useRef<Set<NodeJS.Timeout>>(new Set());

  // Storage key
  const storageKey = `${STORAGE_PREFIX}${persistenceKey}`;

  // Calculate derived values
  const totalDuration = config.phases.reduce((total, phase) => total + phase.duration, 0);
  const currentPhase = config.phases[timerState.currentPhase] || null;
  const isLastPhase = timerState.currentPhase === config.phases.length - 1;
  const currentPhaseRemaining = currentPhase
    ? Math.max(0, currentPhase.duration - timerState.currentPhaseTime)
    : 0;
  const currentPhaseProgress =
    currentPhase && currentPhase.duration > 0
      ? (timerState.currentPhaseTime / currentPhase.duration) * 100
      : 0;
  const totalProgress = totalDuration > 0 ? (timerState.totalElapsedTime / totalDuration) * 100 : 0;

  // Handle app state changes for background tracking
  useEffect(() => {
    if (!config.backgroundTracking) return;

    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'background' && timerState.isRunning) {
        setTimerState(prev => ({
          ...prev,
          lastActiveTime: Date.now(),
        }));
      } else if (nextAppState === 'active' && timerState.isRunning) {
        const backgroundDuration = Math.floor((Date.now() - timerState.lastActiveTime) / 1000);
        setTimerState(prev => ({
          ...prev,
          backgroundTime: prev.backgroundTime + backgroundDuration,
          totalElapsedTime: prev.totalElapsedTime + backgroundDuration,
          currentPhaseTime: prev.currentPhaseTime + backgroundDuration,
        }));
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [config.backgroundTracking, timerState.isRunning, timerState.lastActiveTime]);

  // Persist state on changes
  useEffect(() => {
    if (config.persistState && timerState.hasStarted) {
      const debounceTimer = setTimeout(() => {
        saveState();
      }, 1000);

      return () => clearTimeout(debounceTimer);
    }
  }, [timerState, config.persistState, saveState]);

  // Main timer logic
  const tick = useCallback(() => {
    setTimerState(prev => {
      if (!prev.isRunning || prev.isPaused || prev.isCompleted) return prev;

      const newCurrentPhaseTime = prev.currentPhaseTime + 1;
      const newTotalElapsedTime = prev.totalElapsedTime + 1;
      const newTotalRemainingTime = Math.max(0, totalDuration - newTotalElapsedTime);

      const phase = config.phases[prev.currentPhase];

      // Check for phase completion
      if (phase && newCurrentPhaseTime >= phase.duration) {
        // Phase completed
        config.onPhaseComplete?.(phase, prev.currentPhase);

        // Check if this was the last phase
        if (prev.currentPhase >= config.phases.length - 1) {
          // Timer completed
          config.onTimerComplete?.();
          clearInterval(intervalRef.current!);

          // Completion haptics
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

          return {
            ...prev,
            currentPhaseTime: phase.duration,
            totalElapsedTime: newTotalElapsedTime,
            totalRemainingTime: 0,
            isRunning: false,
            isCompleted: true,
          };
        } else if (config.autoAdvance) {
          // Auto advance to next phase
          const nextPhase = config.phases[prev.currentPhase + 1];
          config.onPhaseChange?.(nextPhase, prev.currentPhase + 1);

          // Phase transition haptics
          impact(nextPhase.hapticPattern || 'light');

          return {
            ...prev,
            currentPhase: prev.currentPhase + 1,
            currentPhaseTime: 0,
            totalElapsedTime: newTotalElapsedTime,
            totalRemainingTime: newTotalRemainingTime,
          };
        }
      }

      // Check for alerts
      if (phase && phase.alertIntervals) {
        const remainingTime = phase.duration - newCurrentPhaseTime;
        phase.alertIntervals.forEach(alertTime => {
          if (remainingTime === alertTime) {
            config.onWarning?.(remainingTime);
            impact('warning');
          }
        });
      }

      // Check for warning threshold
      if (config.warningThreshold && newTotalRemainingTime === config.warningThreshold) {
        config.onWarning?.(newTotalRemainingTime);
        impact('warning');
      }

      return {
        ...prev,
        currentPhaseTime: newCurrentPhaseTime,
        totalElapsedTime: newTotalElapsedTime,
        totalRemainingTime: newTotalRemainingTime,
      };
    });
  }, [config, totalDuration, impact]);

  // Start timer
  const start = useCallback(async (): Promise<void> => {
    if (timerState.isCompleted) return;

    await impact('light');

    setTimerState(prev => ({
      ...prev,
      isRunning: true,
      isPaused: false,
      hasStarted: true,
      lastActiveTime: Date.now(),
    }));

    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    intervalRef.current = setInterval(tick, 1000);

    // Trigger phase change callback if starting fresh
    if (!timerState.hasStarted && currentPhase) {
      config.onPhaseChange?.(currentPhase, timerState.currentPhase);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [timerState.isCompleted, timerState.hasStarted, currentPhase, impact, tick]);

  // Pause timer
  const pause = useCallback(async (): Promise<void> => {
    if (!config.allowPause || !timerState.isRunning) return;

    await impact('medium');

    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    setTimerState(prev => ({
      ...prev,
      isPaused: true,
      isRunning: false,
    }));
  }, [config.allowPause, timerState.isRunning, impact]);

  // Resume timer
  const resume = useCallback(async (): Promise<void> => {
    if (!timerState.isPaused) return;

    await start();
  }, [timerState.isPaused, start]);

  // Stop timer
  const stop = useCallback(async (): Promise<void> => {
    await impact('heavy');

    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    // Clear all alert timeouts
    alertTimeoutsRef.current.forEach(clearTimeout);
    alertTimeoutsRef.current.clear();

    setTimerState(prev => ({
      ...prev,
      isRunning: false,
      isPaused: false,
    }));
  }, [impact]);

  // Reset timer
  const reset = useCallback(async (): Promise<void> => {
    await stop();

    setTimerState({
      currentPhase: 0,
      currentPhaseTime: 0,
      totalElapsedTime: 0,
      totalRemainingTime: totalDuration,
      isRunning: false,
      isPaused: false,
      hasStarted: false,
      isCompleted: false,
      backgroundTime: 0,
      lastActiveTime: Date.now(),
    });

    if (config.persistState) {
      await clearState();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [stop, totalDuration, config.persistState]);

  // Phase navigation
  const nextPhase = useCallback(async (): Promise<void> => {
    if (isLastPhase) return;

    await impact('light');

    const nextPhaseIndex = timerState.currentPhase + 1;
    const nextPhaseData = config.phases[nextPhaseIndex];

    setTimerState(prev => ({
      ...prev,
      currentPhase: nextPhaseIndex,
      currentPhaseTime: 0,
    }));

    config.onPhaseChange?.(nextPhaseData, nextPhaseIndex);
  }, [isLastPhase, timerState.currentPhase, config, impact]);

  const previousPhase = useCallback(async (): Promise<void> => {
    if (timerState.currentPhase === 0) return;

    await impact('light');

    const prevPhaseIndex = timerState.currentPhase - 1;
    const prevPhaseData = config.phases[prevPhaseIndex];

    setTimerState(prev => ({
      ...prev,
      currentPhase: prevPhaseIndex,
      currentPhaseTime: 0,
    }));

    config.onPhaseChange?.(prevPhaseData, prevPhaseIndex);
  }, [timerState.currentPhase, config, impact]);

  const goToPhase = useCallback(
    async (phaseIndex: number): Promise<void> => {
      if (phaseIndex < 0 || phaseIndex >= config.phases.length) return;

      await impact('selection');

      const phaseData = config.phases[phaseIndex];

      setTimerState(prev => ({
        ...prev,
        currentPhase: phaseIndex,
        currentPhaseTime: 0,
      }));

      config.onPhaseChange?.(phaseData, phaseIndex);
    },
    [config, impact]
  );

  const skipPhase = useCallback(async (): Promise<void> => {
    if (!currentPhase || isLastPhase) return;

    await impact('medium');

    // Mark current phase as completed and move to next
    const completedTime = currentPhase.duration - timerState.currentPhaseTime;

    setTimerState(prev => ({
      ...prev,
      currentPhase: prev.currentPhase + 1,
      currentPhaseTime: 0,
      totalElapsedTime: prev.totalElapsedTime + completedTime,
      totalRemainingTime: prev.totalRemainingTime - completedTime,
    }));

    config.onPhaseComplete?.(currentPhase, timerState.currentPhase);

    const nextPhase = config.phases[timerState.currentPhase + 1];
    if (nextPhase) {
      config.onPhaseChange?.(nextPhase, timerState.currentPhase + 1);
    }
  }, [
    currentPhase,
    isLastPhase,
    timerState.currentPhase,
    timerState.currentPhaseTime,
    config,
    impact,
  ]);

  const addExtraTime = useCallback(
    async (seconds: number): Promise<void> => {
      if (seconds <= 0) return;

      await impact('light');

      setTimerState(prev => ({
        ...prev,
        totalRemainingTime: prev.totalRemainingTime + seconds,
      }));

      // Update current phase duration temporarily
      if (currentPhase) {
        currentPhase.duration += seconds;
      }
    },
    [currentPhase, impact]
  );

  // Time formatting utilities
  const formatTime = useCallback((seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }
  }, []);

  const formatCurrentPhaseTime = useCallback((): string => {
    return formatTime(timerState.currentPhaseTime);
  }, [formatTime, timerState.currentPhaseTime]);

  const formatTotalTime = useCallback((): string => {
    return formatTime(timerState.totalElapsedTime);
  }, [formatTime, timerState.totalElapsedTime]);

  const formatRemainingTime = useCallback((): string => {
    return formatTime(timerState.totalRemainingTime);
  }, [formatTime, timerState.totalRemainingTime]);

  // Persistence methods
  const saveState = useCallback(async (): Promise<void> => {
    try {
      const dataToSave = {
        ...timerState,
        savedAt: Date.now(),
      };

      await AsyncStorage.setItem(storageKey, JSON.stringify(dataToSave));
    } catch (error) {
      logger.warn('Failed to save timer state', 'useStepTimer', error);
    }
  }, [timerState, storageKey]);

  const loadState = useCallback(async (): Promise<void> => {
    try {
      const savedData = await AsyncStorage.getItem(storageKey);
      if (savedData) {
        const parsedData = JSON.parse(savedData);

        // Calculate time drift if timer was running when saved
        let timeDrift = 0;
        if (parsedData.isRunning && parsedData.savedAt) {
          timeDrift = Math.floor((Date.now() - parsedData.savedAt) / 1000);
        }

        setTimerState({
          ...parsedData,
          totalElapsedTime: parsedData.totalElapsedTime + timeDrift,
          currentPhaseTime: parsedData.currentPhaseTime + timeDrift,
          totalRemainingTime: Math.max(0, parsedData.totalRemainingTime - timeDrift),
          isRunning: false, // Always start paused when loading
          lastActiveTime: Date.now(),
        });
      }
    } catch (error) {
      logger.warn('Failed to load timer state', 'useStepTimer', error);
    }
  }, [storageKey]);

  const clearState = useCallback(async (): Promise<void> => {
    try {
      await AsyncStorage.removeItem(storageKey);
    } catch (error) {
      logger.warn('Failed to clear timer state', 'useStepTimer', error);
    }
  }, [storageKey]);

  // Cleanup on unmount
  useEffect(() => {
    const currentAlertTimeouts = alertTimeoutsRef.current;
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      currentAlertTimeouts.forEach(clearTimeout);
    };
  }, []);

  // Load persisted state on mount
  useEffect(() => {
    if (config.persistState) {
      loadState();
    }
  }, [config.persistState, loadState]);

  return {
    // Timer state
    currentPhase,
    currentPhaseIndex: timerState.currentPhase,
    currentPhaseTime: timerState.currentPhaseTime,
    currentPhaseRemaining,
    totalElapsedTime: timerState.totalElapsedTime,
    totalRemainingTime: timerState.totalRemainingTime,
    totalDuration,

    // Status flags
    isRunning: timerState.isRunning,
    isPaused: timerState.isPaused,
    hasStarted: timerState.hasStarted,
    isCompleted: timerState.isCompleted,
    isLastPhase,

    // Progress calculations
    currentPhaseProgress: Math.min(100, Math.max(0, currentPhaseProgress)),
    totalProgress: Math.min(100, Math.max(0, totalProgress)),

    // Control actions
    start,
    pause,
    resume,
    stop,
    reset,

    // Phase navigation
    nextPhase,
    previousPhase,
    goToPhase,
    skipPhase,
    addExtraTime,

    // Time formatting
    formatTime,
    formatCurrentPhaseTime,
    formatTotalTime,
    formatRemainingTime,

    // Persistence
    saveState,
    loadState,
    clearState,
  };
}
