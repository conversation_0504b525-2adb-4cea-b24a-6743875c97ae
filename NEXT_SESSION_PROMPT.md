# 🚀 PRÓXIMA SESIÓN: AI Colorimetry Expertise System v2.4.0

## 📋 CONTEXTO DEL PROYECTO

**Salonier** es una aplicación React Native + Expo de asistencia profesional para coloración capilar que usa GPT-4o Vision para análisis de cabello y generación de fórmulas químicas.

**Estado actual:** v2.3.1 - Codebase limpio tras épica sesión de cleanup (111→50 ESLint warnings, React Hooks 100% clean, TypeScript any 89% clean).

## 🚨 PROBLEMA CRÍTICO IDENTIFICADO

### **Issue Core:** La AI genera fórmulas químicamente incorrectas que comprometen la credibilidad profesional

**Ejemplos específicos reportados:**
- ❌ **Viola "Color no levanta color":** Sugiere castaño→rubio dorado con solo oxidante 20vol + color 9.3
- ❌ **Ignora brand specifics:** No considera diferencias entre marcas/líneas (barros vs tintes, tonalidades específicas)
- ❌ **Missing multi-phase planning:** Siempre da una fórmula single-phase cuando a veces requiere múltiples fases
- ❌ **Incorrect mixing ratios:** No explica proporciones específicas por marca
- ❌ **No impossibility detection:** No dice cuándo un color es imposible de lograr

### **Business Impact:**
- Pérdida de credibilidad profesional
- Coloristas no pueden confiar 100% en las fórmulas
- Diferenciación competitiva comprometida

## 🎯 OBJETIVOS DE LA SESIÓN

### **OBJETIVO PRINCIPAL:** 
Implementar AI Colorimetry Expertise System de 3 capas para lograr **95%+ accuracy** en formulación química.

### **SUCCESS METRICS:**
- ✅ Zero violations de reglas químicas fundamentales
- ✅ Brand-specific expertise implementation  
- ✅ Multi-phase planning cuando necesario
- ✅ Impossibility detection con alternatives

## 🏗️ ARQUITECTURA TÉCNICA PROPUESTA

### **Layer 1: Chemical Rules Validation Engine**
```typescript
// Hard-coded chemical rules (NO database)
const ChemicalValidator = {
  validateColorTheory: (current, desired, formula) => {
    // "Color no levanta color" - REGLA ABSOLUTA
    if (isDarkerBase(current) && isLighterTarget(desired) && !hasLiftingProcess(formula)) {
      return { valid: false, error: "Violation: Color no levanta color" };
    }
  },
  validateOxidantLevels: (hairCondition, targetLift, oxidant) => {
    // Cabello dañado = max 20vol, etc.
  }
};
```

### **Layer 2: Brand Intelligence System**
```typescript
// Context-aware AI prompting per marca/línea
const BrandExpertise = {
  buildExpertPrompt: (brand, line) => {
    return `Eres experto certificado en ${brand} ${line}.
    Conoces que esta línea: ${getBrandSpecifics(brand, line)}
    Productos disponibles: ${getAvailableProducts(brand, line)}`;
  },
  validateProductExists: (color, brand, line) => {
    // Check if specific color/tone exists in brand catalog
  }
};
```

### **Layer 3: Multi-Phase Planning Engine**
```typescript
const PhaseCalculator = {
  analyzeViability: (current, desired) => {
    if (requiresDecoloring(current, desired)) {
      return { 
        phases: [
          { step: 1, process: "Decoloración", products: [...] },
          { step: 2, process: "Matización", products: [...] }
        ]
      };
    }
    
    if (isImpossible(current, desired)) {
      return { 
        viable: false, 
        reason: "Color objetivo no alcanzable desde base actual",
        alternatives: [...] 
      };
    }
  }
};
```

## 📁 ARCHIVOS CLAVE A MODIFICAR

### **Existing Files:**
- `supabase/functions/salonier-assistant/` - Edge Function main logic
- `utils/chemical-validator.ts` - Expandir validation rules
- `src/service/components/FormulationStep.tsx` - UI para multi-phase
- `stores/salon-config-store.ts` - Brand/region configuration

### **New Files to Create:**
- `utils/ai/brand-expertise.ts` - Brand intelligence system
- `utils/ai/phase-calculator.ts` - Multi-phase planning
- `utils/ai/colorimetry-rules.ts` - Hard-coded chemical rules
- `types/formulation-phases.ts` - TypeScript interfaces

## 🎯 IMPLEMENTACIÓN ESPECÍFICA PRIMERA SESIÓN

### **FASE 1: Chemical Validation Foundation**
1. **Crear colorimetry-rules.ts** con reglas químicas hard-coded:
   - Color no levanta color detection
   - Oxidant level validation por hair condition  
   - Color wheel compatibility
   - Damage level considerations

2. **Expandir chemical-validator.ts** para integrate new rules:
   - Pre-validation antes de AI call
   - Post-validation después de AI response
   - Error handling con specific messages

### **FASE 2: Brand Intelligence Integration** 
1. **Crear brand-expertise.ts**:
   - Brand-specific prompt building
   - Product catalog integration with inventory
   - Mixing ratios per marca/línea
   - Tone variations per brand

2. **Modify Edge Function** para use brand context:
   - Enhanced prompting with brand expertise
   - Validation against available inventory
   - Brand-specific terminology

### **FASE 3: Multi-Phase UI Flow**
1. **Modify FormulationStep.tsx**:
   - Handle multi-phase results from AI
   - Show phase breakdown UI
   - Impossibility handling with alternatives

## 🔧 CONFIGURACIÓN CONSIDERACIONES

### **Regional Settings Impact:**
- Metric vs Imperial measurements in formulas
- Currency for cost calculations  
- Local brand availability per region
- Regulatory constraints per country

### **Inventory Integration:**
- Only suggest products actually available
- Intelligent substitution when product missing
- Stock level considerations
- Cost calculation per formula

## 🤖 AGENTES RECOMENDADOS

### **USAR PROACTIVAMENTE:**
- **ai-integration-specialist**: Para optimize AI prompts y reduce latency
- **colorimetry-expert**: Para validate chemical rules y formulas  
- **frontend-developer**: Para implement multi-phase UI flow
- **database-architect**: Si need inventory queries optimization

## 🎯 PROMPT PARA INICIAR SESIÓN

```
Hola! Necesito implementar el AI Colorimetry Expertise System v2.4.0 
para resolver problemas críticos de formulación química en Salonier.

PROBLEMA: La AI genera fórmulas químicamente incorrectas (ej: viola 
"color no levanta color", ignora brand specifics, no detecta multi-phases).

OBJETIVO: Crear sistema de 3 capas (Chemical Validation + Brand Intelligence + 
Multi-Phase Planning) para lograr 95%+ accuracy sin databases complejas.

EMPEZAR CON: Chemical Rules Validation Engine - crear hard-coded rules 
para prevenir violations básicas como "color no levanta color".

Usar ai-integration-specialist + colorimetry-expert agents proactivamente.
```

## 📊 SUCCESS CRITERIA

### **Post-Implementación debe lograr:**
- ✅ Zero chemical rule violations
- ✅ Brand-appropriate product suggestions
- ✅ Multi-phase planning cuando necesario  
- ✅ Realistic impossibility detection
- ✅ Proper mixing ratios explanation
- ✅ Integration con configuración regional/inventario

---

**Target completion:** 2-3 sesiones para sistema completo
**Immediate impact:** Credibilidad profesional restaurada