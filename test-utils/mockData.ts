/**
 * Mock Data for Testing
 *
 * Provides realistic mock data for testing components
 * that depend on complex data structures.
 */

import { VisualFormulationData } from '@/types/visual-formulation';
import { HairZone, HairZoneDisplay } from '@/types/hair-diagnosis';

/**
 * Mock VisualFormulationData for testing
 */
export function mockVisualFormulationData(
  overrides?: Partial<VisualFormulationData>
): VisualFormulationData {
  return {
    id: 'test-formula-001',
    created_at: '2024-01-01T00:00:00.000Z',
    updated_at: '2024-01-01T00:00:00.000Z',
    client_id: 'test-client-001',
    salon_id: 'test-salon-001',

    // Hair analysis
    hair_analysis: {
      current_color: 'Castaño medio',
      natural_base: 6,
      length: 'medium',
      texture: 'normal',
      porosity: 'normal',
      condition: 'healthy',
      previous_treatments: [],
    },

    // Desired result
    desired_color: {
      name: 'Rubio dorado',
      level: 7,
      tone: 'golden',
      intensity: 'medium',
    },

    // Hair zones for application
    hair_zones: [
      {
        id: 'roots',
        name: '<PERSON><PERSON><PERSON>',
        current_level: 6,
        target_level: 7,
        lift_needed: 1,
        application_order: 1,
      } as HairZone,
      {
        id: 'mids',
        name: 'Me<PERSON><PERSON>',
        current_level: 5,
        target_level: 7,
        lift_needed: 2,
        application_order: 2,
      } as HairZone,
      {
        id: 'ends',
        name: 'Puntas',
        current_level: 4,
        target_level: 7,
        lift_needed: 3,
        application_order: 3,
      } as HairZone,
    ],

    // Technical formula
    technical_formula: {
      base_color: {
        brand: 'Wella',
        line: 'Koleston Perfect',
        shade: '7/03',
        volume: 60,
      },
      developer: {
        volume: 20,
        amount: 60,
      },
      additives: [
        {
          name: 'Color Fresh',
          amount: 10,
          purpose: 'toning',
        },
      ],
      mixing_ratio: '1:1',
      processing_time: 30,
      application_method: 'foil_highlights',
    },

    // Step-by-step instructions
    instructions: [
      {
        step: 1,
        title: 'Preparación',
        description: 'Preparar materiales y workspace',
        duration: 5,
        materials: [
          'Color base Wella 7/03 - 60ml',
          'Oxidante 20vol - 60ml',
          'Color Fresh - 10ml',
          'Cuenco de mezcla',
          'Pincel aplicador',
          'Guantes',
        ],
        safety_notes: ['Usar guantes de protección', 'Verificar test de alergia'],
      },
      {
        step: 2,
        title: 'Mezclado',
        description: 'Mezclar fórmula según especificaciones',
        duration: 3,
        materials: [],
        instructions_detail: [
          'Mezclar color base con oxidante 1:1',
          'Añadir Color Fresh',
          'Mezclar hasta consistencia homogénea',
        ],
      },
      {
        step: 3,
        title: 'Aplicación',
        description: 'Aplicar por zonas según orden establecido',
        duration: 15,
        materials: [],
        zone_instructions: {
          roots: 'Aplicar primero, evitar cuero cabelludo',
          mids: 'Aplicar segundo, distribución uniforme',
          ends: 'Aplicar último, saturar bien',
        },
      },
      {
        step: 4,
        title: 'Procesado',
        description: 'Tiempo de procesado y monitoreo',
        duration: 30,
        materials: [],
        monitoring_points: [
          '10 min: verificar desarrollo',
          '20 min: comprobar uniformidad',
          '30 min: test de aclarado',
        ],
      },
      {
        step: 5,
        title: 'Enjuague',
        description: 'Enjuague y neutralización',
        duration: 10,
        materials: ['Champú neutralizante', 'Acondicionador protector'],
        temperature: 'lukewarm',
      },
      {
        step: 6,
        title: 'Finalización',
        description: 'Secado y estilizado final',
        duration: 5,
        materials: [],
      },
    ],

    // Results and notes
    estimated_duration: 68, // minutes
    difficulty_level: 'intermediate' as const,
    cost_estimate: 45.5,

    notes: 'Aplicar con cuidado en las puntas por ser más porosas',

    // Optional fields
    before_photos: [],
    after_photos: [],
    client_satisfaction: undefined,
    colorist_notes: undefined,

    ...overrides,
  };
}

/**
 * Mock HairZoneDisplay for testing
 */
export function mockHairZoneDisplay(overrides?: Partial<HairZoneDisplay>): HairZoneDisplay {
  return {
    id: 'test-zone',
    name: 'Test Zone',
    current_level: 5,
    target_level: 7,
    lift_needed: 2,
    application_order: 1,
    processing_time: 30,
    formula_adjustments: {
      developer_volume: 20,
      processing_time_modifier: 0,
    },
    ...overrides,
  };
}

/**
 * Mock user data for authentication
 */
export function mockUser() {
  return {
    id: 'test-user-001',
    email: '<EMAIL>',
    name: 'Test User',
    role: 'colorist' as const,
    salon_id: 'test-salon-001',
  };
}

/**
 * Mock client data
 */
export function mockClient() {
  return {
    id: 'test-client-001',
    name: 'Cliente Test',
    email: '<EMAIL>',
    phone: '+34 600 000 000',
    hair_history: [],
    allergies: [],
    notes: 'Cliente habitual, sin alergias conocidas',
  };
}
