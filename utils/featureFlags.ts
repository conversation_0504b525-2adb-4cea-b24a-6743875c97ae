/**
 * Feature Flag System for Safe A/B Testing
 *
 * Provides controlled rollout capabilities for new features
 * with instant rollback support and user-based distribution.
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuthStore } from '@/stores/auth-store';

interface FeatureFlag {
  key: string;
  name: string;
  description: string;
  rolloutPercentage: number;
  enabled: boolean;
  forceEnabled?: string[]; // User IDs to force enable
  forceDisabled?: string[]; // User IDs to force disable
}

/**
 * Feature flag configurations
 * Update these values to control feature rollouts
 */
const FEATURE_FLAGS: Record<string, FeatureFlag> = {
  NEW_INSTRUCTIONS_FLOW: {
    key: 'NEW_INSTRUCTIONS_FLOW',
    name: 'New Instructions Flow',
    description: 'Refactored modular instructions flow component',
    rolloutPercentage: __DEV__ ? 100 : 10, // 100% in dev, 10% in production
    enabled: true,
    forceEnabled: [], // Add user IDs here to force enable
    forceDisabled: [], // Add user IDs here to force disable
  },
};

/**
 * Simple hash function for consistent user-based distribution
 */
function simpleHash(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash);
}

/**
 * Get user ID for feature flag distribution
 */
function getUserId(): string {
  const { user } = useAuthStore.getState();
  return user?.id || 'anonymous';
}

/**
 * Check if a feature flag is enabled for the current user
 */
export function isFeatureEnabled(flagKey: string): boolean {
  const flag = FEATURE_FLAGS[flagKey];

  if (!flag || !flag.enabled) {
    return false;
  }

  // Development mode override
  if (__DEV__) {
    return flag.rolloutPercentage > 0;
  }

  const userId = getUserId();

  // Force enable check
  if (flag.forceEnabled?.includes(userId)) {
    return true;
  }

  // Force disable check
  if (flag.forceDisabled?.includes(userId)) {
    return false;
  }

  // Percentage-based rollout
  const userHash = simpleHash(userId + flagKey);
  const userPercentile = userHash % 100;

  return userPercentile < flag.rolloutPercentage;
}

/**
 * Specific check for new instructions flow
 */
export function shouldUseNewInstructionsFlow(): boolean {
  return isFeatureEnabled('NEW_INSTRUCTIONS_FLOW');
}

/**
 * Override feature flag for testing (persisted)
 */
export async function setFeatureFlagOverride(flagKey: string, enabled: boolean): Promise<void> {
  try {
    const overrideKey = `feature_flag_override_${flagKey}`;
    await AsyncStorage.setItem(overrideKey, JSON.stringify({ enabled }));
  } catch {}
}

/**
 * Get feature flag override if exists
 */
export async function getFeatureFlagOverride(flagKey: string): Promise<boolean | null> {
  try {
    const overrideKey = `feature_flag_override_${flagKey}`;
    const override = await AsyncStorage.getItem(overrideKey);

    if (override) {
      const { enabled } = JSON.parse(override);
      return enabled;
    }
  } catch {}

  return null;
}

/**
 * Clear all feature flag overrides
 */
export async function clearFeatureFlagOverrides(): Promise<void> {
  try {
    const keys = await AsyncStorage.getAllKeys();
    const overrideKeys = keys.filter(key => key.startsWith('feature_flag_override_'));
    await AsyncStorage.multiRemove(overrideKeys);
  } catch {}
}

/**
 * Get all feature flags status for debugging
 */
export function getFeatureFlagsStatus(): Record<string, boolean> {
  const status: Record<string, boolean> = {};

  Object.keys(FEATURE_FLAGS).forEach(flagKey => {
    status[flagKey] = isFeatureEnabled(flagKey);
  });

  return status;
}

/**
 * Emergency rollback - disable all features instantly
 */
export async function emergencyRollback(): Promise<void> {
  try {
    await AsyncStorage.setItem('emergency_rollback', 'true');
  } catch {}
}

/**
 * Check if emergency rollback is active
 */
export async function isEmergencyRollbackActive(): Promise<boolean> {
  try {
    const rollback = await AsyncStorage.getItem('emergency_rollback');
    return rollback === 'true';
  } catch {
    return false;
  }
}

/**
 * Clear emergency rollback
 */
export async function clearEmergencyRollback(): Promise<void> {
  try {
    await AsyncStorage.removeItem('emergency_rollback');
  } catch {}
}
