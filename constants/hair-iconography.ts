/**
 * PROFESSIONAL HAIR COLORIMETRY ICONOGRAPHY SYSTEM (v1.0.0)
 *
 * Specialized icon mapping system for hair diagnosis and analysis components.
 * Provides context-specific professional imagery throughout the diagnosis flow.
 *
 * DESIGN PHILOSOPHY:
 * "Every icon tells the story of hair health and color transformation"
 * - Visual language that speaks to professional colorists
 * - Industry-standard symbolism and terminology
 * - Consistent metaphors for complex colorimetry concepts
 */

import {
  // Hair Analysis & Diagnosis
  Search,
  Microscope,
  Eye,
  Activity,
  TrendingUp,
  Target,
  Zap,

  // Color & Chemistry
  Palette,
  TestTube,
  Beaker,
  Droplet,
  Sparkles,
  Sun,
  Moon,
  Snowflake,

  // Hair Condition & Structure
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Heart,

  // Professional Tools
  _Scissors,
  Package,
  _Edit3,
  Camera,
  _Lightbulb,
  Award,
  Star,

  // Process & Time
  _Clock,
  _Timer,
  _Calendar,
  _ArrowRight,
  RotateCcw,
  _Play,

  // System & Navigation
  Info,
  _ChevronRight,
  _Plus,
  _Minus,
  _X,
  _Check,
} from 'lucide-react-native';

// =============================================================================
// HAIR LEVEL ICONOGRAPHY (1-10 Scale)
// =============================================================================

/**
 * Hair Level Visual System
 * Maps color depth levels (1-10) to appropriate visual metaphors
 */
export const HAIR_LEVEL_ICONS = {
  // Ultra Dark Levels (1-2)
  1: { icon: 'circle-fill', color: '#1C1C1C', label: 'Negro' },
  2: { icon: 'circle-fill', color: '#2C1810', label: 'Castaño muy oscuro' },

  // Dark Levels (3-4)
  3: { icon: 'circle-fill', color: '#3D2914', label: 'Castaño oscuro' },
  4: { icon: 'circle-fill', color: '#4F3A1A', label: 'Castaño medio' },

  // Medium Levels (5-6)
  5: { icon: 'circle-fill', color: '#6B4423', label: 'Castaño claro' },
  6: { icon: 'circle-fill', color: '#8B5A2B', label: 'Rubio oscuro' },

  // Light Levels (7-8)
  7: { icon: 'circle-fill', color: '#B17A37', label: 'Rubio medio' },
  8: { icon: 'circle-fill', color: '#D4A574', label: 'Rubio claro' },

  // Ultra Light Levels (9-10)
  9: { icon: 'circle-fill', color: '#E6C999', label: 'Rubio muy claro' },
  10: { icon: 'circle-fill', color: '#F5E6C8', label: 'Rubio platino' },
} as const;

// =============================================================================
// HAIR TONE & REFLECT ICONOGRAPHY
// =============================================================================

/**
 * Professional Hair Tone Visual Language
 */
export const HAIR_TONE_ICONS = {
  // Temperature-based categorization
  warm: {
    icon: Sun,
    color: '#F59E0B', // Warm amber
    tones: ['Dorado', 'Cobrizo', 'Rojizo', 'Caoba'],
    description: 'Tonos cálidos - base dorada/cobriza',
  },
  cool: {
    icon: Snowflake,
    color: '#6366F1', // Cool indigo
    tones: ['Cenizo', 'Platino', 'Violeta', 'Beige'],
    description: 'Tonos fríos - base ceniza/violeta',
  },
  neutral: {
    icon: Target,
    color: '#64748B', // Neutral slate
    tones: ['Natural', 'Neutro'],
    description: 'Tonos neutros - sin dominancia térmica',
  },
} as const;

/**
 * Specific Undertone Iconography
 */
export const UNDERTONE_ICONS = {
  // Cool Undertones
  Cenizo: { icon: Snowflake, color: '#8B92A5' },
  Platino: { icon: Star, color: '#C0C5D0' },
  Violeta: { icon: Droplet, color: '#8B5A9F' },
  Beige: { icon: Moon, color: '#B8A082' },

  // Warm Undertones
  Dorado: { icon: Sun, color: '#D4A574' },
  Cobrizo: { icon: Sun, color: '#B87333' },
  Rojizo: { icon: Heart, color: '#CC5B5B' },
  Caoba: { icon: Heart, color: '#8B3A3A' },

  // Neutral
  Natural: { icon: Target, color: '#64748B' },
  Irisado: { icon: Sparkles, color: '#A855F7' },
} as const;

// =============================================================================
// HAIR CONDITION & ANALYSIS ICONOGRAPHY
// =============================================================================

/**
 * Hair Damage Level Visual System
 */
export const DAMAGE_LEVEL_ICONS = {
  Bajo: {
    icon: CheckCircle,
    color: '#10B981', // Success green
    description: 'Cabello saludable con cutícula intacta',
  },
  Medio: {
    icon: AlertTriangle,
    color: '#F59E0B', // Warning amber
    description: 'Daño moderado, requiere cuidado especializado',
  },
  Alto: {
    icon: XCircle,
    color: '#EF4444', // Error red
    description: 'Daño severo, necesita tratamiento reconstructivo',
  },
} as const;

/**
 * Hair Porosity Visual Indicators
 */
export const POROSITY_ICONS = {
  Baja: {
    icon: Shield,
    color: '#3B82F6', // Protected blue
    description: 'Cutícula cerrada, resistente a químicos',
  },
  Media: {
    icon: Droplet,
    color: '#10B981', // Balanced green
    description: 'Absorción equilibrada, ideal para coloración',
  },
  Alta: {
    icon: Search,
    color: '#F59E0B', // Attention amber
    description: 'Muy poroso, requiere productos selladores',
  },
} as const;

/**
 * Hair Elasticity Assessment
 */
export const ELASTICITY_ICONS = {
  Pobre: {
    icon: AlertTriangle,
    color: '#EF4444',
    description: 'Riesgo de rotura, evitar tensión',
  },
  Media: {
    icon: Activity,
    color: '#F59E0B',
    description: 'Elasticidad moderada, usar con precaución',
  },
  Buena: {
    icon: Award,
    color: '#10B981',
    description: 'Excelente resistencia y flexibilidad',
  },
} as const;

// =============================================================================
// PROFESSIONAL DIAGNOSIS CONTEXT ICONS
// =============================================================================

/**
 * Diagnosis Step Context Iconography
 * Different icons for different stages of hair analysis
 */
export const DIAGNOSIS_CONTEXT_ICONS = {
  // Photo Analysis Phase
  'photo-capture': {
    icon: Camera,
    color: '#6366F1',
    label: 'Captura fotográfica',
  },
  'image-processing': {
    icon: Search,
    color: '#8B5CF6',
    label: 'Procesando imagen',
  },

  // Color Analysis Phase
  'color-detection': {
    icon: Palette,
    color: '#EC4899',
    label: 'Detección de color',
  },
  'tone-analysis': {
    icon: Eye,
    color: '#14B8A6',
    label: 'Análisis de tonos',
  },
  'level-assessment': {
    icon: TrendingUp,
    color: '#F59E0B',
    label: 'Evaluación de nivel',
  },

  // Condition Analysis Phase
  'damage-assessment': {
    icon: Microscope,
    color: '#EF4444',
    label: 'Evaluación de daño',
  },
  'porosity-check': {
    icon: Droplet,
    color: '#10B981',
    label: 'Análisis de porosidad',
  },
  'elasticity-test': {
    icon: Activity,
    color: '#F59E0B',
    label: 'Test de elasticidad',
  },

  // Chemical History Analysis
  'process-detection': {
    icon: TestTube,
    color: '#8B5CF6',
    label: 'Detección de procesos',
  },
  'compatibility-check': {
    icon: Shield,
    color: '#14B8A6',
    label: 'Verificación de compatibilidad',
  },

  // AI Processing States
  'ai-analyzing': {
    icon: Sparkles,
    color: '#A855F7',
    label: 'IA analizando',
  },
  'ai-confident': {
    icon: CheckCircle,
    color: '#10B981',
    label: 'Análisis exitoso',
  },
  'ai-uncertain': {
    icon: RefreshCw,
    color: '#F59E0B',
    label: 'Requiere validación',
  },
  'ai-critical': {
    icon: AlertTriangle,
    color: '#EF4444',
    label: 'Consulta experto',
  },
} as const;

// =============================================================================
// CONFIDENCE LEVEL PROFESSIONAL ICONOGRAPHY
// =============================================================================

/**
 * Context-Aware Confidence Indicators
 * Professional icons that change based on analysis context and confidence level
 */
export const CONFIDENCE_CONTEXT_ICONS = {
  diagnosis: {
    high: { icon: Sparkles, label: 'Diagnóstico preciso' },
    medium: { icon: Activity, label: 'Análisis estándar' },
    low: { icon: Search, label: 'Requiere revisión' },
    critical: { icon: AlertTriangle, label: 'Consulta manual' },
  },
  formulation: {
    high: { icon: TestTube, label: 'Fórmula validada' },
    medium: { icon: Beaker, label: 'Fórmula con precauciones' },
    low: { icon: Palette, label: 'Combinación atípica' },
    critical: { icon: Shield, label: 'Riesgo de compatibilidad' },
  },
  matching: {
    high: { icon: Target, label: 'Coincidencia exacta' },
    medium: { icon: Package, label: 'Alternativas disponibles' },
    low: { icon: Search, label: 'Aproximación estimada' },
    critical: { icon: AlertTriangle, label: 'Sin coincidencias' },
  },
} as const;

// =============================================================================
// GRAY HAIR SPECIALIZED ICONOGRAPHY
// =============================================================================

/**
 * Gray Hair Analysis Visual System
 */
export const GRAY_HAIR_ICONS = {
  // Gray Hair Types
  'Resistente/Vidriosa': {
    icon: Shield,
    color: '#6B7280',
    description: 'Cana resistente, requiere mayor tiempo',
  },
  Normal: {
    icon: Target,
    color: '#9CA3AF',
    description: 'Cana estándar, procesamiento normal',
  },
  'Fina/Porosa': {
    icon: Droplet,
    color: '#D1D5DB',
    description: 'Cana delicada, absorbe rápidamente',
  },

  // Gray Patterns
  'Sal y pimienta': {
    icon: Sparkles,
    color: '#6B7280',
    description: 'Distribución uniforme',
  },
  Mechas: {
    icon: Zap,
    color: '#9CA3AF',
    description: 'Concentración en mechones',
  },
  Placas: {
    icon: Target,
    color: '#D1D5DB',
    description: 'Zonas concentradas',
  },
} as const;

// =============================================================================
// UNWANTED TONE CORRECTION ICONOGRAPHY
// =============================================================================

/**
 * Color Correction Visual Language
 */
export const UNWANTED_TONE_ICONS = {
  Naranja: {
    icon: Sun,
    color: '#FB923C',
    corrector: 'Azul/Cenizo',
    description: 'Neutralizar con tonos fríos',
  },
  Amarillo: {
    icon: Sun,
    color: '#FDE047',
    corrector: 'Violeta',
    description: 'Matizar con violeta',
  },
  Verde: {
    icon: Droplet,
    color: '#34D399',
    corrector: 'Rojo/Magenta',
    description: 'Corregir con tonos cálidos',
  },
  Rojo: {
    icon: Heart,
    color: '#F87171',
    corrector: 'Verde',
    description: 'Neutralizar con verde',
  },
  'Cenizo excesivo': {
    icon: Snowflake,
    color: '#A1A1AA',
    corrector: 'Dorado',
    description: 'Calentar con dorados',
  },
} as const;

// =============================================================================
// CHEMICAL PROCESS ICONOGRAPHY
// =============================================================================

/**
 * Chemical Process Visual Indicators
 */
export const CHEMICAL_PROCESS_ICONS = {
  Decoloración: {
    icon: Zap,
    color: '#FDE047',
    risk: 'alto',
    description: 'Proceso de aclarado',
  },
  'Tinte permanente': {
    icon: TestTube,
    color: '#8B5CF6',
    risk: 'medio',
    description: 'Coloración permanente',
  },
  'Tinte semipermanente': {
    icon: Droplet,
    color: '#EC4899',
    risk: 'bajo',
    description: 'Color temporal',
  },
  Mechas: {
    icon: Sparkles,
    color: '#F59E0B',
    risk: 'medio',
    description: 'Aclarado selectivo',
  },
  Alisado: {
    icon: RefreshCw,
    color: '#EF4444',
    risk: 'alto',
    description: 'Modificación de estructura',
  },
  Permanente: {
    icon: RotateCcw,
    color: '#F87171',
    risk: 'alto',
    description: 'Rizado químico',
  },
} as const;

// =============================================================================
// HELPER FUNCTIONS
// =============================================================================

/**
 * Get appropriate icon for hair level
 */
export const getHairLevelIcon = (level: number) => {
  const clampedLevel = Math.max(1, Math.min(10, Math.round(level)));
  return HAIR_LEVEL_ICONS[clampedLevel as keyof typeof HAIR_LEVEL_ICONS];
};

/**
 * Get confidence icon based on context and level
 */
export const getConfidenceIcon = (
  context: 'diagnosis' | 'formulation' | 'matching',
  confidence: number
) => {
  const level =
    confidence >= 85 ? 'high' : confidence >= 70 ? 'medium' : confidence >= 50 ? 'low' : 'critical';

  return CONFIDENCE_CONTEXT_ICONS[context][level];
};

/**
 * Get hair condition icon with color
 */
export const getHairConditionIcon = (
  property: 'damage' | 'porosity' | 'elasticity',
  value: string
) => {
  switch (property) {
    case 'damage':
      return DAMAGE_LEVEL_ICONS[value as keyof typeof DAMAGE_LEVEL_ICONS];
    case 'porosity':
      return POROSITY_ICONS[value as keyof typeof POROSITY_ICONS];
    case 'elasticity':
      return ELASTICITY_ICONS[value as keyof typeof ELASTICITY_ICONS];
    default:
      return { icon: Info, color: '#64748B', description: 'Información no disponible' };
  }
};

/**
 * Get undertone icon and color
 */
export const getUndertoneIcon = (undertone: string) => {
  return (
    UNDERTONE_ICONS[undertone as keyof typeof UNDERTONE_ICONS] || { icon: Target, color: '#64748B' }
  );
};

/**
 * Get diagnosis context icon for current analysis phase
 */
export const getDiagnosisPhaseIcon = (phase: string) => {
  return (
    DIAGNOSIS_CONTEXT_ICONS[phase as keyof typeof DIAGNOSIS_CONTEXT_ICONS] || {
      icon: Activity,
      color: '#64748B',
      label: 'Análisis en progreso',
    }
  );
};

// =============================================================================
// PROFESSIONAL HAIR COLOR SWATCHES
// =============================================================================

/**
 * Professional Color Palette for Hair Visualization
 * Accurate color representations for hair colorists
 */
export const PROFESSIONAL_HAIR_COLORS = {
  // Level 1-2: Black to Very Dark Brown
  1: { primary: '#0A0A0A', secondary: '#1A1A1A', name: 'Negro' },
  2: { primary: '#1C0F0A', secondary: '#2C1810', name: 'Castaño muy oscuro' },

  // Level 3-4: Dark to Medium Brown
  3: { primary: '#2D1B10', secondary: '#3D2914', name: 'Castaño oscuro' },
  4: { primary: '#3F2A15', secondary: '#4F3A1A', name: 'Castaño medio' },

  // Level 5-6: Light Brown to Dark Blonde
  5: { primary: '#51341D', secondary: '#6B4423', name: 'Castaño claro' },
  6: { primary: '#6B4A25', secondary: '#8B5A2B', name: 'Rubio oscuro' },

  // Level 7-8: Medium to Light Blonde
  7: { primary: '#8B6A31', secondary: '#B17A37', name: 'Rubio medio' },
  8: { primary: '#B8956A', secondary: '#D4A574', name: 'Rubio claro' },

  // Level 9-10: Very Light to Platinum Blonde
  9: { primary: '#D4B999', secondary: '#E6C999', name: 'Rubio muy claro' },
  10: { primary: '#E6D6C8', secondary: '#F5E6C8', name: 'Rubio platino' },
} as const;

// Type definitions for TypeScript
export type HairLevel = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10;
export type ConfidenceLevel = 'high' | 'medium' | 'low' | 'critical';
export type AnalysisContext = 'diagnosis' | 'formulation' | 'matching';
export type HairConditionProperty = 'damage' | 'porosity' | 'elasticity';
export type DiagnosisPhase = keyof typeof DIAGNOSIS_CONTEXT_ICONS;
