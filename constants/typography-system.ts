/**
 * SALONIER UNIFIED TYPOGRAPHY SYSTEM (v2.2.0)
 *
 * Replaces fragmented theme.ts and beauty-minimal-theme.ts typography with a
 * unified, professional system optimized for hair colorist workflows.
 *
 * DESIGN PHILOSOPHY:
 * "Professional legibility meets refined aesthetics. Every size serves a purpose."
 *
 * KEY IMPROVEMENTS:
 * - Refined size scale (10px-36px) for professional density
 * - Semantic styles eliminate guesswork and ensure consistency
 * - Optimized line heights for mobile reading comfort
 * - Platform-specific fonts for native feel
 * - Complete TypeScript support with strict typing
 */

import { Platform, TextStyle } from 'react-native';

// =============================================================================
// CORE TYPOGRAPHY SYSTEM
// =============================================================================

/**
 * TYPOGRAPHY SYSTEM CONFIGURATION
 *
 * This is the single source of truth for all typography in Salonier.
 * Changes here cascade through the entire application via semantic styles.
 */
export const TYPOGRAPHY_SYSTEM = {
  // =============================================================================
  // FONT FAMILIES - Platform Optimized
  // =============================================================================
  /**
   * Font families optimized for each platform's design guidelines.
   *
   * - iOS: SF Pro family (Apple's system fonts)
   * - Android: Roboto family (Material Design standard)
   *
   * Usage:
   * - primary: For body text and most UI elements (optimized for reading)
   * - display: For headings 20px+ (optimized for impact and hierarchy)
   */
  fonts: {
    primary: {
      ios: 'SF Pro Text',
      android: 'Roboto',
      weight: 'Uses built-in weights (300-800)',
    },
    display: {
      ios: 'SF Pro Display',
      android: 'Roboto',
      weight: 'For headings 20px+ with better visual hierarchy',
    },
  },

  // =============================================================================
  // FONT SIZES - Professional Scale
  // =============================================================================
  /**
   * Refined 12-step scale optimized for professional tools and mobile legibility.
   *
   * RATIONALE:
   * - Increased caption from 11px→12px for better accessibility
   * - Added intermediate steps (13px, 15px) for precise hierarchy
   * - Reduced large titles for better content density
   * - Each size has a specific purpose in the interface
   */
  sizes: {
    // Data & Metadata Sizes (10-12px)
    micro: 10, // Critical badges only (battery indicators, notifications)
    caption: 12, // Metadatos, timestamps (↑ from 11px for accessibility)
    small: 13, // Secondary labels, form hints (new intermediate step)

    // Primary Text Sizes (14-16px)
    body: 14, // Primary text content (optimal for mobile reading)
    medium: 15, // Emphasized text, important labels (new intermediate)
    subhead: 16, // Section subtitles, card headers

    // Title Hierarchy (18-24px)
    title3: 18, // Component titles, dialog headers
    title2: 20, // Screen titles (↓ from 22px for better density)
    title1: 24, // Main page titles (↓ from 28px for focus)

    // Display Sizes (30-36px)
    display: 30, // Hero sections, splash screens (↑ from 28px for impact)
    hero: 36, // Onboarding, marketing moments (maintained)
  },

  // =============================================================================
  // FONT WEIGHTS - Professional Hierarchy
  // =============================================================================
  /**
   * Four-weight system covering all professional interface needs.
   *
   * PHILOSOPHY:
   * - regular: The default, comfortable reading weight
   * - medium: Subtle emphasis without overwhelming
   * - semibold: Clear hierarchy for titles and CTAs
   * - bold: Maximum impact for critical elements
   */
  weights: {
    regular: '400', // Normal text, comfortable reading
    medium: '500', // Subtle emphasis, form labels
    semibold: '600', // Titles, primary buttons, strong hierarchy
    bold: '700', // Headlines, critical alerts, brand elements
  },

  // =============================================================================
  // LINE HEIGHTS - Optimized for Mobile Reading
  // =============================================================================
  /**
   * Four line heights optimized for different content types and reading patterns.
   *
   * OPTIMIZATION:
   * - tight: Prevents titles from feeling disconnected
   * - snug: Balanced for medium titles and callouts
   * - normal: Perfect for body text reading comfort (1.4 = optimal)
   * - relaxed: Enhanced readability for long content
   */
  lineHeights: {
    tight: 1.2, // Large titles, display text (prevents spacing issues)
    snug: 1.3, // Medium titles, section headers
    normal: 1.4, // Body text (scientifically optimal for reading)
    relaxed: 1.5, // Long-form content, instructions
  },

  // =============================================================================
  // SEMANTIC STYLES - Application-Specific Usage
  // =============================================================================
  /**
   * Semantic styles eliminate guesswork and ensure consistency across the app.
   *
   * NAMING CONVENTION:
   * - Describes the purpose, not the appearance
   * - Maps to specific UI elements and user flows
   * - Easily updatable without hunting through components
   *
   * CATEGORIES:
   * - Navigation: Tab bars, headers, navigation elements
   * - Content: Body text, emphasis, reading materials
   * - Titles: Screen headers, section dividers, card titles
   * - Forms: Inputs, labels, validation messages
   * - Actions: Buttons, CTAs, interactive elements
   * - Chat/AI: Conversational interface elements
   * - Professional: Hair colorist-specific data presentation
   */
  semantic: {
    // NAVIGATION ELEMENTS
    tabLabel: { size: 'small', weight: 'medium' }, // 13px/500 - Bottom tab labels
    tabTitle: { size: 'subhead', weight: 'semibold' }, // 16px/600 - Active tab emphasis

    // CONTENT HIERARCHY
    body: { size: 'body', weight: 'regular', line: 'normal' }, // 14px/400/1.4 - Primary reading text
    bodyEmphasis: { size: 'body', weight: 'medium' }, // 14px/500 - Important text within body

    // TITLE HIERARCHY
    screenTitle: { size: 'title1', weight: 'bold', line: 'tight' }, // 24px/700/1.2 - Main screen headers
    sectionTitle: { size: 'title3', weight: 'semibold' }, // 18px/600 - Section dividers
    cardTitle: { size: 'subhead', weight: 'medium' }, // 16px/500 - Card headers

    // FORM ELEMENTS
    inputLabel: { size: 'small', weight: 'medium' }, // 13px/500 - Form field labels
    inputPlaceholder: { size: 'body', weight: 'regular' }, // 14px/400 - Input placeholder text
    inputError: { size: 'caption', weight: 'medium' }, // 12px/500 - Validation errors

    // ACTION ELEMENTS
    buttonPrimary: { size: 'body', weight: 'semibold' }, // 14px/600 - Primary CTAs
    buttonSecondary: { size: 'body', weight: 'medium' }, // 14px/500 - Secondary actions

    // CHAT & AI INTERFACE
    chatMessage: { size: 'body', weight: 'regular', line: 'normal' }, // 14px/400/1.4 - Chat conversations
    aiConfidence: { size: 'caption', weight: 'medium' }, // 12px/500 - AI confidence indicators

    // PROFESSIONAL HAIR COLORIST DATA
    formulaData: { size: 'small', weight: 'medium' }, // 13px/500 - Chemical formulations, mixing ratios
    diagnosticLabel: { size: 'caption', weight: 'medium' }, // 12px/500 - Hair analysis labels
    diagnosticValue: { size: 'body', weight: 'semibold' }, // 14px/600 - Hair analysis results
  },
} as const;

// =============================================================================
// TYPE DEFINITIONS - Strict TypeScript Support
// =============================================================================

/**
 * TypeScript definitions ensure type safety and IDE autocomplete
 * for all typography-related code throughout the application.
 */

// Core system types
export type FontSize = keyof typeof TYPOGRAPHY_SYSTEM.sizes;
export type FontWeight = keyof typeof TYPOGRAPHY_SYSTEM.weights;
export type LineHeight = keyof typeof TYPOGRAPHY_SYSTEM.lineHeights;
export type FontFamily = keyof typeof TYPOGRAPHY_SYSTEM.fonts;

// Semantic style types
export type SemanticStyle = keyof typeof TYPOGRAPHY_SYSTEM.semantic;

// Style configuration interface
export interface TypographyConfig {
  size: FontSize;
  weight: FontWeight;
  line?: LineHeight;
}

// Computed style interface (returned by helper functions)
export interface ComputedTypographyStyle
  extends Pick<TextStyle, 'fontSize' | 'fontWeight' | 'fontFamily' | 'lineHeight'> {
  fontSize: number;
  fontWeight: TextStyle['fontWeight'];
  fontFamily?: string;
  lineHeight?: number;
}

// =============================================================================
// HELPER FUNCTIONS - Easy Application of Styles
// =============================================================================

/**
 * Primary helper function for applying semantic typography styles.
 *
 * This is the main function components should use to get consistent typography.
 * It automatically resolves sizes, weights, and line heights from semantic definitions.
 *
 * @param semantic - Semantic style name (e.g., 'screenTitle', 'body', 'buttonPrimary')
 * @returns Complete style object ready for React Native Text components
 *
 * @example
 * // In a component:
 * const titleStyle = getTypographyStyle('screenTitle');
 * // Returns: { fontSize: 24, fontWeight: '700', fontFamily: 'SF Pro Display', lineHeight: 28.8 }
 */
export const getTypographyStyle = (semantic: SemanticStyle): ComputedTypographyStyle => {
  const style = TYPOGRAPHY_SYSTEM.semantic[semantic];

  return {
    fontSize: TYPOGRAPHY_SYSTEM.sizes[style.size],
    fontWeight: TYPOGRAPHY_SYSTEM.weights[style.weight],
    fontFamily: getFontFamily(
      // Use display font for larger sizes (title2 and above)
      TYPOGRAPHY_SYSTEM.sizes[style.size] >= 20 ? 'display' : 'primary'
    ),
    lineHeight: style.line
      ? TYPOGRAPHY_SYSTEM.lineHeights[style.line] * TYPOGRAPHY_SYSTEM.sizes[style.size]
      : undefined,
  };
};

/**
 * Platform-specific font family resolver.
 *
 * Automatically selects the appropriate font family based on the current platform,
 * ensuring native look and feel on both iOS and Android.
 *
 * @param type - Font type ('primary' for body text, 'display' for headings)
 * @returns Platform-specific font family name
 *
 * @example
 * const fontFamily = getFontFamily('primary');
 * // iOS: 'SF Pro Text'
 * // Android: 'Roboto'
 */
export const getFontFamily = (type: FontFamily = 'primary'): string => {
  return Platform.select({
    ios: TYPOGRAPHY_SYSTEM.fonts[type].ios,
    android: TYPOGRAPHY_SYSTEM.fonts[type].android,
    default: TYPOGRAPHY_SYSTEM.fonts[type].ios, // Fallback for other platforms
  })!;
};

/**
 * Advanced helper for creating custom typography styles.
 *
 * For cases where semantic styles don't cover specific needs,
 * this function allows manual construction while maintaining system consistency.
 *
 * @param config - Typography configuration object
 * @returns Complete style object with computed values
 *
 * @example
 * // Custom style for special cases:
 * const customStyle = createTypographyStyle({
 *   size: 'medium',
 *   weight: 'bold',
 *   line: 'tight'
 * });
 */
export const createTypographyStyle = (config: TypographyConfig): ComputedTypographyStyle => {
  return {
    fontSize: TYPOGRAPHY_SYSTEM.sizes[config.size],
    fontWeight: TYPOGRAPHY_SYSTEM.weights[config.weight],
    fontFamily: getFontFamily(TYPOGRAPHY_SYSTEM.sizes[config.size] >= 20 ? 'display' : 'primary'),
    lineHeight: config.line
      ? TYPOGRAPHY_SYSTEM.lineHeights[config.line] * TYPOGRAPHY_SYSTEM.sizes[config.size]
      : undefined,
  };
};

// =============================================================================
// LEGACY COMPATIBILITY LAYER
// =============================================================================

/**
 * Compatibility mappings for gradual migration from existing systems.
 *
 * This allows existing components to continue working while we gradually
 * migrate to the new semantic system. Remove these once migration is complete.
 *
 * @deprecated Use semantic styles instead (getTypographyStyle)
 */
export const LEGACY_COMPATIBILITY = {
  // theme.ts mappings
  h1: getTypographyStyle('screenTitle'),
  h3: getTypographyStyle('sectionTitle'),
  h4: getTypographyStyle('cardTitle'),
  title: getTypographyStyle('sectionTitle'),
  body: getTypographyStyle('body'),
  caption: getTypographyStyle('diagnosticLabel'),
  button: getTypographyStyle('buttonPrimary'),

  // beauty-minimal-theme.ts mappings (if they exist)
  chatMessage: getTypographyStyle('chatMessage'),
  professionalData: getTypographyStyle('formulaData'),
} as const;

// =============================================================================
// USAGE EXAMPLES & DOCUMENTATION
// =============================================================================

/**
 * IMPLEMENTATION EXAMPLES:
 *
 * 1. Basic semantic usage (RECOMMENDED):
 * ```tsx
 * import { getTypographyStyle } from '@/constants/typography-system';
 *
 * const styles = StyleSheet.create({
 *   title: {
 *     ...getTypographyStyle('screenTitle'),
 *     color: colors.text.primary,
 *   },
 *   body: {
 *     ...getTypographyStyle('body'),
 *     color: colors.text.secondary,
 *   },
 * });
 * ```
 *
 * 2. Custom configuration for special cases:
 * ```tsx
 * import { createTypographyStyle } from '@/constants/typography-system';
 *
 * const specialStyle = createTypographyStyle({
 *   size: 'medium',
 *   weight: 'bold',
 *   line: 'tight'
 * });
 * ```
 *
 * 3. Direct access for dynamic styles:
 * ```tsx
 * import { TYPOGRAPHY_SYSTEM } from '@/constants/typography-system';
 *
 * const dynamicSize = isLarge ? TYPOGRAPHY_SYSTEM.sizes.title1 : TYPOGRAPHY_SYSTEM.sizes.body;
 * ```
 */

// =============================================================================
// MIGRATION CHECKLIST
// =============================================================================

/**
 * MIGRATION FROM EXISTING SYSTEMS:
 *
 * 1. Replace theme.ts imports:
 *    - OLD: import { typography } from '@/constants/theme';
 *    - NEW: import { getTypographyStyle } from '@/constants/typography-system';
 *
 * 2. Replace hardcoded fontSize/fontWeight with semantic styles:
 *    - OLD: fontSize: 18, fontWeight: '600'
 *    - NEW: ...getTypographyStyle('sectionTitle')
 *
 * 3. Update component styles to use semantic naming:
 *    - Screen titles: 'screenTitle'
 *    - Body text: 'body'
 *    - Buttons: 'buttonPrimary' or 'buttonSecondary'
 *    - Forms: 'inputLabel', 'inputError'
 *
 * 4. Remove LEGACY_COMPATIBILITY once migration complete
 *
 * 5. Update design system documentation
 */

/**
 * SYSTEM HEALTH METRICS:
 *
 * Track these metrics during and after migration:
 *
 * ✅ Consistency: All similar elements use same semantic styles
 * ✅ Performance: No inline styles, proper memoization
 * ✅ Accessibility: All text meets WCAG contrast requirements
 * ✅ Maintainability: Changes in one place affect entire system
 * ✅ Developer Experience: IDE autocomplete, type safety
 */
