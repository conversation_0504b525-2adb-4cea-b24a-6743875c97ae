#!/bin/bash
# Pre-Deployment Validation Script: Inventory Modular Architecture
# Created: $(date)
# Usage: ./pre-deployment-check.sh

set -e

echo "🔍 PRE-DEPLOYMENT VALIDATION: Inventory Modular Architecture"
echo "Started at: $(date)"

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Counters
PASSED=0
FAILED=0

# Function to run check
run_check() {
    local description="$1"
    local command="$2"
    
    echo -n "🔍 $description... "
    
    if eval "$command" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ PASSED${NC}"
        ((PASSED++))
        return 0
    else
        echo -e "${RED}❌ FAILED${NC}"
        ((FAILED++))
        return 1
    fi
}

echo -e "\n📋 CRITICAL CHECKS"
echo "==================="

# 1. Backup exists
run_check "Legacy backup exists" "[ -f 'stores/inventory-store.legacy.ts' ]"

# 2. Facade implementation exists
run_check "Facade implementation exists" "[ -f 'stores/inventory-store-facade.ts' ]"

# 3. Modular stores exist
run_check "ProductStore exists" "[ -f 'stores/product-store.new.ts' ]"
run_check "StockStore exists" "[ -f 'stores/stock-store.new.ts' ]"
run_check "BrandCategoryStore exists" "[ -f 'stores/brand-category-store.new.ts' ]"

# 4. TypeScript compilation
run_check "TypeScript compilation" "npm run type-check"

# 5. Dependencies installed
run_check "Node modules installed" "[ -d 'node_modules' ]"

# 6. Core dependencies present
run_check "Zustand dependency" "npm ls zustand"
run_check "AsyncStorage dependency" "npm ls @react-native-async-storage/async-storage"

echo -e "\n🧪 TEST VALIDATION"
echo "==================="

# 7. Basic tests pass
run_check "Basic inventory tests" "npm test -- --testPathPattern='inventory' --passWithNoTests --silent"

# 8. No critical ESLint errors
if [ -f ".eslintrc.js" ] || [ -f ".eslintrc.json" ]; then
    run_check "ESLint validation" "npm run lint -- --quiet --ext .ts,.tsx stores/"
fi

echo -e "\n🔧 ENVIRONMENT CHECKS"  
echo "======================"

# 9. Expo CLI available
run_check "Expo CLI available" "which expo"

# 10. Node version
NODE_VERSION=$(node -v | cut -d'v' -f2)
run_check "Node version >= 18" "[[ $(echo "$NODE_VERSION 18" | tr ' ' '\n' | sort -V | tail -n1) == "$NODE_VERSION" ]]"

# 11. Available disk space (at least 1GB)
if command -v df > /dev/null; then
    AVAILABLE_KB=$(df . | tail -1 | awk '{print $4}')
    run_check "Sufficient disk space (>1GB)" "[ $AVAILABLE_KB -gt 1048576 ]"
fi

echo -e "\n📁 FILE STRUCTURE VALIDATION"
echo "============================="

# 12. Required directories exist
run_check "Stores directory exists" "[ -d 'stores' ]"
run_check "Scripts directory exists" "[ -d 'scripts' ]"
run_check "Types directory exists" "[ -d 'types' ] || [ -d '@/types' ]"

# 13. Package.json has required scripts
run_check "Package.json has mobile script" "grep -q '\"mobile\"' package.json"
run_check "Package.json has test script" "grep -q '\"test\"' package.json"

echo -e "\n🎯 SPECIFIC FACADE CHECKS"
echo "=========================="

# 14. Check if facade has required exports
if [ -f "stores/inventory-store-facade.ts" ]; then
    run_check "Facade exports useInventoryStore" "grep -q 'export.*useInventoryStore' stores/inventory-store-facade.ts"
    run_check "Facade has migration helpers" "grep -q '__migration' stores/inventory-store-facade.ts"
    run_check "Facade imports modular stores" "grep -q 'useProductStore\|useBrandCategoryStore\|useStockStore' stores/inventory-store-facade.ts"
fi

# 15. Rollback script exists and is executable
run_check "Rollback script exists" "[ -f 'scripts/rollback-inventory-v2.2.0.sh' ]"
run_check "Rollback script is executable" "[ -x 'scripts/rollback-inventory-v2.2.0.sh' ]"

echo -e "\n📊 PERFORMANCE BASELINE"
echo "========================"

# 16. Current store performance baseline
if command -v time > /dev/null; then
    echo "⏱️  Measuring current store performance..."
    
    # Simple load test
    LOAD_TIME=$(node -e "
        const start = Date.now();
        require('./stores/inventory-store.ts');
        console.log(Date.now() - start);
    " 2>/dev/null || echo "0")
    
    if [ "$LOAD_TIME" -gt 0 ]; then
        echo "📈 Current store load time: ${LOAD_TIME}ms"
        if [ "$LOAD_TIME" -lt 500 ]; then
            echo -e "${GREEN}✅ Load time within acceptable range${NC}"
            ((PASSED++))
        else
            echo -e "${YELLOW}⚠️  Load time higher than expected${NC}"
        fi
    fi
fi

echo -e "\n🔒 SAFETY CHECKS"
echo "================="

# 17. No uncommitted changes that could be lost
if git rev-parse --git-dir > /dev/null 2>&1; then
    run_check "No uncommitted critical changes" "! git diff --name-only | grep -E '^stores/inventory-store\.ts$'"
fi

# 18. Backup integrity
if [ -f "stores/inventory-store.legacy.ts" ]; then
    BACKUP_SIZE=$(wc -l < "stores/inventory-store.legacy.ts")
    ORIGINAL_SIZE=$(wc -l < "stores/inventory-store.ts")
    
    if [ "$BACKUP_SIZE" -gt 1500 ]; then  # Should be ~1601 lines
        echo -e "📏 Backup size validation: ${GREEN}✅ PASSED${NC} (${BACKUP_SIZE} lines)"
        ((PASSED++))
    else
        echo -e "📏 Backup size validation: ${RED}❌ FAILED${NC} (${BACKUP_SIZE} lines, expected >1500)"
        ((FAILED++))
    fi
fi

echo -e "\n📋 DEPLOYMENT READINESS SUMMARY"
echo "================================="
echo -e "✅ Passed: ${GREEN}$PASSED${NC}"
echo -e "❌ Failed: ${RED}$FAILED${NC}"
echo -e "📊 Success Rate: $(( PASSED * 100 / (PASSED + FAILED) ))%"

if [ $FAILED -eq 0 ]; then
    echo -e "\n${GREEN}🚀 DEPLOYMENT READY${NC}"
    echo -e "All pre-deployment checks passed successfully."
    echo -e "You can proceed with the modular architecture deployment."
    
    echo -e "\n📋 Next Steps:"
    echo "1. Review the deployment strategy document"
    echo "2. Execute: cp stores/inventory-store-facade.ts stores/inventory-store.ts"
    echo "3. Clear caches and restart: npm run mobile"
    echo "4. Monitor for 15 minutes post-deployment"
    
    exit 0
else
    echo -e "\n${RED}🛑 DEPLOYMENT BLOCKED${NC}"
    echo -e "Critical issues must be resolved before deployment."
    
    echo -e "\n📋 Required Actions:"
    if [ ! -f "stores/inventory-store.legacy.ts" ]; then
        echo "• Create backup: cp stores/inventory-store.ts stores/inventory-store.legacy.ts"
    fi
    
    if [ ! -f "stores/inventory-store-facade.ts" ]; then
        echo "• Ensure facade implementation is complete"
    fi
    
    if [ $FAILED -gt 3 ]; then
        echo "• Fix multiple critical issues before attempting deployment"
        echo "• Consider postponing deployment until all issues resolved"
    fi
    
    exit 1
fi