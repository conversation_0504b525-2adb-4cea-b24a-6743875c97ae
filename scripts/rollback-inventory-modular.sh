#!/bin/bash
# Inventory Store Rollback Script
# 
# This script provides emergency rollback capability from modular facade 
# architecture back to the original monolithic inventory store.
#
# Usage: ./scripts/rollback-inventory-modular.sh
# 
# CRITICAL: Only use this if the modular deployment fails!

set -e  # Exit on any error

echo "🔄 EMERGENCY ROLLBACK: Inventory Store Architecture"
echo "════════════════════════════════════════════════════"

# Timestamp for rollback documentation
TIMESTAMP=$(date '+%Y-%m-%d_%H-%M-%S')
ROLLBACK_LOG="/tmp/inventory-rollback-${TIMESTAMP}.log"

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$ROLLBACK_LOG"
}

log "Starting inventory store rollback process..."

# 1. Verify we're in the correct directory
if [ ! -f "stores/inventory-store.ts" ]; then
    log "ERROR: Not in correct project directory"
    exit 1
fi

# 2. Check if legacy backup exists
if [ ! -f "stores/inventory-store.legacy.ts" ]; then
    log "ERROR: No legacy backup found at stores/inventory-store.legacy.ts"
    exit 1
fi

# 3. Create rollback backup of current facade
log "Creating rollback backup of current facade..."
cp "stores/inventory-store.ts" "stores/inventory-store-facade.backup.${TIMESTAMP}.ts"

# 4. Restore monolithic store from legacy backup
log "Restoring monolithic inventory store from legacy backup..."
cp "stores/inventory-store.legacy.ts" "stores/inventory-store.ts"

# 5. Remove facade import references (keep for debugging)
log "Backing up facade files for debugging..."
mv "stores/inventory-store-facade.ts" "stores/inventory-store-facade.disabled.${TIMESTAMP}.ts"

# 6. Clear any modular store cache that might interfere
log "Clearing potential cache conflicts..."
if command -v npx >/dev/null 2>&1; then
    npx react-native start --reset-cache >/dev/null 2>&1 || true
fi

# 7. Test import resolution
log "Testing import resolution..."
if command -v node >/dev/null 2>&1; then
    node -e "
    try {
      const store = require('./stores/inventory-store.ts');
      console.log('✅ Import test successful');
    } catch (err) {
      console.error('❌ Import test failed:', err.message);
      process.exit(1);
    }
    " 2>/dev/null || log "Warning: Node.js import test failed (normal if TypeScript)"
fi

log "✅ ROLLBACK COMPLETED SUCCESSFULLY"
log "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
log "Rollback Summary:"
log "  • Restored: stores/inventory-store.ts (monolithic)"
log "  • Backup created: stores/inventory-store-facade.backup.${TIMESTAMP}.ts"
log "  • Facade disabled: stores/inventory-store-facade.disabled.${TIMESTAMP}.ts"
log "  • Full log: ${ROLLBACK_LOG}"
log ""
log "Next steps:"
log "  1. Restart your development server: npm run mobile"
log "  2. Test inventory functionality thoroughly"
log "  3. Report any issues for investigation"
log "  4. Consider re-attempting deployment after fixes"

echo ""
echo "Rollback completed! Check log at: ${ROLLBACK_LOG}"