#!/usr/bin/env node

/**
 * Comprehensive Test Runner for Modular Inventory Stores
 * 
 * Orchestrates the complete test suite with performance monitoring,
 * coverage validation, and production readiness checks.
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const chalk = require('chalk');

// Configuration
const CONFIG = {
  coverageThreshold: {
    statements: 90,
    branches: 85,
    functions: 90,
    lines: 90,
  },
  performanceThresholds: {
    maxTestDuration: 30000, // 30 seconds
    maxMemoryUsage: 500, // MB
    maxSlowTests: 5,
  },
  testSuites: {
    unit: 'stores/__tests__/**/*.test.ts',
    integration: 'stores/__tests__/**/integration*.test.ts',
    performance: 'stores/__tests__/**/performance*.test.ts',
    facade: 'stores/__tests__/**/facade*.test.ts',
  },
};

class InventoryStoreTestRunner {
  constructor() {
    this.results = {
      suites: {},
      coverage: null,
      performance: {
        slowTests: [],
        memoryPeaks: [],
      },
      summary: {
        totalTests: 0,
        passedTests: 0,
        failedTests: 0,
        skippedTests: 0,
        duration: 0,
      },
    };

    this.startTime = Date.now();
  }

  /**
   * Main test execution method
   */
  async run(options = {}) {
    console.log(chalk.blue.bold('🧪 Running Comprehensive Inventory Store Tests\n'));

    try {
      // Pre-test validation
      await this.validateEnvironment();

      // Run test suites based on options
      if (options.suite) {
        await this.runTestSuite(options.suite);
      } else {
        await this.runAllTestSuites();
      }

      // Post-test analysis
      await this.analyzeCoverage();
      await this.analyzePerformance();

      // Generate reports
      await this.generateReports();

      // Summary
      this.printSummary();

      // Exit with appropriate code
      const success = this.results.summary.failedTests === 0 && 
                     this.validateCoverageThresholds() && 
                     this.validatePerformanceThresholds();
      
      process.exit(success ? 0 : 1);

    } catch (error) {
      console.error(chalk.red.bold('❌ Test execution failed:'), error.message);
      console.error(error.stack);
      process.exit(1);
    }
  }

  /**
   * Validate test environment
   */
  async validateEnvironment() {
    console.log(chalk.yellow('🔍 Validating test environment...'));

    // Check Node version
    const nodeVersion = process.version;
    console.log(`   Node.js: ${nodeVersion}`);

    // Check if jest is available
    try {
      execSync('npx jest --version', { stdio: 'pipe' });
      console.log('   Jest: ✅ Available');
    } catch (error) {
      throw new Error('Jest is not available. Please install dependencies.');
    }

    // Check test files exist
    const testFiles = [
      'stores/__tests__/product-store.test.ts',
      'stores/__tests__/brand-category-store.test.ts',
      'stores/__tests__/stock-store.test.ts',
      'stores/__tests__/inventory-analytics-store.test.ts',
      'stores/__tests__/inventory-facade-integration.test.ts',
    ];

    testFiles.forEach(file => {
      if (fs.existsSync(file)) {
        console.log(`   ${file}: ✅`);
      } else {
        throw new Error(`Required test file not found: ${file}`);
      }
    });

    // Check TypeScript configuration
    if (fs.existsSync('tsconfig.json')) {
      console.log('   TypeScript config: ✅');
    } else {
      console.warn('   TypeScript config: ⚠️ Not found');
    }

    console.log(chalk.green('✅ Environment validation passed\n'));
  }

  /**
   * Run all test suites
   */
  async runAllTestSuites() {
    const suites = ['unit', 'integration', 'facade', 'performance'];
    
    for (const suite of suites) {
      await this.runTestSuite(suite);
    }
  }

  /**
   * Run specific test suite
   */
  async runTestSuite(suiteName) {
    console.log(chalk.blue(`\n🧪 Running ${suiteName} tests...`));

    const suiteConfig = this.getSuiteConfig(suiteName);
    const startTime = Date.now();

    try {
      const result = await this.executeJest(suiteConfig);
      const duration = Date.now() - startTime;

      this.results.suites[suiteName] = {
        ...result,
        duration,
        success: result.success,
      };

      // Update summary
      this.results.summary.totalTests += result.numTotalTests || 0;
      this.results.summary.passedTests += result.numPassedTests || 0;
      this.results.summary.failedTests += result.numFailedTests || 0;
      this.results.summary.skippedTests += result.numSkippedTests || 0;

      if (result.success) {
        console.log(chalk.green(`✅ ${suiteName} tests passed (${duration}ms)`));
      } else {
        console.log(chalk.red(`❌ ${suiteName} tests failed (${duration}ms)`));
      }

    } catch (error) {
      console.error(chalk.red(`❌ ${suiteName} test suite failed:`, error.message));
      this.results.suites[suiteName] = {
        success: false,
        error: error.message,
        duration: Date.now() - startTime,
      };
    }
  }

  /**
   * Get Jest configuration for specific suite
   */
  getSuiteConfig(suiteName) {
    const baseConfig = {
      config: 'stores/__tests__/jest.config.stores.js',
      coverage: true,
      verbose: true,
      detectOpenHandles: true,
      forceExit: true,
    };

    switch (suiteName) {
      case 'unit':
        return {
          ...baseConfig,
          testPathPattern: 'stores/__tests__/(?!.*integration|.*performance|.*facade).*\\.test\\.(ts|js)$',
        };
      
      case 'integration':
        return {
          ...baseConfig,
          testPathPattern: 'stores/__tests__/.*integration.*\\.test\\.(ts|js)$',
          maxWorkers: 2, // Limit workers for integration tests
        };
      
      case 'facade':
        return {
          ...baseConfig,
          testPathPattern: 'stores/__tests__/.*facade.*\\.test\\.(ts|js)$',
        };
      
      case 'performance':
        return {
          ...baseConfig,
          testPathPattern: 'stores/__tests__/.*performance.*\\.test\\.(ts|js)$',
          maxWorkers: 1, // Single worker for consistent performance measurement
          testTimeout: 60000, // Extended timeout for performance tests
        };
      
      default:
        return baseConfig;
    }
  }

  /**
   * Execute Jest with specific configuration
   */
  async executeJest(config) {
    return new Promise((resolve, reject) => {
      const args = [
        'jest',
        '--passWithNoTests',
        '--testPathPattern=' + (config.testPathPattern || ''),
        '--config=' + config.config,
      ];

      if (config.coverage) {
        args.push('--coverage');
        args.push('--coverageReporters=text,json-summary,lcov');
      }

      if (config.verbose) {
        args.push('--verbose');
      }

      if (config.maxWorkers) {
        args.push(`--maxWorkers=${config.maxWorkers}`);
      }

      if (config.testTimeout) {
        args.push(`--testTimeout=${config.testTimeout}`);
      }

      if (config.detectOpenHandles) {
        args.push('--detectOpenHandles');
      }

      if (config.forceExit) {
        args.push('--forceExit');
      }

      const child = spawn('npx', args, {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: {
          ...process.env,
          NODE_ENV: 'test',
          CI: 'true',
        },
      });

      let stdout = '';
      let stderr = '';

      child.stdout.on('data', (data) => {
        const output = data.toString();
        stdout += output;
        // Stream output in real-time
        process.stdout.write(output);
      });

      child.stderr.on('data', (data) => {
        const output = data.toString();
        stderr += output;
        // Stream errors in real-time
        process.stderr.write(chalk.yellow(output));
      });

      child.on('close', (code) => {
        const result = this.parseJestOutput(stdout, stderr);
        result.success = code === 0;
        result.exitCode = code;
        
        if (code === 0) {
          resolve(result);
        } else {
          reject(new Error(`Jest exited with code ${code}`));
        }
      });

      child.on('error', (error) => {
        reject(error);
      });
    });
  }

  /**
   * Parse Jest output to extract metrics
   */
  parseJestOutput(stdout, stderr) {
    const result = {
      numTotalTests: 0,
      numPassedTests: 0,
      numFailedTests: 0,
      numSkippedTests: 0,
      testResults: [],
      coverageMap: null,
    };

    // Parse test results
    const testSuitePattern = /Test Suites:.*?(\d+) passed.*?(\d+) total/;
    const testPattern = /Tests:.*?(\d+) passed.*?(\d+) total/;

    const suiteMatch = stdout.match(testSuitePattern);
    if (suiteMatch) {
      result.numPassedTestSuites = parseInt(suiteMatch[1]);
      result.numTotalTestSuites = parseInt(suiteMatch[2]);
    }

    const testMatch = stdout.match(testPattern);
    if (testMatch) {
      result.numPassedTests = parseInt(testMatch[1]);
      result.numTotalTests = parseInt(testMatch[2]);
      result.numFailedTests = result.numTotalTests - result.numPassedTests;
    }

    // Extract slow tests
    const slowTestPattern = /(\d+\.?\d*)\s*s.*?stores\/__tests__.*?\.test\.(ts|js)/g;
    let match;
    while ((match = slowTestPattern.exec(stdout)) !== null) {
      const duration = parseFloat(match[1]);
      if (duration > 1) { // More than 1 second
        this.results.performance.slowTests.push({
          test: match[0],
          duration: duration * 1000, // Convert to ms
        });
      }
    }

    return result;
  }

  /**
   * Analyze coverage results
   */
  async analyzeCoverage() {
    console.log(chalk.blue('\n📊 Analyzing coverage...'));

    try {
      const coveragePath = 'coverage/stores/coverage-summary.json';
      if (fs.existsSync(coveragePath)) {
        const coverage = JSON.parse(fs.readFileSync(coveragePath, 'utf8'));
        this.results.coverage = coverage;

        console.log('Coverage Summary:');
        console.log(`   Statements: ${coverage.total.statements.pct}%`);
        console.log(`   Branches: ${coverage.total.branches.pct}%`);
        console.log(`   Functions: ${coverage.total.functions.pct}%`);
        console.log(`   Lines: ${coverage.total.lines.pct}%`);

        // Identify files with low coverage
        const lowCoverageFiles = Object.entries(coverage)
          .filter(([file, data]) => file !== 'total' && data.statements.pct < 80)
          .map(([file, data]) => ({ file, coverage: data.statements.pct }));

        if (lowCoverageFiles.length > 0) {
          console.log(chalk.yellow('\n⚠️  Files with low coverage:'));
          lowCoverageFiles.forEach(({ file, coverage }) => {
            console.log(`   ${file}: ${coverage}%`);
          });
        }
      }
    } catch (error) {
      console.warn('Could not analyze coverage:', error.message);
    }
  }

  /**
   * Analyze performance metrics
   */
  async analyzePerformance() {
    console.log(chalk.blue('\n⚡ Analyzing performance...'));

    if (this.results.performance.slowTests.length > 0) {
      console.log(chalk.yellow('\n🐌 Slow tests detected:'));
      this.results.performance.slowTests
        .sort((a, b) => b.duration - a.duration)
        .slice(0, 10) // Top 10 slowest
        .forEach(({ test, duration }) => {
          console.log(`   ${test}: ${duration.toFixed(2)}ms`);
        });
    }

    // Memory analysis (if available)
    if (typeof process.memoryUsage === 'function') {
      const memory = process.memoryUsage();
      const memoryMB = Math.round(memory.heapUsed / 1024 / 1024);
      console.log(`\n💾 Peak memory usage: ${memoryMB}MB`);
      
      if (memoryMB > CONFIG.performanceThresholds.maxMemoryUsage) {
        console.warn(chalk.yellow(`⚠️  Memory usage exceeds threshold (${CONFIG.performanceThresholds.maxMemoryUsage}MB)`));
      }
    }

    // Total test duration
    this.results.summary.duration = Date.now() - this.startTime;
    console.log(`⏱️  Total test duration: ${this.results.summary.duration}ms`);
  }

  /**
   * Validate coverage thresholds
   */
  validateCoverageThresholds() {
    if (!this.results.coverage) return true;

    const coverage = this.results.coverage.total;
    const thresholds = CONFIG.coverageThreshold;

    return (
      coverage.statements.pct >= thresholds.statements &&
      coverage.branches.pct >= thresholds.branches &&
      coverage.functions.pct >= thresholds.functions &&
      coverage.lines.pct >= thresholds.lines
    );
  }

  /**
   * Validate performance thresholds
   */
  validatePerformanceThresholds() {
    const thresholds = CONFIG.performanceThresholds;
    
    return (
      this.results.summary.duration <= thresholds.maxTestDuration &&
      this.results.performance.slowTests.length <= thresholds.maxSlowTests
    );
  }

  /**
   * Generate comprehensive reports
   */
  async generateReports() {
    console.log(chalk.blue('\n📋 Generating reports...'));

    const reportDir = 'test-results/stores';
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }

    // Generate JSON report
    const jsonReport = {
      timestamp: new Date().toISOString(),
      environment: {
        node: process.version,
        platform: process.platform,
        arch: process.arch,
      },
      results: this.results,
      config: CONFIG,
    };

    fs.writeFileSync(
      path.join(reportDir, 'inventory-stores-test-report.json'),
      JSON.stringify(jsonReport, null, 2)
    );

    console.log(`   JSON report: ${reportDir}/inventory-stores-test-report.json`);

    // Generate markdown summary
    const markdownReport = this.generateMarkdownReport(jsonReport);
    fs.writeFileSync(
      path.join(reportDir, 'inventory-stores-summary.md'),
      markdownReport
    );

    console.log(`   Summary: ${reportDir}/inventory-stores-summary.md`);
  }

  /**
   * Generate markdown report
   */
  generateMarkdownReport(report) {
    const { results } = report;
    const passed = results.summary.failedTests === 0;

    return `# Inventory Stores Test Report

## Summary
- **Status**: ${passed ? '✅ PASSED' : '❌ FAILED'}
- **Total Tests**: ${results.summary.totalTests}
- **Passed**: ${results.summary.passedTests}
- **Failed**: ${results.summary.failedTests}
- **Duration**: ${results.summary.duration}ms
- **Generated**: ${report.timestamp}

## Test Suites
${Object.entries(results.suites).map(([suite, data]) => `
### ${suite}
- **Status**: ${data.success ? '✅ PASSED' : '❌ FAILED'}
- **Duration**: ${data.duration}ms
`).join('')}

## Coverage
${results.coverage ? `
- **Statements**: ${results.coverage.total.statements.pct}%
- **Branches**: ${results.coverage.total.branches.pct}%
- **Functions**: ${results.coverage.total.functions.pct}%
- **Lines**: ${results.coverage.total.lines.pct}%
` : 'Coverage data not available'}

## Performance
- **Slow Tests**: ${results.performance.slowTests.length}
${results.performance.slowTests.length > 0 ? `
### Slowest Tests
${results.performance.slowTests.slice(0, 5).map(test => `
- **${test.test}**: ${test.duration.toFixed(2)}ms
`).join('')}
` : ''}

## Environment
- **Node.js**: ${report.environment.node}
- **Platform**: ${report.environment.platform}
- **Architecture**: ${report.environment.arch}
`;
  }

  /**
   * Print final summary
   */
  printSummary() {
    const { summary } = this.results;
    const passed = summary.failedTests === 0;

    console.log('\n' + '='.repeat(50));
    console.log(chalk.bold('📊 TEST SUMMARY'));
    console.log('='.repeat(50));

    console.log(`Status: ${passed ? chalk.green.bold('✅ PASSED') : chalk.red.bold('❌ FAILED')}`);
    console.log(`Total Tests: ${summary.totalTests}`);
    console.log(`Passed: ${chalk.green(summary.passedTests)}`);
    console.log(`Failed: ${summary.failedTests > 0 ? chalk.red(summary.failedTests) : summary.failedTests}`);
    console.log(`Duration: ${summary.duration}ms`);

    if (this.results.coverage) {
      console.log('\nCoverage:');
      const coverage = this.results.coverage.total;
      console.log(`  Statements: ${this.getColoredPercentage(coverage.statements.pct, 90)}`);
      console.log(`  Branches: ${this.getColoredPercentage(coverage.branches.pct, 85)}`);
      console.log(`  Functions: ${this.getColoredPercentage(coverage.functions.pct, 90)}`);
      console.log(`  Lines: ${this.getColoredPercentage(coverage.lines.pct, 90)}`);
    }

    if (!this.validateCoverageThresholds()) {
      console.log(chalk.yellow('\n⚠️  Coverage thresholds not met'));
    }

    if (!this.validatePerformanceThresholds()) {
      console.log(chalk.yellow('\n⚠️  Performance thresholds not met'));
    }

    console.log('\n' + '='.repeat(50));
  }

  /**
   * Get colored percentage based on threshold
   */
  getColoredPercentage(percentage, threshold) {
    const color = percentage >= threshold ? chalk.green : chalk.red;
    return color(`${percentage}%`);
  }
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const options = {};

  // Parse command line arguments
  args.forEach(arg => {
    if (arg.startsWith('--suite=')) {
      options.suite = arg.split('=')[1];
    }
  });

  const runner = new InventoryStoreTestRunner();
  runner.run(options);
}

module.exports = InventoryStoreTestRunner;