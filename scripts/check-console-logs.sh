#!/bin/bash

# Console Log Checker Script
# Usage: ./scripts/check-console-logs.sh [--production-only]

echo "🔍 Console Log Checker for Salonier"
echo "===================================="

if [ "$1" = "--production-only" ]; then
    echo "📦 Checking PRODUCTION files only..."
    PRODUCTION_LOGS=$(find ./src ./components ./stores ./utils -name "*.ts" -o -name "*.tsx" 2>/dev/null | xargs grep -c "console\." 2>/dev/null | grep -v ":0" | grep -v "./utils/logger.ts")
    
    if [ -z "$PRODUCTION_LOGS" ]; then
        echo "✅ All production files are clean!"
        exit 0
    else
        echo "⚠️  Found console statements in production files:"
        echo "$PRODUCTION_LOGS"
        exit 1
    fi
else
    echo "📊 Full project console statement count:"
    
    # Production files
    PROD_COUNT=$(find ./src ./components ./stores ./utils -name "*.ts" -o -name "*.tsx" 2>/dev/null | xargs grep -o "console\." 2>/dev/null | wc -l)
    echo "Production files: $PROD_COUNT statements"
    
    # Edge Functions
    EDGE_COUNT=$(find ./supabase/functions -name "*.ts" 2>/dev/null | xargs grep -o "console\." 2>/dev/null | wc -l)
    echo "Edge Functions: $EDGE_COUNT statements"
    
    # Scripts and tests
    SCRIPT_COUNT=$(find ./scripts ./e2e -name "*.js" -o -name "*.ts" 2>/dev/null | xargs grep -o "console\." 2>/dev/null | wc -l)
    echo "Scripts/Tests: $SCRIPT_COUNT statements"
    
    # Total
    TOTAL_COUNT=$(find . -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" | grep -v node_modules | grep -v .git | grep -v coverage | xargs grep -o "console\." 2>/dev/null | wc -l)
    echo "Total project: $TOTAL_COUNT statements"
    
    echo ""
    echo "💡 Use --production-only flag to check only critical files"
fi
