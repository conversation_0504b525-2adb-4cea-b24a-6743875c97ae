#!/bin/bash
# Post-Deployment Monitoring Script: Inventory Modular Architecture
# Created: $(date)
# Usage: ./post-deployment-monitor.sh [duration_minutes]

DURATION=${1:-60}  # Default 60 minutes
INTERVAL=300       # 5-minute intervals
CYCLES=$((DURATION * 60 / INTERVAL))

echo "📊 POST-DEPLOYMENT MONITORING: Inventory Modular Architecture"
echo "Duration: ${DURATION} minutes (${CYCLES} cycles)"
echo "Interval: ${INTERVAL} seconds (5 minutes)"
echo "Started at: $(date)"

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Monitoring data
TOTAL_CHECKS=0
FAILED_CHECKS=0
PERFORMANCE_ISSUES=0
ROLLBACK_TRIGGERED=false

# Create monitoring log
MONITOR_LOG="monitoring-$(date +%Y%m%d_%H%M%S).log"
echo "Starting monitoring session at $(date)" > "$MONITOR_LOG"

# Function to log with timestamp
log_message() {
    echo "[$(date '+%H:%M:%S')] $1" | tee -a "$MONITOR_LOG"
}

# Function to check if app is responding
check_app_health() {
    local status="unknown"
    local response_time=0
    
    if command -v curl > /dev/null; then
        local start_time=$(date +%s%3N)
        if curl -s --max-time 10 http://localhost:8081/status > /dev/null 2>&1; then
            local end_time=$(date +%s%3N)
            response_time=$((end_time - start_time))
            status="healthy"
        else
            status="unhealthy"
        fi
    else
        # Fallback: check if expo process is running
        if pgrep -f "expo start" > /dev/null; then
            status="running"
        else
            status="stopped"
        fi
    fi
    
    echo "$status:$response_time"
}

# Function to check memory usage
check_memory_usage() {
    if command -v ps > /dev/null; then
        # Get memory usage of expo/node processes
        local mem_usage=$(ps aux | grep -E "(expo|node)" | grep -v grep | awk '{sum+=$6} END {print sum}')
        echo "${mem_usage:-0}"
    else
        echo "0"
    fi
}

# Function to run test suite
run_health_tests() {
    local test_result="unknown"
    local test_time=0
    
    local start_time=$(date +%s%3N)
    if npm test -- --testPathPattern="inventory" --passWithNoTests --silent > /dev/null 2>&1; then
        local end_time=$(date +%s%3N)
        test_time=$((end_time - start_time))
        test_result="passed"
    else
        test_result="failed"
    fi
    
    echo "$test_result:$test_time"
}

# Function to trigger emergency rollback
trigger_rollback() {
    log_message "🚨 TRIGGERING EMERGENCY ROLLBACK"
    
    if [ -x "scripts/rollback-inventory-v2.2.0.sh" ]; then
        ./scripts/rollback-inventory-v2.2.0.sh
        ROLLBACK_TRIGGERED=true
        log_message "🔄 Emergency rollback executed"
    else
        log_message "❌ Rollback script not found or not executable!"
    fi
}

# Function to check performance metrics
check_performance() {
    local issues=0
    
    # Check response time (should be < 5 seconds)
    local health_check=$(check_app_health)
    local app_status=$(echo "$health_check" | cut -d: -f1)
    local response_time=$(echo "$health_check" | cut -d: -f2)
    
    if [ "$app_status" != "healthy" ] && [ "$app_status" != "running" ]; then
        log_message "⚠️  App not responding properly: $app_status"
        ((issues++))
    fi
    
    if [ "$response_time" -gt 5000 ]; then
        log_message "⚠️  High response time: ${response_time}ms"
        ((issues++))
    fi
    
    # Check memory usage (should be < 200MB = 200000KB)
    local memory=$(check_memory_usage)
    if [ "$memory" -gt 200000 ]; then
        log_message "⚠️  High memory usage: ${memory}KB"
        ((issues++))
    fi
    
    echo "$issues"
}

# Function to display status
display_status() {
    local cycle=$1
    local health_result=$2
    local test_result=$3
    local memory_kb=$4
    local issues=$5
    
    echo -e "\n${BLUE}📊 Monitoring Cycle $cycle/$CYCLES ($(date '+%H:%M:%S'))${NC}"
    echo "─────────────────────────────────────────"
    
    # App Health
    local app_status=$(echo "$health_result" | cut -d: -f1)
    local response_time=$(echo "$health_result" | cut -d: -f2)
    
    if [ "$app_status" = "healthy" ]; then
        echo -e "🏥 App Health: ${GREEN}$app_status${NC} (${response_time}ms)"
    else
        echo -e "🏥 App Health: ${RED}$app_status${NC}"
    fi
    
    # Test Results
    local test_status=$(echo "$test_result" | cut -d: -f1)
    local test_time=$(echo "$test_result" | cut -d: -f2)
    
    if [ "$test_status" = "passed" ]; then
        echo -e "🧪 Tests: ${GREEN}$test_status${NC} (${test_time}ms)"
    else
        echo -e "🧪 Tests: ${RED}$test_status${NC}"
    fi
    
    # Memory Usage
    local memory_mb=$((memory_kb / 1024))
    if [ "$memory_kb" -lt 150000 ]; then
        echo -e "💾 Memory: ${GREEN}${memory_mb}MB${NC}"
    elif [ "$memory_kb" -lt 200000 ]; then
        echo -e "💾 Memory: ${YELLOW}${memory_mb}MB${NC}"
    else
        echo -e "💾 Memory: ${RED}${memory_mb}MB${NC}"
    fi
    
    # Issues Summary
    if [ "$issues" -eq 0 ]; then
        echo -e "⚡ Status: ${GREEN}All Good${NC}"
    else
        echo -e "⚡ Status: ${YELLOW}${issues} issues detected${NC}"
    fi
}

# Main monitoring loop
echo -e "\n🚀 Starting monitoring cycles..."

for ((i=1; i<=CYCLES; i++)); do
    ((TOTAL_CHECKS++))
    
    # Run all health checks
    health_result=$(check_app_health)
    test_result=$(run_health_tests)
    memory_usage=$(check_memory_usage)
    performance_issues=$(check_performance)
    
    # Display current status
    display_status "$i" "$health_result" "$test_result" "$memory_usage" "$performance_issues"
    
    # Check for critical failures
    app_status=$(echo "$health_result" | cut -d: -f1)
    test_status=$(echo "$test_result" | cut -d: -f1)
    
    if [ "$app_status" = "unhealthy" ] || [ "$app_status" = "stopped" ]; then
        log_message "❌ Critical: App is not responding"
        ((FAILED_CHECKS++))
        
        # Trigger rollback after 2 consecutive failures
        if [ $FAILED_CHECKS -ge 2 ]; then
            log_message "🚨 Multiple failures detected. Triggering rollback."
            trigger_rollback
            break
        fi
    elif [ "$test_status" = "failed" ]; then
        log_message "❌ Tests failing"
        ((FAILED_CHECKS++))
    elif [ "$performance_issues" -gt 2 ]; then
        log_message "⚠️  Multiple performance issues detected"
        ((PERFORMANCE_ISSUES++))
    else
        # Reset failure counter on success
        FAILED_CHECKS=0
    fi
    
    # Sleep until next cycle (unless it's the last one)
    if [ $i -lt $CYCLES ]; then
        echo "⏳ Next check in 5 minutes..."
        sleep $INTERVAL
    fi
done

# Final summary
echo -e "\n${BLUE}📊 MONITORING SUMMARY${NC}"
echo "═══════════════════════════════════════════"
echo "Duration: ${DURATION} minutes"
echo "Total Checks: $TOTAL_CHECKS"
echo "Failed Checks: $FAILED_CHECKS"
echo "Performance Issues: $PERFORMANCE_ISSUES"

if [ "$ROLLBACK_TRIGGERED" = true ]; then
    echo -e "Status: ${RED}ROLLBACK TRIGGERED${NC}"
    echo "Reason: Critical failures detected"
elif [ $FAILED_CHECKS -eq 0 ] && [ $PERFORMANCE_ISSUES -lt 3 ]; then
    echo -e "Status: ${GREEN}DEPLOYMENT SUCCESSFUL${NC}"
    echo "No critical issues detected during monitoring period"
elif [ $PERFORMANCE_ISSUES -ge 3 ]; then
    echo -e "Status: ${YELLOW}PERFORMANCE CONCERNS${NC}"
    echo "Multiple performance issues detected - investigate"
else
    echo -e "Status: ${YELLOW}MINOR ISSUES${NC}"
    echo "Some failures detected but within acceptable range"
fi

echo -e "\nCompleted at: $(date)"
echo "Full log available: $MONITOR_LOG"

# Create monitoring report
cat > "monitoring-report-$(date +%Y%m%d_%H%M%S).md" << EOF
# Post-Deployment Monitoring Report

**Date**: $(date)
**Duration**: ${DURATION} minutes
**Deployment**: Inventory Modular Architecture

## Summary
- **Total Checks**: $TOTAL_CHECKS
- **Failed Checks**: $FAILED_CHECKS
- **Performance Issues**: $PERFORMANCE_ISSUES
- **Rollback Triggered**: $ROLLBACK_TRIGGERED

## Status
EOF

if [ "$ROLLBACK_TRIGGERED" = true ]; then
    echo "**ROLLBACK EXECUTED** - Critical failures detected" >> "monitoring-report-$(date +%Y%m%d_%H%M%S).md"
elif [ $FAILED_CHECKS -eq 0 ] && [ $PERFORMANCE_ISSUES -lt 3 ]; then
    echo "**DEPLOYMENT SUCCESSFUL** - No critical issues" >> "monitoring-report-$(date +%Y%m%d_%H%M%S).md"
else
    echo "**MONITORING COMPLETED** - Some issues detected" >> "monitoring-report-$(date +%Y%m%d_%H%M%S).md"
fi

cat >> "monitoring-report-$(date +%Y%m%d_%H%M%S).md" << EOF

## Actions Required
EOF

if [ "$ROLLBACK_TRIGGERED" = true ]; then
    echo "1. Investigate root cause of failures" >> "monitoring-report-$(date +%Y%m%d_%H%M%S).md"
    echo "2. Fix modular architecture issues" >> "monitoring-report-$(date +%Y%m%d_%H%M%S).md"
    echo "3. Plan re-deployment" >> "monitoring-report-$(date +%Y%m%d_%H%M%S).md"
elif [ $PERFORMANCE_ISSUES -ge 3 ]; then
    echo "1. Investigate performance degradation" >> "monitoring-report-$(date +%Y%m%d_%H%M%S).md"
    echo "2. Consider performance optimizations" >> "monitoring-report-$(date +%Y%m%d_%H%M%S).md"
    echo "3. Monitor for continued issues" >> "monitoring-report-$(date +%Y%m%d_%H%M%S).md"
else
    echo "1. Continue normal monitoring" >> "monitoring-report-$(date +%Y%m%d_%H%M%S).md"
    echo "2. Document lessons learned" >> "monitoring-report-$(date +%Y%m%d_%H%M%S).md"
fi

echo -e "\n📄 Monitoring report created: monitoring-report-$(date +%Y%m%d_%H%M%S).md"