#!/bin/bash
# Emergency Rollback Script: Inventory Modular → Monolithic
# Created: $(date)
# Usage: ./rollback-inventory-v2.2.0.sh

set -e

echo "🔄 EMERGENCY ROLLBACK: Inventory Modular Architecture → Monolithic Store"
echo "Started at: $(date)"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Step 1: Stop running processes
echo -e "\n${YELLOW}Step 1: Stopping running processes...${NC}"
if pgrep -f "expo start" > /dev/null; then
    print_status "Stopping Expo development server..."
    pkill -f "expo start" || true
    sleep 2
else
    print_status "No Expo processes running"
fi

# Step 2: Verify backup exists
echo -e "\n${YELLOW}Step 2: Verifying backup exists...${NC}"
if [ ! -f "stores/inventory-store.legacy.ts" ]; then
    print_error "CRITICAL: Legacy backup not found at stores/inventory-store.legacy.ts"
    print_error "Cannot proceed with rollback. Manual intervention required."
    exit 1
fi
print_status "Legacy backup verified: stores/inventory-store.legacy.ts"

# Step 3: Create snapshot of current (failed) state for debugging
echo -e "\n${YELLOW}Step 3: Creating snapshot of failed state...${NC}"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
cp stores/inventory-store.ts "stores/failed-state-${TIMESTAMP}.ts"
print_status "Failed state saved: stores/failed-state-${TIMESTAMP}.ts"

# Step 4: Restore legacy implementation
echo -e "\n${YELLOW}Step 4: Restoring legacy implementation...${NC}"
cp stores/inventory-store.legacy.ts stores/inventory-store.ts
print_status "Legacy store restored to stores/inventory-store.ts"

# Step 5: Clear all caches
echo -e "\n${YELLOW}Step 5: Clearing caches and temporary files...${NC}"
rm -rf .expo/
rm -rf node_modules/.cache/
npm run clear-cache > /dev/null 2>&1 || true
print_status "Caches cleared"

# Step 6: Reinstall dependencies (if needed)
echo -e "\n${YELLOW}Step 6: Verifying dependencies...${NC}"
if [ ! -d "node_modules" ]; then
    print_warning "node_modules missing, reinstalling..."
    npm install
fi
print_status "Dependencies verified"

# Step 7: Start development server
echo -e "\n${YELLOW}Step 7: Starting development server...${NC}"
nohup npm run mobile > rollback.log 2>&1 &
sleep 5
print_status "Development server started (check rollback.log for details)"

# Step 8: Validate rollback
echo -e "\n${YELLOW}Step 8: Validating rollback...${NC}"

# Wait for server to be ready
echo "Waiting for server to start..."
for i in {1..30}; do
    if curl -s http://localhost:8081/status > /dev/null 2>&1; then
        break
    fi
    if [ $i -eq 30 ]; then
        print_warning "Server not responding after 30 seconds, but rollback completed"
        break
    fi
    sleep 1
    echo -n "."
done
echo ""

# Run basic validation tests
echo "Running validation tests..."
if npm test -- --testPathPattern="inventory-store" --testTimeout=10000 --passWithNoTests > /dev/null 2>&1; then
    print_status "Basic validation tests passed"
else
    print_warning "Some tests failed, but legacy store is active"
fi

# Step 9: Generate rollback report
echo -e "\n${YELLOW}Step 9: Generating rollback report...${NC}"
cat > "rollback-report-${TIMESTAMP}.md" << EOF
# Rollback Report - Inventory Modular Architecture

**Date**: $(date)
**Duration**: Started at rollback initialization
**Reason**: Emergency rollback from modular to monolithic inventory store

## Actions Taken
- [x] Stopped Expo development server
- [x] Verified legacy backup exists
- [x] Created snapshot of failed state: failed-state-${TIMESTAMP}.ts
- [x] Restored legacy implementation
- [x] Cleared all caches
- [x] Restarted development server
- [x] Ran validation tests

## Current State
- **Active Store**: stores/inventory-store.ts (legacy monolithic)
- **Failed State**: stores/failed-state-${TIMESTAMP}.ts (for debugging)
- **Server Status**: Running on http://localhost:8081
- **Log File**: rollback.log

## Next Steps
1. Investigate root cause in failed-state-${TIMESTAMP}.ts
2. Fix modular architecture issues
3. Plan re-deployment with proper fixes
4. Schedule post-mortem meeting

## Files Modified
- stores/inventory-store.ts (restored from backup)

## Files Created
- stores/failed-state-${TIMESTAMP}.ts (debugging snapshot)
- rollback-report-${TIMESTAMP}.md (this report)
- rollback.log (server restart log)
EOF

print_status "Rollback report generated: rollback-report-${TIMESTAMP}.md"

# Step 10: Final status
echo -e "\n${GREEN}✅ ROLLBACK COMPLETED SUCCESSFULLY${NC}"
echo -e "📊 Status Summary:"
echo -e "   • Legacy monolithic store: ${GREEN}ACTIVE${NC}"
echo -e "   • Failed state preserved: ${YELLOW}stores/failed-state-${TIMESTAMP}.ts${NC}"
echo -e "   • Development server: ${GREEN}RUNNING${NC}"
echo -e "   • Rollback time: ${GREEN}~2 minutes${NC}"

echo -e "\n📋 Next Actions Required:"
echo -e "   1. Verify app functionality manually"
echo -e "   2. Review rollback-report-${TIMESTAMP}.md"
echo -e "   3. Investigate failed-state-${TIMESTAMP}.ts"
echo -e "   4. Plan fixes for modular architecture"

echo -e "\n🔍 Debug Information:"
echo -e "   • Server log: rollback.log"
echo -e "   • Server URL: http://localhost:8081"
echo -e "   • Metro bundler should be accessible"

echo -e "\nRollback completed at: $(date)"