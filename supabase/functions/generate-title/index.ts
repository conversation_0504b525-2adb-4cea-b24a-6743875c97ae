// Smart Title Generation for Chat Conversations
// Generates Claude-style intelligent titles based on the conversation context

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Max-Age': '86400',
};

const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const openaiApiKey = Deno.env.get('OPENAI_API_KEY');

const supabase = createClient(supabaseUrl, supabaseServiceKey);

interface TitleRequest {
  conversationId: string;
  userMessage: string;
  assistantResponse: string;
  salonId: string;
}

const TITLE_GENERATION_PROMPT = `Genera un título conciso y descriptivo para esta conversación de coloración capilar basándote en el primer intercambio entre el usuario y el asistente.

REGLAS:
- Máximo 4-5 palabras
- Específico al tema de coloración capilar
- En español
- Sin comillas ni caracteres especiales
- Captura la intención principal de la consulta

EJEMPLOS:
- Usuario pregunta sobre rubio: "Fórmula rubio platino"
- Usuario sube foto para análisis: "Análisis color actual"
- Usuario pregunta sobre canas: "Cobertura de canas"
- Usuario pregunta sobre corrección: "Corrección tonos naranjas"
- Usuario pregunta sobre mechas: "Técnica balayage"
- Usuario pregunta sobre inventario: "Consulta inventario"

PRIMER MENSAJE DEL USUARIO:
{userMessage}

RESPUESTA DEL ASISTENTE:
{assistantResponse}

Genera SOLO el título, sin explicaciones adicionales:`;

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { conversationId, userMessage, assistantResponse, salonId }: TitleRequest = await req.json();

    // Validate request
    if (!conversationId || !userMessage || !salonId) {
      throw new Error('Faltan parámetros requeridos');
    }

    // Validate OpenAI API key
    if (!openaiApiKey) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Servicio de IA no configurado',
        }),
        {
          status: 503,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    // Check if conversation already has a custom title (not default)
    const { data: conversation } = await supabase
      .from('chat_conversations')
      .select('title, message_count')
      .eq('id', conversationId)
      .single();

    // Only generate title for new conversations with default titles
    if (
      conversation && 
      !conversation.title.startsWith('Nueva conversación') &&
      conversation.title !== 'Nueva conversación'
    ) {
      return new Response(
        JSON.stringify({
          success: true,
          title: conversation.title,
          message: 'Title already set',
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    // Generate smart title using GPT-4o-mini for cost efficiency
    const prompt = TITLE_GENERATION_PROMPT
      .replace('{userMessage}', userMessage.substring(0, 300)) // Limit length for cost
      .replace('{assistantResponse}', (assistantResponse || '').substring(0, 300));

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini', // Cost-effective model for title generation
        messages: [
          {
            role: 'system',
            content: 'Eres un experto en generar títulos concisos para conversaciones de coloración capilar.',
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        max_tokens: 20, // Very short response for title only
        temperature: 0.3, // Low temperature for consistent results
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    let generatedTitle = data.choices[0].message.content.trim();

    // Clean up the title
    generatedTitle = generatedTitle.replace(/["']/g, ''); // Remove quotes
    generatedTitle = generatedTitle.replace(/^\w+:\s*/, ''); // Remove "Título: " prefix if exists
    
    // Ensure title is reasonable length
    if (generatedTitle.length > 50) {
      generatedTitle = generatedTitle.substring(0, 47) + '...';
    }

    // Fallback to default if title is too generic or empty
    if (
      !generatedTitle || 
      generatedTitle.length < 10 ||
      generatedTitle.toLowerCase().includes('conversación') ||
      generatedTitle.toLowerCase().includes('consulta general')
    ) {
      // Generate simple fallback based on keywords
      const lowerMessage = userMessage.toLowerCase();
      if (lowerMessage.includes('rubio')) generatedTitle = 'Consulta rubio';
      else if (lowerMessage.includes('cana')) generatedTitle = 'Cobertura canas';
      else if (lowerMessage.includes('mechas')) generatedTitle = 'Técnica mechas';
      else if (lowerMessage.includes('correc')) generatedTitle = 'Corrección color';
      else if (lowerMessage.includes('fórmula')) generatedTitle = 'Fórmula personalizada';
      else if (lowerMessage.includes('inventario') || lowerMessage.includes('stock')) generatedTitle = 'Consulta inventario';
      else {
        // Use first few words of user message as last resort
        const words = userMessage.trim().split(/\s+/).slice(0, 4);
        generatedTitle = words.join(' ').substring(0, 40);
      }
    }

    // Update conversation title in database
    const { error: updateError } = await supabase
      .from('chat_conversations')
      .update({ title: generatedTitle })
      .eq('id', conversationId);

    if (updateError) {
      throw updateError;
    }

    return new Response(
      JSON.stringify({
        success: true,
        title: generatedTitle,
        usage: {
          promptTokens: prompt.length / 4, // Rough estimation
          completionTokens: generatedTitle.length / 4,
          totalTokens: (prompt.length + generatedTitle.length) / 4,
          cost: 0.0001, // Minimal cost with gpt-4o-mini
        },
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );

  } catch (error) {
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : 'Error desconocido',
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
});