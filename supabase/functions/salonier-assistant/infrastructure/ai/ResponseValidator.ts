/**
 * Response Validator - Validates and sanitizes AI responses for safety and consistency
 * Ensures all AI outputs meet quality standards and contain required fields
 */

import { REQUIRED_FIELDS } from '../../constants.ts';
import { ILogger, IMetrics, Result } from '../shared/types.ts';

export interface ValidationRule {
  field: string;
  type: 'string' | 'number' | 'object' | 'array' | 'boolean';
  required: boolean;
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: RegExp;
  allowedValues?: any[];
  customValidator?: (value: any) => boolean;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  sanitizedData?: any;
  confidenceScore: number;
}

export interface ValidationError {
  field: string;
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  code: string;
}

export interface ValidationWarning {
  field: string;
  message: string;
  suggestion?: string;
}

export interface TaskValidationConfig {
  taskType: string;
  rules: ValidationRule[];
  requiredConfidenceScore: number;
  allowPartialResponse: boolean;
  sanitizeOutput: boolean;
}

export interface IResponseValidator {
  validateResponse(taskType: string, response: any): ValidationResult;
  sanitizeResponse(response: any): any;
  checkConfidence(response: any): number;
  validateStructure(response: any, rules: ValidationRule[]): ValidationResult;
}

export class ResponseValidator implements IResponseValidator {
  private validationConfigs: Map<string, TaskValidationConfig> = new Map();
  private readonly safetyPatterns: RegExp[] = [
    /(?:suicide|self-harm|dangerous|toxic|harmful)/gi,
    /(?:medication|drug|prescription)/gi,
    /(?:medical advice|diagnosis)/gi,
  ];

  constructor(
    private readonly logger: ILogger,
    private readonly metrics: IMetrics
  ) {
    this.initializeValidationConfigs();
    this.logger.info('Response Validator initialized', {
      configsLoaded: this.validationConfigs.size,
    });
  }

  /**
   * Validate AI response for a specific task type
   */
  validateResponse(taskType: string, response: any): ValidationResult {
    const startTime = Date.now();

    try {
      const config = this.validationConfigs.get(taskType);
      if (!config) {
        this.logger.warn('No validation config found for task type', { taskType });
        return {
          isValid: false,
          errors: [{
            field: 'taskType',
            message: `No validation configuration for task type: ${taskType}`,
            severity: 'high',
            code: 'UNKNOWN_TASK_TYPE',
          }],
          warnings: [],
          confidenceScore: 0,
        };
      }

      // Basic structure validation
      const structureResult = this.validateStructure(response, config.rules);

      // Safety validation
      const safetyResult = this.validateSafety(response);

      // Confidence score validation
      const confidenceScore = this.checkConfidence(response);
      const confidenceValid = confidenceScore >= config.requiredConfidenceScore;

      // Task-specific validation
      const taskSpecificResult = this.validateTaskSpecific(taskType, response);

      // Combine all validation results
      const allErrors = [
        ...structureResult.errors,
        ...safetyResult.errors,
        ...taskSpecificResult.errors,
      ];

      if (!confidenceValid) {
        allErrors.push({
          field: 'confidence',
          message: `Confidence score ${confidenceScore} below required ${config.requiredConfidenceScore}`,
          severity: 'medium',
          code: 'LOW_CONFIDENCE',
        });
      }

      const allWarnings = [
        ...structureResult.warnings,
        ...safetyResult.warnings,
        ...taskSpecificResult.warnings,
      ];

      const isValid = allErrors.filter(e => e.severity === 'high' || e.severity === 'critical').length === 0;

      // Sanitize if needed and valid
      let sanitizedData = response;
      if (isValid && config.sanitizeOutput) {
        sanitizedData = this.sanitizeResponse(response);
      }

      const validationTime = Date.now() - startTime;

      // Record metrics
      this.metrics.recordCounter('response_validations_total', 1, {
        taskType,
        status: isValid ? 'valid' : 'invalid',
      });
      this.metrics.recordHistogram('validation_duration_ms', validationTime, { taskType });
      this.metrics.recordGauge('validation_confidence_score', confidenceScore, { taskType });

      if (allErrors.length > 0) {
        this.metrics.recordCounter('validation_errors_total', allErrors.length, { taskType });
      }

      this.logger.debug('Response validation completed', {
        taskType,
        isValid,
        errorsCount: allErrors.length,
        warningsCount: allWarnings.length,
        confidenceScore,
        validationTime,
      });

      return {
        isValid,
        errors: allErrors,
        warnings: allWarnings,
        sanitizedData,
        confidenceScore,
      };
    } catch (error) {
      this.logger.error('Validation failed with exception', {
        taskType,
        error: error instanceof Error ? error.message : String(error),
      });

      return {
        isValid: false,
        errors: [{
          field: 'validation',
          message: 'Validation failed with internal error',
          severity: 'critical',
          code: 'VALIDATION_ERROR',
        }],
        warnings: [],
        confidenceScore: 0,
      };
    }
  }

  /**
   * Sanitize response by removing potentially harmful content
   */
  sanitizeResponse(response: any): any {
    if (typeof response !== 'object' || response === null) {
      return response;
    }

    const sanitized = JSON.parse(JSON.stringify(response));

    // Recursively sanitize string fields
    this.sanitizeObject(sanitized);

    return sanitized;
  }

  /**
   * Check confidence score in response
   */
  checkConfidence(response: any): number {
    // Look for confidence indicators in various formats
    if (typeof response === 'object' && response !== null) {
      // Direct confidence field
      if (typeof response.confidence === 'number') {
        return Math.max(0, Math.min(1, response.confidence));
      }
      if (typeof response.confidenceScore === 'number') {
        return Math.max(0, Math.min(1, response.confidenceScore));
      }

      // Confidence in nested objects
      if (response.metadata?.confidence) {
        return Math.max(0, Math.min(1, response.metadata.confidence));
      }

      // Calculate based on completeness
      const requiredFields = this.getRequiredFieldsForResponse(response);
      if (requiredFields.length > 0) {
        const completedFields = requiredFields.filter(field => 
          this.getNestedValue(response, field) !== undefined
        ).length;
        return completedFields / requiredFields.length;
      }
    }

    // Default confidence based on response completeness
    return typeof response === 'object' && response !== null ? 0.7 : 0.3;
  }

  /**
   * Validate response structure against rules
   */
  validateStructure(response: any, rules: ValidationRule[]): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    for (const rule of rules) {
      const value = this.getNestedValue(response, rule.field);
      const hasValue = value !== undefined && value !== null;

      // Required field validation
      if (rule.required && !hasValue) {
        errors.push({
          field: rule.field,
          message: `Required field '${rule.field}' is missing`,
          severity: 'high',
          code: 'REQUIRED_FIELD_MISSING',
        });
        continue;
      }

      if (!hasValue) continue;

      // Type validation
      if (!this.validateType(value, rule.type)) {
        errors.push({
          field: rule.field,
          message: `Field '${rule.field}' should be of type ${rule.type}, got ${typeof value}`,
          severity: 'medium',
          code: 'INVALID_TYPE',
        });
        continue;
      }

      // Length validation for strings
      if (rule.type === 'string' && typeof value === 'string') {
        if (rule.minLength && value.length < rule.minLength) {
          warnings.push({
            field: rule.field,
            message: `Field '${rule.field}' is shorter than expected (${value.length} < ${rule.minLength})`,
            suggestion: `Provide more detailed information for ${rule.field}`,
          });
        }
        if (rule.maxLength && value.length > rule.maxLength) {
          warnings.push({
            field: rule.field,
            message: `Field '${rule.field}' is longer than expected (${value.length} > ${rule.maxLength})`,
            suggestion: `Reduce the length of ${rule.field}`,
          });
        }
      }

      // Numeric range validation
      if (rule.type === 'number' && typeof value === 'number') {
        if (rule.min !== undefined && value < rule.min) {
          errors.push({
            field: rule.field,
            message: `Field '${rule.field}' value ${value} is below minimum ${rule.min}`,
            severity: 'medium',
            code: 'VALUE_TOO_LOW',
          });
        }
        if (rule.max !== undefined && value > rule.max) {
          errors.push({
            field: rule.field,
            message: `Field '${rule.field}' value ${value} is above maximum ${rule.max}`,
            severity: 'medium',
            code: 'VALUE_TOO_HIGH',
          });
        }
      }

      // Pattern validation for strings
      if (rule.pattern && rule.type === 'string' && typeof value === 'string') {
        if (!rule.pattern.test(value)) {
          errors.push({
            field: rule.field,
            message: `Field '${rule.field}' does not match required pattern`,
            severity: 'low',
            code: 'PATTERN_MISMATCH',
          });
        }
      }

      // Allowed values validation
      if (rule.allowedValues && !rule.allowedValues.includes(value)) {
        errors.push({
          field: rule.field,
          message: `Field '${rule.field}' value '${value}' is not in allowed values: ${rule.allowedValues.join(', ')}`,
          severity: 'medium',
          code: 'INVALID_VALUE',
        });
      }

      // Custom validation
      if (rule.customValidator && !rule.customValidator(value)) {
        errors.push({
          field: rule.field,
          message: `Field '${rule.field}' failed custom validation`,
          severity: 'medium',
          code: 'CUSTOM_VALIDATION_FAILED',
        });
      }
    }

    return {
      isValid: errors.filter(e => e.severity === 'high' || e.severity === 'critical').length === 0,
      errors,
      warnings,
      confidenceScore: 0.8, // Structure validation doesn't determine confidence
    };
  }

  /**
   * Validate response for safety concerns
   */
  private validateSafety(response: any): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    const responseText = JSON.stringify(response).toLowerCase();

    // Check for safety patterns
    for (const pattern of this.safetyPatterns) {
      if (pattern.test(responseText)) {
        errors.push({
          field: 'content',
          message: 'Response contains potentially unsafe content',
          severity: 'critical',
          code: 'UNSAFE_CONTENT',
        });
        break;
      }
    }

    // Check for medical advice disclaimers in hair coloring context
    if (responseText.includes('medical') && !responseText.includes('consult') && !responseText.includes('professional')) {
      warnings.push({
        field: 'content',
        message: 'Response mentions medical topics without proper disclaimers',
        suggestion: 'Add recommendation to consult professionals for hair/scalp concerns',
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      confidenceScore: errors.length === 0 ? 1.0 : 0.0,
    };
  }

  /**
   * Task-specific validation logic
   */
  private validateTaskSpecific(taskType: string, response: any): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    switch (taskType) {
      case 'diagnose_image':
        return this.validateDiagnosisResponse(response);
      case 'generate_formula':
        return this.validateFormulaResponse(response);
      case 'analyze_product':
        return this.validateProductAnalysisResponse(response);
      default:
        return { isValid: true, errors, warnings, confidenceScore: 0.8 };
    }
  }

  /**
   * Validate hair diagnosis response
   */
  private validateDiagnosisResponse(response: any): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // Check for required diagnosis fields
    const requiredFields = ['hairThickness', 'hairDensity', 'naturalLevel'];
    
    for (const field of requiredFields) {
      if (!response[field]) {
        errors.push({
          field,
          message: `Required diagnosis field '${field}' is missing`,
          severity: 'high',
          code: 'MISSING_DIAGNOSIS_FIELD',
        });
      }
    }

    // Validate level range
    if (response.naturalLevel && (response.naturalLevel < 1 || response.naturalLevel > 10)) {
      errors.push({
        field: 'naturalLevel',
        message: `Natural level ${response.naturalLevel} is outside valid range 1-10`,
        severity: 'medium',
        code: 'INVALID_LEVEL_RANGE',
      });
    }

    return {
      isValid: errors.filter(e => e.severity === 'high').length === 0,
      errors,
      warnings,
      confidenceScore: 0.9,
    };
  }

  /**
   * Validate formula response
   */
  private validateFormulaResponse(response: any): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // Check for formula components
    if (!response.formula && !response.formulaText) {
      errors.push({
        field: 'formula',
        message: 'Formula content is missing',
        severity: 'critical',
        code: 'MISSING_FORMULA',
      });
    }

    // Check for developer information
    if (!response.developer && !response.formulaText?.includes('developer')) {
      warnings.push({
        field: 'developer',
        message: 'Developer information not clearly specified',
        suggestion: 'Include specific developer volume and percentage',
      });
    }

    return {
      isValid: errors.filter(e => e.severity === 'critical').length === 0,
      errors,
      warnings,
      confidenceScore: 0.85,
    };
  }

  /**
   * Validate product analysis response
   */
  private validateProductAnalysisResponse(response: any): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    const requiredFields = ['brand', 'productType', 'category'];
    
    for (const field of requiredFields) {
      if (!response[field]) {
        errors.push({
          field,
          message: `Required product field '${field}' is missing`,
          severity: 'medium',
          code: 'MISSING_PRODUCT_FIELD',
        });
      }
    }

    return {
      isValid: errors.filter(e => e.severity === 'high').length === 0,
      errors,
      warnings,
      confidenceScore: 0.9,
    };
  }

  /**
   * Initialize validation configurations
   */
  private initializeValidationConfigs(): void {
    // Hair diagnosis validation
    this.validationConfigs.set('diagnose_image', {
      taskType: 'diagnose_image',
      rules: [
        { field: 'hairThickness', type: 'string', required: true, allowedValues: ['fine', 'medium', 'thick'] },
        { field: 'hairDensity', type: 'string', required: true, allowedValues: ['low', 'medium', 'high'] },
        { field: 'naturalLevel', type: 'number', required: true, min: 1, max: 10 },
        { field: 'condition', type: 'string', required: false, minLength: 10 },
      ],
      requiredConfidenceScore: 0.7,
      allowPartialResponse: false,
      sanitizeOutput: true,
    });

    // Formula generation validation
    this.validationConfigs.set('generate_formula', {
      taskType: 'generate_formula',
      rules: [
        { field: 'formulaText', type: 'string', required: true, minLength: 50 },
        { field: 'developer', type: 'object', required: false },
        { field: 'processingTime', type: 'number', required: false, min: 5, max: 120 },
      ],
      requiredConfidenceScore: 0.8,
      allowPartialResponse: true,
      sanitizeOutput: true,
    });

    // Product analysis validation
    this.validationConfigs.set('analyze_product', {
      taskType: 'analyze_product',
      rules: [
        { field: 'brand', type: 'string', required: true, minLength: 2 },
        { field: 'productType', type: 'string', required: true },
        { field: 'category', type: 'string', required: true },
        { field: 'shade', type: 'string', required: false },
      ],
      requiredConfidenceScore: 0.6,
      allowPartialResponse: true,
      sanitizeOutput: true,
    });
  }

  /**
   * Helper method to validate type
   */
  private validateType(value: any, expectedType: string): boolean {
    switch (expectedType) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'array':
        return Array.isArray(value);
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      default:
        return false;
    }
  }

  /**
   * Get nested value from object using dot notation
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  /**
   * Get required fields for response type detection
   */
  private getRequiredFieldsForResponse(response: any): string[] {
    // Try to detect response type based on fields present
    if (response.hairThickness || response.naturalLevel) {
      return REQUIRED_FIELDS.diagnosis || [];
    }
    if (response.formulaText || response.formula) {
      return REQUIRED_FIELDS.formula || [];
    }
    if (response.brand || response.productType) {
      return REQUIRED_FIELDS.analyze_product || [];
    }
    return [];
  }

  /**
   * Recursively sanitize object properties
   */
  private sanitizeObject(obj: any): void {
    if (typeof obj !== 'object' || obj === null) return;

    for (const key in obj) {
      if (typeof obj[key] === 'string') {
        // Remove potentially harmful patterns
        obj[key] = obj[key]
          .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
          .replace(/javascript:/gi, '')
          .replace(/on\w+\s*=/gi, '');
      } else if (typeof obj[key] === 'object') {
        this.sanitizeObject(obj[key]);
      }
    }
  }
}