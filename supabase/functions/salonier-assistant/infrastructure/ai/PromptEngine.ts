/**
 * Prompt Engine - Optimized prompt management with template selection and compression
 * Handles dynamic prompt generation, optimization, and caching
 */

import { TemplateType, TemplateContext } from '../../types.ts';
import { OPTIMIZATION_TARGETS, PROMPT_VERSION } from '../../constants.ts';
import { ILogger, IMetrics } from '../shared/types.ts';

export interface PromptRequest {
  taskType: 'diagnose_image' | 'analyze_desired_look' | 'generate_formula' | 'convert_formula' | 'parse_product' | 'analyze_product';
  templateType?: TemplateType;
  context: Record<string, any>;
  userTier: 'free' | 'pro' | 'enterprise';
  optimizeForTokens?: boolean;
}

export interface PromptTemplate {
  id: string;
  version: string;
  type: TemplateType;
  basePrompt: string;
  systemPrompt?: string;
  tokenCount: number;
  compressionRatio: number;
  variables: string[];
  examples?: PromptExample[];
}

export interface PromptExample {
  input: Record<string, any>;
  expectedOutput: any;
  description: string;
}

export interface GeneratedPrompt {
  prompt: string;
  systemPrompt?: string;
  templateUsed: string;
  tokenCount: number;
  compressionRatio: number;
  variables: Record<string, any>;
  cacheKey: string;
}

export interface IPromptEngine {
  generatePrompt(request: PromptRequest): GeneratedPrompt;
  optimizePrompt(prompt: string, targetReduction: number): string;
  getTemplate(taskType: string, templateType: TemplateType): PromptTemplate;
  validatePrompt(prompt: string, requiredVariables: string[]): boolean;
  getCacheKey(request: PromptRequest): string;
}

export class PromptEngine implements IPromptEngine {
  private templates: Map<string, PromptTemplate> = new Map();
  private optimizedCache: Map<string, string> = new Map();

  constructor(
    private readonly logger: ILogger,
    private readonly metrics: IMetrics
  ) {
    this.initializeTemplates();
    this.logger.info('Prompt Engine initialized', {
      templatesLoaded: this.templates.size,
      version: PROMPT_VERSION,
    });
  }

  /**
   * Generate optimized prompt for a specific request
   */
  generatePrompt(request: PromptRequest): GeneratedPrompt {
    const startTime = Date.now();

    // Determine template type if not specified
    const templateType = request.templateType || this.selectOptimalTemplate(request);
    
    // Get template
    const template = this.getTemplate(request.taskType, templateType);
    
    // Generate cache key
    const cacheKey = this.getCacheKey(request);

    // Check cache first
    const cachedPrompt = this.optimizedCache.get(cacheKey);
    if (cachedPrompt) {
      this.metrics.recordCounter('prompt_cache_hits', 1, {
        taskType: request.taskType,
        templateType,
      });

      return {
        prompt: cachedPrompt,
        systemPrompt: template.systemPrompt,
        templateUsed: template.id,
        tokenCount: this.estimateTokenCount(cachedPrompt),
        compressionRatio: template.compressionRatio,
        variables: request.context,
        cacheKey,
      };
    }

    // Generate prompt from template
    let prompt = this.interpolateTemplate(template.basePrompt, request.context);

    // Apply optimizations if requested
    if (request.optimizeForTokens) {
      const targetReduction = this.getTargetReduction(templateType);
      prompt = this.optimizePrompt(prompt, targetReduction);
    }

    // Cache the result
    this.optimizedCache.set(cacheKey, prompt);

    const generationTime = Date.now() - startTime;
    const finalTokenCount = this.estimateTokenCount(prompt);

    // Record metrics
    this.metrics.recordCounter('prompts_generated', 1, {
      taskType: request.taskType,
      templateType,
    });
    this.metrics.recordHistogram('prompt_generation_time_ms', generationTime);
    this.metrics.recordHistogram('prompt_token_count', finalTokenCount, {
      taskType: request.taskType,
      templateType,
    });

    this.logger.debug('Prompt generated', {
      taskType: request.taskType,
      templateType,
      tokenCount: finalTokenCount,
      compressionRatio: template.compressionRatio,
      generationTime,
      cacheKey,
    });

    return {
      prompt,
      systemPrompt: template.systemPrompt,
      templateUsed: template.id,
      tokenCount: finalTokenCount,
      compressionRatio: template.compressionRatio,
      variables: request.context,
      cacheKey,
    };
  }

  /**
   * Optimize prompt for token reduction
   */
  optimizePrompt(prompt: string, targetReduction: number): string {
    let optimized = prompt;

    // Remove redundant whitespace
    optimized = optimized.replace(/\s+/g, ' ').trim();

    // Remove redundant phrases and filler words
    const redundantPhrases = [
      /\bplease\s+/gi,
      /\bkindly\s+/gi,
      /\bin order to\b/gi,
      /\bit is important to\b/gi,
      /\bmake sure to\b/gi,
      /\bensure that\b/gi,
    ];

    redundantPhrases.forEach(phrase => {
      optimized = optimized.replace(phrase, '');
    });

    // Compress common patterns
    optimized = optimized
      .replace(/\bfor example\b/gi, 'e.g.')
      .replace(/\bthat is\b/gi, 'i.e.')
      .replace(/\band so on\b/gi, 'etc.')
      .replace(/\bHair color\b/gi, 'Color')
      .replace(/\bformulation\b/gi, 'formula')
      .replace(/\bapplication\b/gi, 'app');

    // Calculate actual reduction achieved
    const originalLength = prompt.length;
    const optimizedLength = optimized.length;
    const actualReduction = (originalLength - optimizedLength) / originalLength;

    this.metrics.recordGauge('prompt_compression_ratio', actualReduction);

    this.logger.debug('Prompt optimized', {
      originalLength,
      optimizedLength,
      targetReduction,
      actualReduction,
    });

    return optimized.trim();
  }

  /**
   * Get template for task type and template type
   */
  getTemplate(taskType: string, templateType: TemplateType): PromptTemplate {
    const templateKey = `${taskType}_${templateType}`;
    const template = this.templates.get(templateKey);
    
    if (!template) {
      // Fallback to minimal template
      const fallbackKey = `${taskType}_minimal`;
      const fallback = this.templates.get(fallbackKey);
      
      if (!fallback) {
        throw new Error(`No template found for task type: ${taskType}`);
      }
      
      this.logger.warn('Template not found, using fallback', {
        requestedTemplate: templateKey,
        fallbackTemplate: fallbackKey,
      });
      
      return fallback;
    }

    return template;
  }

  /**
   * Validate prompt contains required variables
   */
  validatePrompt(prompt: string, requiredVariables: string[]): boolean {
    return requiredVariables.every(variable => {
      const variablePattern = new RegExp(`{{\\s*${variable}\\s*}}`, 'g');
      const hasVariable = variablePattern.test(prompt);
      
      if (!hasVariable) {
        this.logger.warn('Missing required variable in prompt', {
          variable,
          prompt: prompt.substring(0, 200),
        });
      }
      
      return hasVariable;
    });
  }

  /**
   * Generate cache key for prompt request
   */
  getCacheKey(request: PromptRequest): string {
    const contextKeys = Object.keys(request.context).sort();
    const contextHash = contextKeys
      .map(key => `${key}:${this.hashValue(request.context[key])}`)
      .join('|');

    return `prompt_${request.taskType}_${request.templateType || 'auto'}_${PROMPT_VERSION}_${contextHash}`;
  }

  /**
   * Select optimal template based on request characteristics
   */
  private selectOptimalTemplate(request: PromptRequest): TemplateType {
    // Default templates based on user tier
    const defaultTemplate = OPTIMIZATION_TARGETS.defaultTemplate[request.userTier];

    // Override for specific conditions
    if (request.optimizeForTokens) {
      return 'minimal';
    }

    // Task-specific optimizations
    if (request.taskType === 'diagnose_image' && request.context.imageQuality === 'high') {
      return request.userTier === 'enterprise' ? 'full' : 'optimized';
    }

    if (request.taskType === 'generate_formula' && request.context.complexity === 'high') {
      return request.userTier === 'free' ? 'optimized' : 'full';
    }

    return defaultTemplate;
  }

  /**
   * Interpolate template variables with context values
   */
  private interpolateTemplate(template: string, context: Record<string, any>): string {
    let result = template;

    // Replace template variables
    Object.entries(context).forEach(([key, value]) => {
      const pattern = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
      const stringValue = typeof value === 'string' ? value : JSON.stringify(value);
      result = result.replace(pattern, stringValue);
    });

    // Check for unreplaced variables
    const unreplacedVariables = result.match(/{{[^}]+}}/g);
    if (unreplacedVariables) {
      this.logger.warn('Unreplaced template variables found', {
        variables: unreplacedVariables,
        template: template.substring(0, 100),
      });
    }

    return result;
  }

  /**
   * Estimate token count for a prompt
   */
  private estimateTokenCount(text: string): number {
    // Rough estimation: ~4 characters per token for English text
    return Math.ceil(text.length / 4);
  }

  /**
   * Get target reduction ratio for template type
   */
  private getTargetReduction(templateType: TemplateType): number {
    return OPTIMIZATION_TARGETS.tokenReduction[templateType] || 0;
  }

  /**
   * Hash a value for cache key generation
   */
  private hashValue(value: any): string {
    const str = typeof value === 'string' ? value : JSON.stringify(value);
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Initialize prompt templates
   */
  private initializeTemplates(): void {
    // Hair diagnosis templates
    this.templates.set('diagnose_image_full', {
      id: 'diagnose_image_full_v1',
      version: PROMPT_VERSION,
      type: 'full',
      basePrompt: `You are an expert hair colorist analyzing hair for professional coloration. Provide a comprehensive hair analysis based on the image.

Analyze the hair image and provide detailed information about:
1. Hair thickness (fine/medium/thick)
2. Hair density (low/medium/high)
3. Natural level (1-10 scale)
4. Underlying pigments present
5. Hair condition and porosity
6. Zone-by-zone analysis if applicable
7. Any previous color treatments visible

Client context: {{clientHistory}}
Lighting conditions: {{lightingConditions}}
Image quality: {{imageQuality}}

Respond in JSON format with structured analysis including confidence scores for each assessment.`,
      systemPrompt: 'You are a professional hair colorist with 15+ years of experience. Analyze hair images with precision and provide actionable insights.',
      tokenCount: 180,
      compressionRatio: 0,
      variables: ['clientHistory', 'lightingConditions', 'imageQuality'],
    });

    this.templates.set('diagnose_image_optimized', {
      id: 'diagnose_image_opt_v1',
      version: PROMPT_VERSION,
      type: 'optimized',
      basePrompt: `Expert hair colorist analysis. Analyze image for:
- Thickness (fine/medium/thick)
- Density (low/medium/high)  
- Natural level (1-10)
- Underlying pigments
- Condition/porosity
- Previous treatments

Context: {{clientHistory}}
Light: {{lightingConditions}}

JSON response with confidence scores.`,
      systemPrompt: 'Professional hair colorist. Analyze precisely.',
      tokenCount: 95,
      compressionRatio: 0.47,
      variables: ['clientHistory', 'lightingConditions'],
    });

    this.templates.set('diagnose_image_minimal', {
      id: 'diagnose_image_min_v1',
      version: PROMPT_VERSION,
      type: 'minimal',
      basePrompt: `Hair analysis: thickness, density, level (1-10), pigments, condition. Context: {{clientHistory}}. JSON format.`,
      tokenCount: 45,
      compressionRatio: 0.75,
      variables: ['clientHistory'],
    });

    // Formula generation templates
    this.templates.set('generate_formula_full', {
      id: 'formula_full_v1',
      version: PROMPT_VERSION,
      type: 'full',
      basePrompt: `Create a professional hair color formula based on the hair diagnosis and desired outcome.

Hair Analysis:
{{diagnosis}}

Desired Result:
{{desiredResult}}

Brand: {{brand}}
Line: {{line}}
Technique: {{selectedTechnique}}
{{#customTechnique}}Custom technique: {{customTechnique}}{{/customTechnique}}

Regional Settings:
- Volume unit: {{regionalConfig.volumeUnit}}
- Developer terminology: {{regionalConfig.developerTerminology}}
- Max developer volume: {{regionalConfig.maxDeveloperVolume}}
- Language: {{regionalConfig.language}}

Generate a complete formula including:
1. Exact color mixture with ratios
2. Developer volume and strength
3. Application technique steps
4. Processing time recommendations
5. Safety considerations
6. Expected results and variations
7. Maintenance recommendations

Consider chemical compatibility, underlying pigments, and hair condition. Provide professional-grade instructions.`,
      systemPrompt: 'You are a master hair colorist with expertise in chemical formulation, color theory, and professional techniques.',
      tokenCount: 240,
      compressionRatio: 0,
      variables: ['diagnosis', 'desiredResult', 'brand', 'line', 'selectedTechnique', 'regionalConfig'],
    });

    this.templates.set('generate_formula_optimized', {
      id: 'formula_opt_v1',
      version: PROMPT_VERSION,
      type: 'optimized',
      basePrompt: `Hair color formula:

Analysis: {{diagnosis}}
Goal: {{desiredResult}}
Brand: {{brand}} {{line}}
Technique: {{selectedTechnique}}

Include: color ratios, developer vol/strength, steps, timing, safety notes.
Units: {{regionalConfig.volumeUnit}}, max developer: {{regionalConfig.maxDeveloperVolume}}`,
      tokenCount: 85,
      compressionRatio: 0.65,
      variables: ['diagnosis', 'desiredResult', 'brand', 'line', 'selectedTechnique', 'regionalConfig'],
    });

    this.templates.set('generate_formula_minimal', {
      id: 'formula_min_v1',
      version: PROMPT_VERSION,
      type: 'minimal',
      basePrompt: `Formula for {{desiredResult}} on {{diagnosis.level}} hair. {{brand}} {{line}}, {{selectedTechnique}}. Include ratios, developer, timing.`,
      tokenCount: 35,
      compressionRatio: 0.85,
      variables: ['desiredResult', 'diagnosis', 'brand', 'line', 'selectedTechnique'],
    });

    // Product parsing templates
    this.templates.set('parse_product_full', {
      id: 'product_full_v1',
      version: PROMPT_VERSION,
      type: 'full',
      basePrompt: `Parse the following product text and extract structured information:

"{{productText}}"

Extract and categorize:
1. Brand name
2. Product line/series
3. Product type (permanent, semi-permanent, bleach, etc.)
4. Shade/color information
5. Volume/size if mentioned
6. Key ingredients or features
7. Application instructions if present
8. Professional vs consumer grade indicators

Respond in JSON format with confidence scores for each extracted field.`,
      tokenCount: 110,
      compressionRatio: 0,
      variables: ['productText'],
    });

    this.templates.set('parse_product_optimized', {
      id: 'product_opt_v1',
      version: PROMPT_VERSION,
      type: 'optimized',
      basePrompt: `Parse product: "{{productText}}"

Extract: brand, line, type, shade, volume, features. JSON format with confidence.`,
      tokenCount: 30,
      compressionRatio: 0.73,
      variables: ['productText'],
    });

    this.templates.set('parse_product_minimal', {
      id: 'product_min_v1',
      version: PROMPT_VERSION,
      type: 'minimal',
      basePrompt: `Parse "{{productText}}": brand, type, shade. JSON.`,
      tokenCount: 15,
      compressionRatio: 0.86,
      variables: ['productText'],
    });

    this.logger.info('Prompt templates initialized', {
      totalTemplates: this.templates.size,
      taskTypes: ['diagnose_image', 'generate_formula', 'parse_product'],
      templateTypes: ['full', 'optimized', 'minimal'],
    });
  }
}