/**
 * Shared types for infrastructure layer
 * Common interfaces and types used across all infrastructure services
 */

export interface Result<T> {
  success: boolean;
  data?: T;
  error?: string;
  metadata?: Record<string, any>;
}

export class Result<T> {
  private constructor(
    public readonly success: boolean,
    public readonly data?: T,
    public readonly error?: string,
    public readonly metadata?: Record<string, any>
  ) {}

  static success<T>(data: T, metadata?: Record<string, any>): Result<T> {
    return new Result(true, data, undefined, metadata);
  }

  static error<T>(error: string, metadata?: Record<string, any>): Result<T> {
    return new Result(false, undefined, error, metadata);
  }

  map<U>(fn: (data: T) => U): Result<U> {
    if (this.success && this.data !== undefined) {
      try {
        return Result.success(fn(this.data), this.metadata);
      } catch (error) {
        return Result.error(error instanceof Error ? error.message : String(error));
      }
    }
    return Result.error(this.error || 'No data to map');
  }

  flatMap<U>(fn: (data: T) => Result<U>): Result<U> {
    if (this.success && this.data !== undefined) {
      return fn(this.data);
    }
    return Result.error(this.error || 'No data to flatMap');
  }
}

export interface ILogger {
  debug(message: string, context?: Record<string, any>): void;
  info(message: string, context?: Record<string, any>): void;
  warn(message: string, context?: Record<string, any>): void;
  error(message: string, context?: Record<string, any>): void;
}

export interface IMetrics {
  recordCounter(name: string, value: number, tags?: Record<string, string>): void;
  recordGauge(name: string, value: number, tags?: Record<string, string>): void;
  recordHistogram(name: string, value: number, tags?: Record<string, string>): void;
  recordTimer(name: string, duration: number, tags?: Record<string, string>): void;
}

export interface ICache {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T, ttl?: number): Promise<void>;
  delete(key: string): Promise<void>;
  clear(): Promise<void>;
  exists(key: string): Promise<boolean>;
}

export interface DatabaseConfig {
  url: string;
  apiKey: string;
  schema?: string;
  maxConnections?: number;
  connectionTimeout?: number;
  retryAttempts?: number;
}

export interface CacheConfig {
  provider: 'memory' | 'redis' | 'supabase';
  connectionString?: string;
  defaultTTL: number;
  maxMemoryMB?: number;
  keyPrefix?: string;
}

export interface LoggingConfig {
  level: 'debug' | 'info' | 'warn' | 'error';
  format: 'json' | 'text';
  includeTimestamp: boolean;
  includeLevel: boolean;
  maxLogSize?: number;
}

export interface MetricsConfig {
  provider: 'console' | 'prometheus' | 'cloudwatch';
  endpoint?: string;
  namespace?: string;
  flushInterval?: number;
}

export interface InfrastructureConfig {
  database: DatabaseConfig;
  cache: CacheConfig;
  logging: LoggingConfig;
  metrics: MetricsConfig;
  environment: 'development' | 'staging' | 'production';
}

export interface HealthCheck {
  name: string;
  status: 'healthy' | 'unhealthy' | 'degraded';
  lastCheck: Date;
  responseTime: number;
  error?: string;
  details?: Record<string, any>;
}

export interface IHealthCheckable {
  healthCheck(): Promise<HealthCheck>;
}

export interface CircuitBreakerConfig {
  failureThreshold: number;
  recoveryTimeout: number;
  monitoringPeriod: number;
  name: string;
}

export enum CircuitBreakerState {
  CLOSED = 'CLOSED',
  OPEN = 'OPEN',
  HALF_OPEN = 'HALF_OPEN',
}

export interface RateLimiterConfig {
  maxRequests: number;
  windowMs: number;
  keyGenerator?: (context: any) => string;
  skipIfSuccessful?: boolean;
}

export interface RequestContext {
  requestId: string;
  userId?: string;
  salonId?: string;
  userAgent?: string;
  ipAddress?: string;
  startTime: number;
  metadata?: Record<string, any>;
}

export interface ServiceDependencies {
  logger: ILogger;
  metrics: IMetrics;
  cache?: ICache;
  config: InfrastructureConfig;
}

export interface ConnectionPool<T> {
  acquire(): Promise<T>;
  release(connection: T): void;
  destroy(connection: T): void;
  size(): number;
  availableCount(): number;
  pendingCount(): number;
}

export interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  retryableErrors?: string[];
}

export interface BackgroundTask {
  id: string;
  name: string;
  schedule: string; // cron format
  handler: () => Promise<void>;
  lastRun?: Date;
  nextRun?: Date;
  status: 'idle' | 'running' | 'failed';
}

export interface IBackgroundScheduler {
  schedule(task: BackgroundTask): void;
  unschedule(taskId: string): void;
  getScheduledTasks(): BackgroundTask[];
  start(): void;
  stop(): void;
}

// Utility types for better type safety
export type NonEmptyArray<T> = [T, ...T[]];

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends (infer U)[]
    ? DeepReadonlyArray<U>
    : T[P] extends object
    ? DeepReadonly<T[P]>
    : T[P];
};

export interface DeepReadonlyArray<T> extends ReadonlyArray<DeepReadonly<T>> {}

// Environment variable helpers
export interface EnvironmentVariables {
  OPENAI_API_KEY: string;
  SUPABASE_URL: string;
  SUPABASE_ANON_KEY: string;
  SUPABASE_SERVICE_ROLE_KEY?: string;
  ENVIRONMENT: 'development' | 'staging' | 'production';
  LOG_LEVEL?: 'debug' | 'info' | 'warn' | 'error';
  CACHE_TTL_DEFAULT?: string;
  MAX_REQUEST_TIMEOUT?: string;
  RATE_LIMIT_REQUESTS?: string;
  RATE_LIMIT_WINDOW_MS?: string;
}

export interface ServiceError extends Error {
  code: string;
  statusCode?: number;
  context?: Record<string, any>;
  cause?: Error;
  retryable?: boolean;
}

export class ServiceError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly statusCode?: number,
    public readonly context?: Record<string, any>,
    public readonly cause?: Error,
    public readonly retryable: boolean = false
  ) {
    super(message);
    this.name = 'ServiceError';
    
    // Maintains proper stack trace for where our error was thrown
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ServiceError);
    }
  }

  static from(error: Error, code: string, statusCode?: number): ServiceError {
    return new ServiceError(
      error.message,
      code,
      statusCode,
      undefined,
      error,
      false
    );
  }

  static retryable(message: string, code: string, context?: Record<string, any>): ServiceError {
    return new ServiceError(message, code, undefined, context, undefined, true);
  }

  static nonRetryable(message: string, code: string, statusCode?: number, context?: Record<string, any>): ServiceError {
    return new ServiceError(message, code, statusCode, context, undefined, false);
  }

  toJSON(): Record<string, any> {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      statusCode: this.statusCode,
      context: this.context,
      retryable: this.retryable,
      stack: this.stack,
    };
  }
}