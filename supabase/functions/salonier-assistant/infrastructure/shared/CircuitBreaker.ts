/**
 * Circuit Breaker - Implements circuit breaker pattern for external service resilience
 * Prevents cascading failures by monitoring service health and temporarily blocking requests
 */

import { CircuitBreakerConfig, CircuitBreakerState, ServiceError, Result } from './types.ts';

export interface CircuitBreakerMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  rejectedRequests: number;
  averageResponseTime: number;
  state: CircuitBreakerState;
  lastStateChange: Date;
  failureRate: number;
}

export interface CircuitBreakerOptions extends CircuitBreakerConfig {
  onStateChange?: (state: CircuitBreakerState, metrics: CircuitBreakerMetrics) => void;
  onRequest?: (state: CircuitBreakerState) => void;
  onSuccess?: (responseTime: number) => void;
  onFailure?: (error: Error) => void;
  isRetryableError?: (error: Error) => boolean;
}

export class CircuitBreaker {
  private state: CircuitBreakerState = CircuitBreakerState.CLOSED;
  private failures = 0;
  private successes = 0;
  private requests = 0;
  private rejected = 0;
  private lastFailureTime = 0;
  private lastStateChange = new Date();
  private responseTimes: number[] = [];
  private readonly maxResponseTimeHistory = 100;

  constructor(private readonly options: CircuitBreakerOptions) {
    this.validateConfig();
  }

  /**
   * Execute a function with circuit breaker protection
   */
  async execute<T>(fn: () => Promise<Result<T>>): Promise<Result<T>> {
    this.requests++;

    // Call onRequest hook
    this.options.onRequest?.(this.state);

    // Check if circuit is open
    if (this.state === CircuitBreakerState.OPEN) {
      if (this.shouldAttemptReset()) {
        this.state = CircuitBreakerState.HALF_OPEN;
        this.onStateChange();
      } else {
        this.rejected++;
        return Result.error(`Circuit breaker is OPEN for service: ${this.options.name}`);
      }
    }

    const startTime = Date.now();

    try {
      const result = await fn();
      const responseTime = Date.now() - startTime;

      this.recordResponseTime(responseTime);

      if (result.success) {
        this.onSuccess(responseTime);
        return result;
      } else {
        // Treat unsuccessful results as failures
        const error = new ServiceError(
          result.error || 'Operation failed',
          'OPERATION_FAILED'
        );
        this.onFailure(error);
        return result;
      }
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.recordResponseTime(responseTime);

      const serviceError = error instanceof ServiceError 
        ? error 
        : ServiceError.from(error as Error, 'UNKNOWN_ERROR');

      this.onFailure(serviceError);

      // Re-throw or return error based on whether it's retryable
      if (this.options.isRetryableError?.(serviceError) ?? serviceError.retryable) {
        return Result.error(serviceError.message);
      } else {
        throw serviceError;
      }
    }
  }

  /**
   * Get current circuit breaker metrics
   */
  getMetrics(): CircuitBreakerMetrics {
    const totalRequests = this.requests;
    const failureRate = totalRequests > 0 ? this.failures / totalRequests : 0;
    const averageResponseTime = this.responseTimes.length > 0
      ? this.responseTimes.reduce((a, b) => a + b, 0) / this.responseTimes.length
      : 0;

    return {
      totalRequests,
      successfulRequests: this.successes,
      failedRequests: this.failures,
      rejectedRequests: this.rejected,
      averageResponseTime,
      state: this.state,
      lastStateChange: this.lastStateChange,
      failureRate,
    };
  }

  /**
   * Manually reset the circuit breaker to closed state
   */
  reset(): void {
    this.state = CircuitBreakerState.CLOSED;
    this.failures = 0;
    this.successes = 0;
    this.lastFailureTime = 0;
    this.onStateChange();
  }

  /**
   * Get current state
   */
  getState(): CircuitBreakerState {
    return this.state;
  }

  /**
   * Force circuit breaker to specific state (for testing)
   */
  forceState(state: CircuitBreakerState): void {
    this.state = state;
    this.lastStateChange = new Date();
    this.onStateChange();
  }

  /**
   * Handle successful execution
   */
  private onSuccess(responseTime: number): void {
    this.successes++;
    
    // Call success hook
    this.options.onSuccess?.(responseTime);

    if (this.state === CircuitBreakerState.HALF_OPEN) {
      // Transition back to CLOSED after successful execution in HALF_OPEN
      this.state = CircuitBreakerState.CLOSED;
      this.failures = 0; // Reset failure count
      this.onStateChange();
    }
  }

  /**
   * Handle failed execution
   */
  private onFailure(error: Error): void {
    this.failures++;
    this.lastFailureTime = Date.now();

    // Call failure hook
    this.options.onFailure?.(error);

    if (this.state === CircuitBreakerState.HALF_OPEN) {
      // Go back to OPEN if we fail in HALF_OPEN state
      this.state = CircuitBreakerState.OPEN;
      this.onStateChange();
    } else if (this.state === CircuitBreakerState.CLOSED) {
      // Check if we should open the circuit
      const recentRequests = this.getRecentRequestsCount();
      const recentFailures = this.getRecentFailuresCount();

      if (recentRequests >= this.options.failureThreshold && 
          recentFailures >= this.options.failureThreshold) {
        this.state = CircuitBreakerState.OPEN;
        this.onStateChange();
      }
    }
  }

  /**
   * Check if we should attempt to reset the circuit breaker
   */
  private shouldAttemptReset(): boolean {
    const timeSinceLastFailure = Date.now() - this.lastFailureTime;
    return timeSinceLastFailure >= this.options.recoveryTimeout;
  }

  /**
   * Handle state change
   */
  private onStateChange(): void {
    this.lastStateChange = new Date();
    
    // Call state change hook
    if (this.options.onStateChange) {
      const metrics = this.getMetrics();
      this.options.onStateChange(this.state, metrics);
    }
  }

  /**
   * Record response time for metrics
   */
  private recordResponseTime(responseTime: number): void {
    this.responseTimes.push(responseTime);
    
    // Keep only the most recent response times
    if (this.responseTimes.length > this.maxResponseTimeHistory) {
      this.responseTimes = this.responseTimes.slice(-this.maxResponseTimeHistory);
    }
  }

  /**
   * Get recent requests count (within monitoring period)
   */
  private getRecentRequestsCount(): number {
    // For simplicity, we use total requests
    // In a production implementation, you'd track requests within the monitoring period
    return this.requests;
  }

  /**
   * Get recent failures count (within monitoring period)
   */
  private getRecentFailuresCount(): number {
    // For simplicity, we use total failures
    // In a production implementation, you'd track failures within the monitoring period
    return this.failures;
  }

  /**
   * Validate circuit breaker configuration
   */
  private validateConfig(): void {
    if (this.options.failureThreshold <= 0) {
      throw new Error('Circuit breaker failure threshold must be greater than 0');
    }

    if (this.options.recoveryTimeout <= 0) {
      throw new Error('Circuit breaker recovery timeout must be greater than 0');
    }

    if (this.options.monitoringPeriod <= 0) {
      throw new Error('Circuit breaker monitoring period must be greater than 0');
    }

    if (!this.options.name || this.options.name.trim().length === 0) {
      throw new Error('Circuit breaker name is required');
    }
  }
}

/**
 * Circuit Breaker Factory for creating configured instances
 */
export class CircuitBreakerFactory {
  private static readonly defaultConfig: Partial<CircuitBreakerOptions> = {
    failureThreshold: 5,
    recoveryTimeout: 30000, // 30 seconds
    monitoringPeriod: 60000, // 60 seconds
  };

  /**
   * Create a circuit breaker with default configuration
   */
  static create(name: string, overrides?: Partial<CircuitBreakerOptions>): CircuitBreaker {
    const config: CircuitBreakerOptions = {
      ...this.defaultConfig,
      ...overrides,
      name,
    } as CircuitBreakerOptions;

    return new CircuitBreaker(config);
  }

  /**
   * Create a circuit breaker for API calls
   */
  static createForApi(name: string, overrides?: Partial<CircuitBreakerOptions>): CircuitBreaker {
    const apiDefaults: Partial<CircuitBreakerOptions> = {
      failureThreshold: 3,
      recoveryTimeout: 60000, // 1 minute
      monitoringPeriod: 120000, // 2 minutes
      isRetryableError: (error) => {
        if (error instanceof ServiceError) {
          // Don't retry client errors (4xx), but retry server errors (5xx)
          return error.statusCode ? error.statusCode >= 500 : true;
        }
        return true;
      },
    };

    return this.create(name, { ...apiDefaults, ...overrides });
  }

  /**
   * Create a circuit breaker for database operations
   */
  static createForDatabase(name: string, overrides?: Partial<CircuitBreakerOptions>): CircuitBreaker {
    const dbDefaults: Partial<CircuitBreakerOptions> = {
      failureThreshold: 10,
      recoveryTimeout: 30000, // 30 seconds
      monitoringPeriod: 90000, // 90 seconds
      isRetryableError: (error) => {
        // Retry connection errors, timeouts, and temporary failures
        const retryablePatterns = [
          /connection/i,
          /timeout/i,
          /temporary/i,
          /unavailable/i,
          /overload/i,
        ];
        
        return retryablePatterns.some(pattern => pattern.test(error.message));
      },
    };

    return this.create(name, { ...dbDefaults, ...overrides });
  }

  /**
   * Create a circuit breaker for external services
   */
  static createForExternalService(name: string, overrides?: Partial<CircuitBreakerOptions>): CircuitBreaker {
    const externalDefaults: Partial<CircuitBreakerOptions> = {
      failureThreshold: 5,
      recoveryTimeout: 120000, // 2 minutes
      monitoringPeriod: 180000, // 3 minutes
      isRetryableError: (error) => {
        if (error instanceof ServiceError) {
          // Retry server errors and network issues, but not auth or client errors
          if (error.statusCode) {
            return error.statusCode >= 500 || error.statusCode === 429; // Include rate limiting
          }
          return !error.message.includes('auth') && !error.message.includes('permission');
        }
        return true;
      },
    };

    return this.create(name, { ...externalDefaults, ...overrides });
  }
}