/**
 * Cache Repository - Smart caching system with TTL, compression, and multi-tier storage
 * Optimizes AI response caching to reduce costs and improve performance
 */

import { CacheConfig, ILogger, IMetrics, Result, ServiceError, ICache, IHealthCheckable, HealthCheck } from '../shared/types.ts';
import { CACHE_TTL } from '../../constants.ts';

export interface CacheEntry<T> {
  value: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccess: number;
  compressed?: boolean;
  size: number;
}

export interface CacheStats {
  totalEntries: number;
  memoryUsage: number;
  hitRate: number;
  totalHits: number;
  totalMisses: number;
  evictions: number;
  compressionRatio: number;
  averageResponseTime: number;
}

export interface ICacheRepository extends ICache {
  getWithStats<T>(key: string): Promise<{ value: T | null; hit: boolean; responseTime: number }>;
  setWithCompression<T>(key: string, value: T, ttl?: number, compress?: boolean): Promise<void>;
  getStats(): Promise<CacheStats>;
  evictExpired(): Promise<number>;
  evictLeastUsed(count: number): Promise<number>;
  clear(pattern?: string): Promise<void>;
  getSize(): Promise<number>;
  warmup(keys: string[]): Promise<void>;
}

export class CacheRepository implements ICacheRepository, IHealthCheckable {
  private memoryCache: Map<string, CacheEntry<any>> = new Map();
  private stats = {
    hits: 0,
    misses: 0,
    evictions: 0,
    totalRequests: 0,
    responseTimes: [] as number[],
  };
  private readonly maxMemoryBytes: number;
  private readonly compressionThreshold = 1024; // 1KB
  private readonly maxResponseTimeHistory = 1000;

  constructor(
    private readonly config: CacheConfig,
    private readonly logger: ILogger,
    private readonly metrics: IMetrics
  ) {
    this.maxMemoryBytes = (config.maxMemoryMB || 64) * 1024 * 1024;
    
    // Start background cleanup task
    this.startBackgroundTasks();

    this.logger.info('Cache Repository initialized', {
      provider: config.provider,
      defaultTTL: config.defaultTTL,
      maxMemoryMB: config.maxMemoryMB || 64,
      keyPrefix: config.keyPrefix,
    });
  }

  /**
   * Get value from cache with hit/miss statistics
   */
  async getWithStats<T>(key: string): Promise<{ value: T | null; hit: boolean; responseTime: number }> {
    const startTime = Date.now();
    const prefixedKey = this.getPrefixedKey(key);

    try {
      const entry = this.memoryCache.get(prefixedKey);
      const responseTime = Date.now() - startTime;

      this.recordResponseTime(responseTime);
      this.stats.totalRequests++;

      if (!entry) {
        this.stats.misses++;
        this.recordCacheMetrics('miss', responseTime, key);
        return { value: null, hit: false, responseTime };
      }

      // Check if entry has expired
      if (this.isExpired(entry)) {
        this.memoryCache.delete(prefixedKey);
        this.stats.misses++;
        this.recordCacheMetrics('miss', responseTime, key);
        
        this.logger.debug('Cache entry expired', {
          key: prefixedKey,
          timestamp: entry.timestamp,
          ttl: entry.ttl,
        });

        return { value: null, hit: false, responseTime };
      }

      // Update access statistics
      entry.accessCount++;
      entry.lastAccess = Date.now();

      this.stats.hits++;
      this.recordCacheMetrics('hit', responseTime, key);

      let value = entry.value;

      // Decompress if needed
      if (entry.compressed) {
        value = this.decompress(value);
      }

      this.logger.debug('Cache hit', {
        key: prefixedKey,
        accessCount: entry.accessCount,
        age: Date.now() - entry.timestamp,
        compressed: entry.compressed,
      });

      return { value, hit: true, responseTime };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.logger.error('Cache get error', {
        key: prefixedKey,
        error: error instanceof Error ? error.message : String(error),
        responseTime,
      });

      this.metrics.recordCounter('cache_errors_total', 1, {
        operation: 'get',
        error_type: error instanceof Error ? error.constructor.name : 'Unknown',
      });

      return { value: null, hit: false, responseTime };
    }
  }

  /**
   * Get value from cache (ICache interface)
   */
  async get<T>(key: string): Promise<T | null> {
    const result = await this.getWithStats<T>(key);
    return result.value;
  }

  /**
   * Set value in cache with optional compression
   */
  async setWithCompression<T>(key: string, value: T, ttl?: number, compress?: boolean): Promise<void> {
    const startTime = Date.now();
    const prefixedKey = this.getPrefixedKey(key);

    try {
      const serializedValue = JSON.stringify(value);
      const size = new Blob([serializedValue]).size;
      
      // Auto-compress if size exceeds threshold
      const shouldCompress = compress !== false && size > this.compressionThreshold;
      let finalValue: any = value;
      let finalSize = size;

      if (shouldCompress) {
        finalValue = this.compress(serializedValue);
        finalSize = new Blob([JSON.stringify(finalValue)]).size;

        this.logger.debug('Cache entry compressed', {
          key: prefixedKey,
          originalSize: size,
          compressedSize: finalSize,
          compressionRatio: (size - finalSize) / size,
        });
      }

      const entry: CacheEntry<any> = {
        value: finalValue,
        timestamp: Date.now(),
        ttl: ttl || this.config.defaultTTL,
        accessCount: 0,
        lastAccess: Date.now(),
        compressed: shouldCompress,
        size: finalSize,
      };

      // Check memory constraints before adding
      await this.ensureMemoryCapacity(finalSize);

      this.memoryCache.set(prefixedKey, entry);

      const responseTime = Date.now() - startTime;
      this.recordCacheMetrics('set', responseTime, key);

      this.logger.debug('Cache entry set', {
        key: prefixedKey,
        size: finalSize,
        ttl: entry.ttl,
        compressed: shouldCompress,
        responseTime,
      });
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.logger.error('Cache set error', {
        key: prefixedKey,
        error: error instanceof Error ? error.message : String(error),
        responseTime,
      });

      this.metrics.recordCounter('cache_errors_total', 1, {
        operation: 'set',
        error_type: error instanceof Error ? error.constructor.name : 'Unknown',
      });

      throw error;
    }
  }

  /**
   * Set value in cache (ICache interface)
   */
  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    await this.setWithCompression(key, value, ttl);
  }

  /**
   * Delete entry from cache
   */
  async delete(key: string): Promise<void> {
    const prefixedKey = this.getPrefixedKey(key);
    const deleted = this.memoryCache.delete(prefixedKey);

    if (deleted) {
      this.logger.debug('Cache entry deleted', { key: prefixedKey });
      this.metrics.recordCounter('cache_operations_total', 1, {
        operation: 'delete',
        status: 'success',
      });
    }
  }

  /**
   * Check if key exists in cache
   */
  async exists(key: string): Promise<boolean> {
    const prefixedKey = this.getPrefixedKey(key);
    const entry = this.memoryCache.get(prefixedKey);

    if (!entry) {
      return false;
    }

    if (this.isExpired(entry)) {
      this.memoryCache.delete(prefixedKey);
      return false;
    }

    return true;
  }

  /**
   * Clear cache entries matching pattern
   */
  async clear(pattern?: string): Promise<void> {
    if (!pattern) {
      const size = this.memoryCache.size;
      this.memoryCache.clear();
      
      this.logger.info('Cache cleared completely', { entriesRemoved: size });
      this.metrics.recordCounter('cache_operations_total', 1, {
        operation: 'clear_all',
      });
      return;
    }

    const regex = new RegExp(pattern);
    const keysToDelete: string[] = [];

    for (const key of this.memoryCache.keys()) {
      if (regex.test(key)) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.memoryCache.delete(key));

    this.logger.info('Cache cleared by pattern', {
      pattern,
      entriesRemoved: keysToDelete.length,
    });

    this.metrics.recordCounter('cache_operations_total', 1, {
      operation: 'clear_pattern',
    });
  }

  /**
   * Get cache statistics
   */
  async getStats(): Promise<CacheStats> {
    const totalRequests = this.stats.totalRequests;
    const hitRate = totalRequests > 0 ? this.stats.hits / totalRequests : 0;
    const averageResponseTime = this.stats.responseTimes.length > 0
      ? this.stats.responseTimes.reduce((a, b) => a + b, 0) / this.stats.responseTimes.length
      : 0;

    let totalSize = 0;
    let compressedEntries = 0;
    let originalSizeEstimate = 0;

    for (const entry of this.memoryCache.values()) {
      totalSize += entry.size;
      if (entry.compressed) {
        compressedEntries++;
        // Estimate original size (rough approximation)
        originalSizeEstimate += entry.size * 2;
      } else {
        originalSizeEstimate += entry.size;
      }
    }

    const compressionRatio = originalSizeEstimate > 0 
      ? (originalSizeEstimate - totalSize) / originalSizeEstimate 
      : 0;

    return {
      totalEntries: this.memoryCache.size,
      memoryUsage: totalSize,
      hitRate,
      totalHits: this.stats.hits,
      totalMisses: this.stats.misses,
      evictions: this.stats.evictions,
      compressionRatio,
      averageResponseTime,
    };
  }

  /**
   * Evict expired entries
   */
  async evictExpired(): Promise<number> {
    const keysToEvict: string[] = [];

    for (const [key, entry] of this.memoryCache) {
      if (this.isExpired(entry)) {
        keysToEvict.push(key);
      }
    }

    keysToEvict.forEach(key => this.memoryCache.delete(key));
    this.stats.evictions += keysToEvict.length;

    if (keysToEvict.length > 0) {
      this.logger.debug('Expired cache entries evicted', {
        count: keysToEvict.length,
      });

      this.metrics.recordCounter('cache_evictions_total', keysToEvict.length, {
        type: 'expired',
      });
    }

    return keysToEvict.length;
  }

  /**
   * Evict least recently used entries
   */
  async evictLeastUsed(count: number): Promise<number> {
    const entries = Array.from(this.memoryCache.entries())
      .sort(([, a], [, b]) => {
        // Sort by access count (ascending) then by last access time (ascending)
        if (a.accessCount !== b.accessCount) {
          return a.accessCount - b.accessCount;
        }
        return a.lastAccess - b.lastAccess;
      });

    const toEvict = entries.slice(0, count);
    toEvict.forEach(([key]) => this.memoryCache.delete(key));
    
    this.stats.evictions += toEvict.length;

    if (toEvict.length > 0) {
      this.logger.debug('LRU cache entries evicted', {
        count: toEvict.length,
      });

      this.metrics.recordCounter('cache_evictions_total', toEvict.length, {
        type: 'lru',
      });
    }

    return toEvict.length;
  }

  /**
   * Get current cache size
   */
  async getSize(): Promise<number> {
    return this.memoryCache.size;
  }

  /**
   * Warm up cache with frequently accessed keys
   */
  async warmup(keys: string[]): Promise<void> {
    this.logger.info('Cache warmup requested', {
      keyCount: keys.length,
    });

    // This would typically pre-populate the cache with computed values
    // For now, we just log the intention
    this.metrics.recordCounter('cache_warmup_requests', 1, {
      key_count: keys.length.toString(),
    });
  }

  /**
   * Health check for cache system
   */
  async healthCheck(): Promise<HealthCheck> {
    const startTime = Date.now();

    try {
      // Test cache operations
      const testKey = '__health_check__';
      const testValue = { timestamp: Date.now() };

      await this.set(testKey, testValue, 1000);
      const retrieved = await this.get(testKey);
      await this.delete(testKey);

      const responseTime = Date.now() - startTime;
      const stats = await this.getStats();

      if (retrieved && JSON.stringify(retrieved) === JSON.stringify(testValue)) {
        return {
          name: 'Cache Repository',
          status: stats.memoryUsage > this.maxMemoryBytes * 0.9 ? 'degraded' : 'healthy',
          lastCheck: new Date(),
          responseTime,
          details: {
            ...stats,
            maxMemoryBytes: this.maxMemoryBytes,
            memoryUsagePercent: (stats.memoryUsage / this.maxMemoryBytes) * 100,
          },
        };
      } else {
        return {
          name: 'Cache Repository',
          status: 'unhealthy',
          lastCheck: new Date(),
          responseTime,
          error: 'Cache test operation failed',
        };
      }
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      return {
        name: 'Cache Repository',
        status: 'unhealthy',
        lastCheck: new Date(),
        responseTime,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Ensure cache has enough memory capacity
   */
  private async ensureMemoryCapacity(requiredSize: number): Promise<void> {
    const stats = await this.getStats();
    const availableSpace = this.maxMemoryBytes - stats.memoryUsage;

    if (availableSpace < requiredSize) {
      const bytesToFree = requiredSize - availableSpace + (this.maxMemoryBytes * 0.1); // Extra 10% buffer
      await this.freeMemory(bytesToFree);
    }
  }

  /**
   * Free memory by evicting entries
   */
  private async freeMemory(bytesToFree: number): Promise<void> {
    // First, evict expired entries
    await this.evictExpired();

    // Check if we freed enough memory
    const stats = await this.getStats();
    const remainingToFree = bytesToFree - (this.maxMemoryBytes - stats.memoryUsage);

    if (remainingToFree > 0) {
      // Evict LRU entries
      const entries = Array.from(this.memoryCache.entries())
        .sort(([, a], [, b]) => a.lastAccess - b.lastAccess);

      let freedBytes = 0;
      const keysToEvict: string[] = [];

      for (const [key, entry] of entries) {
        keysToEvict.push(key);
        freedBytes += entry.size;

        if (freedBytes >= remainingToFree) {
          break;
        }
      }

      keysToEvict.forEach(key => this.memoryCache.delete(key));
      this.stats.evictions += keysToEvict.length;

      this.logger.warn('Memory pressure: evicted entries', {
        entriesEvicted: keysToEvict.length,
        bytesFreed: freedBytes,
        bytesRequired: bytesToFree,
      });
    }
  }

  /**
   * Check if cache entry is expired
   */
  private isExpired(entry: CacheEntry<any>): boolean {
    return Date.now() - entry.timestamp > entry.ttl;
  }

  /**
   * Get prefixed cache key
   */
  private getPrefixedKey(key: string): string {
    const prefix = this.config.keyPrefix || 'salonier';
    return `${prefix}:${key}`;
  }

  /**
   * Simple compression using JSON and base64
   */
  private compress(data: string): string {
    // In a real implementation, you'd use a proper compression library
    // For now, we'll just base64 encode (which actually increases size)
    // This is a placeholder for actual compression
    try {
      return btoa(data);
    } catch {
      return data;
    }
  }

  /**
   * Simple decompression
   */
  private decompress(data: string): any {
    try {
      const decompressed = atob(data);
      return JSON.parse(decompressed);
    } catch {
      return data;
    }
  }

  /**
   * Record response time for statistics
   */
  private recordResponseTime(time: number): void {
    this.stats.responseTimes.push(time);
    
    if (this.stats.responseTimes.length > this.maxResponseTimeHistory) {
      this.stats.responseTimes = this.stats.responseTimes.slice(-this.maxResponseTimeHistory);
    }
  }

  /**
   * Record cache metrics
   */
  private recordCacheMetrics(operation: 'hit' | 'miss' | 'set', responseTime: number, key: string): void {
    this.metrics.recordCounter('cache_operations_total', 1, {
      operation,
      status: 'success',
    });

    this.metrics.recordHistogram('cache_operation_duration_ms', responseTime, {
      operation,
    });

    if (operation === 'hit' || operation === 'miss') {
      this.metrics.recordCounter(`cache_${operation}s_total`, 1);
    }
  }

  /**
   * Start background maintenance tasks
   */
  private startBackgroundTasks(): void {
    // Clean up expired entries every 5 minutes
    setInterval(async () => {
      try {
        await this.evictExpired();
      } catch (error) {
        this.logger.error('Background cache cleanup failed', {
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }, 5 * 60 * 1000);

    // Report cache statistics every 10 minutes
    setInterval(async () => {
      try {
        const stats = await this.getStats();
        this.logger.info('Cache statistics', stats);
        
        // Record metrics
        this.metrics.recordGauge('cache_hit_rate', stats.hitRate);
        this.metrics.recordGauge('cache_memory_usage_bytes', stats.memoryUsage);
        this.metrics.recordGauge('cache_total_entries', stats.totalEntries);
        this.metrics.recordGauge('cache_compression_ratio', stats.compressionRatio);
      } catch (error) {
        this.logger.error('Cache statistics reporting failed', {
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }, 10 * 60 * 1000);
  }
}