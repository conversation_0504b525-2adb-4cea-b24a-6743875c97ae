/**
 * Storage Service - Secure file upload/download with multi-tenant isolation
 * Handles image processing, validation, and secure storage operations
 */

import { ILogger, IMetrics, Result, ServiceError, IHealthCheckable, HealthCheck } from '../shared/types.ts';
import { CircuitBreaker, CircuitBreakerFactory } from '../shared/CircuitBreaker.ts';
import { IMAGE_LIMITS } from '../../constants.ts';

export interface StorageConfig {
  supabaseUrl: string;
  serviceRoleKey: string;
  bucketName: string;
  maxFileSize: number;
  allowedMimeTypes: string[];
  enableCompression: boolean;
  imageOptimization: boolean;
}

export interface UploadOptions {
  filename?: string;
  contentType?: string;
  salonId: string;
  userId?: string;
  isPublic?: boolean;
  cacheControl?: string;
  metadata?: Record<string, string>;
}

export interface FileInfo {
  id: string;
  name: string;
  size: number;
  contentType: string;
  url: string;
  publicUrl?: string;
  uploadedAt: Date;
  salonId: string;
  userId?: string;
  metadata?: Record<string, string>;
}

export interface ImageProcessingOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  format?: 'webp' | 'jpeg' | 'png';
  autoOptimize?: boolean;
}

export interface IStorageService {
  uploadFile(data: ArrayBuffer | Uint8Array, options: UploadOptions): Promise<Result<FileInfo>>;
  uploadImage(imageData: ArrayBuffer | Uint8Array, options: UploadOptions & ImageProcessingOptions): Promise<Result<FileInfo>>;
  downloadFile(fileId: string, salonId: string): Promise<Result<ArrayBuffer>>;
  getFileInfo(fileId: string, salonId: string): Promise<Result<FileInfo>>;
  deleteFile(fileId: string, salonId: string, userId?: string): Promise<Result<void>>;
  listFiles(salonId: string, prefix?: string): Promise<Result<FileInfo[]>>;
  generateSignedUrl(fileId: string, expiresIn: number, salonId: string): Promise<Result<string>>;
  validateFile(data: ArrayBuffer, contentType: string): Promise<Result<void>>;
}

export class StorageService implements IStorageService, IHealthCheckable {
  private readonly circuitBreaker: CircuitBreaker;
  private readonly baseUrl: string;
  private readonly headers: Record<string, string>;

  constructor(
    private readonly config: StorageConfig,
    private readonly logger: ILogger,
    private readonly metrics: IMetrics
  ) {
    if (!config.supabaseUrl || !config.serviceRoleKey || !config.bucketName) {
      throw new ServiceError('Storage configuration incomplete', 'CONFIG_ERROR');
    }

    this.baseUrl = config.supabaseUrl.replace(/\/$/, '');
    this.headers = {
      'Authorization': `Bearer ${config.serviceRoleKey}`,
      'apikey': config.serviceRoleKey,
    };

    this.circuitBreaker = CircuitBreakerFactory.createForExternalService('Storage', {
      onStateChange: (state, metrics) => {
        this.logger.warn('Storage circuit breaker state changed', {
          state,
          failureRate: metrics.failureRate,
          totalRequests: metrics.totalRequests,
        });
      },
      onFailure: (error) => {
        this.metrics.recordCounter('storage_errors_total', 1, {
          error_type: error.constructor.name,
        });
      },
    });

    this.logger.info('Storage Service initialized', {
      bucket: config.bucketName,
      maxFileSize: config.maxFileSize,
      allowedTypes: config.allowedMimeTypes,
    });
  }

  /**
   * Upload file to storage with validation
   */
  async uploadFile(data: ArrayBuffer | Uint8Array, options: UploadOptions): Promise<Result<FileInfo>> {
    const startTime = Date.now();
    const fileId = this.generateFileId();

    return this.circuitBreaker.execute(async () => {
      try {
        this.logger.debug('Starting file upload', {
          fileId,
          size: data.byteLength,
          contentType: options.contentType,
          salonId: options.salonId,
        });

        // Validate file
        const validationResult = await this.validateFile(data, options.contentType || 'application/octet-stream');
        if (!validationResult.success) {
          return validationResult;
        }

        // Generate file path with salon isolation
        const filePath = this.generateFilePath(fileId, options.salonId, options.filename);

        // Prepare upload headers
        const uploadHeaders = {
          ...this.headers,
          'Content-Type': options.contentType || 'application/octet-stream',
          ...(options.cacheControl && { 'Cache-Control': options.cacheControl }),
        };

        // Add metadata headers
        if (options.metadata) {
          Object.entries(options.metadata).forEach(([key, value]) => {
            uploadHeaders[`x-metadata-${key}`] = value;
          });
        }

        const url = `${this.baseUrl}/storage/v1/object/${this.config.bucketName}/${filePath}`;

        const response = await fetch(url, {
          method: 'POST',
          headers: uploadHeaders,
          body: data,
        });

        if (!response.ok) {
          const errorText = await response.text();
          this.logger.error('File upload failed', {
            fileId,
            status: response.status,
            error: errorText,
          });

          return Result.error(`Upload failed: ${response.status} ${errorText}`);
        }

        const uploadResult = await response.json();

        // Generate URLs
        const publicUrl = options.isPublic 
          ? `${this.baseUrl}/storage/v1/object/public/${this.config.bucketName}/${filePath}`
          : undefined;

        const fileInfo: FileInfo = {
          id: fileId,
          name: options.filename || fileId,
          size: data.byteLength,
          contentType: options.contentType || 'application/octet-stream',
          url: `${this.baseUrl}/storage/v1/object/${this.config.bucketName}/${filePath}`,
          publicUrl,
          uploadedAt: new Date(),
          salonId: options.salonId,
          userId: options.userId,
          metadata: options.metadata,
        };

        const duration = Date.now() - startTime;

        // Record metrics
        this.metrics.recordCounter('storage_uploads_total', 1, {
          content_type: options.contentType || 'unknown',
          status: 'success',
        });
        this.metrics.recordHistogram('storage_upload_duration_ms', duration);
        this.metrics.recordHistogram('storage_file_size_bytes', data.byteLength);

        this.logger.info('File uploaded successfully', {
          fileId,
          filePath,
          size: data.byteLength,
          duration,
          publicUrl: !!publicUrl,
        });

        return Result.success(fileInfo);
      } catch (error) {
        const duration = Date.now() - startTime;
        this.logger.error('File upload error', {
          fileId,
          error: error instanceof Error ? error.message : String(error),
          duration,
        });

        this.metrics.recordCounter('storage_uploads_total', 1, {
          content_type: options.contentType || 'unknown',
          status: 'error',
        });

        return Result.error(`Upload failed: ${error instanceof Error ? error.message : String(error)}`);
      }
    });
  }

  /**
   * Upload image with processing and optimization
   */
  async uploadImage(
    imageData: ArrayBuffer | Uint8Array, 
    options: UploadOptions & ImageProcessingOptions
  ): Promise<Result<FileInfo>> {
    const startTime = Date.now();

    try {
      this.logger.debug('Starting image upload with processing', {
        size: imageData.byteLength,
        maxWidth: options.maxWidth,
        maxHeight: options.maxHeight,
        quality: options.quality,
        format: options.format,
      });

      // Process image if optimization is enabled
      let processedData = imageData;
      let processedContentType = options.contentType;

      if (this.config.imageOptimization && options.autoOptimize !== false) {
        const processingResult = await this.processImage(imageData, options);
        if (processingResult.success) {
          processedData = processingResult.data!.data;
          processedContentType = processingResult.data!.contentType;

          this.logger.debug('Image processed', {
            originalSize: imageData.byteLength,
            processedSize: processedData.byteLength,
            compressionRatio: (imageData.byteLength - processedData.byteLength) / imageData.byteLength,
          });
        } else {
          this.logger.warn('Image processing failed, uploading original', {
            error: processingResult.error,
          });
        }
      }

      // Upload the processed image
      const uploadOptions: UploadOptions = {
        ...options,
        contentType: processedContentType,
        filename: options.filename || `image_${Date.now()}.${this.getFileExtension(processedContentType || 'image/jpeg')}`,
      };

      const uploadResult = await this.uploadFile(processedData, uploadOptions);

      if (uploadResult.success) {
        const processingTime = Date.now() - startTime;
        this.metrics.recordHistogram('image_processing_duration_ms', processingTime);
        
        if (processedData !== imageData) {
          this.metrics.recordGauge('image_compression_ratio', 
            (imageData.byteLength - processedData.byteLength) / imageData.byteLength
          );
        }
      }

      return uploadResult;
    } catch (error) {
      this.logger.error('Image upload with processing failed', {
        error: error instanceof Error ? error.message : String(error),
      });

      return Result.error(`Image upload failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Download file from storage
   */
  async downloadFile(fileId: string, salonId: string): Promise<Result<ArrayBuffer>> {
    const startTime = Date.now();

    return this.circuitBreaker.execute(async () => {
      try {
        this.logger.debug('Downloading file', {
          fileId,
          salonId,
        });

        const filePath = this.generateFilePath(fileId, salonId);
        const url = `${this.baseUrl}/storage/v1/object/${this.config.bucketName}/${filePath}`;

        const response = await fetch(url, {
          method: 'GET',
          headers: this.headers,
        });

        if (!response.ok) {
          if (response.status === 404) {
            return Result.error('File not found');
          }

          const errorText = await response.text();
          this.logger.error('File download failed', {
            fileId,
            status: response.status,
            error: errorText,
          });

          return Result.error(`Download failed: ${response.status} ${errorText}`);
        }

        const data = await response.arrayBuffer();
        const duration = Date.now() - startTime;

        // Record metrics
        this.metrics.recordCounter('storage_downloads_total', 1, {
          status: 'success',
        });
        this.metrics.recordHistogram('storage_download_duration_ms', duration);

        this.logger.debug('File downloaded successfully', {
          fileId,
          size: data.byteLength,
          duration,
        });

        return Result.success(data);
      } catch (error) {
        const duration = Date.now() - startTime;
        this.logger.error('File download error', {
          fileId,
          error: error instanceof Error ? error.message : String(error),
          duration,
        });

        this.metrics.recordCounter('storage_downloads_total', 1, {
          status: 'error',
        });

        return Result.error(`Download failed: ${error instanceof Error ? error.message : String(error)}`);
      }
    });
  }

  /**
   * Get file information
   */
  async getFileInfo(fileId: string, salonId: string): Promise<Result<FileInfo>> {
    return this.circuitBreaker.execute(async () => {
      try {
        const filePath = this.generateFilePath(fileId, salonId);
        const url = `${this.baseUrl}/storage/v1/object/info/${this.config.bucketName}/${filePath}`;

        const response = await fetch(url, {
          method: 'GET',
          headers: this.headers,
        });

        if (!response.ok) {
          if (response.status === 404) {
            return Result.error('File not found');
          }
          return Result.error(`Failed to get file info: ${response.status}`);
        }

        const info = await response.json();

        const fileInfo: FileInfo = {
          id: fileId,
          name: info.name || fileId,
          size: info.size,
          contentType: info.mimetype,
          url: `${this.baseUrl}/storage/v1/object/${this.config.bucketName}/${filePath}`,
          uploadedAt: new Date(info.created_at),
          salonId,
          metadata: info.metadata,
        };

        return Result.success(fileInfo);
      } catch (error) {
        return Result.error(`Failed to get file info: ${error instanceof Error ? error.message : String(error)}`);
      }
    });
  }

  /**
   * Delete file from storage
   */
  async deleteFile(fileId: string, salonId: string, userId?: string): Promise<Result<void>> {
    const startTime = Date.now();

    return this.circuitBreaker.execute(async () => {
      try {
        this.logger.debug('Deleting file', {
          fileId,
          salonId,
          userId,
        });

        const filePath = this.generateFilePath(fileId, salonId);
        const url = `${this.baseUrl}/storage/v1/object/${this.config.bucketName}/${filePath}`;

        const response = await fetch(url, {
          method: 'DELETE',
          headers: this.headers,
        });

        if (!response.ok && response.status !== 404) {
          const errorText = await response.text();
          this.logger.error('File deletion failed', {
            fileId,
            status: response.status,
            error: errorText,
          });

          return Result.error(`Delete failed: ${response.status} ${errorText}`);
        }

        const duration = Date.now() - startTime;

        // Record metrics
        this.metrics.recordCounter('storage_deletes_total', 1, {
          status: 'success',
        });
        this.metrics.recordHistogram('storage_delete_duration_ms', duration);

        this.logger.debug('File deleted successfully', {
          fileId,
          duration,
        });

        return Result.success(undefined);
      } catch (error) {
        const duration = Date.now() - startTime;
        this.logger.error('File deletion error', {
          fileId,
          error: error instanceof Error ? error.message : String(error),
          duration,
        });

        this.metrics.recordCounter('storage_deletes_total', 1, {
          status: 'error',
        });

        return Result.error(`Delete failed: ${error instanceof Error ? error.message : String(error)}`);
      }
    });
  }

  /**
   * List files in storage
   */
  async listFiles(salonId: string, prefix?: string): Promise<Result<FileInfo[]>> {
    return this.circuitBreaker.execute(async () => {
      try {
        const folderPath = `salon_${salonId}/${prefix || ''}`;
        const url = `${this.baseUrl}/storage/v1/object/list/${this.config.bucketName}?prefix=${encodeURIComponent(folderPath)}`;

        const response = await fetch(url, {
          method: 'GET',
          headers: this.headers,
        });

        if (!response.ok) {
          return Result.error(`Failed to list files: ${response.status}`);
        }

        const files = await response.json();

        const fileInfos: FileInfo[] = files.map((file: any) => ({
          id: this.extractFileIdFromPath(file.name),
          name: file.name,
          size: file.metadata?.size || 0,
          contentType: file.metadata?.mimetype || 'application/octet-stream',
          url: `${this.baseUrl}/storage/v1/object/${this.config.bucketName}/${file.name}`,
          uploadedAt: new Date(file.created_at),
          salonId,
          metadata: file.metadata,
        }));

        return Result.success(fileInfos);
      } catch (error) {
        return Result.error(`Failed to list files: ${error instanceof Error ? error.message : String(error)}`);
      }
    });
  }

  /**
   * Generate signed URL for temporary access
   */
  async generateSignedUrl(fileId: string, expiresIn: number, salonId: string): Promise<Result<string>> {
    return this.circuitBreaker.execute(async () => {
      try {
        const filePath = this.generateFilePath(fileId, salonId);
        const url = `${this.baseUrl}/storage/v1/object/sign/${this.config.bucketName}/${filePath}`;

        const response = await fetch(url, {
          method: 'POST',
          headers: {
            ...this.headers,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ expiresIn }),
        });

        if (!response.ok) {
          return Result.error(`Failed to generate signed URL: ${response.status}`);
        }

        const result = await response.json();
        const signedUrl = `${this.baseUrl}/storage/v1${result.signedURL}`;

        return Result.success(signedUrl);
      } catch (error) {
        return Result.error(`Failed to generate signed URL: ${error instanceof Error ? error.message : String(error)}`);
      }
    });
  }

  /**
   * Validate file before upload
   */
  async validateFile(data: ArrayBuffer, contentType: string): Promise<Result<void>> {
    // Size validation
    if (data.byteLength > this.config.maxFileSize) {
      return Result.error(`File too large: ${data.byteLength} bytes (max: ${this.config.maxFileSize})`);
    }

    if (data.byteLength === 0) {
      return Result.error('File is empty');
    }

    // Content type validation
    if (!this.config.allowedMimeTypes.includes(contentType) && 
        !this.config.allowedMimeTypes.includes('*/*')) {
      return Result.error(`Content type not allowed: ${contentType}`);
    }

    // Image-specific validation
    if (contentType.startsWith('image/')) {
      if (data.byteLength > IMAGE_LIMITS.maxSizeBytes) {
        return Result.error(`Image too large: ${data.byteLength} bytes (max: ${IMAGE_LIMITS.maxSizeBytes})`);
      }

      // Basic image format validation
      const signature = this.getFileSignature(data);
      if (!this.isValidImageSignature(signature, contentType)) {
        return Result.error('Invalid image file or corrupted data');
      }
    }

    return Result.success(undefined);
  }

  /**
   * Health check for storage service
   */
  async healthCheck(): Promise<HealthCheck> {
    const startTime = Date.now();

    try {
      // Test bucket access
      const url = `${this.baseUrl}/storage/v1/bucket/${this.config.bucketName}`;
      const response = await fetch(url, {
        method: 'GET',
        headers: this.headers,
      });

      const responseTime = Date.now() - startTime;

      if (response.ok) {
        return {
          name: 'Storage Service',
          status: 'healthy',
          lastCheck: new Date(),
          responseTime,
          details: {
            bucket: this.config.bucketName,
            circuitBreakerState: this.circuitBreaker.getState(),
          },
        };
      } else {
        return {
          name: 'Storage Service',
          status: 'unhealthy',
          lastCheck: new Date(),
          responseTime,
          error: `HTTP ${response.status}: ${response.statusText}`,
        };
      }
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      return {
        name: 'Storage Service',
        status: 'unhealthy',
        lastCheck: new Date(),
        responseTime,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Process image for optimization
   */
  private async processImage(
    imageData: ArrayBuffer | Uint8Array, 
    options: ImageProcessingOptions
  ): Promise<Result<{ data: ArrayBuffer; contentType: string }>> {
    // This would integrate with an image processing library
    // For now, we'll return the original data
    // In production, you'd use sharp, canvas, or similar library
    
    this.logger.debug('Image processing placeholder - returning original', {
      originalSize: imageData.byteLength,
      options,
    });

    return Result.success({
      data: imageData instanceof ArrayBuffer ? imageData : imageData.buffer,
      contentType: options.format ? `image/${options.format}` : 'image/jpeg',
    });
  }

  /**
   * Generate unique file ID
   */
  private generateFileId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate file path with salon isolation
   */
  private generateFilePath(fileId: string, salonId: string, filename?: string): string {
    const sanitizedFilename = filename ? this.sanitizeFilename(filename) : fileId;
    return `salon_${salonId}/${sanitizedFilename}`;
  }

  /**
   * Sanitize filename for safe storage
   */
  private sanitizeFilename(filename: string): string {
    return filename
      .replace(/[^a-zA-Z0-9._-]/g, '_')
      .replace(/_{2,}/g, '_')
      .substring(0, 255);
  }

  /**
   * Extract file ID from storage path
   */
  private extractFileIdFromPath(path: string): string {
    const parts = path.split('/');
    return parts[parts.length - 1];
  }

  /**
   * Get file extension from content type
   */
  private getFileExtension(contentType: string): string {
    const extensionMap: Record<string, string> = {
      'image/jpeg': 'jpg',
      'image/png': 'png',
      'image/webp': 'webp',
      'image/gif': 'gif',
      'application/pdf': 'pdf',
      'text/plain': 'txt',
    };

    return extensionMap[contentType] || 'bin';
  }

  /**
   * Get file signature for validation
   */
  private getFileSignature(data: ArrayBuffer): string {
    const uint8Array = new Uint8Array(data.slice(0, 8));
    return Array.from(uint8Array)
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
  }

  /**
   * Validate image file signature
   */
  private isValidImageSignature(signature: string, contentType: string): boolean {
    const imageSignatures: Record<string, string[]> = {
      'image/jpeg': ['ffd8ff'],
      'image/png': ['89504e47'],
      'image/gif': ['47494638'],
      'image/webp': ['52494646'],
    };

    const validSignatures = imageSignatures[contentType] || [];
    return validSignatures.some(validSig => signature.startsWith(validSig));
  }
}