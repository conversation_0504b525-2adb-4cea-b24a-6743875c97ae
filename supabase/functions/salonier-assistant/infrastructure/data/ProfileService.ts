/**
 * Profile Service - User and salon profile management with multi-tenant security
 * Handles authentication context, salon configurations, and user preferences
 */

import { ILogger, IMetrics, Result, ServiceError } from '../shared/types.ts';
import { ISupabaseRepository } from './SupabaseRepository.ts';
import { ICacheRepository } from './CacheRepository.ts';

export interface UserProfile {
  id: string;
  email: string;
  name?: string;
  role: 'salon_owner' | 'colorist' | 'assistant';
  salonId: string;
  preferences: UserPreferences;
  permissions: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface SalonProfile {
  id: string;
  name: string;
  settings: SalonSettings;
  subscription: SubscriptionDetails;
  features: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserPreferences {
  language: 'es' | 'en';
  theme: 'light' | 'dark' | 'auto';
  notifications: NotificationPreferences;
  regionalConfig: RegionalConfig;
  aiSettings: AIPreferences;
}

export interface NotificationPreferences {
  email: boolean;
  push: boolean;
  inApp: boolean;
  types: string[];
}

export interface RegionalConfig {
  volumeUnit: string;
  weightUnit: string;
  developerTerminology: string;
  colorTerminology: string;
  maxDeveloperVolume: number;
  currencySymbol: string;
  measurementSystem: 'metric' | 'imperial';
  decimalSeparator: string;
  thousandsSeparator: string;
}

export interface AIPreferences {
  defaultModel: 'gpt-4o' | 'gpt-4o-mini' | 'gpt-3.5-turbo';
  promptTemplate: 'full' | 'optimized' | 'minimal';
  autoCache: boolean;
  explainFormulas: boolean;
  confidenceThreshold: number;
}

export interface SalonSettings {
  timezone: string;
  businessHours: BusinessHours;
  brandPreferences: string[];
  defaultTechniques: string[];
  qualityStandards: QualityStandards;
  integrations: IntegrationSettings;
}

export interface BusinessHours {
  [day: string]: {
    open: string;
    close: string;
    isOpen: boolean;
  };
}

export interface QualityStandards {
  requireConfidenceScore: number;
  mandatoryFormulaTesting: boolean;
  allowExperimentalTechniques: boolean;
  requireSupervisorApproval: string[];
}

export interface IntegrationSettings {
  inventory: {
    enabled: boolean;
    provider?: string;
    syncFrequency?: number;
  };
  pos: {
    enabled: boolean;
    provider?: string;
    webhookUrl?: string;
  };
  analytics: {
    enabled: boolean;
    provider?: string;
    trackingId?: string;
  };
}

export interface SubscriptionDetails {
  plan: 'free' | 'pro' | 'enterprise';
  status: 'active' | 'inactive' | 'cancelled' | 'expired';
  features: string[];
  limits: SubscriptionLimits;
  billingCycle: 'monthly' | 'yearly';
  nextBillingDate?: Date;
  cancelledAt?: Date;
}

export interface SubscriptionLimits {
  maxUsers: number;
  maxAIRequests: number;
  maxStorage: number;
  maxFormulas: number;
  features: string[];
}

export interface AuthContext {
  userId: string;
  salonId: string;
  role: string;
  permissions: string[];
  sessionId: string;
}

export interface IProfileService {
  getUserProfile(userId: string): Promise<Result<UserProfile>>;
  getSalonProfile(salonId: string): Promise<Result<SalonProfile>>;
  updateUserPreferences(userId: string, preferences: Partial<UserPreferences>): Promise<Result<UserProfile>>;
  updateSalonSettings(salonId: string, settings: Partial<SalonSettings>, updatedBy: string): Promise<Result<SalonProfile>>;
  validateAccess(userId: string, resource: string, action: string): Promise<Result<boolean>>;
  getAuthContext(authHeader: string): Promise<Result<AuthContext>>;
  checkSubscriptionLimits(salonId: string, resource: string): Promise<Result<boolean>>;
  getRegionalConfig(salonId: string): Promise<Result<RegionalConfig>>;
  cacheProfile(profile: UserProfile | SalonProfile): Promise<void>;
}

export class ProfileService implements IProfileService {
  private readonly profileCachePrefix = 'profile:';
  private readonly salonCachePrefix = 'salon:';
  private readonly cacheTTL = 15 * 60 * 1000; // 15 minutes

  constructor(
    private readonly repository: ISupabaseRepository,
    private readonly cache: ICacheRepository,
    private readonly logger: ILogger,
    private readonly metrics: IMetrics
  ) {
    this.logger.info('Profile Service initialized');
  }

  /**
   * Get user profile with caching
   */
  async getUserProfile(userId: string): Promise<Result<UserProfile>> {
    const startTime = Date.now();

    try {
      this.logger.debug('Fetching user profile', { userId });

      // Try cache first
      const cacheKey = `${this.profileCachePrefix}${userId}`;
      const cachedProfile = await this.cache.get<UserProfile>(cacheKey);

      if (cachedProfile) {
        this.metrics.recordCounter('profile_cache_hits', 1, { type: 'user' });
        this.logger.debug('User profile served from cache', { userId });
        return Result.success(cachedProfile);
      }

      // Fetch from database
      const profileResult = await this.repository.selectOne('profiles', { id: userId });
      
      if (!profileResult.success || !profileResult.data) {
        return Result.error('User profile not found');
      }

      const profile = this.mapToUserProfile(profileResult.data);

      // Cache the profile
      await this.cacheProfile(profile);

      const duration = Date.now() - startTime;
      this.metrics.recordHistogram('profile_fetch_duration_ms', duration, { type: 'user' });
      this.metrics.recordCounter('profile_cache_misses', 1, { type: 'user' });

      this.logger.debug('User profile fetched from database', { userId, duration });

      return Result.success(profile);
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Failed to fetch user profile', {
        userId,
        error: error instanceof Error ? error.message : String(error),
        duration,
      });

      this.metrics.recordCounter('profile_fetch_errors', 1, { type: 'user' });

      return Result.error(`Failed to fetch user profile: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get salon profile with caching
   */
  async getSalonProfile(salonId: string): Promise<Result<SalonProfile>> {
    const startTime = Date.now();

    try {
      this.logger.debug('Fetching salon profile', { salonId });

      // Try cache first
      const cacheKey = `${this.salonCachePrefix}${salonId}`;
      const cachedSalon = await this.cache.get<SalonProfile>(cacheKey);

      if (cachedSalon) {
        this.metrics.recordCounter('profile_cache_hits', 1, { type: 'salon' });
        this.logger.debug('Salon profile served from cache', { salonId });
        return Result.success(cachedSalon);
      }

      // Fetch from database
      const salonResult = await this.repository.selectOne('salons', { id: salonId });
      
      if (!salonResult.success || !salonResult.data) {
        return Result.error('Salon profile not found');
      }

      const salon = this.mapToSalonProfile(salonResult.data);

      // Cache the salon profile
      await this.cacheProfile(salon);

      const duration = Date.now() - startTime;
      this.metrics.recordHistogram('profile_fetch_duration_ms', duration, { type: 'salon' });
      this.metrics.recordCounter('profile_cache_misses', 1, { type: 'salon' });

      this.logger.debug('Salon profile fetched from database', { salonId, duration });

      return Result.success(salon);
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Failed to fetch salon profile', {
        salonId,
        error: error instanceof Error ? error.message : String(error),
        duration,
      });

      this.metrics.recordCounter('profile_fetch_errors', 1, { type: 'salon' });

      return Result.error(`Failed to fetch salon profile: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Update user preferences
   */
  async updateUserPreferences(
    userId: string, 
    preferences: Partial<UserPreferences>
  ): Promise<Result<UserProfile>> {
    try {
      this.logger.debug('Updating user preferences', { userId, preferences });

      // Get current profile
      const currentProfileResult = await this.getUserProfile(userId);
      if (!currentProfileResult.success) {
        return currentProfileResult;
      }

      const currentProfile = currentProfileResult.data!;
      const updatedPreferences = { ...currentProfile.preferences, ...preferences };

      // Update in database
      const updateResult = await this.repository.update(
        'profiles',
        { preferences: updatedPreferences, updated_at: new Date() },
        { id: userId }
      );

      if (!updateResult.success) {
        return Result.error('Failed to update user preferences');
      }

      // Clear cache to force refresh
      const cacheKey = `${this.profileCachePrefix}${userId}`;
      await this.cache.delete(cacheKey);

      // Return updated profile
      const updatedProfile = { ...currentProfile, preferences: updatedPreferences };

      this.metrics.recordCounter('profile_updates', 1, { type: 'user_preferences' });
      this.logger.info('User preferences updated', { userId });

      return Result.success(updatedProfile);
    } catch (error) {
      this.logger.error('Failed to update user preferences', {
        userId,
        error: error instanceof Error ? error.message : String(error),
      });

      return Result.error(`Failed to update preferences: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Update salon settings
   */
  async updateSalonSettings(
    salonId: string, 
    settings: Partial<SalonSettings>, 
    updatedBy: string
  ): Promise<Result<SalonProfile>> {
    try {
      this.logger.debug('Updating salon settings', { salonId, updatedBy });

      // Get current salon profile
      const currentSalonResult = await this.getSalonProfile(salonId);
      if (!currentSalonResult.success) {
        return currentSalonResult;
      }

      const currentSalon = currentSalonResult.data!;
      const updatedSettings = { ...currentSalon.settings, ...settings };

      // Update in database
      const updateResult = await this.repository.update(
        'salons',
        { 
          settings: updatedSettings, 
          updated_at: new Date(),
          updated_by: updatedBy,
        },
        { id: salonId }
      );

      if (!updateResult.success) {
        return Result.error('Failed to update salon settings');
      }

      // Clear cache to force refresh
      const cacheKey = `${this.salonCachePrefix}${salonId}`;
      await this.cache.delete(cacheKey);

      // Return updated salon profile
      const updatedSalon = { ...currentSalon, settings: updatedSettings };

      this.metrics.recordCounter('profile_updates', 1, { type: 'salon_settings' });
      this.logger.info('Salon settings updated', { salonId, updatedBy });

      return Result.success(updatedSalon);
    } catch (error) {
      this.logger.error('Failed to update salon settings', {
        salonId,
        error: error instanceof Error ? error.message : String(error),
      });

      return Result.error(`Failed to update salon settings: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Validate user access to resource
   */
  async validateAccess(userId: string, resource: string, action: string): Promise<Result<boolean>> {
    try {
      // Get user profile for permissions
      const userResult = await this.getUserProfile(userId);
      if (!userResult.success) {
        return Result.error('User not found');
      }

      const user = userResult.data!;

      // Check if user is active
      if (!user.isActive) {
        return Result.success(false);
      }

      // Check permissions
      const requiredPermission = `${resource}:${action}`;
      const hasPermission = user.permissions.includes(requiredPermission) || 
                           user.permissions.includes('*') ||
                           user.role === 'salon_owner';

      this.metrics.recordCounter('access_validations', 1, {
        resource,
        action,
        result: hasPermission ? 'allowed' : 'denied',
      });

      this.logger.debug('Access validation completed', {
        userId,
        resource,
        action,
        hasPermission,
        userRole: user.role,
      });

      return Result.success(hasPermission);
    } catch (error) {
      this.logger.error('Access validation failed', {
        userId,
        resource,
        action,
        error: error instanceof Error ? error.message : String(error),
      });

      return Result.error(`Access validation failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get authentication context from header
   */
  async getAuthContext(authHeader: string): Promise<Result<AuthContext>> {
    try {
      // Parse JWT token (simplified - in production, use proper JWT library)
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return Result.error('Invalid authorization header');
      }

      const token = authHeader.substring(7);
      
      // This would decode and validate the JWT token
      // For now, we'll extract basic info (placeholder implementation)
      const payload = this.parseJWTPayload(token);
      
      if (!payload.sub || !payload.salon_id) {
        return Result.error('Invalid token payload');
      }

      // Get user profile for additional context
      const userResult = await this.getUserProfile(payload.sub);
      if (!userResult.success) {
        return Result.error('User not found');
      }

      const user = userResult.data!;

      const authContext: AuthContext = {
        userId: user.id,
        salonId: user.salonId,
        role: user.role,
        permissions: user.permissions,
        sessionId: payload.session_id || 'unknown',
      };

      this.logger.debug('Auth context created', {
        userId: authContext.userId,
        salonId: authContext.salonId,
        role: authContext.role,
      });

      return Result.success(authContext);
    } catch (error) {
      this.logger.error('Failed to get auth context', {
        error: error instanceof Error ? error.message : String(error),
      });

      return Result.error(`Auth context creation failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Check subscription limits for resource usage
   */
  async checkSubscriptionLimits(salonId: string, resource: string): Promise<Result<boolean>> {
    try {
      const salonResult = await this.getSalonProfile(salonId);
      if (!salonResult.success) {
        return Result.error('Salon not found');
      }

      const salon = salonResult.data!;
      const subscription = salon.subscription;

      // Check if subscription is active
      if (subscription.status !== 'active') {
        return Result.success(false);
      }

      // Check resource-specific limits
      switch (resource) {
        case 'ai_requests':
          // This would check current usage against limits
          // For now, assume within limits for active subscriptions
          return Result.success(true);
          
        case 'storage':
          return Result.success(true);
          
        case 'formulas':
          return Result.success(true);
          
        default:
          // Unknown resource, allow by default
          return Result.success(true);
      }
    } catch (error) {
      this.logger.error('Subscription limit check failed', {
        salonId,
        resource,
        error: error instanceof Error ? error.message : String(error),
      });

      return Result.error(`Subscription check failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get regional configuration for a salon
   */
  async getRegionalConfig(salonId: string): Promise<Result<RegionalConfig>> {
    try {
      const salonResult = await this.getSalonProfile(salonId);
      if (!salonResult.success) {
        return Result.error('Salon not found');
      }

      // Get from first active user's preferences or use salon defaults
      const usersResult = await this.repository.select('profiles', { salon_id: salonId, is_active: true });
      
      let regionalConfig: RegionalConfig;

      if (usersResult.success && usersResult.data && usersResult.data.length > 0) {
        const firstUser = this.mapToUserProfile(usersResult.data[0]);
        regionalConfig = firstUser.preferences.regionalConfig;
      } else {
        // Default regional config
        regionalConfig = {
          volumeUnit: 'ml',
          weightUnit: 'g',
          developerTerminology: 'oxidante',
          colorTerminology: 'tinte',
          maxDeveloperVolume: 40,
          currencySymbol: '€',
          measurementSystem: 'metric',
          decimalSeparator: ',',
          thousandsSeparator: '.',
        };
      }

      return Result.success(regionalConfig);
    } catch (error) {
      return Result.error(`Failed to get regional config: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Cache profile data
   */
  async cacheProfile(profile: UserProfile | SalonProfile): Promise<void> {
    try {
      let cacheKey: string;
      
      if ('email' in profile) {
        // User profile
        cacheKey = `${this.profileCachePrefix}${profile.id}`;
      } else {
        // Salon profile
        cacheKey = `${this.salonCachePrefix}${profile.id}`;
      }

      await this.cache.set(cacheKey, profile, this.cacheTTL);
    } catch (error) {
      this.logger.warn('Failed to cache profile', {
        profileId: profile.id,
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  /**
   * Map database row to UserProfile
   */
  private mapToUserProfile(row: any): UserProfile {
    return {
      id: row.id,
      email: row.email,
      name: row.name,
      role: row.role,
      salonId: row.salon_id,
      preferences: row.preferences || this.getDefaultUserPreferences(),
      permissions: row.permissions || [],
      isActive: row.is_active ?? true,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
    };
  }

  /**
   * Map database row to SalonProfile
   */
  private mapToSalonProfile(row: any): SalonProfile {
    return {
      id: row.id,
      name: row.name,
      settings: row.settings || this.getDefaultSalonSettings(),
      subscription: row.subscription || this.getDefaultSubscription(),
      features: row.features || [],
      isActive: row.is_active ?? true,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
    };
  }

  /**
   * Parse JWT payload (simplified)
   */
  private parseJWTPayload(token: string): any {
    try {
      // This is a simplified JWT parsing - in production, use a proper JWT library
      const parts = token.split('.');
      if (parts.length !== 3) {
        throw new Error('Invalid JWT format');
      }

      const payload = JSON.parse(atob(parts[1]));
      return payload;
    } catch (error) {
      throw new Error('Invalid JWT token');
    }
  }

  /**
   * Get default user preferences
   */
  private getDefaultUserPreferences(): UserPreferences {
    return {
      language: 'es',
      theme: 'light',
      notifications: {
        email: true,
        push: true,
        inApp: true,
        types: ['formulas', 'updates', 'tips'],
      },
      regionalConfig: {
        volumeUnit: 'ml',
        weightUnit: 'g',
        developerTerminology: 'oxidante',
        colorTerminology: 'tinte',
        maxDeveloperVolume: 40,
        currencySymbol: '€',
        measurementSystem: 'metric',
        decimalSeparator: ',',
        thousandsSeparator: '.',
      },
      aiSettings: {
        defaultModel: 'gpt-4o-mini',
        promptTemplate: 'optimized',
        autoCache: true,
        explainFormulas: true,
        confidenceThreshold: 0.8,
      },
    };
  }

  /**
   * Get default salon settings
   */
  private getDefaultSalonSettings(): SalonSettings {
    return {
      timezone: 'Europe/Madrid',
      businessHours: {
        monday: { open: '09:00', close: '18:00', isOpen: true },
        tuesday: { open: '09:00', close: '18:00', isOpen: true },
        wednesday: { open: '09:00', close: '18:00', isOpen: true },
        thursday: { open: '09:00', close: '18:00', isOpen: true },
        friday: { open: '09:00', close: '18:00', isOpen: true },
        saturday: { open: '10:00', close: '16:00', isOpen: true },
        sunday: { open: '10:00', close: '16:00', isOpen: false },
      },
      brandPreferences: ['Wella', 'L\'Oréal', 'Matrix'],
      defaultTechniques: ['Balayage', 'Highlights', 'Root Touch-up'],
      qualityStandards: {
        requireConfidenceScore: 0.7,
        mandatoryFormulaTesting: false,
        allowExperimentalTechniques: true,
        requireSupervisorApproval: ['bleaching', 'corrective_color'],
      },
      integrations: {
        inventory: { enabled: false },
        pos: { enabled: false },
        analytics: { enabled: true, provider: 'internal' },
      },
    };
  }

  /**
   * Get default subscription
   */
  private getDefaultSubscription(): SubscriptionDetails {
    return {
      plan: 'free',
      status: 'active',
      features: ['basic_formulas', 'image_analysis'],
      limits: {
        maxUsers: 1,
        maxAIRequests: 100,
        maxStorage: 100 * 1024 * 1024, // 100MB
        maxFormulas: 50,
        features: ['basic_formulas'],
      },
      billingCycle: 'monthly',
    };
  }
}