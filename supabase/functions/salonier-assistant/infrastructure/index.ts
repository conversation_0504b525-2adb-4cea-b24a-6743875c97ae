/**
 * Infrastructure Layer Index - Central export for all infrastructure services
 * Production-ready infrastructure services for the Salonier Assistant Edge Function
 */

// Shared types and utilities
export * from './shared/types.ts';
export * from './shared/CircuitBreaker.ts';

// AI Services
export * from './ai/OpenAIService.ts';
export * from './ai/AIModelRouter.ts';
export * from './ai/PromptEngine.ts';
export * from './ai/ResponseValidator.ts';

// Data Services
export * from './data/SupabaseRepository.ts';
export * from './data/CacheRepository.ts';
export * from './data/StorageService.ts';
export * from './data/ProfileService.ts';

// External Services
export * from './external/ColorimetryService.ts';
export * from './external/FormulaValidator.ts';
export * from './external/BrandExpertise.ts';

// Infrastructure Factory for dependency injection
import { OpenAIService } from './ai/OpenAIService.ts';
import { AIModelRouter } from './ai/AIModelRouter.ts';
import { PromptEngine } from './ai/PromptEngine.ts';
import { ResponseValidator } from './ai/ResponseValidator.ts';
import { SupabaseRepository } from './data/SupabaseRepository.ts';
import { CacheRepository } from './data/CacheRepository.ts';
import { StorageService } from './data/StorageService.ts';
import { ProfileService } from './data/ProfileService.ts';
import { ColorimetryService } from './external/ColorimetryService.ts';
import { FormulaValidator } from './external/FormulaValidator.ts';
import { BrandExpertise } from './external/BrandExpertise.ts';

import { 
  InfrastructureConfig, 
  ILogger, 
  IMetrics,
  ServiceDependencies,
  EnvironmentVariables,
  Result
} from './shared/types.ts';

/**
 * Infrastructure Service Container
 * Manages all infrastructure services with proper dependency injection
 */
export class InfrastructureContainer {
  private services: Map<string, any> = new Map();
  private initialized = false;

  constructor(
    private config: InfrastructureConfig,
    private logger: ILogger,
    private metrics: IMetrics
  ) {}

  /**
   * Initialize all infrastructure services
   */
  async initialize(): Promise<Result<void>> {
    if (this.initialized) {
      return Result.success(undefined);
    }

    try {
      this.logger.info('Initializing infrastructure services...');

      // Initialize core services first
      await this.initializeCoreServices();

      // Initialize AI services
      await this.initializeAIServices();

      // Initialize data services
      await this.initializeDataServices();

      // Initialize external services
      await this.initializeExternalServices();

      // Perform health checks
      await this.performHealthChecks();

      this.initialized = true;
      this.logger.info('Infrastructure services initialized successfully');
      
      return Result.success(undefined);
    } catch (error) {
      this.logger.error('Failed to initialize infrastructure services', {
        error: error instanceof Error ? error.message : String(error),
      });
      
      return Result.error(`Infrastructure initialization failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get service by type
   */
  getService<T>(serviceType: string): T {
    if (!this.initialized) {
      throw new Error('Infrastructure container not initialized');
    }

    const service = this.services.get(serviceType);
    if (!service) {
      throw new Error(`Service not found: ${serviceType}`);
    }

    return service as T;
  }

  /**
   * Check if service is available
   */
  hasService(serviceType: string): boolean {
    return this.services.has(serviceType);
  }

  /**
   * Get all registered services
   */
  getRegisteredServices(): string[] {
    return Array.from(this.services.keys());
  }

  /**
   * Shutdown all services
   */
  async shutdown(): Promise<void> {
    this.logger.info('Shutting down infrastructure services...');

    // Perform cleanup for services that need it
    for (const [serviceType, service] of this.services.entries()) {
      try {
        if (service.shutdown && typeof service.shutdown === 'function') {
          await service.shutdown();
          this.logger.debug(`Service shut down: ${serviceType}`);
        }
      } catch (error) {
        this.logger.error(`Error shutting down service: ${serviceType}`, {
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }

    this.services.clear();
    this.initialized = false;
    
    this.logger.info('Infrastructure services shut down');
  }

  /**
   * Initialize core services
   */
  private async initializeCoreServices(): Promise<void> {
    // Cache Repository
    const cacheRepository = new CacheRepository(
      this.config.cache,
      this.logger,
      this.metrics
    );
    this.services.set('cache', cacheRepository);

    // Supabase Repository
    const supabaseRepository = new SupabaseRepository(
      this.config.database,
      this.logger,
      this.metrics
    );
    this.services.set('database', supabaseRepository);

    this.logger.debug('Core services initialized');
  }

  /**
   * Initialize AI services
   */
  private async initializeAIServices(): Promise<void> {
    const cache = this.getService<CacheRepository>('cache');

    // OpenAI Service
    const openaiService = new OpenAIService(
      {
        apiKey: process.env.OPENAI_API_KEY || '',
        timeout: 30000,
        maxRetries: 3,
      },
      this.logger,
      this.metrics
    );
    this.services.set('openai', openaiService);

    // AI Model Router
    const modelRouter = new AIModelRouter(
      this.logger,
      this.metrics
    );
    this.services.set('modelRouter', modelRouter);

    // Prompt Engine
    const promptEngine = new PromptEngine(
      this.logger,
      this.metrics
    );
    this.services.set('promptEngine', promptEngine);

    // Response Validator
    const responseValidator = new ResponseValidator(
      this.logger,
      this.metrics
    );
    this.services.set('responseValidator', responseValidator);

    this.logger.debug('AI services initialized');
  }

  /**
   * Initialize data services
   */
  private async initializeDataServices(): Promise<void> {
    const cache = this.getService<CacheRepository>('cache');
    const database = this.getService<SupabaseRepository>('database');

    // Storage Service
    const storageService = new StorageService(
      {
        supabaseUrl: this.config.database.url,
        serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY || this.config.database.apiKey,
        bucketName: 'salon-files',
        maxFileSize: 4 * 1024 * 1024, // 4MB
        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp', 'application/pdf'],
        enableCompression: true,
        imageOptimization: true,
      },
      this.logger,
      this.metrics
    );
    this.services.set('storage', storageService);

    // Profile Service
    const profileService = new ProfileService(
      database,
      cache,
      this.logger,
      this.metrics
    );
    this.services.set('profile', profileService);

    this.logger.debug('Data services initialized');
  }

  /**
   * Initialize external services
   */
  private async initializeExternalServices(): Promise<void> {
    const cache = this.getService<CacheRepository>('cache');

    // Colorimetry Service
    const colorimetryService = new ColorimetryService(
      cache,
      this.logger,
      this.metrics
    );
    this.services.set('colorimetry', colorimetryService);

    // Formula Validator
    const formulaValidator = new FormulaValidator(
      colorimetryService,
      cache,
      this.logger,
      this.metrics
    );
    this.services.set('formulaValidator', formulaValidator);

    // Brand Expertise
    const brandExpertise = new BrandExpertise(
      cache,
      this.logger,
      this.metrics
    );
    this.services.set('brandExpertise', brandExpertise);

    this.logger.debug('External services initialized');
  }

  /**
   * Perform health checks on all services
   */
  private async performHealthChecks(): Promise<void> {
    this.logger.info('Performing infrastructure health checks...');

    const healthCheckPromises: Promise<void>[] = [];

    for (const [serviceType, service] of this.services.entries()) {
      if (service.healthCheck && typeof service.healthCheck === 'function') {
        healthCheckPromises.push(
          service.healthCheck()
            .then((health: any) => {
              this.logger.debug(`Health check passed: ${serviceType}`, {
                status: health.status,
                responseTime: health.responseTime,
              });
              
              this.metrics.recordGauge(`infrastructure_health_${serviceType}`, 
                health.status === 'healthy' ? 1 : 0
              );
            })
            .catch((error: Error) => {
              this.logger.warn(`Health check failed: ${serviceType}`, {
                error: error.message,
              });
              
              this.metrics.recordGauge(`infrastructure_health_${serviceType}`, 0);
            })
        );
      }
    }

    await Promise.allSettled(healthCheckPromises);
    
    this.logger.info('Infrastructure health checks completed');
  }
}

/**
 * Infrastructure Factory
 * Creates and configures infrastructure services based on environment
 */
export class InfrastructureFactory {
  /**
   * Create infrastructure configuration from environment variables
   */
  static createConfigFromEnv(env: EnvironmentVariables): InfrastructureConfig {
    return {
      database: {
        url: env.SUPABASE_URL,
        apiKey: env.SUPABASE_ANON_KEY,
        maxConnections: 10,
        connectionTimeout: 30000,
        retryAttempts: 3,
      },
      cache: {
        provider: 'memory',
        defaultTTL: parseInt(env.CACHE_TTL_DEFAULT || '900000'), // 15 minutes
        maxMemoryMB: 64,
        keyPrefix: 'salonier',
      },
      logging: {
        level: (env.LOG_LEVEL as any) || 'info',
        format: 'json',
        includeTimestamp: true,
        includeLevel: true,
      },
      metrics: {
        provider: 'console',
        namespace: 'salonier_assistant',
        flushInterval: 60000,
      },
      environment: env.ENVIRONMENT,
    };
  }

  /**
   * Create infrastructure container with logger and metrics
   */
  static createContainer(
    config: InfrastructureConfig,
    logger: ILogger,
    metrics: IMetrics
  ): InfrastructureContainer {
    return new InfrastructureContainer(config, logger, metrics);
  }

  /**
   * Create a simple console logger
   */
  static createConsoleLogger(level: string = 'info'): ILogger {
    const levels = ['debug', 'info', 'warn', 'error'];
    const logLevel = levels.indexOf(level.toLowerCase());

    return {
      debug: (message: string, context?: Record<string, any>) => {
        if (logLevel <= 0) console.debug(`[DEBUG] ${message}`, context || '');
      },
      info: (message: string, context?: Record<string, any>) => {
        if (logLevel <= 1) console.info(`[INFO] ${message}`, context || '');
      },
      warn: (message: string, context?: Record<string, any>) => {
        if (logLevel <= 2) console.warn(`[WARN] ${message}`, context || '');
      },
      error: (message: string, context?: Record<string, any>) => {
        if (logLevel <= 3) console.error(`[ERROR] ${message}`, context || '');
      },
    };
  }

  /**
   * Create a simple console metrics recorder
   */
  static createConsoleMetrics(): IMetrics {
    return {
      recordCounter: (name: string, value: number, tags?: Record<string, string>) => {
        console.debug(`[METRIC] Counter: ${name} = ${value}`, tags);
      },
      recordGauge: (name: string, value: number, tags?: Record<string, string>) => {
        console.debug(`[METRIC] Gauge: ${name} = ${value}`, tags);
      },
      recordHistogram: (name: string, value: number, tags?: Record<string, string>) => {
        console.debug(`[METRIC] Histogram: ${name} = ${value}`, tags);
      },
      recordTimer: (name: string, duration: number, tags?: Record<string, string>) => {
        console.debug(`[METRIC] Timer: ${name} = ${duration}ms`, tags);
      },
    };
  }

  /**
   * Extract environment variables from Deno or process
   */
  static getEnvironmentVariables(): EnvironmentVariables {
    // For Deno environment (Edge Functions)
    if (typeof Deno !== 'undefined') {
      return {
        OPENAI_API_KEY: Deno.env.get('OPENAI_API_KEY') || '',
        SUPABASE_URL: Deno.env.get('SUPABASE_URL') || '',
        SUPABASE_ANON_KEY: Deno.env.get('SUPABASE_ANON_KEY') || '',
        SUPABASE_SERVICE_ROLE_KEY: Deno.env.get('SUPABASE_SERVICE_ROLE_KEY'),
        ENVIRONMENT: (Deno.env.get('ENVIRONMENT') as any) || 'development',
        LOG_LEVEL: Deno.env.get('LOG_LEVEL') as any,
        CACHE_TTL_DEFAULT: Deno.env.get('CACHE_TTL_DEFAULT'),
        MAX_REQUEST_TIMEOUT: Deno.env.get('MAX_REQUEST_TIMEOUT'),
        RATE_LIMIT_REQUESTS: Deno.env.get('RATE_LIMIT_REQUESTS'),
        RATE_LIMIT_WINDOW_MS: Deno.env.get('RATE_LIMIT_WINDOW_MS'),
      };
    }

    // For Node.js environment (testing)
    return {
      OPENAI_API_KEY: process.env.OPENAI_API_KEY || '',
      SUPABASE_URL: process.env.SUPABASE_URL || '',
      SUPABASE_ANON_KEY: process.env.SUPABASE_ANON_KEY || '',
      SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY,
      ENVIRONMENT: (process.env.ENVIRONMENT as any) || 'development',
      LOG_LEVEL: process.env.LOG_LEVEL as any,
      CACHE_TTL_DEFAULT: process.env.CACHE_TTL_DEFAULT,
      MAX_REQUEST_TIMEOUT: process.env.MAX_REQUEST_TIMEOUT,
      RATE_LIMIT_REQUESTS: process.env.RATE_LIMIT_REQUESTS,
      RATE_LIMIT_WINDOW_MS: process.env.RATE_LIMIT_WINDOW_MS,
    };
  }

  /**
   * Create a complete infrastructure setup
   * Ready-to-use factory method for Edge Functions
   */
  static async createComplete(): Promise<Result<InfrastructureContainer>> {
    try {
      const env = this.getEnvironmentVariables();
      const config = this.createConfigFromEnv(env);
      const logger = this.createConsoleLogger(env.LOG_LEVEL || 'info');
      const metrics = this.createConsoleMetrics();

      const container = this.createContainer(config, logger, metrics);
      
      const initResult = await container.initialize();
      if (!initResult.success) {
        return initResult;
      }

      return Result.success(container);
    } catch (error) {
      return Result.error(`Failed to create infrastructure: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}

/**
 * Global infrastructure instance for singleton usage
 */
let globalInfrastructure: InfrastructureContainer | null = null;

/**
 * Get or create global infrastructure instance
 */
export async function getInfrastructure(): Promise<InfrastructureContainer> {
  if (!globalInfrastructure) {
    const result = await InfrastructureFactory.createComplete();
    if (!result.success) {
      throw new Error(`Failed to initialize infrastructure: ${result.error}`);
    }
    globalInfrastructure = result.data!;
  }

  return globalInfrastructure;
}

/**
 * Reset global infrastructure (for testing)
 */
export async function resetInfrastructure(): Promise<void> {
  if (globalInfrastructure) {
    await globalInfrastructure.shutdown();
    globalInfrastructure = null;
  }
}

// Export service interfaces for type checking
export type {
  IAIService,
  IModelRouter,
  IPromptEngine,
  IResponseValidator,
  ISupabaseRepository,
  ICacheRepository,
  IStorageService,
  IProfileService,
  IColorimetryService,
  IFormulaValidator,
  IBrandExpertise,
} from './ai/OpenAIService.ts';

export type { IAIService } from './ai/OpenAIService.ts';
export type { IModelRouter } from './ai/AIModelRouter.ts';
export type { IPromptEngine } from './ai/PromptEngine.ts';
export type { IResponseValidator } from './ai/ResponseValidator.ts';
export type { ISupabaseRepository } from './data/SupabaseRepository.ts';
export type { ICacheRepository } from './data/CacheRepository.ts';
export type { IStorageService } from './data/StorageService.ts';
export type { IProfileService } from './data/ProfileService.ts';
export type { IColorimetryService } from './external/ColorimetryService.ts';
export type { IFormulaValidator } from './external/FormulaValidator.ts';
export type { IBrandExpertise } from './external/BrandExpertise.ts';