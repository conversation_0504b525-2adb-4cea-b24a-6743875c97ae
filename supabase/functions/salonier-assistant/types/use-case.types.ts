/**
 * Common types for AI Use Cases
 */

export interface Result<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface AIUsage {
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
}

export interface AIRequestMetrics {
  model: string;
  tokens: number;
  cost: number;
  latency: number;
  success: boolean;
  errorType?: string;
}

// Hair Diagnosis Types
export interface DiagnoseImageRequest {
  imageUrl?: string;
  imageBase64?: string;
  diagnosis?: any;
  salonId: string;
}

export interface HairDiagnosis {
  averageLevel: number;
  overallTone: string;
  overallReflect: string;
  hairThickness: string;
  hairDensity: string;
  overallCondition: string;
  zoneAnalysis: {
    roots: ZoneAnalysis;
    mids: ZoneAnalysis;
    ends: ZoneAnalysis;
  };
  detectedChemicalProcess: string;
  estimatedLastProcessDate: string;
  detectedHomeRemedies: string[];
  detectedRisks: string[];
  recommendations: string[];
  overallConfidence: number;
  serviceComplexity: string;
  estimatedTime: number;
  elasticity?: string;
  resistance?: string;
  cuticleState?: string;
  grayType?: string;
  grayPattern?: string;
  unwantedTone?: string;
}

export interface ZoneAnalysis {
  level: number;
  tone: string;
  reflect: string;
  confidence: number;
  condition?: string;
  porosity?: string;
  grayPercentage?: number;
}

// Desired Look Analysis Types
export interface AnalyzeDesiredLookRequest {
  imageUrl?: string;
  imageBase64?: string;
  currentLevel?: number;
  diagnosis?: HairDiagnosis;
  salonId: string;
}

export interface DesiredLookAnalysis {
  targetLevel: number;
  targetTone: string;
  targetReflect: string;
  viability: {
    achievable: boolean;
    sessionCount: number;
    riskLevel: 'low' | 'medium' | 'high';
    concerns: string[];
  };
  recommendations: string[];
  colorimetryValidation: {
    valid: boolean;
    warnings: string[];
  };
  estimatedTime: number;
  requiredProducts: string[];
}

// Formula Generation Types
export interface GenerateFormulaRequest {
  currentDiagnosis: HairDiagnosis;
  desiredLook: DesiredLookAnalysis;
  availableProducts?: ProductInventory[];
  clientPreferences?: ClientPreferences;
  salonId: string;
}

export interface ProductInventory {
  brand: string;
  line: string;
  type: string;
  shade: string;
  level?: number;
  reflect?: string;
  volume?: number;
}

export interface ClientPreferences {
  allergies?: string[];
  preferredBrands?: string[];
  previousReactions?: string[];
  maintenanceLevel?: 'low' | 'medium' | 'high';
}

export interface HairFormula {
  technique: string;
  processingTime: number;
  applicationMethod: string;
  formula: {
    base: string;
    additives: string[];
    mixingRatio: string;
    developer: {
      volume: number;
      amount: string;
    };
  };
  stepByStep: string[];
  warnings: string[];
  expectedResult: string;
  maintenanceInstructions: string[];
  costEstimate?: number;
}

// Chat Assistant Types
export interface ChatAssistantRequest {
  message: string;
  conversationHistory?: ChatMessage[];
  context?: {
    currentService?: any;
    clientProfile?: any;
    salonInfo?: any;
  };
  salonId: string;
  userId: string;
}

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  metadata?: any;
}

export interface ChatAssistantResponse {
  message: string;
  intent: string;
  confidence: number;
  suggestedActions?: string[];
  followUpQuestions?: string[];
  requiresHumanIntervention: boolean;
}

// Cache Types
export interface CacheEntry {
  result: any;
  timestamp: number;
  ttl: number;
  hits: number;
  tokens: number;
  cost: number;
}

export interface CacheConfig {
  ttl: number;
  maxSize: number;
  enabled: boolean;
}

// AI Service Configuration
export interface AIServiceConfig {
  models: {
    primary: string;
    fallback: string[];
  };
  maxTokens: number;
  temperature: number;
  timeout: number;
  retries: number;
}