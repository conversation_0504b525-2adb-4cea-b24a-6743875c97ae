/**
 * Service interfaces for dependency injection
 */

import { 
  AIUsage, 
  AIRequestMetrics, 
  CacheEntry, 
  CacheConfig,
  AIServiceConfig 
} from '../types/use-case.types.ts';

export interface IAIService {
  callOpenAI(params: OpenAIParams): Promise<OpenAIResponse>;
  calculateCost(model: string, inputTokens: number, outputTokens: number): number;
  determineComplexity(context?: any): 'low' | 'medium' | 'high';
  selectOptimalModel(complexity: 'low' | 'medium' | 'high', hasVision?: boolean): string;
}

export interface OpenAIParams {
  model: string;
  messages: any[];
  maxTokens: number;
  temperature: number;
  responseFormat?: { type: string };
  seed?: number;
}

export interface OpenAIResponse {
  choices: Array<{
    message: {
      content: string;
      refusal?: string;
    };
    finish_reason: string;
  }>;
  usage: AIUsage;
  model: string;
}

export interface ICacheService {
  get(salonId: string, task: string, inputHash: string): Promise<any | null>;
  set(
    salonId: string, 
    task: string, 
    inputHash: string,
    payload: any,
    result: any,
    model: string,
    tokens: number,
    cost: number,
    ttl?: number
  ): Promise<void>;
  generateKey(task: string, payload: any): string;
  cleanup(salonId: string): Promise<void>;
  getStats(salonId: string): Promise<any>;
}

export interface IImageProcessor {
  validateImage(imageUrl?: string, imageBase64?: string): Promise<void>;
  convertToDataUrl(imageUrl?: string, imageBase64?: string): Promise<string>;
  validateImageSize(base64: string, maxSizeMB?: number): boolean;
  validateBase64Format(base64: string): boolean;
}

export interface IValidator {
  validateRequest<T>(request: T, schema: ValidationSchema): ValidationResult;
  validateAIResponse(response: any, expectedFields: string[]): ValidationResult;
  sanitizeForLog(data: any): any;
}

export interface ValidationSchema {
  required: string[];
  optional?: string[];
  types?: Record<string, string>;
}

export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings?: string[];
}

export interface ILogger {
  debug(message: string, data?: any): void;
  info(message: string, data?: any): void;
  warn(message: string, data?: any): void;
  error(message: string, error?: any): void;
  diagnostic(message: string, data?: any): void;
  trackMetrics(metrics: AIRequestMetrics): Promise<void>;
}

export interface IPromptTemplateService {
  getDiagnosisPrompt(complexity: 'simple' | 'full', language: string): string;
  getDesiredLookPrompt(complexity: 'simple' | 'full', language: string): string;
  getFormulaPrompt(complexity: 'simple' | 'full', language: string): string;
  getChatPrompt(intent: string, language: string): string;
  optimizePrompt(prompt: string, maxTokens?: number): string;
}

export interface IRetryService {
  retryWithBackoff<T>(
    operation: () => Promise<T>,
    maxRetries?: number,
    baseDelay?: number
  ): Promise<T>;
}

export interface IFallbackService {
  executeWithFallback<T>(
    primaryOperation: () => Promise<T>,
    fallbackOperations: Array<() => Promise<T>>,
    context: any
  ): Promise<T>;
}