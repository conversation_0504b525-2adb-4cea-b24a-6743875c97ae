/**
 * Configuration Management
 * 
 * Centralized configuration for the Salonier Assistant Edge Function.
 * Handles environment variable validation, security settings, and
 * deployment-specific configurations with proper defaults.
 */

export interface AppConfig {
  // API Configuration
  openai: {
    apiKey: string;
    timeout: number;
    maxRetries: number;
    defaultModel: string;
    fallbackModel: string;
  };

  // Database Configuration
  database: {
    url: string;
    apiKey: string;
    serviceRoleKey?: string;
    maxConnections: number;
    connectionTimeout: number;
  };

  // Cache Configuration
  cache: {
    defaultTTL: number;
    maxMemoryMB: number;
    keyPrefix: string;
  };

  // Security Configuration
  security: {
    corsOrigins: string[];
    rateLimiting: {
      requests: number;
      windowMs: number;
    };
    authentication: {
      required: boolean;
      allowAnonymous: boolean;
    };
  };

  // Performance Configuration
  performance: {
    requestTimeout: number;
    maxPayloadSize: number;
    enableCompression: boolean;
  };

  // Monitoring Configuration
  monitoring: {
    logLevel: 'debug' | 'info' | 'warn' | 'error';
    enableMetrics: boolean;
    enableTracing: boolean;
  };

  // Environment
  environment: 'development' | 'staging' | 'production';
}

/**
 * Environment Variables Interface
 */
interface EnvironmentVariables {
  // Required variables
  OPENAI_API_KEY: string;
  SUPABASE_URL: string;
  SUPABASE_ANON_KEY: string;

  // Optional variables with defaults
  SUPABASE_SERVICE_ROLE_KEY?: string;
  ENVIRONMENT?: string;
  LOG_LEVEL?: string;
  CACHE_TTL_DEFAULT?: string;
  MAX_REQUEST_TIMEOUT?: string;
  RATE_LIMIT_REQUESTS?: string;
  RATE_LIMIT_WINDOW_MS?: string;
  CORS_ORIGINS?: string;
  ENABLE_METRICS?: string;
  ENABLE_TRACING?: string;
}

/**
 * Configuration Builder
 */
export class ConfigBuilder {
  private config: Partial<AppConfig> = {};

  /**
   * Load configuration from environment variables
   */
  static fromEnvironment(): AppConfig {
    const env = ConfigBuilder.getEnvironmentVariables();
    
    // Validate required environment variables
    ConfigBuilder.validateRequiredVariables(env);

    return {
      openai: {
        apiKey: env.OPENAI_API_KEY,
        timeout: 30000,
        maxRetries: 3,
        defaultModel: 'gpt-4o-mini',
        fallbackModel: 'gpt-3.5-turbo',
      },

      database: {
        url: env.SUPABASE_URL,
        apiKey: env.SUPABASE_ANON_KEY,
        serviceRoleKey: env.SUPABASE_SERVICE_ROLE_KEY,
        maxConnections: 10,
        connectionTimeout: 30000,
      },

      cache: {
        defaultTTL: parseInt(env.CACHE_TTL_DEFAULT || '900000'), // 15 minutes
        maxMemoryMB: 64,
        keyPrefix: 'salonier_v2',
      },

      security: {
        corsOrigins: env.CORS_ORIGINS ? env.CORS_ORIGINS.split(',') : ['*'],
        rateLimiting: {
          requests: parseInt(env.RATE_LIMIT_REQUESTS || '100'),
          windowMs: parseInt(env.RATE_LIMIT_WINDOW_MS || '60000'), // 1 minute
        },
        authentication: {
          required: true,
          allowAnonymous: false,
        },
      },

      performance: {
        requestTimeout: parseInt(env.MAX_REQUEST_TIMEOUT || '30000'),
        maxPayloadSize: 4 * 1024 * 1024, // 4MB
        enableCompression: true,
      },

      monitoring: {
        logLevel: (env.LOG_LEVEL as any) || 'info',
        enableMetrics: env.ENABLE_METRICS !== 'false',
        enableTracing: env.ENABLE_TRACING === 'true',
      },

      environment: (env.ENVIRONMENT as any) || 'development',
    };
  }

  /**
   * Get environment variables from Deno or Node.js
   */
  private static getEnvironmentVariables(): EnvironmentVariables {
    // Deno environment (Edge Functions)
    if (typeof Deno !== 'undefined') {
      return {
        OPENAI_API_KEY: Deno.env.get('OPENAI_API_KEY') || '',
        SUPABASE_URL: Deno.env.get('SUPABASE_URL') || '',
        SUPABASE_ANON_KEY: Deno.env.get('SUPABASE_ANON_KEY') || '',
        SUPABASE_SERVICE_ROLE_KEY: Deno.env.get('SUPABASE_SERVICE_ROLE_KEY'),
        ENVIRONMENT: Deno.env.get('ENVIRONMENT'),
        LOG_LEVEL: Deno.env.get('LOG_LEVEL'),
        CACHE_TTL_DEFAULT: Deno.env.get('CACHE_TTL_DEFAULT'),
        MAX_REQUEST_TIMEOUT: Deno.env.get('MAX_REQUEST_TIMEOUT'),
        RATE_LIMIT_REQUESTS: Deno.env.get('RATE_LIMIT_REQUESTS'),
        RATE_LIMIT_WINDOW_MS: Deno.env.get('RATE_LIMIT_WINDOW_MS'),
        CORS_ORIGINS: Deno.env.get('CORS_ORIGINS'),
        ENABLE_METRICS: Deno.env.get('ENABLE_METRICS'),
        ENABLE_TRACING: Deno.env.get('ENABLE_TRACING'),
      };
    }

    // Node.js environment (testing)
    return {
      OPENAI_API_KEY: process.env.OPENAI_API_KEY || '',
      SUPABASE_URL: process.env.SUPABASE_URL || '',
      SUPABASE_ANON_KEY: process.env.SUPABASE_ANON_KEY || '',
      SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY,
      ENVIRONMENT: process.env.ENVIRONMENT,
      LOG_LEVEL: process.env.LOG_LEVEL,
      CACHE_TTL_DEFAULT: process.env.CACHE_TTL_DEFAULT,
      MAX_REQUEST_TIMEOUT: process.env.MAX_REQUEST_TIMEOUT,
      RATE_LIMIT_REQUESTS: process.env.RATE_LIMIT_REQUESTS,
      RATE_LIMIT_WINDOW_MS: process.env.RATE_LIMIT_WINDOW_MS,
      CORS_ORIGINS: process.env.CORS_ORIGINS,
      ENABLE_METRICS: process.env.ENABLE_METRICS,
      ENABLE_TRACING: process.env.ENABLE_TRACING,
    };
  }

  /**
   * Validate required environment variables
   */
  private static validateRequiredVariables(env: EnvironmentVariables): void {
    const required = ['OPENAI_API_KEY', 'SUPABASE_URL', 'SUPABASE_ANON_KEY'];
    const missing: string[] = [];

    for (const key of required) {
      if (!env[key as keyof EnvironmentVariables]) {
        missing.push(key);
      }
    }

    if (missing.length > 0) {
      throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
    }

    // Validate API key format
    if (!env.OPENAI_API_KEY.startsWith('sk-')) {
      console.warn('OpenAI API key does not match expected format (sk-...)');
    }

    // Validate Supabase URL format
    if (!env.SUPABASE_URL.includes('.supabase.co')) {
      console.warn('Supabase URL does not match expected format');
    }
  }

  /**
   * Create configuration for testing
   */
  static forTesting(): AppConfig {
    return {
      openai: {
        apiKey: 'sk-test-key',
        timeout: 5000,
        maxRetries: 1,
        defaultModel: 'gpt-3.5-turbo',
        fallbackModel: 'gpt-3.5-turbo',
      },

      database: {
        url: 'https://test.supabase.co',
        apiKey: 'test-anon-key',
        maxConnections: 5,
        connectionTimeout: 10000,
      },

      cache: {
        defaultTTL: 60000, // 1 minute
        maxMemoryMB: 16,
        keyPrefix: 'test',
      },

      security: {
        corsOrigins: ['*'],
        rateLimiting: {
          requests: 1000,
          windowMs: 60000,
        },
        authentication: {
          required: false,
          allowAnonymous: true,
        },
      },

      performance: {
        requestTimeout: 10000,
        maxPayloadSize: 1024 * 1024, // 1MB
        enableCompression: false,
      },

      monitoring: {
        logLevel: 'debug',
        enableMetrics: false,
        enableTracing: false,
      },

      environment: 'development',
    };
  }

  /**
   * Validate configuration object
   */
  static validate(config: AppConfig): string[] {
    const errors: string[] = [];

    // Validate timeouts
    if (config.openai.timeout < 1000 || config.openai.timeout > 60000) {
      errors.push('OpenAI timeout must be between 1-60 seconds');
    }

    if (config.performance.requestTimeout < 1000 || config.performance.requestTimeout > 300000) {
      errors.push('Request timeout must be between 1-300 seconds');
    }

    // Validate rate limiting
    if (config.security.rateLimiting.requests < 1 || config.security.rateLimiting.requests > 10000) {
      errors.push('Rate limit requests must be between 1-10000');
    }

    // Validate cache settings
    if (config.cache.maxMemoryMB < 1 || config.cache.maxMemoryMB > 512) {
      errors.push('Cache memory must be between 1-512 MB');
    }

    return errors;
  }
}

/**
 * Global configuration instance
 */
let globalConfig: AppConfig | null = null;

/**
 * Get or create global configuration
 */
export function getConfig(): AppConfig {
  if (!globalConfig) {
    globalConfig = ConfigBuilder.fromEnvironment();
    
    // Validate configuration
    const errors = ConfigBuilder.validate(globalConfig);
    if (errors.length > 0) {
      throw new Error(`Configuration validation failed: ${errors.join(', ')}`);
    }
  }

  return globalConfig;
}

/**
 * Reset global configuration (for testing)
 */
export function resetConfig(): void {
  globalConfig = null;
}

/**
 * Get configuration for specific environment
 */
export function getConfigForEnvironment(env: string): AppConfig {
  switch (env) {
    case 'test':
      return ConfigBuilder.forTesting();
    default:
      return getConfig();
  }
}