/**
 * Edge Function Logger - Optimized for production Deno environment
 * Provides structured logging with minimal overhead for Supabase Edge Functions
 */

export type EdgeLogLevel = 'debug' | 'info' | 'warn' | 'error';

// Production environment detection
const isProduction = Deno.env.get('NODE_ENV') === 'production' || !Deno.env.get('DEBUG_MODE');

class EdgeLogger {
  private context: string;

  constructor(context = 'EdgeFunction') {
    this.context = context;
  }

  private formatMessage(level: EdgeLogLevel, message: string, data?: any): void {
    const timestamp = new Date().toISOString();
    const prefix = `[${timestamp}] [${level.toUpperCase()}] [${this.context}]`;
    
    if (data !== undefined) {
      console[level === 'debug' ? 'log' : level](`${prefix} ${message}`, data);
    } else {
      console[level === 'debug' ? 'log' : level](`${prefix} ${message}`);
    }
  }

  debug(message: string, data?: any): void {
    if (isProduction) return;
    this.formatMessage('debug', message, data);
  }

  info(message: string, data?: any): void {
    if (isProduction) return;
    this.formatMessage('info', message, data);
  }

  warn(message: string, data?: any): void {
    this.formatMessage('warn', message, data);
  }

  error(message: string, error?: any): void {
    if (error instanceof Error) {
      this.formatMessage('error', message, {
        name: error.name,
        message: error.message,
        stack: error.stack,
      });
    } else {
      this.formatMessage('error', message, error);
    }
  }

  withContext(context: string): EdgeLogger {
    return new EdgeLogger(`${this.context}:${context}`);
  }
}

// Export singleton for common usage
export const edgeLogger = new EdgeLogger();
export { EdgeLogger };
