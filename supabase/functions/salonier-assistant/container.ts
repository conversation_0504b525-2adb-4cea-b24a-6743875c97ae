/**
 * Dependency Injection Container
 * 
 * Manages service lifecycle, dependency injection, and configuration
 * for the Salonier Assistant Edge Function. Provides clean separation
 * between service creation, configuration, and usage.
 */

import { AppConfig } from './config.ts';
import { getInfrastructure, InfrastructureContainer } from './infrastructure/index.ts';

// Application Layer Imports
import {
  Diagnose<PERSON>mage<PERSON>and<PERSON>,
  GenerateFormulaHandler,
  ChatAssistantHandler,
  UploadPhotoHandler,
  ConvertFormulaHandler,
  DiagnosisService,
  FormulationService,
  ChatService,
  AdvancedCacheService,
  ValidationService,
} from './application/index.ts';

/**
 * Service Registry for dependency injection
 */
export interface ServiceRegistry {
  // Infrastructure Services
  infrastructure: InfrastructureContainer;

  // Application Services
  diagnosisService: DiagnosisService;
  formulationService: FormulationService;
  chatService: ChatService;
  cacheService: AdvancedCacheService;
  validationService: ValidationService;

  // Request Handlers
  diagnoseImageHandler: DiagnoseImageHandler;
  generateFormulaHandler: GenerateFormulaHandler;
  chatAssistantHandler: ChatAssistantHandler;
  uploadPhotoHandler: UploadPhotoHandler;
  convertFormulaHandler: ConvertFormulaHandler;
}

/**
 * Request Context for dependency injection
 */
export interface RequestContext {
  requestId: string;
  userId?: string;
  salonId?: string;
  clientId?: string;
  timestamp: number;
  userAgent?: string;
  ipAddress?: string;
  correlationId?: string;
}

/**
 * Health Check Result
 */
export interface HealthCheckResult {
  service: string;
  status: 'healthy' | 'unhealthy' | 'degraded';
  responseTime: number;
  lastCheck: number;
  error?: string;
  metadata?: Record<string, any>;
}

/**
 * Application Container
 * 
 * Manages the complete lifecycle of all application services with
 * proper dependency injection, health checking, and graceful shutdown.
 */
export class ApplicationContainer {
  private services: Partial<ServiceRegistry> = {};
  private initialized = false;
  private healthChecks = new Map<string, HealthCheckResult>();
  
  constructor(
    private config: AppConfig
  ) {}

  /**
   * Initialize all services with proper dependency injection
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      console.info('Initializing Application Container...');
      
      // 1. Initialize Infrastructure Layer
      await this.initializeInfrastructure();
      
      // 2. Initialize Application Services
      await this.initializeApplicationServices();
      
      // 3. Initialize Request Handlers
      await this.initializeRequestHandlers();
      
      // 4. Perform Initial Health Checks
      await this.performHealthChecks();
      
      this.initialized = true;
      console.info('Application Container initialized successfully');
      
    } catch (error) {
      console.error('Failed to initialize Application Container:', error);
      
      // Cleanup on failure
      await this.cleanup();
      
      throw new Error(`Container initialization failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get service from registry
   */
  getService<T extends keyof ServiceRegistry>(serviceName: T): ServiceRegistry[T] {
    if (!this.initialized) {
      throw new Error('Container not initialized. Call initialize() first.');
    }

    const service = this.services[serviceName];
    if (!service) {
      throw new Error(`Service not found: ${serviceName}`);
    }

    return service as ServiceRegistry[T];
  }

  /**
   * Check if service is available
   */
  hasService<T extends keyof ServiceRegistry>(serviceName: T): boolean {
    return !!this.services[serviceName];
  }

  /**
   * Get service health status
   */
  getServiceHealth(serviceName: string): HealthCheckResult | undefined {
    return this.healthChecks.get(serviceName);
  }

  /**
   * Get overall container health
   */
  getOverallHealth(): {
    status: 'healthy' | 'unhealthy' | 'degraded';
    services: Record<string, HealthCheckResult>;
    summary: {
      total: number;
      healthy: number;
      unhealthy: number;
      degraded: number;
    };
  } {
    const services: Record<string, HealthCheckResult> = {};
    let healthy = 0;
    let unhealthy = 0;
    let degraded = 0;

    for (const [name, health] of this.healthChecks) {
      services[name] = health;
      
      switch (health.status) {
        case 'healthy':
          healthy++;
          break;
        case 'unhealthy':
          unhealthy++;
          break;
        case 'degraded':
          degraded++;
          break;
      }
    }

    const total = this.healthChecks.size;
    let overallStatus: 'healthy' | 'unhealthy' | 'degraded' = 'healthy';

    if (unhealthy > 0) {
      overallStatus = 'unhealthy';
    } else if (degraded > 0) {
      overallStatus = 'degraded';
    }

    return {
      status: overallStatus,
      services,
      summary: {
        total,
        healthy,
        unhealthy,
        degraded,
      },
    };
  }

  /**
   * Graceful shutdown of all services
   */
  async shutdown(): Promise<void> {
    console.info('Shutting down Application Container...');

    // Shutdown in reverse order of initialization
    const shutdownTasks: Promise<void>[] = [];

    // Shutdown infrastructure last
    if (this.services.infrastructure) {
      shutdownTasks.push(
        this.services.infrastructure.shutdown().catch((error) => {
          console.error('Error shutting down infrastructure:', error);
        })
      );
    }

    await Promise.allSettled(shutdownTasks);
    
    this.services = {};
    this.healthChecks.clear();
    this.initialized = false;
    
    console.info('Application Container shut down successfully');
  }

  /**
   * Create request context for dependency injection
   */
  createRequestContext(request: Request): RequestContext {
    const requestId = crypto.randomUUID();
    const timestamp = Date.now();
    
    // Extract user context from headers
    const authHeader = request.headers.get('authorization');
    const userAgent = request.headers.get('user-agent') || undefined;
    const correlationId = request.headers.get('x-correlation-id') || requestId;
    
    // In a real implementation, you would decode the JWT to get userId/salonId
    // For now, we'll use placeholder logic
    const userId = authHeader ? 'user-from-jwt' : undefined;
    const salonId = authHeader ? 'salon-from-jwt' : undefined;

    return {
      requestId,
      userId,
      salonId,
      timestamp,
      userAgent,
      correlationId,
    };
  }

  /**
   * Initialize Infrastructure Services
   */
  private async initializeInfrastructure(): Promise<void> {
    console.debug('Initializing Infrastructure Services...');
    
    this.services.infrastructure = await getInfrastructure();
    
    console.debug('Infrastructure Services initialized');
  }

  /**
   * Initialize Application Services
   */
  private async initializeApplicationServices(): Promise<void> {
    console.debug('Initializing Application Services...');
    
    const infrastructure = this.getService('infrastructure');
    
    // Initialize services with proper dependency injection
    this.services.diagnosisService = new DiagnosisService(infrastructure);
    this.services.formulationService = new FormulationService(infrastructure);
    this.services.chatService = new ChatService(infrastructure);
    this.services.cacheService = new AdvancedCacheService(infrastructure);
    this.services.validationService = new ValidationService(infrastructure);
    
    console.debug('Application Services initialized');
  }

  /**
   * Initialize Request Handlers
   */
  private async initializeRequestHandlers(): Promise<void> {
    console.debug('Initializing Request Handlers...');
    
    // Get required services
    const diagnosisService = this.getService('diagnosisService');
    const formulationService = this.getService('formulationService');
    const chatService = this.getService('chatService');
    const cacheService = this.getService('cacheService');
    const validationService = this.getService('validationService');
    
    // Initialize handlers with service dependencies
    this.services.diagnoseImageHandler = new DiagnoseImageHandler(
      diagnosisService,
      cacheService,
      validationService
    );
    
    this.services.generateFormulaHandler = new GenerateFormulaHandler(
      formulationService,
      cacheService,
      validationService
    );
    
    this.services.chatAssistantHandler = new ChatAssistantHandler(
      chatService,
      cacheService,
      validationService
    );
    
    this.services.uploadPhotoHandler = new UploadPhotoHandler(
      diagnosisService,
      cacheService,
      validationService
    );
    
    this.services.convertFormulaHandler = new ConvertFormulaHandler(
      formulationService,
      cacheService,
      validationService
    );
    
    console.debug('Request Handlers initialized');
  }

  /**
   * Perform health checks on all services
   */
  private async performHealthChecks(): Promise<void> {
    console.debug('Performing initial health checks...');
    
    const healthPromises: Promise<void>[] = [];
    
    // Check infrastructure health
    if (this.services.infrastructure) {
      healthPromises.push(this.checkServiceHealth('infrastructure', async () => {
        const health = this.services.infrastructure!.getOverallHealth();
        return {
          status: health.summary.unhealthy > 0 ? 'unhealthy' : 
                  health.summary.degraded > 0 ? 'degraded' : 'healthy',
          metadata: { services: health.summary },
        };
      }));
    }
    
    // Check application services
    const appServices = ['diagnosisService', 'formulationService', 'chatService'] as const;
    
    for (const serviceName of appServices) {
      if (this.services[serviceName]) {
        healthPromises.push(this.checkServiceHealth(serviceName, async () => {
          // Simple connectivity check - in production you'd do more thorough checks
          return { status: 'healthy' as const };
        }));
      }
    }
    
    await Promise.allSettled(healthPromises);
    
    console.debug('Initial health checks completed');
  }

  /**
   * Check individual service health
   */
  private async checkServiceHealth(
    serviceName: string,
    healthCheck: () => Promise<{ status: 'healthy' | 'unhealthy' | 'degraded'; metadata?: any }>
  ): Promise<void> {
    const startTime = Date.now();
    
    try {
      const result = await healthCheck();
      const responseTime = Date.now() - startTime;
      
      this.healthChecks.set(serviceName, {
        service: serviceName,
        status: result.status,
        responseTime,
        lastCheck: Date.now(),
        metadata: result.metadata,
      });
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      this.healthChecks.set(serviceName, {
        service: serviceName,
        status: 'unhealthy',
        responseTime,
        lastCheck: Date.now(),
        error: error instanceof Error ? error.message : String(error),
      });
      
      console.warn(`Health check failed for ${serviceName}:`, error);
    }
  }

  /**
   * Cleanup resources on failure
   */
  private async cleanup(): Promise<void> {
    console.debug('Cleaning up container resources...');
    
    if (this.services.infrastructure) {
      try {
        await this.services.infrastructure.shutdown();
      } catch (error) {
        console.error('Error during infrastructure cleanup:', error);
      }
    }
    
    this.services = {};
    this.healthChecks.clear();
    this.initialized = false;
  }
}

/**
 * Global container instance
 */
let globalContainer: ApplicationContainer | null = null;

/**
 * Get or create global container instance
 */
export async function getContainer(config: AppConfig): Promise<ApplicationContainer> {
  if (!globalContainer) {
    globalContainer = new ApplicationContainer(config);
    await globalContainer.initialize();
  }
  
  return globalContainer;
}

/**
 * Reset global container (for testing)
 */
export async function resetContainer(): Promise<void> {
  if (globalContainer) {
    await globalContainer.shutdown();
    globalContainer = null;
  }
}

/**
 * Create container for testing
 */
export async function createTestContainer(config: AppConfig): Promise<ApplicationContainer> {
  const container = new ApplicationContainer(config);
  await container.initialize();
  return container;
}