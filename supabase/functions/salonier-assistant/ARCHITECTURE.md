# Clean Architecture - Salonier Assistant Edge Function

This document describes the Clean Architecture refactoring of the salonier-assistant Edge Function, implementing domain-driven design principles with clear separation of concerns.

## 📁 Directory Structure

```
supabase/functions/salonier-assistant/
├── domain/                 # Core business logic (no dependencies)
│   ├── entities/           # Business entities with validation rules
│   ├── use-cases/          # Application business rules
│   └── repositories/       # Abstract data access interfaces
├── infrastructure/         # External concerns (frameworks, databases, APIs)
│   ├── ai/                 # OpenAI integration and prompt management
│   ├── data/               # Database access implementations
│   └── external/           # Third-party service integrations
├── application/            # Application layer orchestration
│   ├── services/           # Application services
│   └── handlers/           # Request/response handling
├── shared/                 # Cross-cutting concerns
│   ├── types/              # Shared type definitions
│   ├── utils/              # Utility functions
│   └── constants/          # Application constants
└── tests/                  # Test specifications
```

## 🏗️ Domain Entities

### HairDiagnosis Entity (~50 lines)

Represents the complete assessment of a client's current hair condition.

**Key Business Rules:**
- Hair levels must be between 1-10
- Gray percentage must be 0-100%
- Confidence scores must be 0-100%
- Salon ID required for multi-tenant isolation
- Processing time calculations based on hair condition

**Core Methods:**
- `canLighten(targetLevel)` - Safety check for lightening processes
- `requiresPreTreatment()` - Determines if pre-treatment is needed
- `getProcessingTimeMultiplier()` - Calculates time adjustments

### ColorFormula Entity (~80 lines)

Represents a complete hair coloring formula with steps, ingredients, and safety validations.

**Key Business Rules:**
- All steps must have positive processing times
- Ingredients must have positive amounts
- Coverage percentage must be 0-100%
- Critical risks prevent formula execution
- Minimum confidence threshold of 70%

**Core Methods:**
- `isSafeToExecute()` - Safety validation before application
- `getEstimatedCost()` - Cost calculation for products
- `getRequiredProducts()` - Inventory requirements
- `validate(validatedBy)` - Professional validation workflow

### DesiredLook Entity (~40 lines)

Captures the client's vision and requirements for their hair transformation.

**Key Business Rules:**
- Target level must be 1-10
- Budget range validation (min ≤ max, non-negative)
- Time constraints must be positive
- Required fields: salon ID, client ID, description

**Core Methods:**
- `isTimeRealistic(estimatedTime)` - Time constraint validation
- `isWithinBudget(estimatedCost)` - Budget compatibility
- `getSkinToneCompatibility()` - Color theory compatibility (0-100)
- `getRecommendedConsultationLevel()` - Consultation complexity assessment

## 🔒 Security & Multi-tenancy

All entities enforce salon-level isolation through:
- Required `salonId` in all entities
- Business rule validation in constructors
- Immutable entity design
- Safe serialization methods

## 📊 Business Logic Highlights

### Chemical Safety Validation
```typescript
// HairDiagnosis checks for dangerous combinations
canLighten(targetLevel: number): boolean {
  if (this._damage === 'severe') return false;
  if (this._hasMetallicSalts) return false;
  if (this._hasHenna) return false;
  // Additional safety rules...
}
```

### Formula Safety Assessment
```typescript
// ColorFormula ensures safe execution
isSafeToExecute(): boolean {
  return !this._risks.some(risk => risk.severity === 'critical') && 
         this._confidence >= 70;
}
```

### Color Theory Integration
```typescript
// DesiredLook validates skin tone compatibility
getSkinToneCompatibility(): number {
  // Implements professional color theory rules
  // Returns 0-100 compatibility score
}
```

## 🎯 Design Principles Applied

1. **Single Responsibility** - Each entity has one clear business purpose
2. **Immutability** - Entities cannot be modified after creation
3. **Business Rule Enforcement** - All validation happens in constructors
4. **Factory Pattern** - Static `create()` methods for entity creation
5. **Domain Model** - Rich objects with behavior, not anemic data structures

## 🚀 Next Steps

1. **Use Cases Implementation** - Business workflow orchestration
2. **Repository Interfaces** - Abstract data access patterns  
3. **Infrastructure Layer** - OpenAI, database, and external integrations
4. **Application Services** - Request/response handling
5. **Comprehensive Testing** - Unit tests for all business rules

## 📈 Benefits Achieved

- **Testability** - Pure business logic without external dependencies
- **Maintainability** - Clear separation of concerns
- **Scalability** - Easy to extend with new features
- **Safety** - Comprehensive business rule validation
- **Multi-tenancy** - Built-in salon isolation
- **Type Safety** - Full TypeScript coverage with strict typing