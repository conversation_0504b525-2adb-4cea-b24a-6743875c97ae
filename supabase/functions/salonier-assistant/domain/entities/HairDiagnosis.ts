/**
 * HairDiagnosis Domain Entity
 * 
 * Represents a complete hair diagnosis with all relevant attributes
 * for determining the optimal coloration strategy. This entity encapsulates
 * the current state of hair and provides business validation rules.
 */

/**
 * Hair porosity levels affecting product absorption
 */
export type HairPorosity = 'low' | 'medium' | 'high';

/**
 * Hair damage assessment levels
 */
export type HairDamage = 'none' | 'low' | 'medium' | 'high' | 'severe';

/**
 * Hair texture classifications
 */
export type HairTexture = 'fine' | 'medium' | 'coarse';

/**
 * Hair density measurements
 */
export type HairDensity = 'thin' | 'medium' | 'thick';

/**
 * Previous chemical treatments that affect coloring
 */
export interface ChemicalTreatment {
  type: 'coloring' | 'bleaching' | 'perm' | 'straightening' | 'keratin' | 'henna' | 'metallic_dye';
  date: Date;
  products?: string[];
  result?: string;
}

/**
 * HairDiagnosis Entity
 * 
 * Immutable entity representing the complete diagnosis of hair condition
 * and characteristics needed for professional color formulation.
 */
export class HairDiagnosis {
  private constructor(
    private readonly _id: string,
    private readonly _salonId: string,
    private readonly _currentLevel: number,
    private readonly _currentTone: string,
    private readonly _underlyingPigment: string,
    private readonly _porosity: HairPorosity,
    private readonly _damage: HairDamage,
    private readonly _texture: HairTexture,
    private readonly _density: HairDensity,
    private readonly _grayPercentage: number,
    private readonly _previousTreatments: ChemicalTreatment[],
    private readonly _hasMetallicSalts: boolean,
    private readonly _hasHenna: boolean,
    private readonly _naturalColor: string,
    private readonly _notes: string,
    private readonly _diagnosedAt: Date,
    private readonly _confidence: number
  ) {}

  // Getters
  get id(): string { return this._id; }
  get salonId(): string { return this._salonId; }
  get currentLevel(): number { return this._currentLevel; }
  get currentTone(): string { return this._currentTone; }
  get underlyingPigment(): string { return this._underlyingPigment; }
  get porosity(): HairPorosity { return this._porosity; }
  get damage(): HairDamage { return this._damage; }
  get texture(): HairTexture { return this._texture; }
  get density(): HairDensity { return this._density; }
  get grayPercentage(): number { return this._grayPercentage; }
  get previousTreatments(): ChemicalTreatment[] { return [...this._previousTreatments]; }
  get hasMetallicSalts(): boolean { return this._hasMetallicSalts; }
  get hasHenna(): boolean { return this._hasHenna; }
  get naturalColor(): string { return this._naturalColor; }
  get notes(): string { return this._notes; }
  get diagnosedAt(): Date { return this._diagnosedAt; }
  get confidence(): number { return this._confidence; }

  /**
   * Factory method to create a new HairDiagnosis
   */
  static create(data: {
    id: string;
    salonId: string;
    currentLevel: number;
    currentTone: string;
    underlyingPigment: string;
    porosity: HairPorosity;
    damage: HairDamage;
    texture: HairTexture;
    density: HairDensity;
    grayPercentage: number;
    previousTreatments: ChemicalTreatment[];
    hasMetallicSalts: boolean;
    hasHenna: boolean;
    naturalColor: string;
    notes?: string;
    diagnosedAt?: Date;
    confidence: number;
  }): HairDiagnosis {
    this.validateBusinessRules(data);
    
    return new HairDiagnosis(
      data.id,
      data.salonId,
      data.currentLevel,
      data.currentTone,
      data.underlyingPigment,
      data.porosity,
      data.damage,
      data.texture,
      data.density,
      data.grayPercentage,
      data.previousTreatments,
      data.hasMetallicSalts,
      data.hasHenna,
      data.naturalColor,
      data.notes || '',
      data.diagnosedAt || new Date(),
      data.confidence
    );
  }

  /**
   * Business rule validation
   */
  private static validateBusinessRules(data: any): void {
    if (data.currentLevel < 1 || data.currentLevel > 10) {
      throw new Error('Hair level must be between 1 and 10');
    }
    
    if (data.grayPercentage < 0 || data.grayPercentage > 100) {
      throw new Error('Gray percentage must be between 0 and 100');
    }
    
    if (data.confidence < 0 || data.confidence > 100) {
      throw new Error('Confidence must be between 0 and 100');
    }
    
    if (!data.salonId || data.salonId.trim() === '') {
      throw new Error('Salon ID is required for multi-tenant isolation');
    }
    
    if (!data.id || data.id.trim() === '') {
      throw new Error('Diagnosis ID is required');
    }
  }

  /**
   * Check if hair can safely undergo lightening
   */
  canLighten(targetLevel: number): boolean {
    if (this._damage === 'severe') return false;
    if (this._hasMetallicSalts) return false;
    if (this._hasHenna) return false;
    
    const maxLevelIncrease = this._damage === 'high' ? 2 : 
                           this._damage === 'medium' ? 3 : 4;
    
    return (targetLevel - this._currentLevel) <= maxLevelIncrease;
  }

  /**
   * Check if hair requires pre-treatment
   */
  requiresPreTreatment(): boolean {
    return this._porosity === 'high' || 
           this._damage === 'high' || 
           this._damage === 'severe' ||
           this._hasMetallicSalts;
  }

  /**
   * Get recommended processing time multiplier based on hair condition
   */
  getProcessingTimeMultiplier(): number {
    let multiplier = 1.0;
    
    if (this._porosity === 'low') multiplier += 0.2;
    if (this._porosity === 'high') multiplier -= 0.1;
    
    if (this._damage === 'high') multiplier -= 0.2;
    if (this._damage === 'severe') multiplier -= 0.3;
    
    return Math.max(0.5, multiplier);
  }

  /**
   * Serialize to JSON for storage
   */
  toJSON(): Record<string, any> {
    return {
      id: this._id,
      salonId: this._salonId,
      currentLevel: this._currentLevel,
      currentTone: this._currentTone,
      underlyingPigment: this._underlyingPigment,
      porosity: this._porosity,
      damage: this._damage,
      texture: this._texture,
      density: this._density,
      grayPercentage: this._grayPercentage,
      previousTreatments: this._previousTreatments,
      hasMetallicSalts: this._hasMetallicSalts,
      hasHenna: this._hasHenna,
      naturalColor: this._naturalColor,
      notes: this._notes,
      diagnosedAt: this._diagnosedAt.toISOString(),
      confidence: this._confidence
    };
  }

  /**
   * Create from JSON data
   */
  static fromJSON(data: Record<string, any>): HairDiagnosis {
    return HairDiagnosis.create({
      ...data,
      diagnosedAt: new Date(data.diagnosedAt),
      previousTreatments: data.previousTreatments.map((t: any) => ({
        ...t,
        date: new Date(t.date)
      }))
    });
  }
}