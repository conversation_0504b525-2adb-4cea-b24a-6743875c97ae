/**
 * ColorFormula Domain Entity
 * 
 * Represents a complete hair coloring formula with all steps, ingredients,
 * and processing instructions. This entity encapsulates the business logic
 * for formula creation, validation, and execution.
 */

/**
 * Types of products used in hair coloring
 */
export type ProductType = 'color' | 'developer' | 'additive' | 'bleach' | 'toner' | 'pre-pigment';

/**
 * Units of measurement for formula ingredients
 */
export type MeasurementUnit = 'ml' | 'g' | 'cm' | 'oz';

/**
 * Processing techniques available
 */
export type ProcessingTechnique = 'global' | 'highlights' | 'lowlights' | 'balayage' | 'ombre' | 'color_correction';

/**
 * Formula ingredient with precise measurements
 */
export interface FormulaIngredient {
  readonly productId: string;
  readonly productName: string;
  readonly shade?: string;
  readonly amount: number;
  readonly unit: MeasurementUnit;
  readonly type: ProductType;
  readonly volume?: number; // For developers (10, 20, 30, 40 vol)
  readonly brand: string;
  readonly line: string;
}

/**
 * Individual processing step in the formula
 */
export interface FormulaStep {
  readonly id: string;
  readonly title: string;
  readonly ingredients: FormulaIngredient[];
  readonly processingTimeMinutes: number;
  readonly instructions: string;
  readonly technique: ProcessingTechnique;
  readonly temperature?: number; // Room temperature or heat
  readonly order: number;
  readonly isOptional: boolean;
}

/**
 * Risk assessment for formula safety
 */
export interface FormulaRisk {
  readonly type: 'chemical' | 'damage' | 'color' | 'allergic' | 'processing';
  readonly severity: 'low' | 'medium' | 'high' | 'critical';
  readonly description: string;
  readonly mitigation: string;
}

/**
 * Expected results from formula application
 */
export interface ExpectedResult {
  readonly targetLevel: number;
  readonly targetTone: string;
  readonly coveragePercentage: number; // For gray coverage
  readonly durabilityWeeks: number;
  readonly maintenanceRequired: string;
}

/**
 * ColorFormula Entity
 * 
 * Immutable entity representing a complete hair coloring formula
 * with all necessary steps, ingredients, and safety considerations.
 */
export class ColorFormula {
  private constructor(
    private readonly _id: string,
    private readonly _salonId: string,
    private readonly _name: string,
    private readonly _steps: FormulaStep[],
    private readonly _currentLevel: number,
    private readonly _targetLevel: number,
    private readonly _currentTone: string,
    private readonly _targetTone: string,
    private readonly _brand: string,
    private readonly _line: string,
    private readonly _technique: ProcessingTechnique,
    private readonly _totalTimeMinutes: number,
    private readonly _expectedResult: ExpectedResult,
    private readonly _risks: FormulaRisk[],
    private readonly _warnings: string[],
    private readonly _notes: string,
    private readonly _confidence: number,
    private readonly _createdAt: Date,
    private readonly _isValidated: boolean,
    private readonly _validatedBy?: string
  ) {}

  // Getters
  get id(): string { return this._id; }
  get salonId(): string { return this._salonId; }
  get name(): string { return this._name; }
  get steps(): FormulaStep[] { return [...this._steps]; }
  get currentLevel(): number { return this._currentLevel; }
  get targetLevel(): number { return this._targetLevel; }
  get currentTone(): string { return this._currentTone; }
  get targetTone(): string { return this._targetTone; }
  get brand(): string { return this._brand; }
  get line(): string { return this._line; }
  get technique(): ProcessingTechnique { return this._technique; }
  get totalTimeMinutes(): number { return this._totalTimeMinutes; }
  get expectedResult(): ExpectedResult { return this._expectedResult; }
  get risks(): FormulaRisk[] { return [...this._risks]; }
  get warnings(): string[] { return [...this._warnings]; }
  get notes(): string { return this._notes; }
  get confidence(): number { return this._confidence; }
  get createdAt(): Date { return this._createdAt; }
  get isValidated(): boolean { return this._isValidated; }
  get validatedBy(): string | undefined { return this._validatedBy; }

  /**
   * Factory method to create a new ColorFormula
   */
  static create(data: {
    id: string;
    salonId: string;
    name: string;
    steps: FormulaStep[];
    currentLevel: number;
    targetLevel: number;
    currentTone: string;
    targetTone: string;
    brand: string;
    line: string;
    technique: ProcessingTechnique;
    expectedResult: ExpectedResult;
    risks?: FormulaRisk[];
    warnings?: string[];
    notes?: string;
    confidence: number;
    createdAt?: Date;
    isValidated?: boolean;
    validatedBy?: string;
  }): ColorFormula {
    this.validateBusinessRules(data);
    
    const totalTime = data.steps.reduce((sum, step) => sum + step.processingTimeMinutes, 0);
    
    return new ColorFormula(
      data.id,
      data.salonId,
      data.name,
      data.steps.sort((a, b) => a.order - b.order),
      data.currentLevel,
      data.targetLevel,
      data.currentTone,
      data.targetTone,
      data.brand,
      data.line,
      data.technique,
      totalTime,
      data.expectedResult,
      data.risks || [],
      data.warnings || [],
      data.notes || '',
      data.confidence,
      data.createdAt || new Date(),
      data.isValidated || false,
      data.validatedBy
    );
  }

  /**
   * Business rule validation
   */
  private static validateBusinessRules(data: any): void {
    if (data.currentLevel < 1 || data.currentLevel > 10) {
      throw new Error('Current hair level must be between 1 and 10');
    }
    
    if (data.targetLevel < 1 || data.targetLevel > 10) {
      throw new Error('Target hair level must be between 1 and 10');
    }
    
    if (data.confidence < 0 || data.confidence > 100) {
      throw new Error('Confidence must be between 0 and 100');
    }
    
    if (!data.salonId || data.salonId.trim() === '') {
      throw new Error('Salon ID is required for multi-tenant isolation');
    }
    
    if (!data.steps || data.steps.length === 0) {
      throw new Error('Formula must have at least one processing step');
    }
    
    if (data.expectedResult.coveragePercentage < 0 || data.expectedResult.coveragePercentage > 100) {
      throw new Error('Coverage percentage must be between 0 and 100');
    }
    
    // Validate all steps have positive processing times
    data.steps.forEach((step: FormulaStep, index: number) => {
      if (step.processingTimeMinutes <= 0) {
        throw new Error(`Step ${index + 1} must have positive processing time`);
      }
      
      if (step.ingredients.length === 0) {
        throw new Error(`Step ${index + 1} must have at least one ingredient`);
      }
      
      // Validate ingredient amounts
      step.ingredients.forEach((ingredient: FormulaIngredient, ingredientIndex: number) => {
        if (ingredient.amount <= 0) {
          throw new Error(`Step ${index + 1}, ingredient ${ingredientIndex + 1} must have positive amount`);
        }
      });
    });
  }

  /**
   * Check if formula is safe to execute
   */
  isSafeToExecute(): boolean {
    return !this._risks.some(risk => risk.severity === 'critical') && 
           this._confidence >= 70;
  }

  /**
   * Get total product cost estimation
   */
  getEstimatedCost(): number {
    // This would integrate with product pricing in a real implementation
    return this._steps.reduce((total, step) => {
      return total + step.ingredients.reduce((stepTotal, ingredient) => {
        // Base cost estimation - would be replaced with actual product prices
        const baseCost = ingredient.type === 'color' ? 2.5 : 
                        ingredient.type === 'developer' ? 1.0 :
                        ingredient.type === 'bleach' ? 3.0 : 1.5;
        return stepTotal + (baseCost * ingredient.amount / 100); // Per 100ml/g
      }, 0);
    }, 0);
  }

  /**
   * Get all unique products needed for this formula
   */
  getRequiredProducts(): FormulaIngredient[] {
    const allIngredients = this._steps.flatMap(step => step.ingredients);
    const uniqueProducts = new Map<string, FormulaIngredient>();
    
    allIngredients.forEach(ingredient => {
      const key = `${ingredient.productId}-${ingredient.shade || ''}-${ingredient.volume || ''}`;
      if (!uniqueProducts.has(key)) {
        uniqueProducts.set(key, ingredient);
      }
    });
    
    return Array.from(uniqueProducts.values());
  }

  /**
   * Create a validated copy of this formula
   */
  validate(validatedBy: string): ColorFormula {
    return new ColorFormula(
      this._id,
      this._salonId,
      this._name,
      this._steps,
      this._currentLevel,
      this._targetLevel,
      this._currentTone,
      this._targetTone,
      this._brand,
      this._line,
      this._technique,
      this._totalTimeMinutes,
      this._expectedResult,
      this._risks,
      this._warnings,
      this._notes,
      this._confidence,
      this._createdAt,
      true,
      validatedBy
    );
  }

  /**
   * Add warning to formula
   */
  addWarning(warning: string): ColorFormula {
    return new ColorFormula(
      this._id,
      this._salonId,
      this._name,
      this._steps,
      this._currentLevel,
      this._targetLevel,
      this._currentTone,
      this._targetTone,
      this._brand,
      this._line,
      this._technique,
      this._totalTimeMinutes,
      this._expectedResult,
      this._risks,
      [...this._warnings, warning],
      this._notes,
      this._confidence,
      this._createdAt,
      this._isValidated,
      this._validatedBy
    );
  }

  /**
   * Serialize to JSON for storage
   */
  toJSON(): Record<string, any> {
    return {
      id: this._id,
      salonId: this._salonId,
      name: this._name,
      steps: this._steps,
      currentLevel: this._currentLevel,
      targetLevel: this._targetLevel,
      currentTone: this._currentTone,
      targetTone: this._targetTone,
      brand: this._brand,
      line: this._line,
      technique: this._technique,
      totalTimeMinutes: this._totalTimeMinutes,
      expectedResult: this._expectedResult,
      risks: this._risks,
      warnings: this._warnings,
      notes: this._notes,
      confidence: this._confidence,
      createdAt: this._createdAt.toISOString(),
      isValidated: this._isValidated,
      validatedBy: this._validatedBy
    };
  }

  /**
   * Create from JSON data
   */
  static fromJSON(data: Record<string, any>): ColorFormula {
    return ColorFormula.create({
      ...data,
      createdAt: new Date(data.createdAt)
    });
  }
}