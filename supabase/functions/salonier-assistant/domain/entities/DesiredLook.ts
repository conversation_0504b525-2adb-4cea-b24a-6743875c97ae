/**
 * DesiredLook Domain Entity
 * 
 * Represents the client's desired hair color outcome, including aesthetic
 * preferences, lifestyle considerations, and maintenance requirements.
 * This entity captures the target vision for the coloring process.
 */

/**
 * Color intensity preferences
 */
export type ColorIntensity = 'natural' | 'subtle' | 'medium' | 'vibrant' | 'dramatic';

/**
 * Maintenance commitment levels
 */
export type MaintenanceLevel = 'low' | 'medium' | 'high';

/**
 * Lifestyle factors affecting color choice
 */
export type LifestyleFactor = 'professional' | 'active_sports' | 'frequent_swimming' | 'heat_styling' | 'sun_exposure';

/**
 * Color application methods
 */
export type ApplicationMethod = 'all_over' | 'highlights' | 'lowlights' | 'balayage' | 'ombre' | 'partial' | 'root_touch_up';

/**
 * Reference image for desired look
 */
export interface ReferenceImage {
  readonly url: string;
  readonly description: string;
  readonly confidence: number; // How closely it matches what's achievable
  readonly notes?: string;
}

/**
 * Color tone preferences
 */
export interface ColorTone {
  readonly primary: string; // Main color family
  readonly secondary?: string; // Accent tones
  readonly warmth: 'cool' | 'neutral' | 'warm';
  readonly depth: number; // 1-10 scale
}

/**
 * DesiredLook Entity
 * 
 * Immutable entity representing the client's vision for their hair color
 * transformation, including aesthetic and practical considerations.
 */
export class DesiredLook {
  private constructor(
    private readonly _id: string,
    private readonly _salonId: string,
    private readonly _clientId: string,
    private readonly _targetLevel: number,
    private readonly _colorTone: ColorTone,
    private readonly _intensity: ColorIntensity,
    private readonly _applicationMethod: ApplicationMethod,
    private readonly _referenceImages: ReferenceImage[],
    private readonly _description: string,
    private readonly _maintenanceLevel: MaintenanceLevel,
    private readonly _budgetRange: { min: number; max: number; currency: string },
    private readonly _lifestyleFactors: LifestyleFactor[],
    private readonly _timeConstraint: number, // Maximum time willing to spend in minutes
    private readonly _previousExperience: string,
    private readonly _skinTone: string,
    private readonly _eyeColor: string,
    private readonly _isFirstTime: boolean,
    private readonly _specialRequests: string[],
    private readonly _avoidColors: string[],
    private readonly _inspirationSource: string, // Celebrity, Pinterest, etc.
    private readonly _createdAt: Date,
    private readonly _priority: 'consultation' | 'standard' | 'urgent'
  ) {}

  // Getters
  get id(): string { return this._id; }
  get salonId(): string { return this._salonId; }
  get clientId(): string { return this._clientId; }
  get targetLevel(): number { return this._targetLevel; }
  get colorTone(): ColorTone { return { ...this._colorTone }; }
  get intensity(): ColorIntensity { return this._intensity; }
  get applicationMethod(): ApplicationMethod { return this._applicationMethod; }
  get referenceImages(): ReferenceImage[] { return [...this._referenceImages]; }
  get description(): string { return this._description; }
  get maintenanceLevel(): MaintenanceLevel { return this._maintenanceLevel; }
  get budgetRange(): { min: number; max: number; currency: string } { return { ...this._budgetRange }; }
  get lifestyleFactors(): LifestyleFactor[] { return [...this._lifestyleFactors]; }
  get timeConstraint(): number { return this._timeConstraint; }
  get previousExperience(): string { return this._previousExperience; }
  get skinTone(): string { return this._skinTone; }
  get eyeColor(): string { return this._eyeColor; }
  get isFirstTime(): boolean { return this._isFirstTime; }
  get specialRequests(): string[] { return [...this._specialRequests]; }
  get avoidColors(): string[] { return [...this._avoidColors]; }
  get inspirationSource(): string { return this._inspirationSource; }
  get createdAt(): Date { return this._createdAt; }
  get priority(): 'consultation' | 'standard' | 'urgent' { return this._priority; }

  /**
   * Factory method to create a new DesiredLook
   */
  static create(data: {
    id: string;
    salonId: string;
    clientId: string;
    targetLevel: number;
    colorTone: ColorTone;
    intensity: ColorIntensity;
    applicationMethod: ApplicationMethod;
    referenceImages?: ReferenceImage[];
    description: string;
    maintenanceLevel: MaintenanceLevel;
    budgetRange: { min: number; max: number; currency: string };
    lifestyleFactors?: LifestyleFactor[];
    timeConstraint: number;
    previousExperience?: string;
    skinTone: string;
    eyeColor: string;
    isFirstTime?: boolean;
    specialRequests?: string[];
    avoidColors?: string[];
    inspirationSource?: string;
    createdAt?: Date;
    priority?: 'consultation' | 'standard' | 'urgent';
  }): DesiredLook {
    this.validateBusinessRules(data);
    
    return new DesiredLook(
      data.id,
      data.salonId,
      data.clientId,
      data.targetLevel,
      data.colorTone,
      data.intensity,
      data.applicationMethod,
      data.referenceImages || [],
      data.description,
      data.maintenanceLevel,
      data.budgetRange,
      data.lifestyleFactors || [],
      data.timeConstraint,
      data.previousExperience || '',
      data.skinTone,
      data.eyeColor,
      data.isFirstTime || false,
      data.specialRequests || [],
      data.avoidColors || [],
      data.inspirationSource || '',
      data.createdAt || new Date(),
      data.priority || 'standard'
    );
  }

  /**
   * Business rule validation
   */
  private static validateBusinessRules(data: any): void {
    if (data.targetLevel < 1 || data.targetLevel > 10) {
      throw new Error('Target level must be between 1 and 10');
    }
    
    if (data.timeConstraint <= 0) {
      throw new Error('Time constraint must be positive');
    }
    
    if (data.budgetRange.min < 0 || data.budgetRange.max < 0) {
      throw new Error('Budget range values must be non-negative');
    }
    
    if (data.budgetRange.min > data.budgetRange.max) {
      throw new Error('Budget minimum cannot exceed maximum');
    }
    
    if (!data.salonId || data.salonId.trim() === '') {
      throw new Error('Salon ID is required for multi-tenant isolation');
    }
    
    if (!data.clientId || data.clientId.trim() === '') {
      throw new Error('Client ID is required');
    }
    
    if (!data.description || data.description.trim() === '') {
      throw new Error('Description of desired look is required');
    }
  }

  /**
   * Check if the desired look is realistic given time constraints
   */
  isTimeRealistic(estimatedProcessingTime: number): boolean {
    return estimatedProcessingTime <= this._timeConstraint;
  }

  /**
   * Check if the desired look fits within budget
   */
  isWithinBudget(estimatedCost: number): boolean {
    return estimatedCost >= this._budgetRange.min && 
           estimatedCost <= this._budgetRange.max;
  }

  /**
   * Get compatibility score with skin tone (0-100)
   */
  getSkinToneCompatibility(): number {
    // This would implement color theory rules for skin tone matching
    // Simplified version here - real implementation would be more sophisticated
    const coolSkinTones = ['fair-cool', 'medium-cool', 'deep-cool'];
    const warmSkinTones = ['fair-warm', 'medium-warm', 'deep-warm'];
    
    let score = 80; // Base score
    
    if (coolSkinTones.includes(this._skinTone) && this._colorTone.warmth === 'cool') {
      score += 15;
    } else if (warmSkinTones.includes(this._skinTone) && this._colorTone.warmth === 'warm') {
      score += 15;
    } else if (this._colorTone.warmth === 'neutral') {
      score += 10; // Neutral works with most skin tones
    } else {
      score -= 10; // Contrasting warmth
    }
    
    return Math.min(100, Math.max(0, score));
  }

  /**
   * Check if maintenance requirements match client's commitment
   */
  isMaintenanceRealistic(): boolean {
    const highMaintenanceMethods = ['highlights', 'ombre', 'balayage'];
    const isHighMaintenanceMethod = highMaintenanceMethods.includes(this._applicationMethod);
    
    if (isHighMaintenanceMethod && this._maintenanceLevel === 'low') {
      return false;
    }
    
    if (this._intensity === 'dramatic' && this._maintenanceLevel === 'low') {
      return false;
    }
    
    return true;
  }

  /**
   * Get recommended consultation level
   */
  getRecommendedConsultationLevel(): 'basic' | 'detailed' | 'extensive' {
    if (this._isFirstTime || this._intensity === 'dramatic' || this._priority === 'consultation') {
      return 'extensive';
    }
    
    if (this._applicationMethod === 'balayage' || this._applicationMethod === 'ombre' || 
        this.getSkinToneCompatibility() < 70) {
      return 'detailed';
    }
    
    return 'basic';
  }

  /**
   * Serialize to JSON for storage
   */
  toJSON(): Record<string, any> {
    return {
      id: this._id,
      salonId: this._salonId,
      clientId: this._clientId,
      targetLevel: this._targetLevel,
      colorTone: this._colorTone,
      intensity: this._intensity,
      applicationMethod: this._applicationMethod,
      referenceImages: this._referenceImages,
      description: this._description,
      maintenanceLevel: this._maintenanceLevel,
      budgetRange: this._budgetRange,
      lifestyleFactors: this._lifestyleFactors,
      timeConstraint: this._timeConstraint,
      previousExperience: this._previousExperience,
      skinTone: this._skinTone,
      eyeColor: this._eyeColor,
      isFirstTime: this._isFirstTime,
      specialRequests: this._specialRequests,
      avoidColors: this._avoidColors,
      inspirationSource: this._inspirationSource,
      createdAt: this._createdAt.toISOString(),
      priority: this._priority
    };
  }

  /**
   * Create from JSON data
   */
  static fromJSON(data: Record<string, any>): DesiredLook {
    return DesiredLook.create({
      ...data,
      createdAt: new Date(data.createdAt)
    });
  }
}