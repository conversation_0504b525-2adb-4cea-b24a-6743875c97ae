/**
 * AnalyzeDesiredLookUseCase - Analyze desired hair color look
 * 
 * Processes reference images to determine target color specs and viability.
 * Includes colorimetry validation to ensure achievable results.
 * 
 * Key Features:
 * - GPT-4 Vision integration for reference image analysis
 * - Colorimetry rules validation (color doesn't lift color)
 * - Session calculation based on current state
 * - Viability scoring and risk assessment
 * - Zone-based target analysis
 */

import { 
  Result, 
  AnalyzeDesiredLookRequest, 
  DesiredLookAnalysis,
  HairDiagnosis 
} from '../types/use-case.types.ts';

import {
  IAIService,
  ICacheService,
  IImageProcessor,
  IValidator,
  ILogger,
  IPromptTemplateService,
  IRetryService,
  OpenAIParams
} from '../services/interfaces.ts';

export class AnalyzeDesiredLookUseCase {
  constructor(
    private aiService: IAIService,
    private cacheService: ICacheService,
    private imageProcessor: IImageProcessor,
    private validator: IValidator,
    private logger: ILogger,
    private promptService: IPromptTemplateService,
    private retryService: IRetryService
  ) {}

  async execute(request: AnalyzeDesiredLookRequest): Promise<Result<DesiredLookAnalysis>> {
    const startTime = Date.now();
    let cacheKey: string | null = null;

    try {
      // 1. Validate Request
      const validationResult = this.validator.validateRequest(request, {
        required: ['salonId'],
        optional: ['imageUrl', 'imageBase64', 'currentLevel', 'diagnosis']
      });

      if (!validationResult.valid) {
        throw new Error(`Invalid request: ${validationResult.errors.join(', ')}`);
      }

      // 2. Check Cache First
      cacheKey = this.cacheService.generateKey('analyze_desired_look', request);
      const cachedResult = await this.cacheService.get(
        request.salonId, 
        'analyze_desired_look', 
        cacheKey
      );

      if (cachedResult) {
        this.logger.debug('Cache hit for desired look analysis', { cacheKey });
        return { success: true, data: cachedResult };
      }

      // 3. Process Hair Context (backward compatibility)
      const hairContext = this.processHairContext(request);

      // 4. Validate and Process Image
      await this.imageProcessor.validateImage(request.imageUrl, request.imageBase64);
      const imageDataUrl = await this.imageProcessor.convertToDataUrl(
        request.imageUrl, 
        request.imageBase64
      );

      // 5. Generate Contextual Prompt
      const prompt = this.generateDesiredLookPrompt(hairContext);

      // 6. Prepare AI Request
      const aiParams: OpenAIParams = {
        model: 'gpt-4o', // Always use full model for complex colorimetry analysis
        messages: [
          {
            role: 'user',
            content: [
              { type: 'text', text: prompt },
              { 
                type: 'image_url', 
                image_url: { 
                  url: imageDataUrl, 
                  detail: 'high' 
                } 
              }
            ]
          }
        ],
        maxTokens: 800,
        temperature: 0,
        responseFormat: { type: 'json_object' },
        seed: 42
      };

      // 7. Call AI with Retry Logic
      const aiResponse = await this.retryService.retryWithBackoff(
        () => this.aiService.callOpenAI(aiParams),
        3,
        1000
      );

      // 8. Parse and Validate Response
      const analysisResult = this.parseAIResponse(aiResponse);

      // 9. Apply Colorimetry Validation
      const desiredLookAnalysis = this.applyColorimetryValidation(
        analysisResult,
        hairContext
      );

      // 10. Calculate Metrics
      const latency = Date.now() - startTime;
      const cost = this.aiService.calculateCost(
        'gpt-4o',
        aiResponse.usage.prompt_tokens,
        aiResponse.usage.completion_tokens
      );

      // 11. Log Success Metrics
      await this.logger.trackMetrics({
        model: 'gpt-4o',
        tokens: aiResponse.usage.total_tokens,
        cost,
        latency,
        success: true
      });

      this.logger.info('Desired look analysis completed successfully', {
        salonId: request.salonId,
        targetLevel: desiredLookAnalysis.targetLevel,
        viabilityScore: desiredLookAnalysis.viability.achievable,
        sessionCount: desiredLookAnalysis.viability.sessionCount,
        riskLevel: desiredLookAnalysis.viability.riskLevel,
        cost: cost.toFixed(4),
        tokens: aiResponse.usage.total_tokens,
        latency
      });

      // 12. Cache Result
      if (cacheKey) {
        await this.cacheService.set(
          request.salonId,
          'analyze_desired_look',
          cacheKey,
          request,
          desiredLookAnalysis,
          'gpt-4o',
          aiResponse.usage.total_tokens,
          cost
        );
      }

      return { success: true, data: desiredLookAnalysis };

    } catch (error: any) {
      // Log Error Metrics
      const latency = Date.now() - startTime;
      await this.logger.trackMetrics({
        model: 'gpt-4o',
        tokens: 0,
        cost: 0,
        latency,
        success: false,
        errorType: error.constructor.name
      });

      this.logger.error('Desired look analysis failed', {
        salonId: request.salonId,
        error: error.message,
        latency,
        cacheKey
      });

      return { success: false, error: error.message };
    }
  }

  private processHairContext(request: AnalyzeDesiredLookRequest): any {
    // Backward compatibility: use currentLevel if no diagnosis
    const hairContext = request.diagnosis || { 
      averageLevel: request.currentLevel || 6 
    };

    // Extract current level and state for colorimetry validation
    const actualCurrentLevel = 
      request.diagnosis?.level ||
      request.diagnosis?.averageLevel ||
      request.diagnosis?.averageDepthLevel ||
      request.currentLevel ||
      6;

    const currentState = this.determineHairState(request.diagnosis);

    return {
      ...hairContext,
      actualCurrentLevel,
      currentState
    };
  }

  private determineHairState(diagnosis?: HairDiagnosis): 'natural' | 'colored' | 'bleached' {
    if (!diagnosis?.detectedChemicalProcess) {
      return 'natural';
    }

    const process = diagnosis.detectedChemicalProcess.toLowerCase();
    
    if (process.includes('teñido') || process.includes('colored')) {
      return 'colored';
    }
    
    if (process.includes('decolorado') || process.includes('bleached')) {
      return 'bleached';
    }

    return 'natural';
  }

  private generateDesiredLookPrompt(hairContext: any): string {
    const diagnosticContext = hairContext.averageLevel ? `
  El análisis del cabello actual muestra:
  - Nivel actual: ${hairContext.averageLevel}
  - Tono actual: ${hairContext.overallTone || 'No especificado'}
  - Estado: ${hairContext.detectedChemicalProcess || 'Natural'}
  - Condición: ${hairContext.overallCondition || 'Buena'}
  ${hairContext.zoneAnalysis?.roots?.grayPercentage ? `- Canas: ${hairContext.zoneAnalysis.roots.grayPercentage}%` : ''}
  
  Considera estos factores al evaluar la viabilidad del color deseado.` : '';

    return `Analiza esta imagen de referencia de color de cabello deseado.
  ${diagnosticContext}
  
  IMPORTANTE - Principios de colorimetría para calcular sesiones:
  - COLOR NO LEVANTA COLOR: Si el cabello tiene color artificial (Teñido/Colored), NO se puede aclarar con tinte
  - Cabello natural: puede aclarar hasta 3 niveles con tinte en una sesión
  - Cabello con color: requiere decapado primero para aclarar cualquier nivel
  - Máximo aclarado por sesión con decoloración: 4 niveles
  - Al oscurecer 3+ niveles: requiere pre-pigmentación
  
  Para calcular estimatedSessions:
  - Si necesita aclarar cabello con color: mínimo 2 sesiones (decapado + decoloración)
  - Si aclara >4 niveles: dividir entre 4 y redondear hacia arriba
  - Si el proceso no es viable: indicar en warnings
  
  Proporciona un análisis en formato JSON con esta estructura exacta:
  {
    "detectedLevel": número decimal del nivel objetivo general,
    "detectedTone": "tono principal detectado",
    "detectedTechnique": "técnica de aplicación detectada",
    "detectedTones": ["lista de tonos presentes"],
    "viabilityScore": 0-100,
    "estimatedSessions": número de sesiones necesarias (basado en principios de colorimetría),
    "requiredProcesses": ["procesos necesarios"],
    "confidence": porcentaje de confianza,
    "warnings": ["advertencias si las hay"],
    "zoneAnalysis": {
      "roots": {
        "level": número decimal del nivel en raíces,
        "tone": "tono detectado en raíces",
        "reflect": "reflejo detectado (Ceniza, Natural, Dorado, etc.)"
      },
      "mids": {
        "level": número decimal del nivel en medios,
        "tone": "tono detectado en medios",
        "reflect": "reflejo detectado"
      },
      "ends": {
        "level": número decimal del nivel en puntas,
        "tone": "tono detectado en puntas",
        "reflect": "reflejo detectado"
      }
    }
  }`;
  }

  private parseAIResponse(response: any): any {
    if (!response.choices || !response.choices[0]?.message?.content) {
      throw new Error('Invalid AI response structure');
    }

    const choice = response.choices[0];

    // Handle refusals and errors
    if (choice.message.refusal) {
      throw new Error(`OpenAI refused request: ${choice.message.refusal}`);
    }

    if (choice.finish_reason === 'content_filter') {
      throw new Error('Image blocked by content filter');
    }

    // Parse JSON content
    try {
      const content = choice.message.content.trim();
      return JSON.parse(content);
    } catch (parseError: any) {
      throw new Error(`Failed to parse AI response: ${parseError.message}`);
    }
  }

  private applyColorimetryValidation(
    analysisResult: any,
    hairContext: any
  ): DesiredLookAnalysis {
    const currentLevel = hairContext.actualCurrentLevel || 6;
    const currentState = hairContext.currentState || 'natural';
    const targetLevel = analysisResult.detectedLevel || currentLevel;
    const levelDifference = targetLevel - currentLevel;

    // Apply colorimetry rules
    const colorimetryValidation = this.validateColorimetryRules(
      currentLevel,
      targetLevel,
      currentState,
      analysisResult.requiredProcesses || []
    );

    // Determine viability
    const viability = this.calculateViability(
      levelDifference,
      currentState,
      analysisResult.estimatedSessions || 1,
      colorimetryValidation
    );

    // Calculate estimated time based on processes
    const estimatedTime = this.calculateEstimatedTime(
      analysisResult.requiredProcesses || [],
      analysisResult.estimatedSessions || 1
    );

    return {
      targetLevel: analysisResult.detectedLevel || currentLevel,
      targetTone: analysisResult.detectedTone || 'Natural',
      targetReflect: analysisResult.detectedTones?.[0] || 'Natural',
      viability,
      recommendations: this.generateRecommendations(
        levelDifference,
        currentState,
        colorimetryValidation
      ),
      colorimetryValidation,
      estimatedTime,
      requiredProducts: this.inferRequiredProducts(
        analysisResult.requiredProcesses || []
      )
    };
  }

  private validateColorimetryRules(
    currentLevel: number,
    targetLevel: number,
    currentState: string,
    requiredProcesses: string[]
  ): { valid: boolean; warnings: string[] } {
    const warnings: string[] = [];
    let valid = true;

    const levelDifference = targetLevel - currentLevel;

    // Rule 1: Color cannot lift color
    if (levelDifference > 0 && currentState === 'colored') {
      const hasDecolorationProcess = requiredProcesses.some(process =>
        process.toLowerCase().includes('decoloración') ||
        process.toLowerCase().includes('bleach') ||
        process.toLowerCase().includes('decapado')
      );

      if (!hasDecolorationProcess && levelDifference > 2) {
        warnings.push(
          `Impossible to lift ${levelDifference} levels on colored hair without bleaching`
        );
        valid = false;
      }
    }

    // Rule 2: Maximum lift with permanent color
    if (levelDifference > 3 && currentState === 'natural') {
      const hasDecolorationProcess = requiredProcesses.some(process =>
        process.toLowerCase().includes('decoloración') ||
        process.toLowerCase().includes('bleach')
      );

      if (!hasDecolorationProcess) {
        warnings.push(
          `Permanent color cannot lift more than 3 levels (attempting ${levelDifference})`
        );
        valid = false;
      }
    }

    // Rule 3: Pre-pigmentation for darkening
    if (levelDifference < -2) {
      const hasPrePigmentation = requiredProcesses.some(process =>
        process.toLowerCase().includes('pre-pigment')
      );

      if (!hasPrePigmentation) {
        warnings.push('Pre-pigmentation recommended when darkening 3+ levels');
      }
    }

    return { valid, warnings };
  }

  private calculateViability(
    levelDifference: number,
    currentState: string,
    estimatedSessions: number,
    colorimetryValidation: { valid: boolean; warnings: string[] }
  ): {
    achievable: boolean;
    sessionCount: number;
    riskLevel: 'low' | 'medium' | 'high';
    concerns: string[];
  } {
    const concerns: string[] = [...colorimetryValidation.warnings];
    let riskLevel: 'low' | 'medium' | 'high' = 'low';
    let achievable = true;

    // Adjust session count based on colorimetry rules
    let adjustedSessions = estimatedSessions;

    if (Math.abs(levelDifference) > 4) {
      riskLevel = 'high';
      concerns.push('Extreme level change may require multiple appointments');
      adjustedSessions = Math.max(adjustedSessions, Math.ceil(Math.abs(levelDifference) / 4));
    } else if (Math.abs(levelDifference) > 2) {
      riskLevel = 'medium';
      concerns.push('Significant level change requires careful process timing');
    }

    // Special case: colored hair requiring lifting
    if (levelDifference > 0 && currentState === 'colored') {
      riskLevel = 'high';
      adjustedSessions = Math.max(adjustedSessions, 2);
      concerns.push('Color removal required before lightening');
    }

    // Check if colorimetry violations make it unachievable
    if (!colorimetryValidation.valid) {
      achievable = false;
      riskLevel = 'high';
    }

    return {
      achievable,
      sessionCount: adjustedSessions,
      riskLevel,
      concerns
    };
  }

  private calculateEstimatedTime(processes: string[], sessions: number): number {
    // Base time per process type (in minutes)
    const processTimes: Record<string, number> = {
      'decoloración': 45,
      'bleach': 45,
      'decapado': 30,
      'color_removal': 30,
      'coloración': 35,
      'color': 35,
      'matizado': 20,
      'toning': 20,
      'pre-pigmentación': 15
    };

    let totalTime = 0;

    for (const process of processes) {
      const processLower = process.toLowerCase();
      
      for (const [key, time] of Object.entries(processTimes)) {
        if (processLower.includes(key)) {
          totalTime += time;
          break;
        }
      }
    }

    // Default time if no specific processes identified
    if (totalTime === 0) {
      totalTime = 45; // Standard color service
    }

    // Add setup and finishing time
    totalTime += 30;

    // Multiply by sessions if multi-session service
    if (sessions > 1) {
      totalTime *= sessions;
    }

    return totalTime;
  }

  private generateRecommendations(
    levelDifference: number,
    currentState: string,
    colorimetryValidation: { valid: boolean; warnings: string[] }
  ): string[] {
    const recommendations: string[] = [];

    if (Math.abs(levelDifference) > 3) {
      recommendations.push('Consider gradual approach over multiple sessions');
    }

    if (currentState === 'colored' && levelDifference > 0) {
      recommendations.push('Strand test recommended before full application');
      recommendations.push('Consider color removal treatment first');
    }

    if (!colorimetryValidation.valid) {
      recommendations.push('Adjust expectations or technique to follow colorimetry rules');
    }

    if (levelDifference < -2) {
      recommendations.push('Pre-pigmentation treatment recommended');
    }

    if (recommendations.length === 0) {
      recommendations.push('Achievable with standard coloring technique');
    }

    return recommendations;
  }

  private inferRequiredProducts(processes: string[]): string[] {
    const products = new Set<string>();

    for (const process of processes) {
      const processLower = process.toLowerCase();

      if (processLower.includes('decoloración') || processLower.includes('bleach')) {
        products.add('Decolorante');
        products.add('Oxidante 20 vol');
      }

      if (processLower.includes('decapado') || processLower.includes('color_removal')) {
        products.add('Decapante');
      }

      if (processLower.includes('coloración') || processLower.includes('color')) {
        products.add('Tinte permanente');
        products.add('Oxidante');
      }

      if (processLower.includes('matizado') || processLower.includes('toning')) {
        products.add('Matizador');
        products.add('Oxidante 10 vol');
      }

      if (processLower.includes('pre-pigment')) {
        products.add('Pre-pigmentador');
      }
    }

    if (products.size === 0) {
      products.add('Tinte permanente');
      products.add('Oxidante 20 vol');
    }

    return Array.from(products);
  }
}