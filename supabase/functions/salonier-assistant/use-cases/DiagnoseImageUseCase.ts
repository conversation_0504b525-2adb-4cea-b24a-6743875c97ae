/**
 * DiagnoseImageUseCase - Hair diagnosis via GPT-4 Vision
 * 
 * Handles image-based hair analysis using OpenAI's vision models.
 * Supports both URL and base64 image inputs with comprehensive validation.
 * 
 * Key Features:
 * - GPT-4 Vision integration for hair analysis
 * - Image validation and format conversion
 * - Caching for cost optimization
 * - Comprehensive error handling
 * - Zone-based hair analysis
 */

import { 
  Result, 
  DiagnoseImageRequest, 
  HairDiagnosis 
} from '../types/use-case.types.ts';

import {
  IAIService,
  ICacheService,
  IImageProcessor,
  IValidator,
  ILogger,
  IPromptTemplateService,
  IRetryService,
  OpenAIParams
} from '../services/interfaces.ts';

export class DiagnoseImageUseCase {
  constructor(
    private aiService: IAIService,
    private cacheService: ICacheService,
    private imageProcessor: IImageProcessor,
    private validator: IValidator,
    private logger: ILogger,
    private promptService: IPromptTemplateService,
    private retryService: IRetryService
  ) {}

  async execute(request: DiagnoseImageRequest): Promise<Result<HairDiagnosis>> {
    const startTime = Date.now();
    let cacheKey: string | null = null;

    try {
      // 1. Validate Request
      const validationResult = this.validator.validateRequest(request, {
        required: ['salonId'],
        optional: ['imageUrl', 'imageBase64', 'diagnosis']
      });

      if (!validationResult.valid) {
        throw new Error(`Invalid request: ${validationResult.errors.join(', ')}`);
      }

      // 2. Check Cache First
      cacheKey = this.cacheService.generateKey('diagnose_image', request);
      const cachedResult = await this.cacheService.get(
        request.salonId, 
        'diagnose_image', 
        cacheKey
      );

      if (cachedResult) {
        this.logger.debug('Cache hit for image diagnosis', { cacheKey });
        return { success: true, data: cachedResult };
      }

      // 3. Validate and Process Image
      await this.imageProcessor.validateImage(request.imageUrl, request.imageBase64);
      const imageDataUrl = await this.imageProcessor.convertToDataUrl(
        request.imageUrl, 
        request.imageBase64
      );

      // 4. Determine Complexity and Select Model
      const complexity = this.aiService.determineComplexity(request.diagnosis);
      const model = this.aiService.selectOptimalModel(complexity, true); // true = needs vision

      // 5. Generate Prompt
      const prompt = this.promptService.getDiagnosisPrompt('full', 'es');
      const optimizedPrompt = this.promptService.optimizePrompt(prompt, 1500);

      // 6. Prepare OpenAI Request
      const aiParams: OpenAIParams = {
        model,
        messages: [
          {
            role: 'user',
            content: [
              { type: 'text', text: optimizedPrompt },
              { 
                type: 'image_url', 
                image_url: { 
                  url: imageDataUrl, 
                  detail: 'high' 
                } 
              }
            ]
          }
        ],
        maxTokens: 1500,
        temperature: 0,
        responseFormat: { type: 'json_object' },
        seed: 42
      };

      // 7. Call AI with Retry Logic
      const aiResponse = await this.retryService.retryWithBackoff(
        () => this.aiService.callOpenAI(aiParams),
        3, // maxRetries
        1000 // baseDelay
      );

      // 8. Validate and Parse Response
      const diagnosis = await this.parseAndValidateResponse(aiResponse);

      // 9. Calculate Metrics
      const latency = Date.now() - startTime;
      const cost = this.aiService.calculateCost(
        model, 
        aiResponse.usage.prompt_tokens,
        aiResponse.usage.completion_tokens
      );

      // 10. Log Success Metrics
      await this.logger.trackMetrics({
        model,
        tokens: aiResponse.usage.total_tokens,
        cost,
        latency,
        success: true
      });

      this.logger.info('Hair diagnosis completed successfully', {
        salonId: request.salonId,
        model,
        complexity,
        latency,
        cost: cost.toFixed(4),
        tokens: aiResponse.usage.total_tokens,
        averageLevel: diagnosis.averageLevel,
        serviceComplexity: diagnosis.serviceComplexity
      });

      // 11. Cache Result
      if (cacheKey) {
        await this.cacheService.set(
          request.salonId,
          'diagnose_image',
          cacheKey,
          request,
          diagnosis,
          model,
          aiResponse.usage.total_tokens,
          cost
        );
      }

      return { success: true, data: diagnosis };

    } catch (error: any) {
      // Log Error Metrics
      const latency = Date.now() - startTime;
      await this.logger.trackMetrics({
        model: 'unknown',
        tokens: 0,
        cost: 0,
        latency,
        success: false,
        errorType: error.constructor.name
      });

      this.logger.error('Hair diagnosis failed', {
        salonId: request.salonId,
        error: error.message,
        latency,
        cacheKey
      });

      return { success: false, error: error.message };
    }
  }

  private async parseAndValidateResponse(response: any): Promise<HairDiagnosis> {
    // Validate response structure
    if (!response.choices || !response.choices[0]?.message?.content) {
      throw new Error('Invalid AI response structure');
    }

    const choice = response.choices[0];

    // Handle content moderation
    if (choice.message.refusal) {
      throw new Error(`OpenAI refused to process image: ${choice.message.refusal}`);
    }

    // Handle finish reasons
    switch (choice.finish_reason) {
      case 'content_filter':
        throw new Error(
          'Image was blocked by content filters. Please use a photo showing only hair.'
        );
      case 'length':
        throw new Error('Response was truncated. Please try with a simpler image.');
    }

    // Parse JSON response
    let result: any;
    const aiResponse = choice.message.content;

    try {
      let jsonStr = aiResponse.trim();

      // Remove markdown code blocks if present
      if (jsonStr.includes('```json')) {
        jsonStr = jsonStr.replace(/```json\\s*/g, '').replace(/```/g, '');
      }

      // Find JSON object boundaries
      const jsonStart = jsonStr.indexOf('{');
      const jsonEnd = jsonStr.lastIndexOf('}') + 1;

      if (jsonStart !== -1 && jsonEnd > jsonStart) {
        jsonStr = jsonStr.substring(jsonStart, jsonEnd);
      }

      result = JSON.parse(jsonStr);
    } catch (parseError: any) {
      throw new Error(`Invalid JSON from AI: ${parseError.message}`);
    }

    // Validate required fields
    const requiredFields = [
      'averageLevel', 'overallTone', 'overallReflect', 
      'hairThickness', 'hairDensity', 'overallCondition',
      'zoneAnalysis'
    ];

    const validationResult = this.validator.validateAIResponse(result, requiredFields);
    if (!validationResult.valid) {
      throw new Error(`AI response missing fields: ${validationResult.errors.join(', ')}`);
    }

    // Map compatibility fields for backward compatibility
    this.mapCompatibilityFields(result);

    // Ensure zone analysis is complete
    this.validateZoneAnalysis(result);

    return result as HairDiagnosis;
  }

  private mapCompatibilityFields(result: any): void {
    // Map old field names to new ones for backward compatibility
    if (result.overallUndertone && !result.overallReflect) {
      result.overallReflect = result.overallUndertone;
      delete result.overallUndertone;
    }

    if (result.averageDepthLevel && !result.averageLevel) {
      result.averageLevel = result.averageDepthLevel;
      delete result.averageDepthLevel;
    }

    // Map zone analysis fields
    if (result.zoneAnalysis) {
      ['roots', 'mids', 'ends'].forEach(zone => {
        const zoneData = result.zoneAnalysis[zone];
        if (zoneData) {
          // Map depth to level
          if (zoneData.depth && !zoneData.level) {
            zoneData.level = zoneData.depth;
            delete zoneData.depth;
          }
          // Map undertone to reflect
          if (zoneData.undertone && !zoneData.reflect) {
            zoneData.reflect = zoneData.undertone;
            delete zoneData.undertone;
          }
        }
      });
    }
  }

  private validateZoneAnalysis(result: any): void {
    if (!result.zoneAnalysis) {
      throw new Error('Zone analysis is missing from AI response');
    }

    const requiredZones = ['roots', 'mids', 'ends'];
    const zoneFields = ['level', 'tone', 'reflect', 'confidence'];

    for (const zone of requiredZones) {
      const zoneData = result.zoneAnalysis[zone];
      if (!zoneData) {
        throw new Error(`Zone analysis missing for: ${zone}`);
      }

      for (const field of zoneFields) {
        if (zoneData[field] === undefined || zoneData[field] === null) {
          this.logger.warn(`Missing ${field} in ${zone} analysis`, { zoneData });
        }
      }
    }
  }
}