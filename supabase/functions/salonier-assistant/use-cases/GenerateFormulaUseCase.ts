/**
 * GenerateFormulaUseCase - Generate hair coloring formulas
 * 
 * Creates detailed hair formulas based on diagnosis and desired look.
 * Integrates proven formulas, chemical validation, and brand expertise.
 * 
 * Key Features:
 * - Proven formula lookup and reuse
 * - AI-powered formula generation with GPT-4
 * - Chemical validation and colorimetry rules
 * - Brand-specific product integration
 * - Cost optimization and caching
 * - Multi-step process handling
 */

import { 
  Result, 
  GenerateFormulaRequest, 
  HairFormula,
  HairDiagnosis,
  DesiredLookAnalysis
} from '../types/use-case.types.ts';

import {
  IAIService,
  ICacheService,
  IValidator,
  ILogger,
  IPromptTemplateService,
  IRetryService,
  OpenAIParams
} from '../services/interfaces.ts';

interface ProvenFormula {
  id: string;
  formula: any;
  success_count: number;
  avg_rating: number;
  total_uses: number;
}

interface ColorProcess {
  currentLevel: number;
  desiredLevel: number;
  currentState: 'natural' | 'colored' | 'bleached';
  hasMetallicSalts: boolean;
  hasHenna: boolean;
}

export class GenerateFormulaUseCase {
  constructor(
    private aiService: IAIService,
    private cacheService: ICacheService,
    private validator: IValidator,
    private logger: ILogger,
    private promptService: IPromptTemplateService,
    private retryService: IRetryService,
    private supabase: any, // Supabase client
    private provenFormulaService: any, // Service for proven formulas
    private brandExpertiseService: any, // Service for brand-specific rules
    private colorimetryValidator: any // Service for colorimetry validation
  ) {}

  async execute(request: GenerateFormulaRequest): Promise<Result<HairFormula>> {
    const startTime = Date.now();
    let cacheKey: string | null = null;

    try {
      // 1. Validate Request
      const validationResult = this.validator.validateRequest(request, {
        required: ['currentDiagnosis', 'desiredLook', 'salonId'],
        optional: ['availableProducts', 'clientPreferences']
      });

      if (!validationResult.valid) {
        throw new Error(`Invalid request: ${validationResult.errors.join(', ')}`);
      }

      // 2. Check Cache First
      cacheKey = this.cacheService.generateKey('generate_formula', request);
      const cachedResult = await this.cacheService.get(
        request.salonId, 
        'generate_formula', 
        cacheKey
      );

      if (cachedResult) {
        this.logger.debug('Cache hit for formula generation', { cacheKey });
        return { success: true, data: cachedResult };
      }

      // 3. Check for Proven Formulas
      const provenFormula = await this.findProvenFormula(request);
      
      if (provenFormula) {
        this.logger.info('Using proven formula', {
          formulaId: provenFormula.id,
          successCount: provenFormula.success_count
        });
        
        const formulaResult = await this.processProvenFormula(provenFormula, request);
        return { success: true, data: formulaResult };
      }

      // 4. Generate New Formula with AI
      this.logger.info('No suitable proven formula found, generating new formula with AI');
      
      const formulaResult = await this.generateNewFormula(request);

      // 5. Save as Potential Proven Formula
      await this.saveAsProvenFormula(request, formulaResult);

      // 6. Calculate Metrics and Cache
      const latency = Date.now() - startTime;
      
      if (cacheKey) {
        await this.cacheService.set(
          request.salonId,
          'generate_formula',
          cacheKey,
          request,
          formulaResult,
          'gpt-4',
          formulaResult.tokens || 0,
          formulaResult.cost || 0
        );
      }

      this.logger.info('Formula generation completed', {
        salonId: request.salonId,
        technique: formulaResult.technique,
        stepCount: formulaResult.stepByStep.length,
        totalTime: formulaResult.processingTime,
        latency
      });

      return { success: true, data: formulaResult };

    } catch (error: any) {
      const latency = Date.now() - startTime;
      
      await this.logger.trackMetrics({
        model: 'gpt-4',
        tokens: 0,
        cost: 0,
        latency,
        success: false,
        errorType: error.constructor.name
      });

      this.logger.error('Formula generation failed', {
        salonId: request.salonId,
        error: error.message,
        latency
      });

      return { success: false, error: error.message };
    }
  }

  private async findProvenFormula(request: GenerateFormulaRequest): Promise<ProvenFormula | null> {
    try {
      // Generate scenario hash for lookup
      const scenarioHash = await this.generateScenarioHash(
        request.currentDiagnosis,
        request.desiredLook
      );

      const diagnosisSummary = this.createDiagnosisSummary(request.currentDiagnosis);
      const desiredResultSummary = this.createDesiredResultSummary(request.desiredLook);

      // First try exact match
      const { data: exactMatch } = await this.supabase
        .from('proven_formulas')
        .select('*')
        .eq('scenario_hash', scenarioHash)
        .gte('success_count', 3)
        .gte('avg_rating', 4.0)
        .order('avg_rating', { ascending: false })
        .order('success_count', { ascending: false })
        .limit(1)
        .single();

      if (exactMatch) {
        return exactMatch;
      }

      // Search for similar formulas using RPC
      const { data: similarFormulas } = await this.supabase
        .rpc('find_similar_formulas', {
          p_diagnosis_keywords: diagnosisSummary,
          p_desired_keywords: desiredResultSummary,
          p_min_rating: 4.0,
          p_limit: 3
        });

      if (similarFormulas && similarFormulas.length > 0) {
        const bestMatch = similarFormulas[0];
        
        if (bestMatch.similarity_score > 0.7) {
          return bestMatch;
        }
      }

      return null;
    } catch (error: any) {
      this.logger.error('Error searching proven formulas', error);
      return null;
    }
  }

  private async processProvenFormula(
    provenFormula: ProvenFormula, 
    request: GenerateFormulaRequest
  ): Promise<HairFormula> {
    const formulationData = provenFormula.formula;

    // Add proven formula metadata
    formulationData.provenFormula = {
      id: provenFormula.id,
      successCount: provenFormula.success_count,
      avgRating: provenFormula.avg_rating,
      totalUses: provenFormula.total_uses,
      isProven: true,
      confidence: Math.min(0.95, 0.7 + (provenFormula.avg_rating / 5.0) * 0.25)
    };

    // Add proven formula indicator to warnings
    if (!formulationData.warnings) {
      formulationData.warnings = [];
    }
    formulationData.warnings.unshift(
      `✅ PROVEN FORMULA: This formula has been successful ${provenFormula.success_count} times with an average rating of ${provenFormula.avg_rating}/5.0`
    );

    // Run basic safety validation even on proven formulas
    try {
      const basicWarnings = await this.colorimetryValidator.validateBasic(
        formulationData,
        request.currentDiagnosis,
        request.desiredLook
      );
      
      if (basicWarnings.length > 0) {
        formulationData.warnings.push(...basicWarnings);
      }
    } catch (error) {
      this.logger.warn('Basic validation failed on proven formula', error);
    }

    // Convert to standard HairFormula format
    return this.convertToHairFormula(formulationData);
  }

  private async generateNewFormula(request: GenerateFormulaRequest): Promise<HairFormula> {
    // 1. Validate colorimetry rules
    const colorimetryValidation = await this.validateColorimetryRules(request);
    
    // 2. Get brand expertise if available
    const brandExpertise = await this.getBrandExpertise(request);

    // 3. Generate optimized prompt
    const prompt = await this.generateFormulaPrompt(request, colorimetryValidation, brandExpertise);

    // 4. Prepare AI request
    const complexity = this.aiService.determineComplexity({
      levelDifference: Math.abs(request.desiredLook.targetLevel - request.currentDiagnosis.averageLevel),
      hasChemicalProcess: request.currentDiagnosis.detectedChemicalProcess !== 'none',
      techniqueComplexity: this.getTechniqueComplexity(request.desiredLook)
    });

    const model = this.aiService.selectOptimalModel(complexity, false);

    const aiParams: OpenAIParams = {
      model,
      messages: [
        {
          role: 'system',
          content: 'You are an expert hair colorist with 20+ years of experience. Generate safe, professional formulas following strict colorimetry rules.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      maxTokens: 2000,
      temperature: 0.1, // Low temperature for consistent technical results
      responseFormat: { type: 'json_object' }
    };

    // 5. Call AI with retry logic
    const aiResponse = await this.retryService.retryWithBackoff(
      () => this.aiService.callOpenAI(aiParams),
      3,
      1000
    );

    // 6. Parse and validate response
    const formulationData = this.parseAIResponse(aiResponse);

    // 7. Apply chemical validation
    const validatedFormula = await this.applyChemicalValidation(formulationData, request);

    // 8. Calculate costs and metrics
    const cost = this.aiService.calculateCost(
      model,
      aiResponse.usage.prompt_tokens,
      aiResponse.usage.completion_tokens
    );

    await this.logger.trackMetrics({
      model,
      tokens: aiResponse.usage.total_tokens,
      cost,
      latency: 0, // Will be calculated by parent
      success: true
    });

    // 9. Convert to standard format
    const formula = this.convertToHairFormula(validatedFormula);
    formula.costEstimate = cost;

    return formula;
  }

  private async validateColorimetryRules(request: GenerateFormulaRequest): Promise<any> {
    const currentLevel = request.currentDiagnosis.averageLevel;
    const desiredLevel = request.desiredLook.targetLevel;
    const currentState = this.determineHairState(request.currentDiagnosis);

    const colorProcess: ColorProcess = {
      currentLevel: Math.round(currentLevel),
      desiredLevel: Math.round(desiredLevel),
      currentState,
      hasMetallicSalts: request.currentDiagnosis.detectedRisks?.includes('metallic') || false,
      hasHenna: request.currentDiagnosis.detectedRisks?.includes('henna') || false
    };

    return await this.colorimetryValidator.validate(colorProcess);
  }

  private determineHairState(diagnosis: HairDiagnosis): 'natural' | 'colored' | 'bleached' {
    const process = diagnosis.detectedChemicalProcess?.toLowerCase() || '';
    
    if (process.includes('color') || process.includes('teñido')) {
      return 'colored';
    }
    
    if (process.includes('bleach') || process.includes('decolorado')) {
      return 'bleached';
    }

    return 'natural';
  }

  private async getBrandExpertise(request: GenerateFormulaRequest): Promise<any> {
    if (!request.availableProducts || request.availableProducts.length === 0) {
      return null;
    }

    const primaryBrand = request.availableProducts[0].brand;
    
    try {
      return await this.brandExpertiseService.getExpertise(primaryBrand);
    } catch (error) {
      this.logger.warn('Failed to get brand expertise', { brand: primaryBrand, error });
      return null;
    }
  }

  private getTechniqueComplexity(desiredLook: DesiredLookAnalysis): 'low' | 'medium' | 'high' {
    // Determine complexity based on technique and requirements
    if (desiredLook.viability.riskLevel === 'high') return 'high';
    if (desiredLook.viability.sessionCount > 1) return 'high';
    if (Math.abs(desiredLook.targetLevel - 6) > 3) return 'medium';
    
    return 'low';
  }

  private async generateFormulaPrompt(
    request: GenerateFormulaRequest,
    colorimetryValidation: any,
    brandExpertise: any
  ): Promise<string> {
    // Generate comprehensive prompt based on all inputs
    let prompt = `Generate a professional hair coloring formula based on the following analysis:

CURRENT DIAGNOSIS:
- Hair Level: ${request.currentDiagnosis.averageLevel}
- Overall Condition: ${request.currentDiagnosis.overallCondition}
- Chemical Process History: ${request.currentDiagnosis.detectedChemicalProcess}
- Hair Density: ${request.currentDiagnosis.hairDensity}
- Hair Thickness: ${request.currentDiagnosis.hairThickness}
- Zone Analysis: Roots(${request.currentDiagnosis.zoneAnalysis.roots.level}), Mids(${request.currentDiagnosis.zoneAnalysis.mids.level}), Ends(${request.currentDiagnosis.zoneAnalysis.ends.level})

TARGET RESULT:
- Target Level: ${request.desiredLook.targetLevel}
- Target Tone: ${request.desiredLook.targetTone}
- Target Reflect: ${request.desiredLook.targetReflect}
- Estimated Sessions: ${request.desiredLook.viability.sessionCount}
- Risk Level: ${request.desiredLook.viability.riskLevel}

COLORIMETRY VALIDATION:
${colorimetryValidation?.instructions || 'Standard colorimetry rules apply'}

CLIENT PREFERENCES:
${request.clientPreferences ? JSON.stringify(request.clientPreferences, null, 2) : 'No specific preferences'}

AVAILABLE PRODUCTS:
${request.availableProducts ? JSON.stringify(request.availableProducts.slice(0, 10), null, 2) : 'Use generic product recommendations'}

${brandExpertise ? `BRAND EXPERTISE:\n${JSON.stringify(brandExpertise, null, 2)}` : ''}

Generate a comprehensive formula in JSON format with:
- Step-by-step process
- Exact product quantities and mixing ratios
- Processing times for each step
- Application techniques
- Safety warnings and precautions
- Expected results and maintenance instructions`;

    return this.promptService.optimizePrompt(prompt, 1800);
  }

  private parseAIResponse(response: any): any {
    if (!response.choices || !response.choices[0]?.message?.content) {
      throw new Error('Invalid AI response structure');
    }

    const choice = response.choices[0];
    
    if (choice.message.refusal) {
      throw new Error(`AI refused request: ${choice.message.refusal}`);
    }

    try {
      const content = choice.message.content.trim();
      return JSON.parse(content);
    } catch (parseError: any) {
      throw new Error(`Failed to parse AI response: ${parseError.message}`);
    }
  }

  private async applyChemicalValidation(formulationData: any, request: GenerateFormulaRequest): Promise<any> {
    try {
      const validationResult = await this.colorimetryValidator.validateFormula(
        formulationData,
        request.currentDiagnosis,
        request.desiredLook
      );

      if (!validationResult.isValid) {
        // Add validation warnings
        if (!formulationData.warnings) {
          formulationData.warnings = [];
        }
        formulationData.warnings.push(...validationResult.violations.map((v: any) => v.message));
      }

      return formulationData;
    } catch (error) {
      this.logger.warn('Chemical validation failed', error);
      return formulationData;
    }
  }

  private convertToHairFormula(formulationData: any): HairFormula {
    return {
      technique: formulationData.technique || 'full_color',
      processingTime: formulationData.totalTime || 45,
      applicationMethod: formulationData.applicationMethod || 'roots_to_ends',
      formula: {
        base: formulationData.steps?.[0]?.mix?.[0]?.productName || 'Color Base',
        additives: formulationData.steps?.flatMap((step: any) => 
          step.mix?.slice(1).map((product: any) => product.productName) || []
        ) || [],
        mixingRatio: formulationData.mixingRatio || '1:1',
        developer: {
          volume: formulationData.developerVolume || 20,
          amount: formulationData.developerAmount || '60ml'
        }
      },
      stepByStep: formulationData.steps?.map((step: any, index: number) => 
        `Step ${index + 1}: ${step.stepTitle || step.instructions}`
      ) || [],
      warnings: formulationData.warnings || [],
      expectedResult: formulationData.expectedResult || 'Professional color result',
      maintenanceInstructions: formulationData.maintenanceInstructions || [
        'Use color-safe shampoo',
        'Deep condition weekly',
        'Touch-up roots every 6-8 weeks'
      ]
    };
  }

  private async generateScenarioHash(diagnosis: HairDiagnosis, desiredLook: DesiredLookAnalysis): Promise<string> {
    const normalizedScenario = {
      currentLevel: Math.round(diagnosis.averageLevel),
      targetLevel: Math.round(desiredLook.targetLevel),
      condition: diagnosis.overallCondition,
      chemicalProcess: diagnosis.detectedChemicalProcess
    };

    const hashInput = JSON.stringify(normalizedScenario);
    const encoder = new TextEncoder();
    const data = encoder.encode(hashInput);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('').substring(0, 32);
  }

  private createDiagnosisSummary(diagnosis: HairDiagnosis): string {
    return `Level ${diagnosis.averageLevel}, ${diagnosis.overallCondition} condition, ${diagnosis.detectedChemicalProcess || 'natural'}`;
  }

  private createDesiredResultSummary(desiredLook: DesiredLookAnalysis): string {
    return `Target level ${desiredLook.targetLevel}, ${desiredLook.targetTone} tone, ${desiredLook.viability.sessionCount} sessions`;
  }

  private async saveAsProvenFormula(request: GenerateFormulaRequest, formula: HairFormula): Promise<void> {
    try {
      const scenarioHash = await this.generateScenarioHash(request.currentDiagnosis, request.desiredLook);
      
      await this.supabase.rpc('upsert_proven_formula', {
        p_scenario_hash: scenarioHash,
        p_diagnosis_summary: this.createDiagnosisSummary(request.currentDiagnosis),
        p_desired_result_summary: this.createDesiredResultSummary(request.desiredLook),
        p_formula: formula,
        p_initial_rating: 0 // Mark as pending validation
      });

      this.logger.info('Formula saved as potential proven formula', { scenarioHash });
    } catch (error: any) {
      this.logger.warn('Failed to save as proven formula', error);
    }
  }
}