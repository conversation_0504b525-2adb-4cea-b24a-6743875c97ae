/**
 * AI Use Cases Index
 * 
 * Clean, focused AI use case classes extracted from the monolithic index.ts.
 * Each use case follows dependency injection pattern with proper error handling,
 * caching strategies, and cost optimization.
 */

export { DiagnoseImageUseCase } from './DiagnoseImageUseCase.ts';
export { AnalyzeDesiredLookUseCase } from './AnalyzeDesiredLookUseCase.ts';
export { GenerateFormulaUseCase } from './GenerateFormulaUseCase.ts';
export { ChatAssistantUseCase } from './ChatAssistantUseCase.ts';

// Re-export types for convenience
export type {
  Result,
  DiagnoseImageRequest,
  HairDiagnosis,
  AnalyzeDesiredLookRequest,
  DesiredLookAnalysis,
  GenerateFormulaRequest,
  HairFormula,
  ChatAssistantRequest,
  ChatAssistantResponse,
  AIUsage,
  AIRequestMetrics
} from '../types/use-case.types.ts';

// Re-export service interfaces
export type {
  IAIService,
  ICacheService,
  IImageProcessor,
  IValidator,
  ILogger,
  IPromptTemplateService,
  IRetryService,
  OpenAIParams,
  OpenAIResponse
} from '../services/interfaces.ts';