# Salonier Assistant Edge Function Test Suite

Comprehensive test suite for the refactored Salonier Assistant Edge Function, covering all Clean Architecture layers with >90% coverage and rigorous performance benchmarking.

## 🏗️ Architecture Overview

The test suite mirrors the Clean Architecture structure:

```
tests/
├── unit/                           # Layer-isolated unit tests
│   ├── domain/entities/           # Domain entity business logic
│   ├── use-cases/                 # Use case orchestration
│   ├── infrastructure/            # External service integration
│   └── application/               # Service and handler logic
├── integration/                    # Cross-layer integration tests
│   ├── services/                  # Application service workflows
│   ├── handlers/                  # HTTP handler integration
│   └── workflows/                 # End-to-end business flows
├── contract/                       # API compatibility and contracts
│   ├── edge-function.test.ts      # HTTP API contract tests
│   ├── openai-integration.test.ts # OpenAI API contracts
│   └── supabase-integration.test.ts # Database contracts
├── performance/                    # Performance and load testing
│   ├── load-test.ts              # Concurrent request handling
│   ├── memory-usage.test.ts      # Memory efficiency tests
│   └── cache-efficiency.test.ts  # Cache performance validation
├── mocks/                          # Test data and service mocks
│   ├── data/                      # Comprehensive test scenarios
│   └── services/                  # Mock service implementations
└── test-utils.ts                   # Shared testing utilities
```

## 🚀 Quick Start

### Run All Tests
```bash
# Execute complete test suite
deno run --allow-all run-tests.ts

# With coverage report
deno run --allow-all run-tests.ts --coverage
```

### Run Specific Test Types
```bash
# Unit tests only
deno run --allow-all run-tests.ts --unit

# Integration tests only  
deno run --allow-all run-tests.ts --integration

# Performance tests only
deno run --allow-all run-tests.ts --performance --verbose

# Contract tests only
deno run --allow-all run-tests.ts --contract
```

### Development Workflow
```bash
# Watch mode for TDD
deno run --allow-all run-tests.ts --unit --watch

# Run with verbose output and stop on first failure
deno run --allow-all run-tests.ts --verbose --bail

# Quick unit test run
deno test tests/unit/ --allow-all
```

## 📋 Test Categories

### Unit Tests (60% of suite)
Tests individual components in isolation with comprehensive mocking.

**Domain Entities:**
- `HairDiagnosis.test.ts` - Business rules, validation, immutability
- `ColorFormula.test.ts` - Formula calculation, safety validation
- `DesiredLook.test.ts` - Compatibility scoring, viability assessment

**Use Cases:**
- `DiagnoseImageUseCase.test.ts` - Image processing workflow
- `GenerateFormulaUseCase.test.ts` - Formula generation logic
- `AnalyzeDesiredLookUseCase.test.ts` - Look analysis pipeline
- `ChatAssistantUseCase.test.ts` - Conversation handling

**Infrastructure:**
- Service implementations
- Repository patterns
- External API integrations

### Integration Tests (30% of suite)
Tests component interactions and cross-layer workflows.

**Service Integration:**
- `DiagnosisService.test.ts` - Complete diagnosis orchestration
- `FormulationService.test.ts` - End-to-end formula generation
- `ChatService.test.ts` - Conversation flow management

**Handler Integration:**
- HTTP request/response handling
- Authentication and validation
- Error handling across layers

**Workflow Tests:**
- Complete service flows
- Multi-step operations
- Context enhancement logic

### Contract Tests (5% of suite)
Ensures API compatibility and external service contracts.

**API Contracts:**
- HTTP endpoint structure
- Request/response formats
- Error response consistency
- CORS and authentication

**External Service Contracts:**
- OpenAI API integration
- Supabase database operations
- Cache service behavior

### Performance Tests (5% of suite)
Validates system performance under various load conditions.

**Load Testing:**
- Response time requirements (<3s P95)
- Concurrent request handling (25+ simultaneous)
- Memory usage stability
- Error rate under stress

**Cache Performance:**
- Hit rate effectiveness (>40% target)
- Latency improvement (3-5x with cache)
- TTL behavior validation

## 🎯 Coverage Goals

| Layer | Target Coverage | Focus Areas |
|-------|----------------|-------------|
| Domain Entities | >95% | Business rules, validation logic |
| Use Cases | >90% | Workflow orchestration, error handling |
| Application Services | >85% | Service integration, context enhancement |
| Infrastructure | >80% | External service calls, data persistence |
| **Overall Target** | **>90%** | **Critical paths 100% covered** |

## 📊 Performance Benchmarks

### Response Time Requirements
- **Average**: <1.2s for typical diagnosis
- **P95**: <3.0s for complex scenarios
- **P99**: <5.0s for worst-case conditions

### Concurrency Targets
- **Light Load**: 5 concurrent requests, >98% success
- **Moderate Load**: 15 concurrent requests, >95% success
- **Heavy Load**: 25+ concurrent requests, >90% success

### Cache Effectiveness
- **Hit Rate**: >40% for production patterns
- **Performance Improvement**: 3-5x faster with cache hits
- **Memory Efficiency**: Stable usage under sustained load

## 🧪 Test Data & Scenarios

### Comprehensive Test Scenarios
The test suite includes realistic scenarios covering:

**Hair Diagnosis Scenarios:**
- Healthy virgin hair
- Previously colored hair needing refresh  
- Damaged hair requiring careful handling
- Gray hair coverage challenges
- Complex color correction cases
- Edge cases with low confidence

**Performance Scenarios:**
- Realistic salon workflow patterns
- Burst activity with quiet periods
- Variable complexity requests
- Mixed success/failure rates

**Error Scenarios:**
- Invalid image data
- Network timeouts
- AI service unavailability
- Content filter rejections

### Mock Data Factory
Provides consistent, realistic test data:
```typescript
// Generate realistic hair diagnosis
const diagnosis = MockDataFactory.createHairDiagnosis({
  averageLevel: 6,
  overallCondition: 'good',
  serviceComplexity: 'medium'
});

// Create complex color formula
const formula = MockDataFactory.createColorFormula({
  technique: 'global application with toner',
  processingTime: 35
});
```

## ⚡ Test Utilities

### Performance Testing
```typescript
// Measure latency
const { result, latency } = await PerformanceTestUtils.measureLatency(() => 
  useCase.execute(request)
);

// Load testing
const loadResult = await PerformanceTestUtils.runLoadTest(testFn, {
  concurrency: 10,
  duration: 5000,
  expectedLatencyP95: 1500,
  expectedSuccessRate: 0.95
});
```

### Assertions
```typescript
// Validate hair diagnosis structure
TestAssertions.assertValidHairDiagnosis(diagnosis);

// Validate color formula business rules
TestAssertions.assertValidColorFormula(formula);

// Performance benchmarks
TestAssertions.assertValidPerformanceMetrics(result, expectations);
```

### Mock Services
```typescript
// Complete mock setup
const mockAIService = MockServices.createMockAIService();
const mockCacheService = MockServices.createMockCacheService();
const mockValidator = MockServices.createMockValidator();
```

## 🔧 Configuration

### Deno Configuration
```json
{
  "compilerOptions": {
    "allowJs": true,
    "lib": ["deno.window", "deno.unstable"],
    "strict": true
  },
  "test": {
    "include": ["**/*.test.ts"],
    "exclude": ["node_modules/**/*"]
  }
}
```

### Environment Variables
```bash
# Test environment configuration
TEST_ENV=test
LOG_LEVEL=debug
CACHE_TTL=300
PERFORMANCE_TIMEOUT=10000
```

## 📈 CI/CD Integration

### GitHub Actions Workflow
```yaml
name: Test Suite
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: denoland/setup-deno@v1
        with:
          deno-version: v1.x
      - name: Run Tests
        run: deno run --allow-all run-tests.ts --coverage --bail
      - name: Upload Coverage
        uses: codecov/codecov-action@v3
        with:
          files: coverage_report/lcov.info
```

### Pre-commit Hooks
```bash
#!/bin/sh
# .git/hooks/pre-commit
echo "Running tests before commit..."
deno run --allow-all run-tests.ts --unit --bail
```

## 🐛 Debugging Tests

### Verbose Debugging
```bash
# Maximum verbosity
deno run --allow-all run-tests.ts --verbose

# Debug specific test
deno test tests/unit/domain/entities/HairDiagnosis.test.ts --allow-all --verbose
```

### Test-Specific Debugging
```typescript
// Add debugging to tests
Deno.test('debug specific scenario', async () => {
  console.log('Debug info:', { request, expectedResponse });
  
  const result = await useCase.execute(request);
  
  console.log('Actual result:', JSON.stringify(result, null, 2));
  assert(result.success);
});
```

### Performance Debugging
```bash
# Run performance tests with detailed output
deno run --allow-all run-tests.ts --performance --verbose

# Profile specific test
deno test --allow-all --v8-flags=--prof tests/performance/load-test.ts
```

## 🏆 Best Practices

### Test Structure
1. **AAA Pattern**: Arrange, Act, Assert
2. **Descriptive Names**: Tests should read like specifications
3. **Independent Tests**: No test dependencies or shared state
4. **Fast Execution**: Unit tests <100ms, integration tests <1s

### Mock Strategy
1. **Mock External Dependencies**: All network calls, file system, etc.
2. **Realistic Data**: Use factory methods for consistent test data
3. **Fail Fast**: Mock failures for error path testing
4. **State Isolation**: Reset mocks between tests

### Performance Testing
1. **Baseline Measurements**: Establish performance benchmarks
2. **Stress Testing**: Test beyond normal operating conditions
3. **Memory Monitoring**: Watch for leaks and excessive usage
4. **Real-world Patterns**: Test with realistic usage scenarios

## 🚨 Troubleshooting

### Common Issues

**Test Timeouts:**
```bash
# Increase timeout for performance tests
deno test --allow-all --timeout=30000 tests/performance/
```

**Permission Errors:**
```bash
# Ensure all required permissions
deno test --allow-all --unstable
```

**Coverage Issues:**
```bash
# Generate detailed coverage report
deno test --allow-all --coverage=coverage_report
deno coverage coverage_report --html --output=coverage_html
```

**Flaky Tests:**
- Check for race conditions in concurrent tests
- Ensure proper mock cleanup between tests
- Verify test independence (no shared state)

### Getting Help

1. **Check Test Output**: Run with `--verbose` for detailed information
2. **Review Logs**: Check mock service calls and responses
3. **Performance Issues**: Use `--performance --verbose` for benchmarks
4. **Debug Mode**: Add console.log statements to failing tests

## 📚 Related Documentation

- [Clean Architecture Guide](../docs/clean-architecture.md)
- [Performance Requirements](../docs/performance-requirements.md)
- [API Documentation](../docs/api-reference.md)
- [Deployment Guide](../docs/deployment.md)

---

**Test Coverage Status:** ![Coverage Badge](https://img.shields.io/badge/coverage-90%25-brightgreen)
**Build Status:** ![Build Status](https://img.shields.io/badge/build-passing-brightgreen)
**Performance:** ![Performance](https://img.shields.io/badge/P95%20latency-2.1s-green)