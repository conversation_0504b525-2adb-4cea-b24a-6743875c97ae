/**
 * DiagnosisService Integration Tests
 * 
 * Tests for the DiagnosisService application service, focusing on:
 * - End-to-end workflow orchestration
 * - Use case integration
 * - Context enhancement logic
 * - Risk assessment calculations
 * - Next steps determination
 * - Error handling across layers
 * - Performance under realistic loads
 */

import { assertEquals, assert, stub, spy, PerformanceTestUtils, TestAssertions } from '../../test-utils.ts';
import { DiagnosisService, DiagnosisRequest } from '../../../application/services/DiagnosisService.ts';
import { DiagnoseImageUseCase } from '../../../use-cases/DiagnoseImageUseCase.ts';
import { MockServices, MockDataFactory } from '../../test-utils.ts';

Deno.test('DiagnosisService Integration - Complete Workflow', async (t) => {
  await t.step('should process complete diagnosis workflow successfully', async () => {
    // Arrange
    const mockDiagnoseImageUseCase = {
      execute: stub().returns(Promise.resolve({
        success: true,
        data: MockDataFactory.createHairDiagnosis({
          averageLevel: 6,
          overallCondition: 'good',
          detectedRisks: ['moderate porosity'],
          overallConfidence: 0.85,
          serviceComplexity: 'medium',
          estimatedTime: 120
        })
      }))
    };

    const mockCacheService = MockServices.createMockCacheService();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();

    const service = new DiagnosisService(
      mockDiagnoseImageUseCase as any,
      mockCacheService,
      mockValidator,
      mockLogger
    );

    const request: DiagnosisRequest = {
      imageUrl: 'https://example.com/hair-photo.jpg',
      salonId: 'salon-123',
      userId: 'user-456',
      clientId: 'client-789',
      requestId: 'req-001',
      metadata: {
        clientAge: 35,
        previousServices: ['coloring'],
        skinTone: 'warm',
        naturalHairColor: 'medium brown'
      }
    };

    // Act
    const result = await service.processImageDiagnosis(request);

    // Assert
    assert(result.success);
    assert(result.data);

    const response = result.data!;
    TestAssertions.assertValidHairDiagnosis(response.diagnosis);
    
    // Verify response structure
    assert(typeof response.confidence === 'number');
    assert(response.confidence >= 0 && response.confidence <= 100);
    assert(typeof response.processingTime === 'number');
    assert(response.processingTime > 0);
    assert(Array.isArray(response.recommendedActions));
    assert(response.riskAssessment);
    assert(response.nextSteps);
    assert(response.metadata);

    // Verify risk assessment structure
    assert(['low', 'medium', 'high', 'critical'].includes(response.riskAssessment.level));
    assert(Array.isArray(response.riskAssessment.factors));
    assert(Array.isArray(response.riskAssessment.recommendations));

    // Verify next steps structure
    assert(Array.isArray(response.nextSteps.immediate));
    assert(Array.isArray(response.nextSteps.followUp));
    assert(typeof response.nextSteps.consultation === 'boolean');

    // Verify metadata
    assert(response.metadata.timestamp instanceof Date);
    assert(typeof response.metadata.cached === 'boolean');

    // Verify use case was called
    assert(mockDiagnoseImageUseCase.execute.called);
    assert(mockLogger.info.called);
  });

  await t.step('should handle use case failures gracefully', async () => {
    // Arrange
    const mockDiagnoseImageUseCase = {
      execute: stub().returns(Promise.resolve({
        success: false,
        error: 'Image processing failed'
      }))
    };

    const mockCacheService = MockServices.createMockCacheService();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();

    const service = new DiagnosisService(
      mockDiagnoseImageUseCase as any,
      mockCacheService,
      mockValidator,
      mockLogger
    );

    const request: DiagnosisRequest = {
      imageUrl: 'https://example.com/hair-photo.jpg',
      salonId: 'salon-123',
      userId: 'user-456'
    };

    // Act
    const result = await service.processImageDiagnosis(request);

    // Assert
    assert(!result.success);
    assertEquals(result.error, 'Image processing failed');
    assert(mockLogger.error.called);
  });

  await t.step('should validate request before processing', async () => {
    // Arrange
    const mockDiagnoseImageUseCase = { execute: stub() };
    const mockCacheService = MockServices.createMockCacheService();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();

    const service = new DiagnosisService(
      mockDiagnoseImageUseCase as any,
      mockCacheService,
      mockValidator,
      mockLogger
    );

    const invalidRequest: DiagnosisRequest = {
      // Missing required fields
      imageUrl: 'test.jpg',
      salonId: '',
      userId: ''
    };

    // Act
    const result = await service.processImageDiagnosis(invalidRequest);

    // Assert
    assert(!result.success);
    assert(result.error?.includes('Validation failed'));
    assert(mockLogger.warn.called);
    
    // Verify use case was not called due to validation failure
    assert(!mockDiagnoseImageUseCase.execute.called);
  });
});

Deno.test('DiagnosisService Integration - Context Enhancement', async (t) => {
  await t.step('should enhance diagnosis with client history', async () => {
    // Arrange
    const baseDiagnosis = MockDataFactory.createHairDiagnosis({
      overallTone: 'warm golden',
      averageLevel: 6,
      detectedRisks: []
    });

    const mockDiagnoseImageUseCase = {
      execute: stub().returns(Promise.resolve({
        success: true,
        data: baseDiagnosis
      }))
    };

    const mockCacheService = MockServices.createMockCacheService();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();

    const service = new DiagnosisService(
      mockDiagnoseImageUseCase as any,
      mockCacheService,
      mockValidator,
      mockLogger
    );

    const request: DiagnosisRequest = {
      imageUrl: 'test.jpg',
      salonId: 'salon-123',
      userId: 'user-456',
      clientId: 'client-789',
      metadata: {
        clientAge: 28,
        previousServices: ['bleaching', 'coloring'],
        skinTone: 'cool',
        naturalHairColor: 'dark blonde'
      }
    };

    // Act
    const result = await service.processImageDiagnosis(request);

    // Assert
    assert(result.success);
    assert(result.data);

    const enhancedDiagnosis = result.data.diagnosis as any;
    assert(enhancedDiagnosis.contextualRecommendations);
    assert(Array.isArray(enhancedDiagnosis.contextualRecommendations));
    
    // Should include bleaching history recommendation
    assert(enhancedDiagnosis.contextualRecommendations.some((rec: string) => 
      rec.includes('protein treatment') && rec.includes('bleaching')
    ));

    // Should include cool skin tone recommendation for warm hair
    assert(enhancedDiagnosis.contextualRecommendations.some((rec: string) => 
      rec.includes('ash series') && rec.includes('neutralization')
    ));
  });

  await t.step('should create historical comparison when previous data available', async () => {
    // Arrange
    const currentDiagnosis = MockDataFactory.createHairDiagnosis({
      averageLevel: 7,
      overallCondition: 'fair'
    });

    const mockDiagnoseImageUseCase = {
      execute: stub().returns(Promise.resolve({
        success: true,
        data: currentDiagnosis
      }))
    };

    const mockCacheService = MockServices.createMockCacheService();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();

    const service = new DiagnosisService(
      mockDiagnoseImageUseCase as any,
      mockCacheService,
      mockValidator,
      mockLogger
    );

    const request: DiagnosisRequest = {
      imageUrl: 'test.jpg',
      salonId: 'salon-123',
      userId: 'user-456',
      clientId: 'client-789',
      metadata: {
        previousLevel: 5, // Previously level 5, now level 7
        previousServices: ['coloring']
      }
    };

    // Act
    const result = await service.processImageDiagnosis(request);

    // Assert
    assert(result.success);
    assert(result.data);

    const enhancedDiagnosis = result.data.diagnosis as any;
    assert(enhancedDiagnosis.historicalComparison);
    assertEquals(enhancedDiagnosis.historicalComparison.previousLevel, 5);
    assert(enhancedDiagnosis.historicalComparison.progressionAnalysis.includes('lightened significantly'));
    assert(enhancedDiagnosis.historicalComparison.trendPrediction);
  });
});

Deno.test('DiagnosisService Integration - Risk Assessment', async (t) => {
  await t.step('should calculate low risk for healthy hair', async () => {
    // Arrange
    const healthyDiagnosis = MockDataFactory.createHairDiagnosis({
      overallCondition: 'excellent',
      detectedRisks: [],
      overallConfidence: 0.95
    });

    const mockDiagnoseImageUseCase = {
      execute: stub().returns(Promise.resolve({
        success: true,
        data: healthyDiagnosis
      }))
    };

    const mockCacheService = MockServices.createMockCacheService();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();

    const service = new DiagnosisService(
      mockDiagnoseImageUseCase as any,
      mockCacheService,
      mockValidator,
      mockLogger
    );

    const request: DiagnosisRequest = {
      imageUrl: 'test.jpg',
      salonId: 'salon-123',
      userId: 'user-456'
    };

    // Act
    const result = await service.processImageDiagnosis(request);

    // Assert
    assert(result.success);
    assert(result.data);
    assertEquals(result.data.riskAssessment.level, 'low');
    assert(result.data.riskAssessment.factors.length === 0);
  });

  await t.step('should calculate critical risk for severe issues', async () => {
    // Arrange
    const criticalDiagnosis = MockDataFactory.createHairDiagnosis({
      overallCondition: 'poor',
      detectedRisks: [
        'severe chemical damage',
        'critical structural integrity loss',
        'incompatible previous treatments'
      ],
      overallConfidence: 0.65
    });

    const mockDiagnoseImageUseCase = {
      execute: stub().returns(Promise.resolve({
        success: true,
        data: criticalDiagnosis
      }))
    };

    const mockCacheService = MockServices.createMockCacheService();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();

    const service = new DiagnosisService(
      mockDiagnoseImageUseCase as any,
      mockCacheService,
      mockValidator,
      mockLogger
    );

    const request: DiagnosisRequest = {
      imageUrl: 'test.jpg',
      salonId: 'salon-123',
      userId: 'user-456'
    };

    // Act
    const result = await service.processImageDiagnosis(request);

    // Assert
    assert(result.success);
    assert(result.data);
    assertEquals(result.data.riskAssessment.level, 'critical');
    assert(result.data.riskAssessment.factors.length >= 3);
    assert(result.data.riskAssessment.factors.includes('severe chemical damage'));
    assert(result.data.riskAssessment.recommendations.includes('Mandatory consultation with senior colorist'));
  });

  await t.step('should adjust risk level based on confidence', async () => {
    // Arrange
    const lowConfidenceDiagnosis = MockDataFactory.createHairDiagnosis({
      overallCondition: 'good',
      detectedRisks: ['minor processing sensitivity'],
      overallConfidence: 0.65 // Low confidence should increase risk level
    });

    const mockDiagnoseImageUseCase = {
      execute: stub().returns(Promise.resolve({
        success: true,
        data: lowConfidenceDiagnosis
      }))
    };

    const mockCacheService = MockServices.createMockCacheService();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();

    const service = new DiagnosisService(
      mockDiagnoseImageUseCase as any,
      mockCacheService,
      mockValidator,
      mockLogger
    );

    const request: DiagnosisRequest = {
      imageUrl: 'test.jpg',
      salonId: 'salon-123',
      userId: 'user-456'
    };

    // Act
    const result = await service.processImageDiagnosis(request);

    // Assert
    assert(result.success);
    assert(result.data);
    assert(['medium', 'high'].includes(result.data.riskAssessment.level));
    assert(result.data.riskAssessment.factors.includes('Low diagnostic confidence'));
  });
});

Deno.test('DiagnosisService Integration - Next Steps Logic', async (t) => {
  await t.step('should require consultation for critical risk', async () => {
    // Arrange
    const criticalDiagnosis = MockDataFactory.createHairDiagnosis({
      detectedRisks: ['critical structural damage'],
      serviceComplexity: 'high'
    });

    const mockDiagnoseImageUseCase = {
      execute: stub().returns(Promise.resolve({
        success: true,
        data: criticalDiagnosis
      }))
    };

    const mockCacheService = MockServices.createMockCacheService();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();

    const service = new DiagnosisService(
      mockDiagnoseImageUseCase as any,
      mockCacheService,
      mockValidator,
      mockLogger
    );

    const request: DiagnosisRequest = {
      imageUrl: 'test.jpg',
      salonId: 'salon-123',
      userId: 'user-456'
    };

    // Act
    const result = await service.processImageDiagnosis(request);

    // Assert
    assert(result.success);
    assert(result.data);
    
    const nextSteps = result.data.nextSteps;
    assertEquals(nextSteps.consultation, true);
    assert(nextSteps.immediate.includes('Stop - Consult senior colorist before proceeding'));
    assert(nextSteps.immediate.includes('Perform additional strand test'));
  });

  await t.step('should suggest extended time for complex services', async () => {
    // Arrange
    const complexDiagnosis = MockDataFactory.createHairDiagnosis({
      serviceComplexity: 'high',
      estimatedTime: 240 // 4 hours
    });

    const mockDiagnoseImageUseCase = {
      execute: stub().returns(Promise.resolve({
        success: true,
        data: complexDiagnosis
      }))
    };

    const mockCacheService = MockServices.createMockCacheService();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();

    const service = new DiagnosisService(
      mockDiagnoseImageUseCase as any,
      mockCacheService,
      mockValidator,
      mockLogger
    );

    const request: DiagnosisRequest = {
      imageUrl: 'test.jpg',
      salonId: 'salon-123',
      userId: 'user-456'
    };

    // Act
    const result = await service.processImageDiagnosis(request);

    // Assert
    assert(result.success);
    assert(result.data);
    
    const nextSteps = result.data.nextSteps;
    assert(nextSteps.followUp.includes('Schedule extended appointment time'));
    assert(nextSteps.followUp.includes('Prepare specialized products'));
    assert(nextSteps.followUp.includes('Consider splitting service across multiple appointments'));
  });

  await t.step('should provide standard steps for low risk cases', async () => {
    // Arrange
    const normalDiagnosis = MockDataFactory.createHairDiagnosis({
      detectedRisks: [],
      overallCondition: 'good',
      serviceComplexity: 'low',
      estimatedTime: 90
    });

    const mockDiagnoseImageUseCase = {
      execute: stub().returns(Promise.resolve({
        success: true,
        data: normalDiagnosis
      }))
    };

    const mockCacheService = MockServices.createMockCacheService();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();

    const service = new DiagnosisService(
      mockDiagnoseImageUseCase as any,
      mockCacheService,
      mockValidator,
      mockLogger
    );

    const request: DiagnosisRequest = {
      imageUrl: 'test.jpg',
      salonId: 'salon-123',
      userId: 'user-456'
    };

    // Act
    const result = await service.processImageDiagnosis(request);

    // Assert
    assert(result.success);
    assert(result.data);
    
    const nextSteps = result.data.nextSteps;
    assertEquals(nextSteps.consultation, false);
    assert(nextSteps.immediate.includes('Proceed with standard color consultation'));
    assert(nextSteps.followUp.includes('Document results for client history'));
    assert(nextSteps.followUp.includes('Schedule follow-up consultation in 4-6 weeks'));
  });
});

Deno.test('DiagnosisService Integration - Validation Logic', async (t) => {
  await t.step('should validate diagnosis results and adjust confidence', async () => {
    // Arrange
    const questionableDiagnosis = MockDataFactory.createHairDiagnosis({
      averageLevel: 15, // Invalid level - will be caught by validation
      overallTone: '',
      overallCondition: 'excellent',
      detectedRisks: ['severe damage', 'critical issues'], // Conflicting with excellent condition
      overallConfidence: 0.75
    });

    const mockDiagnoseImageUseCase = {
      execute: stub().returns(Promise.resolve({
        success: true,
        data: questionableDiagnosis
      }))
    };

    const mockCacheService = MockServices.createMockCacheService();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();

    const service = new DiagnosisService(
      mockDiagnoseImageUseCase as any,
      mockCacheService,
      mockValidator,
      mockLogger
    );

    const request: DiagnosisRequest = {
      imageUrl: 'test.jpg',
      salonId: 'salon-123',
      userId: 'user-456'
    };

    // Act
    const result = await service.processImageDiagnosis(request);

    // Assert
    assert(result.success);
    assert(result.data);
    assert(mockLogger.warn.called); // Should log validation warnings
    
    // Should still process but with warnings logged
    const warnCalls = mockLogger.warn.calls.filter(call => 
      call.args[0] === 'Diagnosis validation failed'
    );
    assert(warnCalls.length > 0);
  });
});

Deno.test('DiagnosisService Integration - Error Scenarios', async (t) => {
  await t.step('should handle exceptions gracefully', async () => {
    // Arrange
    const mockDiagnoseImageUseCase = {
      execute: stub().throws(new Error('Unexpected use case error'))
    };

    const mockCacheService = MockServices.createMockCacheService();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();

    const service = new DiagnosisService(
      mockDiagnoseImageUseCase as any,
      mockCacheService,
      mockValidator,
      mockLogger
    );

    const request: DiagnosisRequest = {
      imageUrl: 'test.jpg',
      salonId: 'salon-123',
      userId: 'user-456'
    };

    // Act
    const result = await service.processImageDiagnosis(request);

    // Assert
    assert(!result.success);
    assertEquals(result.error, 'Unexpected use case error');
    assert(mockLogger.error.calledWith('Diagnosis process failed with exception'));
  });

  await t.step('should handle context enhancement failures gracefully', async () => {
    // Arrange
    const validDiagnosis = MockDataFactory.createHairDiagnosis();

    const mockDiagnoseImageUseCase = {
      execute: stub().returns(Promise.resolve({
        success: true,
        data: validDiagnosis
      }))
    };

    const mockCacheService = MockServices.createMockCacheService();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();

    const service = new DiagnosisService(
      mockDiagnoseImageUseCase as any,
      mockCacheService,
      mockValidator,
      mockLogger
    );

    // Corrupt metadata that might cause enhancement to fail
    const request: DiagnosisRequest = {
      imageUrl: 'test.jpg',
      salonId: 'salon-123',
      userId: 'user-456',
      metadata: {
        previousServices: null as any, // Invalid data that might cause issues
        clientAge: -5 // Invalid age
      }
    };

    // Act
    const result = await service.processImageDiagnosis(request);

    // Assert - Should still succeed with basic diagnosis even if enhancement fails
    assert(result.success);
    assert(result.data);
    TestAssertions.assertValidHairDiagnosis(result.data.diagnosis);
  });
});

Deno.test('DiagnosisService Integration - Performance Benchmarks', async (t) => {
  await t.step('should process diagnosis within performance targets', async () => {
    // Arrange
    const fastDiagnosis = MockDataFactory.createHairDiagnosis();

    const mockDiagnoseImageUseCase = {
      execute: stub().callsFake(async () => {
        // Simulate realistic processing delay
        await new Promise(resolve => setTimeout(resolve, 50));
        return {
          success: true,
          data: fastDiagnosis
        };
      })
    };

    const mockCacheService = MockServices.createMockCacheService();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();

    const service = new DiagnosisService(
      mockDiagnoseImageUseCase as any,
      mockCacheService,
      mockValidator,
      mockLogger
    );

    const request: DiagnosisRequest = {
      imageUrl: 'test.jpg',
      salonId: 'salon-123',
      userId: 'user-456'
    };

    // Act
    const { result, latency } = await PerformanceTestUtils.measureLatency(() => 
      service.processImageDiagnosis(request)
    );

    // Assert
    assert(result.success);
    assert(latency < 3000); // Should complete within 3 seconds
    assert(result.data!.processingTime <= latency); // Reported time should be accurate
  });

  await t.step('should handle concurrent requests efficiently', async () => {
    // Arrange
    const fastDiagnosis = MockDataFactory.createHairDiagnosis();

    const mockDiagnoseImageUseCase = {
      execute: stub().callsFake(async () => {
        await new Promise(resolve => setTimeout(resolve, 30));
        return {
          success: true,
          data: fastDiagnosis
        };
      })
    };

    const mockCacheService = MockServices.createMockCacheService();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();

    const service = new DiagnosisService(
      mockDiagnoseImageUseCase as any,
      mockCacheService,
      mockValidator,
      mockLogger
    );

    const request: DiagnosisRequest = {
      imageUrl: 'test.jpg',
      salonId: 'salon-123',
      userId: 'user-456'
    };

    // Act - Process 5 concurrent requests
    const concurrentRequests = Array(5).fill(0).map(() => 
      service.processImageDiagnosis(request)
    );

    const results = await Promise.all(concurrentRequests);

    // Assert - All should succeed
    results.forEach(result => {
      assert(result.success);
      assert(result.data);
    });

    // Verify all use case calls were made
    assertEquals(mockDiagnoseImageUseCase.execute.calls.length, 5);
  });
});