{"compilerOptions": {"allowJs": true, "lib": ["deno.window", "deno.unstable"], "strict": true}, "test": {"include": ["**/*.test.ts"], "exclude": ["node_modules/**/*"]}, "tasks": {"test": "deno test --allow-all --unstable", "test:unit": "deno test tests/unit/ --allow-all --unstable", "test:integration": "deno test tests/integration/ --allow-all --unstable", "test:contract": "deno test tests/contract/ --allow-all --unstable", "test:performance": "deno test tests/performance/ --allow-all --unstable", "test:watch": "deno test --allow-all --unstable --watch", "test:coverage": "deno test --allow-all --unstable --coverage=coverage_report", "test:coverage-html": "deno coverage coverage_report --html"}}