/**
 * Performance and Load Tests
 * 
 * Tests for performance characteristics of the refactored system, focusing on:
 * - Response time requirements (<3s P95)
 * - Concurrent request handling
 * - Cache effectiveness
 * - Memory usage patterns
 * - Error rate under load
 * - Scalability characteristics
 * - Resource utilization efficiency
 */

import { 
  assertEquals, 
  assert, 
  PerformanceTestUtils, 
  TestAssertions, 
  LoadTestResult 
} from '../test-utils.ts';
import { DiagnosisService } from '../../application/services/DiagnosisService.ts';
import { DiagnoseImageUseCase } from '../../use-cases/DiagnoseImageUseCase.ts';
import { MockServices, MockDataFactory } from '../test-utils.ts';

Deno.test('Performance - Individual Component Response Times', async (t) => {
  await t.step('DiagnoseImageUseCase should meet latency requirements', async () => {
    // Arrange
    const mockAIService = MockServices.createMockAIService();
    const mockCacheService = MockServices.createMockCacheService();
    const mockImageProcessor = MockServices.createMockImageProcessor();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();
    const mockPromptService = MockServices.createMockPromptService();
    const mockRetryService = MockServices.createMockRetryService();

    // Simulate realistic AI service delay
    mockRetryService.retryWithBackoff.callsFake(async () => {
      await new Promise(resolve => setTimeout(resolve, 800)); // 800ms AI latency
      return {
        choices: [{
          message: { content: JSON.stringify(MockDataFactory.createHairDiagnosis()) },
          finish_reason: 'stop'
        }],
        usage: { prompt_tokens: 150, completion_tokens: 200, total_tokens: 350 }
      };
    });

    const useCase = new DiagnoseImageUseCase(
      mockAIService,
      mockCacheService,
      mockImageProcessor,
      mockValidator,
      mockLogger,
      mockPromptService,
      mockRetryService
    );

    const request = MockDataFactory.createDiagnoseImageRequest();

    // Act - Measure performance multiple times for consistency
    const measurements = [];
    for (let i = 0; i < 10; i++) {
      const { result, latency } = await PerformanceTestUtils.measureLatency(() => 
        useCase.execute(request)
      );
      assert(result.success);
      measurements.push(latency);
    }

    // Assert
    const avgLatency = measurements.reduce((a, b) => a + b, 0) / measurements.length;
    const maxLatency = Math.max(...measurements);
    
    console.log(`DiagnoseImageUseCase - Avg: ${avgLatency}ms, Max: ${maxLatency}ms`);
    
    // Requirements: P95 < 3000ms, but individual use case should be much faster
    assert(avgLatency < 1500, `Average latency ${avgLatency}ms exceeds 1500ms threshold`);
    assert(maxLatency < 2500, `Max latency ${maxLatency}ms exceeds 2500ms threshold`);
  });

  await t.step('DiagnosisService should orchestrate efficiently', async () => {
    // Arrange
    const mockDiagnoseImageUseCase = {
      execute: async () => {
        await new Promise(resolve => setTimeout(resolve, 600)); // 600ms use case time
        return {
          success: true,
          data: MockDataFactory.createHairDiagnosis()
        };
      }
    };

    const mockCacheService = MockServices.createMockCacheService();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();

    const service = new DiagnosisService(
      mockDiagnoseImageUseCase as any,
      mockCacheService,
      mockValidator,
      mockLogger
    );

    const request = {
      imageUrl: 'test.jpg',
      salonId: 'salon-123',
      userId: 'user-456',
      metadata: { clientAge: 30 }
    };

    // Act - Measure orchestration overhead
    const measurements = [];
    for (let i = 0; i < 5; i++) {
      const { result, latency } = await PerformanceTestUtils.measureLatency(() => 
        service.processImageDiagnosis(request)
      );
      assert(result.success);
      measurements.push(latency);
    }

    // Assert
    const avgLatency = measurements.reduce((a, b) => a + b, 0) / measurements.length;
    console.log(`DiagnosisService orchestration - Avg: ${avgLatency}ms`);
    
    // Should add minimal overhead beyond use case execution
    assert(avgLatency < 800, `Service orchestration adds too much overhead: ${avgLatency}ms`);
  });
});

Deno.test('Performance - Concurrent Request Handling', async (t) => {
  await t.step('should handle moderate concurrent load', async () => {
    // Arrange
    const mockDiagnoseImageUseCase = {
      execute: async () => {
        // Simulate variable processing time (400-800ms)
        const delay = 400 + Math.random() * 400;
        await new Promise(resolve => setTimeout(resolve, delay));
        return {
          success: true,
          data: MockDataFactory.createHairDiagnosis()
        };
      }
    };

    const mockCacheService = MockServices.createMockCacheService();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();

    const service = new DiagnosisService(
      mockDiagnoseImageUseCase as any,
      mockCacheService,
      mockValidator,
      mockLogger
    );

    const request = {
      imageUrl: 'test.jpg',
      salonId: 'salon-123',
      userId: 'user-456'
    };

    // Act - Test concurrent execution
    const testFn = () => service.processImageDiagnosis(request);
    const loadTestResult = await PerformanceTestUtils.runLoadTest(testFn, {
      concurrency: 10,           // 10 concurrent requests
      duration: 5000,           // 5 seconds
      expectedLatencyP95: 1500, // P95 should be under 1.5s
      expectedSuccessRate: 0.95 // 95% success rate
    });

    // Assert
    console.log('Concurrent Load Test Results:', {
      totalRequests: loadTestResult.totalRequests,
      successRate: loadTestResult.successRate,
      p95Latency: loadTestResult.p95Latency,
      averageLatency: loadTestResult.averageLatency
    });

    TestAssertions.assertValidPerformanceMetrics(loadTestResult, {
      maxLatencyP95: 1500,
      minSuccessRate: 0.95
    });

    assert(loadTestResult.totalRequests >= 25, 'Should have processed reasonable number of requests');
  });

  await t.step('should maintain performance under high concurrency', async () => {
    // Arrange - Faster mock for high concurrency testing
    const mockDiagnoseImageUseCase = {
      execute: async () => {
        await new Promise(resolve => setTimeout(resolve, 200 + Math.random() * 200));
        return {
          success: true,
          data: MockDataFactory.createHairDiagnosis()
        };
      }
    };

    const mockCacheService = MockServices.createMockCacheService();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();

    const service = new DiagnosisService(
      mockDiagnoseImageUseCase as any,
      mockCacheService,
      mockValidator,
      mockLogger
    );

    const request = {
      imageUrl: 'test.jpg',
      salonId: 'salon-123',
      userId: 'user-456'
    };

    // Act - Higher concurrency test
    const testFn = () => service.processImageDiagnosis(request);
    const loadTestResult = await PerformanceTestUtils.runLoadTest(testFn, {
      concurrency: 25,          // 25 concurrent requests
      duration: 3000,           // 3 seconds
      expectedLatencyP95: 1000, // P95 should stay under 1s
      expectedSuccessRate: 0.90 // 90% success rate under stress
    });

    // Assert
    console.log('High Concurrency Test Results:', {
      totalRequests: loadTestResult.totalRequests,
      successRate: loadTestResult.successRate,
      p95Latency: loadTestResult.p95Latency
    });

    TestAssertions.assertValidPerformanceMetrics(loadTestResult, {
      maxLatencyP95: 1000,
      minSuccessRate: 0.90
    });

    assert(loadTestResult.totalRequests >= 50, 'Should handle significant load');
  });
});

Deno.test('Performance - Cache Effectiveness', async (t) => {
  await t.step('should demonstrate cache performance improvement', async () => {
    // Arrange - Slow service without cache
    let callCount = 0;
    const slowUseCase = {
      execute: async () => {
        callCount++;
        await new Promise(resolve => setTimeout(resolve, 500)); // Slow AI call
        return {
          success: true,
          data: MockDataFactory.createHairDiagnosis()
        };
      }
    };

    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();

    // Test without cache
    const noCacheService = MockServices.createMockCacheService();
    noCacheService.get.returns(Promise.resolve(null)); // Always cache miss

    const serviceWithoutCache = new DiagnosisService(
      slowUseCase as any,
      noCacheService,
      mockValidator,
      mockLogger
    );

    // Test with cache
    const cachedData = MockDataFactory.createHairDiagnosis();
    const withCacheService = MockServices.createMockCacheService();
    withCacheService.get.returns(Promise.resolve(cachedData)); // Always cache hit

    const serviceWithCache = new DiagnosisService(
      slowUseCase as any,
      withCacheService,
      mockValidator,
      mockLogger
    );

    const request = {
      imageUrl: 'test.jpg',
      salonId: 'salon-123',
      userId: 'user-456'
    };

    // Act - Test both scenarios
    const withoutCacheTest = async () => {
      const result = await serviceWithoutCache.processImageDiagnosis(request);
      assert(result.success);
      return result;
    };

    const withCacheTest = async () => {
      const result = await serviceWithCache.processImageDiagnosis(request);
      assert(result.success);
      return result;
    };

    const noCacheResult = await PerformanceTestUtils.runLoadTest(withoutCacheTest, {
      concurrency: 5,
      duration: 2000,
      expectedLatencyP95: 1000,
      expectedSuccessRate: 0.95
    });

    callCount = 0; // Reset for cache test

    const cacheResult = await PerformanceTestUtils.runLoadTest(withCacheTest, {
      concurrency: 5,
      duration: 2000,
      expectedLatencyP95: 100, // Should be much faster with cache
      expectedSuccessRate: 0.95
    });

    // Assert
    console.log('Cache Performance Comparison:', {
      withoutCache: { p95: noCacheResult.p95Latency, avg: noCacheResult.averageLatency },
      withCache: { p95: cacheResult.p95Latency, avg: cacheResult.averageLatency },
      improvement: `${((noCacheResult.p95Latency - cacheResult.p95Latency) / noCacheResult.p95Latency * 100).toFixed(1)}%`
    });

    TestAssertions.assertCacheHitImprovement(noCacheResult, cacheResult);
    
    // Cache should provide significant performance boost
    const improvementRatio = noCacheResult.p95Latency / cacheResult.p95Latency;
    assert(improvementRatio >= 3, `Cache improvement ratio ${improvementRatio} should be at least 3x`);
  });
});

Deno.test('Performance - Memory and Resource Efficiency', async (t) => {
  await t.step('should maintain stable memory usage under load', async () => {
    // This test would ideally monitor actual memory usage
    // For now, we'll test that the system can handle many sequential operations
    // without degrading performance (indicating memory leaks)

    const mockDiagnoseImageUseCase = {
      execute: async () => {
        await new Promise(resolve => setTimeout(resolve, 50));
        return {
          success: true,
          data: MockDataFactory.createHairDiagnosis()
        };
      }
    };

    const mockCacheService = MockServices.createMockCacheService();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();

    const service = new DiagnosisService(
      mockDiagnoseImageUseCase as any,
      mockCacheService,
      mockValidator,
      mockLogger
    );

    const request = {
      imageUrl: 'test.jpg',
      salonId: 'salon-123',
      userId: 'user-456'
    };

    // Act - Run many operations to test for memory leaks
    const iterations = 100;
    const latencies: number[] = [];

    for (let i = 0; i < iterations; i++) {
      const { result, latency } = await PerformanceTestUtils.measureLatency(() => 
        service.processImageDiagnosis(request)
      );
      
      assert(result.success);
      latencies.push(latency);

      // Force garbage collection hint (if available)
      if (global.gc) {
        global.gc();
      }
    }

    // Assert - Performance should remain consistent
    const firstHalf = latencies.slice(0, iterations / 2);
    const secondHalf = latencies.slice(iterations / 2);

    const firstHalfAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length;
    const secondHalfAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length;

    console.log('Memory Stability Test:', {
      firstHalfAvg: `${firstHalfAvg.toFixed(2)}ms`,
      secondHalfAvg: `${secondHalfAvg.toFixed(2)}ms`,
      degradation: `${((secondHalfAvg - firstHalfAvg) / firstHalfAvg * 100).toFixed(2)}%`
    });

    // Performance should not degrade by more than 20%
    const degradation = (secondHalfAvg - firstHalfAvg) / firstHalfAvg;
    assert(degradation < 0.20, `Performance degraded by ${(degradation * 100).toFixed(2)}%`);
  });
});

Deno.test('Performance - Error Handling Performance Impact', async (t) => {
  await t.step('should handle errors efficiently without performance degradation', async () => {
    // Arrange - Service that fails 30% of the time
    let callCount = 0;
    const flakyUseCase = {
      execute: async () => {
        callCount++;
        await new Promise(resolve => setTimeout(resolve, 100));
        
        if (callCount % 3 === 0) {
          return {
            success: false,
            error: 'Simulated failure'
          };
        }
        
        return {
          success: true,
          data: MockDataFactory.createHairDiagnosis()
        };
      }
    };

    const mockCacheService = MockServices.createMockCacheService();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();

    const service = new DiagnosisService(
      flakyUseCase as any,
      mockCacheService,
      mockValidator,
      mockLogger
    );

    const request = {
      imageUrl: 'test.jpg',
      salonId: 'salon-123',
      userId: 'user-456'
    };

    // Act - Test performance with mixed success/failure
    const testFn = () => service.processImageDiagnosis(request);
    const mixedResult = await PerformanceTestUtils.runLoadTest(testFn, {
      concurrency: 8,
      duration: 3000,
      expectedLatencyP95: 500,     // Should still be fast
      expectedSuccessRate: 0.60    // Expect ~67% success rate
    });

    // Assert
    console.log('Mixed Success/Failure Performance:', {
      totalRequests: mixedResult.totalRequests,
      successRate: mixedResult.successRate,
      failureRate: (1 - mixedResult.successRate),
      p95Latency: mixedResult.p95Latency
    });

    assert(mixedResult.successRate >= 0.60 && mixedResult.successRate <= 0.75, 
      'Success rate should reflect the 67% expected rate');
    
    // Error handling shouldn't significantly impact performance
    assert(mixedResult.p95Latency < 500, 
      'Error handling should not slow down the system significantly');
    
    assert(mixedResult.totalRequests >= 30, 
      'Should have processed significant number of mixed requests');
  });
});

Deno.test('Performance - System Limits and Scaling', async (t) => {
  await t.step('should identify system breaking point gracefully', async () => {
    // This test pushes the system to find its limits
    const mockDiagnoseImageUseCase = {
      execute: async () => {
        // Simulate processing that gets slower under high load
        const delay = 100 + Math.random() * 100;
        await new Promise(resolve => setTimeout(resolve, delay));
        
        // Simulate occasional failures under extreme load
        if (Math.random() < 0.05) { // 5% failure rate
          return {
            success: false,
            error: 'System overloaded'
          };
        }
        
        return {
          success: true,
          data: MockDataFactory.createHairDiagnosis()
        };
      }
    };

    const mockCacheService = MockServices.createMockCacheService();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();

    const service = new DiagnosisService(
      mockDiagnoseImageUseCase as any,
      mockCacheService,
      mockValidator,
      mockLogger
    );

    const request = {
      imageUrl: 'test.jpg',
      salonId: 'salon-123',
      userId: 'user-456'
    };

    // Act - Progressive load testing
    const concurrencyLevels = [5, 10, 20, 35];
    const results: LoadTestResult[] = [];

    for (const concurrency of concurrencyLevels) {
      const testFn = () => service.processImageDiagnosis(request);
      const result = await PerformanceTestUtils.runLoadTest(testFn, {
        concurrency,
        duration: 2000,
        expectedLatencyP95: 2000,   // More lenient for stress testing
        expectedSuccessRate: 0.85   // Accept higher failure rate under stress
      });

      results.push(result);
      
      console.log(`Concurrency ${concurrency}:`, {
        requests: result.totalRequests,
        successRate: result.successRate.toFixed(3),
        p95Latency: result.p95Latency
      });
    }

    // Assert - System should handle progressive load reasonably
    // Performance may degrade, but shouldn't completely collapse
    for (let i = 0; i < results.length; i++) {
      const result = results[i];
      const concurrency = concurrencyLevels[i];
      
      assert(result.successRate >= 0.80, 
        `Success rate at concurrency ${concurrency} dropped below 80%: ${result.successRate}`);
      
      assert(result.totalRequests >= concurrency, 
        `Should process at least one request per concurrent worker`);
    }

    // Performance degradation should be predictable, not catastrophic
    const firstResult = results[0];
    const lastResult = results[results.length - 1];
    const latencyIncrease = lastResult.p95Latency / firstResult.p95Latency;
    
    console.log(`Latency increase from ${concurrencyLevels[0]} to ${concurrencyLevels[concurrencyLevels.length - 1]} concurrency: ${latencyIncrease.toFixed(2)}x`);
    
    assert(latencyIncrease < 10, 
      'Latency should not increase by more than 10x under reasonable load');
  });
});

Deno.test('Performance - Real-world Usage Patterns', async (t) => {
  await t.step('should handle realistic salon workflow patterns', async () => {
    // Simulate realistic salon usage: bursts of activity with quiet periods
    const mockDiagnoseImageUseCase = {
      execute: async () => {
        // Realistic AI processing time variation
        const delay = 600 + Math.random() * 800; // 600-1400ms
        await new Promise(resolve => setTimeout(resolve, delay));
        return {
          success: true,
          data: MockDataFactory.createHairDiagnosis()
        };
      }
    };

    const mockCacheService = MockServices.createMockCacheService();
    // Simulate 20% cache hit rate (realistic for image-based diagnosis)
    let requestCount = 0;
    mockCacheService.get.callsFake(() => {
      requestCount++;
      return Promise.resolve(requestCount % 5 === 0 ? MockDataFactory.createHairDiagnosis() : null);
    });

    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();

    const service = new DiagnosisService(
      mockDiagnoseImageUseCase as any,
      mockCacheService,
      mockValidator,
      mockLogger
    );

    // Simulate varied request patterns (different salons, clients)
    const requests = [
      { imageUrl: 'client1.jpg', salonId: 'salon-123', userId: 'stylist-1' },
      { imageUrl: 'client2.jpg', salonId: 'salon-123', userId: 'stylist-2' },
      { imageUrl: 'client3.jpg', salonId: 'salon-456', userId: 'stylist-3' },
      { imageUrl: 'client4.jpg', salonId: 'salon-123', userId: 'stylist-1' }, // Repeat stylist
    ];

    // Act - Test realistic burst pattern
    const results: number[] = [];
    const startTime = Date.now();

    // Simulate 3 busy periods with quiet time in between
    for (let burst = 0; burst < 3; burst++) {
      const burstPromises = [];
      
      // Each burst: 4-6 concurrent requests
      const concurrentRequests = 4 + Math.floor(Math.random() * 3);
      for (let i = 0; i < concurrentRequests; i++) {
        const request = requests[i % requests.length];
        burstPromises.push(
          PerformanceTestUtils.measureLatency(() => 
            service.processImageDiagnosis(request)
          ).then(({ result, latency }) => {
            assert(result.success);
            return latency;
          })
        );
      }
      
      const burstLatencies = await Promise.all(burstPromises);
      results.push(...burstLatencies);
      
      // Quiet period between bursts (except after last burst)
      if (burst < 2) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    const totalTime = Date.now() - startTime;

    // Assert
    const avgLatency = results.reduce((a, b) => a + b, 0) / results.length;
    const p95Index = Math.floor(results.length * 0.95);
    const p95Latency = results.sort((a, b) => a - b)[p95Index];

    console.log('Realistic Workflow Performance:', {
      totalRequests: results.length,
      totalTime: `${totalTime}ms`,
      avgLatency: `${avgLatency.toFixed(0)}ms`,
      p95Latency: `${p95Latency}ms`,
      throughput: `${(results.length / (totalTime / 1000)).toFixed(2)} req/s`
    });

    // Realistic performance expectations
    assert(avgLatency < 2000, `Average latency ${avgLatency}ms should be under 2s`);
    assert(p95Latency < 3000, `P95 latency ${p95Latency}ms should meet SLA of 3s`);
    assert(results.length >= 12, 'Should have processed multiple bursts of requests');
    
    // Should maintain reasonable throughput
    const throughput = results.length / (totalTime / 1000);
    assert(throughput >= 0.5, `Throughput ${throughput} req/s too low for salon workflow`);
  });
});