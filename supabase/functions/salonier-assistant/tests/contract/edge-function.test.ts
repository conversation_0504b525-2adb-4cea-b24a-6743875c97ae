/**
 * Edge Function Contract Tests
 * 
 * Tests for the complete Edge Function HTTP interface, focusing on:
 * - HTTP request/response compatibility
 * - API endpoint behavior
 * - Error response formats
 * - Authentication and authorization
 * - Request validation
 * - Response structure consistency
 * - Backwards compatibility
 */

import { assertEquals, assert } from '../test-utils.ts';
import { MockDataFactory, TestAssertions } from '../test-utils.ts';

// Import the main Edge Function handler
// Note: In a real implementation, you'd import the actual handler
// For testing purposes, we'll mock the behavior

interface EdgeFunctionRequest {
  method: string;
  url: string;
  headers: Headers;
  body?: string;
}

interface EdgeFunctionResponse {
  status: number;
  headers: Headers;
  body: string;
}

// Mock Edge Function handler for contract testing
class MockEdgeFunctionHandler {
  async handle(request: EdgeFunctionRequest): Promise<EdgeFunctionResponse> {
    const url = new URL(request.url);
    const body = request.body ? JSON.parse(request.body) : null;

    // Route handling simulation
    if (request.method === 'POST') {
      if (url.pathname === '/diagnose-image') {
        return this.handleDiagnoseImage(body, request.headers);
      } else if (url.pathname === '/generate-formula') {
        return this.handleGenerateFormula(body, request.headers);
      } else if (url.pathname === '/chat-assistant') {
        return this.handleChatAssistant(body, request.headers);
      } else if (url.pathname === '/analyze-desired-look') {
        return this.handleAnalyzeDesiredLook(body, request.headers);
      }
    } else if (request.method === 'GET' && url.pathname === '/health') {
      return this.handleHealthCheck();
    } else if (request.method === 'OPTIONS') {
      return this.handlePreflight();
    }

    return {
      status: 404,
      headers: new Headers({ 'Content-Type': 'application/json' }),
      body: JSON.stringify({ error: 'Endpoint not found' })
    };
  }

  private async handleDiagnoseImage(body: any, headers: Headers): Promise<EdgeFunctionResponse> {
    // Simulate authentication check
    if (!headers.get('Authorization')) {
      return {
        status: 401,
        headers: new Headers({ 'Content-Type': 'application/json' }),
        body: JSON.stringify({ error: 'Authorization required' })
      };
    }

    // Simulate validation
    if (!body || !body.salonId) {
      return {
        status: 400,
        headers: new Headers({ 'Content-Type': 'application/json' }),
        body: JSON.stringify({ 
          error: 'Validation failed', 
          details: ['salonId is required'] 
        })
      };
    }

    if (!body.imageUrl && !body.imageBase64) {
      return {
        status: 400,
        headers: new Headers({ 'Content-Type': 'application/json' }),
        body: JSON.stringify({ 
          error: 'Validation failed', 
          details: ['Either imageUrl or imageBase64 is required'] 
        })
      };
    }

    // Simulate successful response
    const diagnosis = MockDataFactory.createHairDiagnosis();
    return {
      status: 200,
      headers: new Headers({ 
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }),
      body: JSON.stringify({
        success: true,
        data: diagnosis,
        metadata: {
          model: 'gpt-4o',
          tokens: 350,
          cost: 0.0042,
          latency: 1250,
          cached: false,
          timestamp: new Date().toISOString()
        }
      })
    };
  }

  private async handleGenerateFormula(body: any, headers: Headers): Promise<EdgeFunctionResponse> {
    if (!headers.get('Authorization')) {
      return {
        status: 401,
        headers: new Headers({ 'Content-Type': 'application/json' }),
        body: JSON.stringify({ error: 'Authorization required' })
      };
    }

    if (!body || !body.currentDiagnosis || !body.desiredLook) {
      return {
        status: 400,
        headers: new Headers({ 'Content-Type': 'application/json' }),
        body: JSON.stringify({ 
          error: 'Validation failed', 
          details: ['currentDiagnosis and desiredLook are required'] 
        })
      };
    }

    const formula = MockDataFactory.createColorFormula();
    return {
      status: 200,
      headers: new Headers({ 
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }),
      body: JSON.stringify({
        success: true,
        data: formula,
        metadata: {
          model: 'gpt-4o',
          tokens: 425,
          cost: 0.0051,
          latency: 1800,
          cached: false,
          timestamp: new Date().toISOString()
        }
      })
    };
  }

  private async handleChatAssistant(body: any, headers: Headers): Promise<EdgeFunctionResponse> {
    if (!headers.get('Authorization')) {
      return {
        status: 401,
        headers: new Headers({ 'Content-Type': 'application/json' }),
        body: JSON.stringify({ error: 'Authorization required' })
      };
    }

    if (!body || !body.message || !body.salonId) {
      return {
        status: 400,
        headers: new Headers({ 'Content-Type': 'application/json' }),
        body: JSON.stringify({ 
          error: 'Validation failed', 
          details: ['message and salonId are required'] 
        })
      };
    }

    return {
      status: 200,
      headers: new Headers({ 
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }),
      body: JSON.stringify({
        success: true,
        data: {
          message: 'Based on your question about orange undertones, I recommend using an ash-based toner...',
          intent: 'color_correction_advice',
          confidence: 0.92,
          suggestedActions: [
            'Apply ash-based toner',
            'Monitor processing time closely',
            'Consider protein treatment'
          ],
          followUpQuestions: [
            'What is the current hair level?',
            'Has the client had recent chemical treatments?'
          ],
          requiresHumanIntervention: false
        },
        metadata: {
          model: 'gpt-4o-mini',
          tokens: 180,
          cost: 0.0008,
          latency: 650,
          cached: false,
          timestamp: new Date().toISOString()
        }
      })
    };
  }

  private async handleAnalyzeDesiredLook(body: any, headers: Headers): Promise<EdgeFunctionResponse> {
    if (!headers.get('Authorization')) {
      return {
        status: 401,
        headers: new Headers({ 'Content-Type': 'application/json' }),
        body: JSON.stringify({ error: 'Authorization required' })
      };
    }

    if (!body || !body.salonId) {
      return {
        status: 400,
        headers: new Headers({ 'Content-Type': 'application/json' }),
        body: JSON.stringify({ 
          error: 'Validation failed', 
          details: ['salonId is required'] 
        })
      };
    }

    const desiredLook = MockDataFactory.createDesiredLookAnalysis();
    return {
      status: 200,
      headers: new Headers({ 
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }),
      body: JSON.stringify({
        success: true,
        data: desiredLook,
        metadata: {
          model: 'gpt-4o',
          tokens: 275,
          cost: 0.0033,
          latency: 950,
          cached: false,
          timestamp: new Date().toISOString()
        }
      })
    };
  }

  private handleHealthCheck(): EdgeFunctionResponse {
    return {
      status: 200,
      headers: new Headers({ 'Content-Type': 'application/json' }),
      body: JSON.stringify({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '2.2.0',
        services: {
          openai: 'connected',
          supabase: 'connected',
          cache: 'connected'
        }
      })
    };
  }

  private handlePreflight(): EdgeFunctionResponse {
    return {
      status: 200,
      headers: new Headers({
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Max-Age': '86400'
      }),
      body: ''
    };
  }
}

const handler = new MockEdgeFunctionHandler();

Deno.test('Edge Function Contract - Diagnose Image Endpoint', async (t) => {
  await t.step('should accept valid diagnose image request', async () => {
    // Arrange
    const requestBody = {
      imageUrl: 'https://example.com/hair-photo.jpg',
      salonId: 'salon-123'
    };

    const request: EdgeFunctionRequest = {
      method: 'POST',
      url: 'https://functions.supabase.com/diagnose-image',
      headers: new Headers({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      }),
      body: JSON.stringify(requestBody)
    };

    // Act
    const response = await handler.handle(request);

    // Assert
    assertEquals(response.status, 200);
    assertEquals(response.headers.get('Content-Type'), 'application/json');
    assert(response.headers.get('Access-Control-Allow-Origin'));

    const responseData = JSON.parse(response.body);
    assert(responseData.success);
    assert(responseData.data);
    assert(responseData.metadata);

    TestAssertions.assertValidHairDiagnosis(responseData.data);
    
    // Verify metadata structure
    assert(typeof responseData.metadata.model === 'string');
    assert(typeof responseData.metadata.tokens === 'number');
    assert(typeof responseData.metadata.cost === 'number');
    assert(typeof responseData.metadata.latency === 'number');
    assert(typeof responseData.metadata.cached === 'boolean');
    assert(typeof responseData.metadata.timestamp === 'string');
  });

  await t.step('should reject request without authentication', async () => {
    // Arrange
    const requestBody = {
      imageUrl: 'https://example.com/hair-photo.jpg',
      salonId: 'salon-123'
    };

    const request: EdgeFunctionRequest = {
      method: 'POST',
      url: 'https://functions.supabase.com/diagnose-image',
      headers: new Headers({
        'Content-Type': 'application/json'
        // Missing Authorization header
      }),
      body: JSON.stringify(requestBody)
    };

    // Act
    const response = await handler.handle(request);

    // Assert
    assertEquals(response.status, 401);
    assertEquals(response.headers.get('Content-Type'), 'application/json');

    const responseData = JSON.parse(response.body);
    assertEquals(responseData.error, 'Authorization required');
  });

  await t.step('should reject request without salonId', async () => {
    // Arrange
    const requestBody = {
      imageUrl: 'https://example.com/hair-photo.jpg'
      // Missing salonId
    };

    const request: EdgeFunctionRequest = {
      method: 'POST',
      url: 'https://functions.supabase.com/diagnose-image',
      headers: new Headers({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      }),
      body: JSON.stringify(requestBody)
    };

    // Act
    const response = await handler.handle(request);

    // Assert
    assertEquals(response.status, 400);
    
    const responseData = JSON.parse(response.body);
    assertEquals(responseData.error, 'Validation failed');
    assert(Array.isArray(responseData.details));
    assert(responseData.details.includes('salonId is required'));
  });

  await t.step('should reject request without image data', async () => {
    // Arrange
    const requestBody = {
      salonId: 'salon-123'
      // Missing both imageUrl and imageBase64
    };

    const request: EdgeFunctionRequest = {
      method: 'POST',
      url: 'https://functions.supabase.com/diagnose-image',
      headers: new Headers({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      }),
      body: JSON.stringify(requestBody)
    };

    // Act
    const response = await handler.handle(request);

    // Assert
    assertEquals(response.status, 400);
    
    const responseData = JSON.parse(response.body);
    assertEquals(responseData.error, 'Validation failed');
    assert(responseData.details.includes('Either imageUrl or imageBase64 is required'));
  });

  await t.step('should accept base64 image instead of URL', async () => {
    // Arrange
    const requestBody = {
      imageBase64: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEA...',
      salonId: 'salon-123'
    };

    const request: EdgeFunctionRequest = {
      method: 'POST',
      url: 'https://functions.supabase.com/diagnose-image',
      headers: new Headers({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      }),
      body: JSON.stringify(requestBody)
    };

    // Act
    const response = await handler.handle(request);

    // Assert
    assertEquals(response.status, 200);
    
    const responseData = JSON.parse(response.body);
    assert(responseData.success);
    TestAssertions.assertValidHairDiagnosis(responseData.data);
  });
});

Deno.test('Edge Function Contract - Generate Formula Endpoint', async (t) => {
  await t.step('should accept valid formula generation request', async () => {
    // Arrange
    const requestBody = {
      currentDiagnosis: MockDataFactory.createHairDiagnosis(),
      desiredLook: MockDataFactory.createDesiredLookAnalysis(),
      salonId: 'salon-123',
      availableProducts: [
        {
          brand: 'Wella',
          line: 'Koleston Perfect',
          type: 'permanent color',
          shade: '8.1',
          level: 8,
          reflect: 'ash'
        }
      ]
    };

    const request: EdgeFunctionRequest = {
      method: 'POST',
      url: 'https://functions.supabase.com/generate-formula',
      headers: new Headers({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      }),
      body: JSON.stringify(requestBody)
    };

    // Act
    const response = await handler.handle(request);

    // Assert
    assertEquals(response.status, 200);
    
    const responseData = JSON.parse(response.body);
    assert(responseData.success);
    assert(responseData.data);
    TestAssertions.assertValidColorFormula(responseData.data);
  });

  await t.step('should reject request without required fields', async () => {
    // Arrange
    const requestBody = {
      currentDiagnosis: MockDataFactory.createHairDiagnosis()
      // Missing desiredLook
    };

    const request: EdgeFunctionRequest = {
      method: 'POST',
      url: 'https://functions.supabase.com/generate-formula',
      headers: new Headers({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      }),
      body: JSON.stringify(requestBody)
    };

    // Act
    const response = await handler.handle(request);

    // Assert
    assertEquals(response.status, 400);
    
    const responseData = JSON.parse(response.body);
    assertEquals(responseData.error, 'Validation failed');
    assert(responseData.details.includes('currentDiagnosis and desiredLook are required'));
  });
});

Deno.test('Edge Function Contract - Chat Assistant Endpoint', async (t) => {
  await t.step('should accept valid chat request', async () => {
    // Arrange
    const requestBody = {
      message: 'How do I handle orange undertones on level 6 hair?',
      salonId: 'salon-123',
      userId: 'user-456',
      conversationHistory: []
    };

    const request: EdgeFunctionRequest = {
      method: 'POST',
      url: 'https://functions.supabase.com/chat-assistant',
      headers: new Headers({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      }),
      body: JSON.stringify(requestBody)
    };

    // Act
    const response = await handler.handle(request);

    // Assert
    assertEquals(response.status, 200);
    
    const responseData = JSON.parse(response.body);
    assert(responseData.success);
    assert(responseData.data);
    
    const chatResponse = responseData.data;
    assert(typeof chatResponse.message === 'string');
    assert(typeof chatResponse.intent === 'string');
    assert(typeof chatResponse.confidence === 'number');
    assert(chatResponse.confidence >= 0 && chatResponse.confidence <= 1);
    assert(Array.isArray(chatResponse.suggestedActions));
    assert(Array.isArray(chatResponse.followUpQuestions));
    assert(typeof chatResponse.requiresHumanIntervention === 'boolean');
  });

  await t.step('should reject request without message', async () => {
    // Arrange
    const requestBody = {
      salonId: 'salon-123',
      userId: 'user-456'
      // Missing message
    };

    const request: EdgeFunctionRequest = {
      method: 'POST',
      url: 'https://functions.supabase.com/chat-assistant',
      headers: new Headers({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      }),
      body: JSON.stringify(requestBody)
    };

    // Act
    const response = await handler.handle(request);

    // Assert
    assertEquals(response.status, 400);
    
    const responseData = JSON.parse(response.body);
    assertEquals(responseData.error, 'Validation failed');
    assert(responseData.details.includes('message and salonId are required'));
  });
});

Deno.test('Edge Function Contract - CORS and Options Handling', async (t) => {
  await t.step('should handle preflight OPTIONS request', async () => {
    // Arrange
    const request: EdgeFunctionRequest = {
      method: 'OPTIONS',
      url: 'https://functions.supabase.com/diagnose-image',
      headers: new Headers({
        'Origin': 'https://app.salonier.com',
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type, Authorization'
      })
    };

    // Act
    const response = await handler.handle(request);

    // Assert
    assertEquals(response.status, 200);
    assertEquals(response.headers.get('Access-Control-Allow-Origin'), '*');
    assertEquals(response.headers.get('Access-Control-Allow-Methods'), 'GET, POST, OPTIONS');
    assertEquals(response.headers.get('Access-Control-Allow-Headers'), 'Content-Type, Authorization');
    assert(response.headers.get('Access-Control-Max-Age'));
    assertEquals(response.body, '');
  });

  await t.step('should include CORS headers in actual responses', async () => {
    // Arrange
    const requestBody = {
      imageUrl: 'https://example.com/hair-photo.jpg',
      salonId: 'salon-123'
    };

    const request: EdgeFunctionRequest = {
      method: 'POST',
      url: 'https://functions.supabase.com/diagnose-image',
      headers: new Headers({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      }),
      body: JSON.stringify(requestBody)
    };

    // Act
    const response = await handler.handle(request);

    // Assert
    assertEquals(response.status, 200);
    assertEquals(response.headers.get('Access-Control-Allow-Origin'), '*');
  });
});

Deno.test('Edge Function Contract - Health Check and Monitoring', async (t) => {
  await t.step('should provide health check endpoint', async () => {
    // Arrange
    const request: EdgeFunctionRequest = {
      method: 'GET',
      url: 'https://functions.supabase.com/health',
      headers: new Headers()
    };

    // Act
    const response = await handler.handle(request);

    // Assert
    assertEquals(response.status, 200);
    assertEquals(response.headers.get('Content-Type'), 'application/json');

    const responseData = JSON.parse(response.body);
    assertEquals(responseData.status, 'healthy');
    assert(typeof responseData.timestamp === 'string');
    assert(typeof responseData.version === 'string');
    assert(responseData.services);
    assert(responseData.services.openai);
    assert(responseData.services.supabase);
    assert(responseData.services.cache);
  });
});

Deno.test('Edge Function Contract - Error Handling', async (t) => {
  await t.step('should return 404 for unknown endpoints', async () => {
    // Arrange
    const request: EdgeFunctionRequest = {
      method: 'POST',
      url: 'https://functions.supabase.com/unknown-endpoint',
      headers: new Headers({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      }),
      body: JSON.stringify({})
    };

    // Act
    const response = await handler.handle(request);

    // Assert
    assertEquals(response.status, 404);
    assertEquals(response.headers.get('Content-Type'), 'application/json');

    const responseData = JSON.parse(response.body);
    assertEquals(responseData.error, 'Endpoint not found');
  });

  await t.step('should return consistent error format across endpoints', async () => {
    // Test multiple endpoints for consistent error structure
    const endpoints = [
      '/diagnose-image',
      '/generate-formula',
      '/chat-assistant',
      '/analyze-desired-look'
    ];

    for (const endpoint of endpoints) {
      const request: EdgeFunctionRequest = {
        method: 'POST',
        url: `https://functions.supabase.com${endpoint}`,
        headers: new Headers({
          'Content-Type': 'application/json'
          // Missing Authorization
        }),
        body: JSON.stringify({})
      };

      const response = await handler.handle(request);

      // All should return 401 for missing auth
      assertEquals(response.status, 401);
      
      const responseData = JSON.parse(response.body);
      assert(responseData.error);
      assertEquals(responseData.error, 'Authorization required');
    }
  });
});

Deno.test('Edge Function Contract - Response Format Consistency', async (t) => {
  await t.step('should return consistent success response format', async () => {
    // Test multiple endpoints for consistent success structure
    const testCases = [
      {
        endpoint: '/diagnose-image',
        body: {
          imageUrl: 'https://example.com/test.jpg',
          salonId: 'salon-123'
        }
      },
      {
        endpoint: '/generate-formula',
        body: {
          currentDiagnosis: MockDataFactory.createHairDiagnosis(),
          desiredLook: MockDataFactory.createDesiredLookAnalysis(),
          salonId: 'salon-123'
        }
      },
      {
        endpoint: '/chat-assistant',
        body: {
          message: 'Test question',
          salonId: 'salon-123',
          userId: 'user-123'
        }
      },
      {
        endpoint: '/analyze-desired-look',
        body: {
          imageUrl: 'https://example.com/desired.jpg',
          salonId: 'salon-123'
        }
      }
    ];

    for (const testCase of testCases) {
      const request: EdgeFunctionRequest = {
        method: 'POST',
        url: `https://functions.supabase.com${testCase.endpoint}`,
        headers: new Headers({
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token'
        }),
        body: JSON.stringify(testCase.body)
      };

      const response = await handler.handle(request);
      assertEquals(response.status, 200);

      const responseData = JSON.parse(response.body);
      
      // All success responses should have consistent structure
      assert(responseData.success === true);
      assert(responseData.data);
      assert(responseData.metadata);
      
      // Metadata should have consistent fields
      assert(typeof responseData.metadata.model === 'string');
      assert(typeof responseData.metadata.tokens === 'number');
      assert(typeof responseData.metadata.cost === 'number');
      assert(typeof responseData.metadata.latency === 'number');
      assert(typeof responseData.metadata.cached === 'boolean');
      assert(typeof responseData.metadata.timestamp === 'string');
      
      // Validate timestamp is ISO string
      assert(!isNaN(Date.parse(responseData.metadata.timestamp)));
    }
  });
});