/**
 * HairDiagnosis Domain Entity Tests
 * 
 * Tests for the HairDiagnosis domain entity, focusing on:
 * - Business rule validation
 * - Immutability guarantees
 * - Factory method behavior
 * - Business logic methods
 * - Serialization/deserialization
 */

import { assertEquals, assertRejects, assert } from '../../../test-utils.ts';
import { HairDiagnosis, HairPorosity, HairDamage, HairTexture, HairDensity, ChemicalTreatment } from '../../../../domain/entities/HairDiagnosis.ts';

Deno.test('HairDiagnosis Entity - Creation and Validation', async (t) => {
  await t.step('should create valid hair diagnosis', () => {
    const diagnosis = HairDiagnosis.create({
      id: 'diag-001',
      salonId: 'salon-123',
      currentLevel: 6,
      currentTone: 'warm golden',
      underlyingPigment: 'orange',
      porosity: 'medium',
      damage: 'low',
      texture: 'medium',
      density: 'medium',
      grayPercentage: 15,
      previousTreatments: [],
      hasMetallicSalts: false,
      hasHenna: false,
      naturalColor: 'medium brown',
      confidence: 85,
      notes: 'Healthy hair with some gray at temples'
    });

    assertEquals(diagnosis.id, 'diag-001');
    assertEquals(diagnosis.salonId, 'salon-123');
    assertEquals(diagnosis.currentLevel, 6);
    assertEquals(diagnosis.currentTone, 'warm golden');
    assertEquals(diagnosis.porosity, 'medium');
    assertEquals(diagnosis.confidence, 85);
  });

  await t.step('should reject invalid hair level', () => {
    assertRejects(
      () => HairDiagnosis.create({
        id: 'diag-001',
        salonId: 'salon-123',
        currentLevel: 15, // Invalid - exceeds 10
        currentTone: 'warm',
        underlyingPigment: 'orange',
        porosity: 'medium',
        damage: 'low',
        texture: 'medium',
        density: 'medium',
        grayPercentage: 15,
        previousTreatments: [],
        hasMetallicSalts: false,
        hasHenna: false,
        naturalColor: 'medium brown',
        confidence: 85
      }),
      Error,
      'Hair level must be between 1 and 10'
    );
  });

  await t.step('should reject invalid gray percentage', () => {
    assertRejects(
      () => HairDiagnosis.create({
        id: 'diag-001',
        salonId: 'salon-123',
        currentLevel: 6,
        currentTone: 'warm',
        underlyingPigment: 'orange',
        porosity: 'medium',
        damage: 'low',
        texture: 'medium',
        density: 'medium',
        grayPercentage: 150, // Invalid - exceeds 100
        previousTreatments: [],
        hasMetallicSalts: false,
        hasHenna: false,
        naturalColor: 'medium brown',
        confidence: 85
      }),
      Error,
      'Gray percentage must be between 0 and 100'
    );
  });

  await t.step('should reject invalid confidence', () => {
    assertRejects(
      () => HairDiagnosis.create({
        id: 'diag-001',
        salonId: 'salon-123',
        currentLevel: 6,
        currentTone: 'warm',
        underlyingPigment: 'orange',
        porosity: 'medium',
        damage: 'low',
        texture: 'medium',
        density: 'medium',
        grayPercentage: 15,
        previousTreatments: [],
        hasMetallicSalts: false,
        hasHenna: false,
        naturalColor: 'medium brown',
        confidence: 150 // Invalid - exceeds 100
      }),
      Error,
      'Confidence must be between 0 and 100'
    );
  });

  await t.step('should reject empty salon ID', () => {
    assertRejects(
      () => HairDiagnosis.create({
        id: 'diag-001',
        salonId: '', // Invalid - empty
        currentLevel: 6,
        currentTone: 'warm',
        underlyingPigment: 'orange',
        porosity: 'medium',
        damage: 'low',
        texture: 'medium',
        density: 'medium',
        grayPercentage: 15,
        previousTreatments: [],
        hasMetallicSalts: false,
        hasHenna: false,
        naturalColor: 'medium brown',
        confidence: 85
      }),
      Error,
      'Salon ID is required for multi-tenant isolation'
    );
  });

  await t.step('should reject empty diagnosis ID', () => {
    assertRejects(
      () => HairDiagnosis.create({
        id: '', // Invalid - empty
        salonId: 'salon-123',
        currentLevel: 6,
        currentTone: 'warm',
        underlyingPigment: 'orange',
        porosity: 'medium',
        damage: 'low',
        texture: 'medium',
        density: 'medium',
        grayPercentage: 15,
        previousTreatments: [],
        hasMetallicSalts: false,
        hasHenna: false,
        naturalColor: 'medium brown',
        confidence: 85
      }),
      Error,
      'Diagnosis ID is required'
    );
  });
});

Deno.test('HairDiagnosis Entity - Business Logic Methods', async (t) => {
  const createTestDiagnosis = (overrides: Partial<any> = {}) => {
    return HairDiagnosis.create({
      id: 'diag-001',
      salonId: 'salon-123',
      currentLevel: 6,
      currentTone: 'warm golden',
      underlyingPigment: 'orange',
      porosity: 'medium',
      damage: 'low',
      texture: 'medium',
      density: 'medium',
      grayPercentage: 15,
      previousTreatments: [],
      hasMetallicSalts: false,
      hasHenna: false,
      naturalColor: 'medium brown',
      confidence: 85,
      ...overrides
    });
  };

  await t.step('canLighten should return true for safe lightening', () => {
    const diagnosis = createTestDiagnosis();
    assert(diagnosis.canLighten(8)); // Can lighten 2 levels safely
  });

  await t.step('canLighten should return false for severe damage', () => {
    const diagnosis = createTestDiagnosis({ damage: 'severe' });
    assert(!diagnosis.canLighten(8)); // Cannot lighten with severe damage
  });

  await t.step('canLighten should return false with metallic salts', () => {
    const diagnosis = createTestDiagnosis({ hasMetallicSalts: true });
    assert(!diagnosis.canLighten(8)); // Cannot lighten with metallic salts
  });

  await t.step('canLighten should return false with henna', () => {
    const diagnosis = createTestDiagnosis({ hasHenna: true });
    assert(!diagnosis.canLighten(8)); // Cannot lighten with henna
  });

  await t.step('canLighten should limit lightening based on damage level', () => {
    const highDamage = createTestDiagnosis({ damage: 'high', currentLevel: 4 });
    assert(highDamage.canLighten(6)); // Can lighten 2 levels max with high damage
    assert(!highDamage.canLighten(7)); // Cannot lighten 3 levels with high damage

    const mediumDamage = createTestDiagnosis({ damage: 'medium', currentLevel: 4 });
    assert(mediumDamage.canLighten(7)); // Can lighten 3 levels max with medium damage
    assert(!mediumDamage.canLighten(8)); // Cannot lighten 4 levels with medium damage
  });

  await t.step('requiresPreTreatment should return true for high porosity', () => {
    const diagnosis = createTestDiagnosis({ porosity: 'high' });
    assert(diagnosis.requiresPreTreatment());
  });

  await t.step('requiresPreTreatment should return true for high damage', () => {
    const diagnosis = createTestDiagnosis({ damage: 'high' });
    assert(diagnosis.requiresPreTreatment());
  });

  await t.step('requiresPreTreatment should return true for severe damage', () => {
    const diagnosis = createTestDiagnosis({ damage: 'severe' });
    assert(diagnosis.requiresPreTreatment());
  });

  await t.step('requiresPreTreatment should return true with metallic salts', () => {
    const diagnosis = createTestDiagnosis({ hasMetallicSalts: true });
    assert(diagnosis.requiresPreTreatment());
  });

  await t.step('requiresPreTreatment should return false for healthy hair', () => {
    const diagnosis = createTestDiagnosis();
    assert(!diagnosis.requiresPreTreatment());
  });
});

Deno.test('HairDiagnosis Entity - Processing Time Multiplier', async (t) => {
  const createTestDiagnosis = (overrides: Partial<any> = {}) => {
    return HairDiagnosis.create({
      id: 'diag-001',
      salonId: 'salon-123',
      currentLevel: 6,
      currentTone: 'warm golden',
      underlyingPigment: 'orange',
      porosity: 'medium',
      damage: 'low',
      texture: 'medium',
      density: 'medium',
      grayPercentage: 15,
      previousTreatments: [],
      hasMetallicSalts: false,
      hasHenna: false,
      naturalColor: 'medium brown',
      confidence: 85,
      ...overrides
    });
  };

  await t.step('should return 1.0 for normal hair', () => {
    const diagnosis = createTestDiagnosis();
    assertEquals(diagnosis.getProcessingTimeMultiplier(), 1.0);
  });

  await t.step('should increase time for low porosity', () => {
    const diagnosis = createTestDiagnosis({ porosity: 'low' });
    assertEquals(diagnosis.getProcessingTimeMultiplier(), 1.2);
  });

  await t.step('should decrease time for high porosity', () => {
    const diagnosis = createTestDiagnosis({ porosity: 'high' });
    assertEquals(diagnosis.getProcessingTimeMultiplier(), 0.9);
  });

  await t.step('should decrease time for high damage', () => {
    const diagnosis = createTestDiagnosis({ damage: 'high' });
    assertEquals(diagnosis.getProcessingTimeMultiplier(), 0.8);
  });

  await t.step('should decrease time for severe damage', () => {
    const diagnosis = createTestDiagnosis({ damage: 'severe' });
    assertEquals(diagnosis.getProcessingTimeMultiplier(), 0.7);
  });

  await t.step('should not go below 0.5 multiplier', () => {
    const diagnosis = createTestDiagnosis({ 
      damage: 'severe', 
      porosity: 'high' 
    });
    // Would be 0.7 * 0.9 = 0.63, but should be at least 0.5
    assert(diagnosis.getProcessingTimeMultiplier() >= 0.5);
  });

  await t.step('should combine multiple factors correctly', () => {
    const diagnosis = createTestDiagnosis({ 
      porosity: 'low', 
      damage: 'high' 
    });
    // Should be 1.0 + 0.2 - 0.2 = 1.0
    assertEquals(diagnosis.getProcessingTimeMultiplier(), 1.0);
  });
});

Deno.test('HairDiagnosis Entity - Chemical Treatments', async (t) => {
  await t.step('should handle previous treatments correctly', () => {
    const treatments: ChemicalTreatment[] = [
      {
        type: 'coloring',
        date: new Date('2024-06-01'),
        products: ['Professional color line'],
        result: 'Good color result'
      },
      {
        type: 'bleaching',
        date: new Date('2024-03-01'),
        products: ['Lightener powder', 'Developer 30vol']
      }
    ];

    const diagnosis = HairDiagnosis.create({
      id: 'diag-001',
      salonId: 'salon-123',
      currentLevel: 8,
      currentTone: 'neutral',
      underlyingPigment: 'yellow',
      porosity: 'medium',
      damage: 'medium',
      texture: 'fine',
      density: 'thick',
      grayPercentage: 0,
      previousTreatments: treatments,
      hasMetallicSalts: false,
      hasHenna: false,
      naturalColor: 'dark blonde',
      confidence: 90
    });

    const retrievedTreatments = diagnosis.previousTreatments;
    assertEquals(retrievedTreatments.length, 2);
    assertEquals(retrievedTreatments[0].type, 'coloring');
    assertEquals(retrievedTreatments[1].type, 'bleaching');
    
    // Test immutability - modifying returned array shouldn't affect original
    retrievedTreatments.push({
      type: 'perm',
      date: new Date(),
      products: []
    });
    assertEquals(diagnosis.previousTreatments.length, 2); // Original unchanged
  });
});

Deno.test('HairDiagnosis Entity - Serialization', async (t) => {
  await t.step('should serialize and deserialize correctly', () => {
    const originalDate = new Date('2024-09-07T10:30:00Z');
    const treatments: ChemicalTreatment[] = [
      {
        type: 'coloring',
        date: new Date('2024-06-01T14:00:00Z'),
        products: ['Color tube', 'Developer'],
        result: 'Successful coverage'
      }
    ];

    const original = HairDiagnosis.create({
      id: 'diag-001',
      salonId: 'salon-123',
      currentLevel: 6,
      currentTone: 'warm golden',
      underlyingPigment: 'orange',
      porosity: 'medium',
      damage: 'low',
      texture: 'medium',
      density: 'medium',
      grayPercentage: 15,
      previousTreatments: treatments,
      hasMetallicSalts: false,
      hasHenna: false,
      naturalColor: 'medium brown',
      notes: 'Test notes',
      diagnosedAt: originalDate,
      confidence: 85
    });

    const json = original.toJSON();
    const deserialized = HairDiagnosis.fromJSON(json);

    assertEquals(deserialized.id, original.id);
    assertEquals(deserialized.salonId, original.salonId);
    assertEquals(deserialized.currentLevel, original.currentLevel);
    assertEquals(deserialized.currentTone, original.currentTone);
    assertEquals(deserialized.porosity, original.porosity);
    assertEquals(deserialized.damage, original.damage);
    assertEquals(deserialized.confidence, original.confidence);
    assertEquals(deserialized.notes, original.notes);
    assertEquals(deserialized.diagnosedAt.toISOString(), originalDate.toISOString());
    
    const deserializedTreatments = deserialized.previousTreatments;
    assertEquals(deserializedTreatments.length, 1);
    assertEquals(deserializedTreatments[0].type, 'coloring');
    assertEquals(deserializedTreatments[0].date.toISOString(), '2024-06-01T14:00:00.000Z');
  });

  await t.step('should handle missing optional fields in JSON', () => {
    const json = {
      id: 'diag-001',
      salonId: 'salon-123',
      currentLevel: 6,
      currentTone: 'warm',
      underlyingPigment: 'orange',
      porosity: 'medium',
      damage: 'low',
      texture: 'medium',
      density: 'medium',
      grayPercentage: 15,
      previousTreatments: [],
      hasMetallicSalts: false,
      hasHenna: false,
      naturalColor: 'medium brown',
      diagnosedAt: '2024-09-07T10:30:00Z',
      confidence: 85
      // notes missing - should default to empty string
    };

    const diagnosis = HairDiagnosis.fromJSON(json);
    assertEquals(diagnosis.notes, '');
  });
});

Deno.test('HairDiagnosis Entity - Immutability', async (t) => {
  await t.step('should maintain immutability of all fields', () => {
    const treatments: ChemicalTreatment[] = [
      {
        type: 'coloring',
        date: new Date(),
        products: ['test']
      }
    ];

    const diagnosis = HairDiagnosis.create({
      id: 'diag-001',
      salonId: 'salon-123',
      currentLevel: 6,
      currentTone: 'warm golden',
      underlyingPigment: 'orange',
      porosity: 'medium',
      damage: 'low',
      texture: 'medium',
      density: 'medium',
      grayPercentage: 15,
      previousTreatments: treatments,
      hasMetallicSalts: false,
      hasHenna: false,
      naturalColor: 'medium brown',
      confidence: 85
    });

    // Attempt to modify returned arrays - should not affect original
    const returnedTreatments = diagnosis.previousTreatments;
    returnedTreatments.push({
      type: 'bleaching',
      date: new Date(),
      products: []
    });

    // Original should remain unchanged
    assertEquals(diagnosis.previousTreatments.length, 1);
    assertEquals(diagnosis.previousTreatments[0].type, 'coloring');
  });

  await t.step('should not allow direct property modification', () => {
    const diagnosis = HairDiagnosis.create({
      id: 'diag-001',
      salonId: 'salon-123',
      currentLevel: 6,
      currentTone: 'warm golden',
      underlyingPigment: 'orange',
      porosity: 'medium',
      damage: 'low',
      texture: 'medium',
      density: 'medium',
      grayPercentage: 15,
      previousTreatments: [],
      hasMetallicSalts: false,
      hasHenna: false,
      naturalColor: 'medium brown',
      confidence: 85
    });

    // Properties should be read-only
    const originalLevel = diagnosis.currentLevel;
    (diagnosis as any).currentLevel = 10; // This should not work
    assertEquals(diagnosis.currentLevel, originalLevel);
  });
});