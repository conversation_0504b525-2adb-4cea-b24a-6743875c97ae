/**
 * ColorFormula Domain Entity Tests
 * 
 * Tests for the ColorFormula domain entity, focusing on:
 * - Business rule validation
 * - Formula calculation logic
 * - Step validation and ordering
 * - Risk assessment
 * - Cost calculations
 * - Safety validations
 */

import { assertEquals, assertRejects, assert } from '../../../test-utils.ts';
import { 
  ColorFormula, 
  FormulaStep, 
  FormulaIngredient, 
  FormulaRisk, 
  ExpectedResult,
  ProcessingTechnique 
} from '../../../../domain/entities/ColorFormula.ts';

Deno.test('ColorFormula Entity - Creation and Validation', async (t) => {
  const createValidIngredient = (): FormulaIngredient => ({
    productId: 'prod-001',
    productName: '8.1 Light Ash Blonde',
    shade: '8.1',
    amount: 50,
    unit: 'ml',
    type: 'color',
    brand: 'Wella',
    line: 'Koleston Perfect'
  });

  const createValidStep = (order = 1): FormulaStep => ({
    id: `step-${order}`,
    title: `Step ${order}`,
    ingredients: [createValidIngredient()],
    processingTimeMinutes: 35,
    instructions: 'Apply evenly from roots to tips',
    technique: 'global',
    order,
    isOptional: false
  });

  const createValidExpectedResult = (): ExpectedResult => ({
    targetLevel: 8,
    targetTone: 'ash blonde',
    coveragePercentage: 95,
    durabilityWeeks: 6,
    maintenanceRequired: 'Purple shampoo weekly'
  });

  await t.step('should create valid color formula', () => {
    const formula = ColorFormula.create({
      id: 'formula-001',
      salonId: 'salon-123',
      name: 'Ash Blonde Transformation',
      steps: [createValidStep()],
      currentLevel: 6,
      targetLevel: 8,
      currentTone: 'warm golden',
      targetTone: 'ash blonde',
      brand: 'Wella',
      line: 'Koleston Perfect',
      technique: 'global',
      expectedResult: createValidExpectedResult(),
      confidence: 85
    });

    assertEquals(formula.id, 'formula-001');
    assertEquals(formula.salonId, 'salon-123');
    assertEquals(formula.name, 'Ash Blonde Transformation');
    assertEquals(formula.currentLevel, 6);
    assertEquals(formula.targetLevel, 8);
    assertEquals(formula.steps.length, 1);
    assertEquals(formula.totalTimeMinutes, 35);
  });

  await t.step('should reject invalid current level', () => {
    assertRejects(
      () => ColorFormula.create({
        id: 'formula-001',
        salonId: 'salon-123',
        name: 'Test Formula',
        steps: [createValidStep()],
        currentLevel: 15, // Invalid - exceeds 10
        targetLevel: 8,
        currentTone: 'warm',
        targetTone: 'ash',
        brand: 'Wella',
        line: 'Test',
        technique: 'global',
        expectedResult: createValidExpectedResult(),
        confidence: 85
      }),
      Error,
      'Current hair level must be between 1 and 10'
    );
  });

  await t.step('should reject invalid target level', () => {
    assertRejects(
      () => ColorFormula.create({
        id: 'formula-001',
        salonId: 'salon-123',
        name: 'Test Formula',
        steps: [createValidStep()],
        currentLevel: 6,
        targetLevel: 0, // Invalid - below 1
        currentTone: 'warm',
        targetTone: 'ash',
        brand: 'Wella',
        line: 'Test',
        technique: 'global',
        expectedResult: createValidExpectedResult(),
        confidence: 85
      }),
      Error,
      'Target hair level must be between 1 and 10'
    );
  });

  await t.step('should reject empty salon ID', () => {
    assertRejects(
      () => ColorFormula.create({
        id: 'formula-001',
        salonId: '', // Invalid - empty
        name: 'Test Formula',
        steps: [createValidStep()],
        currentLevel: 6,
        targetLevel: 8,
        currentTone: 'warm',
        targetTone: 'ash',
        brand: 'Wella',
        line: 'Test',
        technique: 'global',
        expectedResult: createValidExpectedResult(),
        confidence: 85
      }),
      Error,
      'Salon ID is required for multi-tenant isolation'
    );
  });

  await t.step('should reject formula without steps', () => {
    assertRejects(
      () => ColorFormula.create({
        id: 'formula-001',
        salonId: 'salon-123',
        name: 'Test Formula',
        steps: [], // Invalid - empty
        currentLevel: 6,
        targetLevel: 8,
        currentTone: 'warm',
        targetTone: 'ash',
        brand: 'Wella',
        line: 'Test',
        technique: 'global',
        expectedResult: createValidExpectedResult(),
        confidence: 85
      }),
      Error,
      'Formula must have at least one processing step'
    );
  });

  await t.step('should reject invalid coverage percentage', () => {
    const invalidExpectedResult = {
      ...createValidExpectedResult(),
      coveragePercentage: 150 // Invalid - exceeds 100
    };

    assertRejects(
      () => ColorFormula.create({
        id: 'formula-001',
        salonId: 'salon-123',
        name: 'Test Formula',
        steps: [createValidStep()],
        currentLevel: 6,
        targetLevel: 8,
        currentTone: 'warm',
        targetTone: 'ash',
        brand: 'Wella',
        line: 'Test',
        technique: 'global',
        expectedResult: invalidExpectedResult,
        confidence: 85
      }),
      Error,
      'Coverage percentage must be between 0 and 100'
    );
  });
});

Deno.test('ColorFormula Entity - Step Validation', async (t) => {
  const createValidIngredient = (): FormulaIngredient => ({
    productId: 'prod-001',
    productName: '8.1 Light Ash Blonde',
    amount: 50,
    unit: 'ml',
    type: 'color',
    brand: 'Wella',
    line: 'Koleston Perfect'
  });

  const createValidExpectedResult = (): ExpectedResult => ({
    targetLevel: 8,
    targetTone: 'ash blonde',
    coveragePercentage: 95,
    durabilityWeeks: 6,
    maintenanceRequired: 'Purple shampoo weekly'
  });

  await t.step('should reject step with zero processing time', () => {
    const invalidStep: FormulaStep = {
      id: 'step-1',
      title: 'Invalid Step',
      ingredients: [createValidIngredient()],
      processingTimeMinutes: 0, // Invalid - must be positive
      instructions: 'Test instructions',
      technique: 'global',
      order: 1,
      isOptional: false
    };

    assertRejects(
      () => ColorFormula.create({
        id: 'formula-001',
        salonId: 'salon-123',
        name: 'Test Formula',
        steps: [invalidStep],
        currentLevel: 6,
        targetLevel: 8,
        currentTone: 'warm',
        targetTone: 'ash',
        brand: 'Wella',
        line: 'Test',
        technique: 'global',
        expectedResult: createValidExpectedResult(),
        confidence: 85
      }),
      Error,
      'Step 1 must have positive processing time'
    );
  });

  await t.step('should reject step with no ingredients', () => {
    const invalidStep: FormulaStep = {
      id: 'step-1',
      title: 'Invalid Step',
      ingredients: [], // Invalid - must have ingredients
      processingTimeMinutes: 35,
      instructions: 'Test instructions',
      technique: 'global',
      order: 1,
      isOptional: false
    };

    assertRejects(
      () => ColorFormula.create({
        id: 'formula-001',
        salonId: 'salon-123',
        name: 'Test Formula',
        steps: [invalidStep],
        currentLevel: 6,
        targetLevel: 8,
        currentTone: 'warm',
        targetTone: 'ash',
        brand: 'Wella',
        line: 'Test',
        technique: 'global',
        expectedResult: createValidExpectedResult(),
        confidence: 85
      }),
      Error,
      'Step 1 must have at least one ingredient'
    );
  });

  await t.step('should reject ingredient with zero amount', () => {
    const invalidIngredient: FormulaIngredient = {
      productId: 'prod-001',
      productName: 'Test Color',
      amount: 0, // Invalid - must be positive
      unit: 'ml',
      type: 'color',
      brand: 'Test Brand',
      line: 'Test Line'
    };

    const invalidStep: FormulaStep = {
      id: 'step-1',
      title: 'Test Step',
      ingredients: [invalidIngredient],
      processingTimeMinutes: 35,
      instructions: 'Test instructions',
      technique: 'global',
      order: 1,
      isOptional: false
    };

    assertRejects(
      () => ColorFormula.create({
        id: 'formula-001',
        salonId: 'salon-123',
        name: 'Test Formula',
        steps: [invalidStep],
        currentLevel: 6,
        targetLevel: 8,
        currentTone: 'warm',
        targetTone: 'ash',
        brand: 'Wella',
        line: 'Test',
        technique: 'global',
        expectedResult: createValidExpectedResult(),
        confidence: 85
      }),
      Error,
      'Step 1, ingredient 1 must have positive amount'
    );
  });

  await t.step('should sort steps by order', () => {
    const step1: FormulaStep = {
      id: 'step-1',
      title: 'Second Step',
      ingredients: [createValidIngredient()],
      processingTimeMinutes: 20,
      instructions: 'Second step',
      technique: 'global',
      order: 2,
      isOptional: false
    };

    const step2: FormulaStep = {
      id: 'step-2',
      title: 'First Step',
      ingredients: [createValidIngredient()],
      processingTimeMinutes: 15,
      instructions: 'First step',
      technique: 'global',
      order: 1,
      isOptional: false
    };

    const formula = ColorFormula.create({
      id: 'formula-001',
      salonId: 'salon-123',
      name: 'Test Formula',
      steps: [step1, step2], // Passed in wrong order
      currentLevel: 6,
      targetLevel: 8,
      currentTone: 'warm',
      targetTone: 'ash',
      brand: 'Wella',
      line: 'Test',
      technique: 'global',
      expectedResult: createValidExpectedResult(),
      confidence: 85
    });

    // Should be sorted by order
    assertEquals(formula.steps[0].order, 1);
    assertEquals(formula.steps[0].title, 'First Step');
    assertEquals(formula.steps[1].order, 2);
    assertEquals(formula.steps[1].title, 'Second Step');
    assertEquals(formula.totalTimeMinutes, 35); // 15 + 20
  });
});

Deno.test('ColorFormula Entity - Business Logic Methods', async (t) => {
  const createTestFormula = (overrides: Partial<any> = {}) => {
    const ingredient: FormulaIngredient = {
      productId: 'prod-001',
      productName: '8.1 Light Ash Blonde',
      amount: 50,
      unit: 'ml',
      type: 'color',
      brand: 'Wella',
      line: 'Koleston Perfect'
    };

    const step: FormulaStep = {
      id: 'step-1',
      title: 'Color Application',
      ingredients: [ingredient],
      processingTimeMinutes: 35,
      instructions: 'Apply evenly',
      technique: 'global',
      order: 1,
      isOptional: false
    };

    const expectedResult: ExpectedResult = {
      targetLevel: 8,
      targetTone: 'ash blonde',
      coveragePercentage: 95,
      durabilityWeeks: 6,
      maintenanceRequired: 'Purple shampoo weekly'
    };

    return ColorFormula.create({
      id: 'formula-001',
      salonId: 'salon-123',
      name: 'Test Formula',
      steps: [step],
      currentLevel: 6,
      targetLevel: 8,
      currentTone: 'warm',
      targetTone: 'ash',
      brand: 'Wella',
      line: 'Koleston Perfect',
      technique: 'global',
      expectedResult,
      confidence: 85,
      ...overrides
    });
  };

  await t.step('isSafeToExecute should return true for safe formula', () => {
    const formula = createTestFormula();
    assert(formula.isSafeToExecute());
  });

  await t.step('isSafeToExecute should return false with critical risks', () => {
    const criticalRisk: FormulaRisk = {
      type: 'chemical',
      severity: 'critical',
      description: 'Incompatible chemicals detected',
      mitigation: 'Do not proceed'
    };

    const formula = createTestFormula({ 
      risks: [criticalRisk] 
    });

    assert(!formula.isSafeToExecute());
  });

  await t.step('isSafeToExecute should return false with low confidence', () => {
    const formula = createTestFormula({ 
      confidence: 60 // Below 70 threshold
    });

    assert(!formula.isSafeToExecute());
  });

  await t.step('getEstimatedCost should calculate basic cost', () => {
    const formula = createTestFormula();
    const cost = formula.getEstimatedCost();
    
    assert(typeof cost === 'number');
    assert(cost > 0);
    // For 50ml color at 2.5 base cost per 100ml/g: 50 * 2.5 / 100 = 1.25
    assertEquals(cost, 1.25);
  });

  await t.step('getEstimatedCost should handle different product types', () => {
    const colorIngredient: FormulaIngredient = {
      productId: 'color-001',
      productName: 'Color',
      amount: 50,
      unit: 'ml',
      type: 'color',
      brand: 'Wella',
      line: 'Test'
    };

    const developerIngredient: FormulaIngredient = {
      productId: 'dev-001',
      productName: 'Developer',
      amount: 75,
      unit: 'ml',
      type: 'developer',
      volume: 20,
      brand: 'Wella',
      line: 'Test'
    };

    const bleachIngredient: FormulaIngredient = {
      productId: 'bleach-001',
      productName: 'Bleach',
      amount: 30,
      unit: 'g',
      type: 'bleach',
      brand: 'Wella',
      line: 'Test'
    };

    const step: FormulaStep = {
      id: 'step-1',
      title: 'Multi-ingredient step',
      ingredients: [colorIngredient, developerIngredient, bleachIngredient],
      processingTimeMinutes: 35,
      instructions: 'Apply mixture',
      technique: 'global',
      order: 1,
      isOptional: false
    };

    const formula = createTestFormula({ steps: [step] });
    const cost = formula.getEstimatedCost();
    
    // Expected: (50 * 2.5 / 100) + (75 * 1.0 / 100) + (30 * 3.0 / 100)
    // = 1.25 + 0.75 + 0.90 = 2.90
    assertEquals(cost, 2.90);
  });

  await t.step('getRequiredProducts should return unique products', () => {
    const ingredient1: FormulaIngredient = {
      productId: 'prod-001',
      productName: 'Color 8.1',
      shade: '8.1',
      amount: 50,
      unit: 'ml',
      type: 'color',
      brand: 'Wella',
      line: 'Koleston'
    };

    const ingredient2: FormulaIngredient = {
      productId: 'prod-001', // Same product ID
      productName: 'Color 8.1',
      shade: '8.1',
      amount: 25, // Different amount
      unit: 'ml',
      type: 'color',
      brand: 'Wella',
      line: 'Koleston'
    };

    const ingredient3: FormulaIngredient = {
      productId: 'prod-002',
      productName: 'Developer',
      volume: 20,
      amount: 75,
      unit: 'ml',
      type: 'developer',
      brand: 'Wella',
      line: 'Koleston'
    };

    const step1: FormulaStep = {
      id: 'step-1',
      title: 'Step 1',
      ingredients: [ingredient1],
      processingTimeMinutes: 20,
      instructions: 'First application',
      technique: 'global',
      order: 1,
      isOptional: false
    };

    const step2: FormulaStep = {
      id: 'step-2',
      title: 'Step 2',
      ingredients: [ingredient2, ingredient3],
      processingTimeMinutes: 15,
      instructions: 'Second application',
      technique: 'global',
      order: 2,
      isOptional: false
    };

    const formula = createTestFormula({ steps: [step1, step2] });
    const requiredProducts = formula.getRequiredProducts();
    
    assertEquals(requiredProducts.length, 2); // Should deduplicate prod-001
    assert(requiredProducts.some(p => p.productId === 'prod-001'));
    assert(requiredProducts.some(p => p.productId === 'prod-002'));
  });
});

Deno.test('ColorFormula Entity - Validation and Warning Methods', async (t) => {
  const createTestFormula = () => {
    const ingredient: FormulaIngredient = {
      productId: 'prod-001',
      productName: '8.1 Light Ash Blonde',
      amount: 50,
      unit: 'ml',
      type: 'color',
      brand: 'Wella',
      line: 'Koleston Perfect'
    };

    const step: FormulaStep = {
      id: 'step-1',
      title: 'Color Application',
      ingredients: [ingredient],
      processingTimeMinutes: 35,
      instructions: 'Apply evenly',
      technique: 'global',
      order: 1,
      isOptional: false
    };

    const expectedResult: ExpectedResult = {
      targetLevel: 8,
      targetTone: 'ash blonde',
      coveragePercentage: 95,
      durabilityWeeks: 6,
      maintenanceRequired: 'Purple shampoo weekly'
    };

    return ColorFormula.create({
      id: 'formula-001',
      salonId: 'salon-123',
      name: 'Test Formula',
      steps: [step],
      currentLevel: 6,
      targetLevel: 8,
      currentTone: 'warm',
      targetTone: 'ash',
      brand: 'Wella',
      line: 'Koleston Perfect',
      technique: 'global',
      expectedResult,
      confidence: 85
    });
  };

  await t.step('validate should create validated copy', () => {
    const formula = createTestFormula();
    const validated = formula.validate('senior-colorist-001');

    assert(validated.isValidated);
    assertEquals(validated.validatedBy, 'senior-colorist-001');
    // Original should remain unvalidated
    assert(!formula.isValidated);
    assertEquals(formula.validatedBy, undefined);
  });

  await t.step('addWarning should create copy with additional warning', () => {
    const formula = createTestFormula();
    const withWarning = formula.addWarning('Monitor processing time closely');

    assertEquals(withWarning.warnings.length, formula.warnings.length + 1);
    assert(withWarning.warnings.includes('Monitor processing time closely'));
    // Original should remain unchanged
    assertEquals(formula.warnings.length, 0);
  });
});

Deno.test('ColorFormula Entity - Serialization', async (t) => {
  await t.step('should serialize and deserialize correctly', () => {
    const ingredient: FormulaIngredient = {
      productId: 'prod-001',
      productName: '8.1 Light Ash Blonde',
      shade: '8.1',
      amount: 50,
      unit: 'ml',
      type: 'color',
      volume: 20,
      brand: 'Wella',
      line: 'Koleston Perfect'
    };

    const step: FormulaStep = {
      id: 'step-1',
      title: 'Color Application',
      ingredients: [ingredient],
      processingTimeMinutes: 35,
      instructions: 'Apply evenly from roots to tips',
      technique: 'global',
      temperature: 25,
      order: 1,
      isOptional: false
    };

    const risk: FormulaRisk = {
      type: 'damage',
      severity: 'medium',
      description: 'Possible over-processing of ends',
      mitigation: 'Monitor carefully and rinse early if needed'
    };

    const expectedResult: ExpectedResult = {
      targetLevel: 8,
      targetTone: 'ash blonde',
      coveragePercentage: 95,
      durabilityWeeks: 6,
      maintenanceRequired: 'Purple shampoo weekly'
    };

    const createdDate = new Date('2024-09-07T10:30:00Z');

    const original = ColorFormula.create({
      id: 'formula-001',
      salonId: 'salon-123',
      name: 'Ash Blonde Transformation',
      steps: [step],
      currentLevel: 6,
      targetLevel: 8,
      currentTone: 'warm golden',
      targetTone: 'ash blonde',
      brand: 'Wella',
      line: 'Koleston Perfect',
      technique: 'global',
      expectedResult,
      risks: [risk],
      warnings: ['Test warning'],
      notes: 'Test notes',
      confidence: 85,
      createdAt: createdDate,
      isValidated: true,
      validatedBy: 'senior-colorist-001'
    });

    const json = original.toJSON();
    const deserialized = ColorFormula.fromJSON(json);

    assertEquals(deserialized.id, original.id);
    assertEquals(deserialized.salonId, original.salonId);
    assertEquals(deserialized.name, original.name);
    assertEquals(deserialized.steps.length, original.steps.length);
    assertEquals(deserialized.currentLevel, original.currentLevel);
    assertEquals(deserialized.targetLevel, original.targetLevel);
    assertEquals(deserialized.brand, original.brand);
    assertEquals(deserialized.technique, original.technique);
    assertEquals(deserialized.totalTimeMinutes, original.totalTimeMinutes);
    assertEquals(deserialized.risks.length, original.risks.length);
    assertEquals(deserialized.warnings.length, original.warnings.length);
    assertEquals(deserialized.confidence, original.confidence);
    assertEquals(deserialized.isValidated, original.isValidated);
    assertEquals(deserialized.validatedBy, original.validatedBy);
    assertEquals(deserialized.createdAt.toISOString(), createdDate.toISOString());
    
    // Test deep equality of complex objects
    assertEquals(deserialized.steps[0].id, step.id);
    assertEquals(deserialized.steps[0].ingredients[0].productId, ingredient.productId);
    assertEquals(deserialized.risks[0].type, risk.type);
    assertEquals(deserialized.expectedResult.targetLevel, expectedResult.targetLevel);
  });
});

Deno.test('ColorFormula Entity - Immutability', async (t) => {
  await t.step('should maintain immutability of all complex fields', () => {
    const ingredient: FormulaIngredient = {
      productId: 'prod-001',
      productName: 'Test Color',
      amount: 50,
      unit: 'ml',
      type: 'color',
      brand: 'Test',
      line: 'Test'
    };

    const step: FormulaStep = {
      id: 'step-1',
      title: 'Test Step',
      ingredients: [ingredient],
      processingTimeMinutes: 35,
      instructions: 'Test',
      technique: 'global',
      order: 1,
      isOptional: false
    };

    const expectedResult: ExpectedResult = {
      targetLevel: 8,
      targetTone: 'test',
      coveragePercentage: 95,
      durabilityWeeks: 6,
      maintenanceRequired: 'test'
    };

    const formula = ColorFormula.create({
      id: 'formula-001',
      salonId: 'salon-123',
      name: 'Test Formula',
      steps: [step],
      currentLevel: 6,
      targetLevel: 8,
      currentTone: 'warm',
      targetTone: 'ash',
      brand: 'Test',
      line: 'Test',
      technique: 'global',
      expectedResult,
      confidence: 85,
      warnings: ['Original warning'],
      risks: []
    });

    // Attempt to modify returned arrays - should not affect original
    const returnedSteps = formula.steps;
    const returnedWarnings = formula.warnings;
    const returnedRisks = formula.risks;

    returnedSteps.push({
      id: 'step-2',
      title: 'New Step',
      ingredients: [],
      processingTimeMinutes: 10,
      instructions: 'New',
      technique: 'global',
      order: 2,
      isOptional: true
    });

    returnedWarnings.push('New warning');
    returnedRisks.push({
      type: 'chemical',
      severity: 'low',
      description: 'New risk',
      mitigation: 'Handle carefully'
    });

    // Original should remain unchanged
    assertEquals(formula.steps.length, 1);
    assertEquals(formula.warnings.length, 1);
    assertEquals(formula.warnings[0], 'Original warning');
    assertEquals(formula.risks.length, 0);
  });
});