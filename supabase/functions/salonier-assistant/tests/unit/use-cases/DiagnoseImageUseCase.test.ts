/**
 * DiagnoseImageUseCase Tests
 * 
 * Tests for the DiagnoseImageUseCase, focusing on:
 * - Request validation
 * - Image processing workflow
 * - AI service integration
 * - Cache behavior
 * - Error handling and retries
 * - Response parsing and validation
 * - Performance metrics tracking
 */

import { assertEquals, assert, assertRejects, stub, spy } from '../../test-utils.ts';
import { DiagnoseImageUseCase } from '../../../use-cases/DiagnoseImageUseCase.ts';
import { MockServices, MockDataFactory, TestAssertions } from '../../test-utils.ts';

Deno.test('DiagnoseImageUseCase - Successful Execution', async (t) => {
  await t.step('should successfully process image with valid request', async () => {
    // Arrange
    const mockAIService = MockServices.createMockAIService();
    const mockCacheService = MockServices.createMockCacheService();
    const mockImageProcessor = MockServices.createMockImageProcessor();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();
    const mockPromptService = MockServices.createMockPromptService();
    const mockRetryService = MockServices.createMockRetryService();

    const useCase = new DiagnoseImageUseCase(
      mockAIService,
      mockCacheService,
      mockImageProcessor,
      mockValidator,
      mockLogger,
      mockPromptService,
      mockRetryService
    );

    const request = MockDataFactory.createDiagnoseImageRequest();
    const expectedDiagnosis = MockDataFactory.createHairDiagnosis();

    // Mock successful AI response
    mockAIService.callOpenAI.returns(Promise.resolve({
      choices: [{
        message: { content: JSON.stringify(expectedDiagnosis) },
        finish_reason: 'stop'
      }],
      usage: {
        prompt_tokens: 150,
        completion_tokens: 200,
        total_tokens: 350
      }
    }));

    // Act
    const result = await useCase.execute(request);

    // Assert
    assert(result.success);
    assert(result.data);
    TestAssertions.assertValidHairDiagnosis(result.data);
    
    // Verify service interactions
    assert(mockValidator.validateRequest.called);
    assert(mockImageProcessor.validateImage.called);
    assert(mockImageProcessor.convertToDataUrl.called);
    assert(mockAIService.determineComplexity.called);
    assert(mockAIService.selectOptimalModel.called);
    assert(mockPromptService.getDiagnosisPrompt.called);
    assert(mockRetryService.retryWithBackoff.called);
    assert(mockLogger.trackMetrics.called);
  });

  await t.step('should use cached result when available', async () => {
    // Arrange
    const mockAIService = MockServices.createMockAIService();
    const mockCacheService = MockServices.createMockCacheService();
    const mockImageProcessor = MockServices.createMockImageProcessor();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();
    const mockPromptService = MockServices.createMockPromptService();
    const mockRetryService = MockServices.createMockRetryService();

    const cachedDiagnosis = MockDataFactory.createHairDiagnosis();
    mockCacheService.get.returns(Promise.resolve(cachedDiagnosis));

    const useCase = new DiagnoseImageUseCase(
      mockAIService,
      mockCacheService,
      mockImageProcessor,
      mockValidator,
      mockLogger,
      mockPromptService,
      mockRetryService
    );

    const request = MockDataFactory.createDiagnoseImageRequest();

    // Act
    const result = await useCase.execute(request);

    // Assert
    assert(result.success);
    assertEquals(result.data, cachedDiagnosis);
    
    // Verify cache was used and AI service was not called
    assert(mockCacheService.get.called);
    assert(!mockAIService.callOpenAI.called);
    assert(mockLogger.debug.calledWith('Cache hit for image diagnosis'));
  });

  await t.step('should cache successful results', async () => {
    // Arrange
    const mockAIService = MockServices.createMockAIService();
    const mockCacheService = MockServices.createMockCacheService();
    const mockImageProcessor = MockServices.createMockImageProcessor();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();
    const mockPromptService = MockServices.createMockPromptService();
    const mockRetryService = MockServices.createMockRetryService();

    // Cache miss, then successful AI call
    mockCacheService.get.returns(Promise.resolve(null));
    const expectedDiagnosis = MockDataFactory.createHairDiagnosis();
    mockAIService.callOpenAI.returns(Promise.resolve({
      choices: [{
        message: { content: JSON.stringify(expectedDiagnosis) },
        finish_reason: 'stop'
      }],
      usage: {
        prompt_tokens: 150,
        completion_tokens: 200,
        total_tokens: 350
      }
    }));

    const useCase = new DiagnoseImageUseCase(
      mockAIService,
      mockCacheService,
      mockImageProcessor,
      mockValidator,
      mockLogger,
      mockPromptService,
      mockRetryService
    );

    const request = MockDataFactory.createDiagnoseImageRequest();

    // Act
    const result = await useCase.execute(request);

    // Assert
    assert(result.success);
    assert(mockCacheService.set.called);
    
    // Verify cache.set was called with correct parameters
    const setCalls = mockCacheService.set.calls;
    assertEquals(setCalls.length, 1);
    assertEquals(setCalls[0].args[0], request.salonId); // salonId
    assertEquals(setCalls[0].args[1], 'diagnose_image'); // operation
  });
});

Deno.test('DiagnoseImageUseCase - Error Handling', async (t) => {
  await t.step('should handle validation errors', async () => {
    // Arrange
    const mockAIService = MockServices.createMockAIService();
    const mockCacheService = MockServices.createMockCacheService();
    const mockImageProcessor = MockServices.createMockImageProcessor();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();
    const mockPromptService = MockServices.createMockPromptService();
    const mockRetryService = MockServices.createMockRetryService();

    // Mock validation failure
    mockValidator.validateRequest.returns({
      valid: false,
      errors: ['Missing required field: salonId']
    });

    const useCase = new DiagnoseImageUseCase(
      mockAIService,
      mockCacheService,
      mockImageProcessor,
      mockValidator,
      mockLogger,
      mockPromptService,
      mockRetryService
    );

    const invalidRequest = { imageUrl: 'test.jpg' }; // Missing salonId

    // Act
    const result = await useCase.execute(invalidRequest as any);

    // Assert
    assert(!result.success);
    assert(result.error?.includes('Invalid request'));
    assert(result.error?.includes('Missing required field: salonId'));
    
    // Verify error was logged
    assert(mockLogger.error.called);
  });

  await t.step('should handle image validation errors', async () => {
    // Arrange
    const mockAIService = MockServices.createMockAIService();
    const mockCacheService = MockServices.createMockCacheService();
    const mockImageProcessor = MockServices.createMockImageProcessor();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();
    const mockPromptService = MockServices.createMockPromptService();
    const mockRetryService = MockServices.createMockRetryService();

    // Mock image validation failure
    mockImageProcessor.validateImage.rejects(new Error('Invalid image format'));

    const useCase = new DiagnoseImageUseCase(
      mockAIService,
      mockCacheService,
      mockImageProcessor,
      mockValidator,
      mockLogger,
      mockPromptService,
      mockRetryService
    );

    const request = MockDataFactory.createDiagnoseImageRequest();

    // Act
    const result = await useCase.execute(request);

    // Assert
    assert(!result.success);
    assertEquals(result.error, 'Invalid image format');
    assert(mockLogger.error.called);
  });

  await t.step('should handle AI service errors', async () => {
    // Arrange
    const mockAIService = MockServices.createMockAIService();
    const mockCacheService = MockServices.createMockCacheService();
    const mockImageProcessor = MockServices.createMockImageProcessor();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();
    const mockPromptService = MockServices.createMockPromptService();
    const mockRetryService = MockServices.createMockRetryService();

    // Mock AI service failure
    mockRetryService.retryWithBackoff.rejects(new Error('AI service timeout'));

    const useCase = new DiagnoseImageUseCase(
      mockAIService,
      mockCacheService,
      mockImageProcessor,
      mockValidator,
      mockLogger,
      mockPromptService,
      mockRetryService
    );

    const request = MockDataFactory.createDiagnoseImageRequest();

    // Act
    const result = await useCase.execute(request);

    // Assert
    assert(!result.success);
    assertEquals(result.error, 'AI service timeout');
    assert(mockLogger.error.called);
    assert(mockLogger.trackMetrics.called);
    
    // Verify error metrics were tracked
    const trackMetricsCalls = mockLogger.trackMetrics.calls;
    const errorMetrics = trackMetricsCalls.find(call => 
      call.args[0].success === false
    );
    assert(errorMetrics);
  });

  await t.step('should handle content filter rejection', async () => {
    // Arrange
    const mockAIService = MockServices.createMockAIService();
    const mockCacheService = MockServices.createMockCacheService();
    const mockImageProcessor = MockServices.createMockImageProcessor();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();
    const mockPromptService = MockServices.createMockPromptService();
    const mockRetryService = MockServices.createMockRetryService();

    // Mock content filter response
    mockRetryService.retryWithBackoff.returns(Promise.resolve({
      choices: [{
        message: { content: null },
        finish_reason: 'content_filter'
      }],
      usage: { prompt_tokens: 50, completion_tokens: 0, total_tokens: 50 }
    }));

    const useCase = new DiagnoseImageUseCase(
      mockAIService,
      mockCacheService,
      mockImageProcessor,
      mockValidator,
      mockLogger,
      mockPromptService,
      mockRetryService
    );

    const request = MockDataFactory.createDiagnoseImageRequest();

    // Act
    const result = await useCase.execute(request);

    // Assert
    assert(!result.success);
    assert(result.error?.includes('blocked by content filters'));
  });

  await t.step('should handle refusal response', async () => {
    // Arrange
    const mockAIService = MockServices.createMockAIService();
    const mockCacheService = MockServices.createMockCacheService();
    const mockImageProcessor = MockServices.createMockImageProcessor();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();
    const mockPromptService = MockServices.createMockPromptService();
    const mockRetryService = MockServices.createMockRetryService();

    // Mock refusal response
    mockRetryService.retryWithBackoff.returns(Promise.resolve({
      choices: [{
        message: { 
          content: null,
          refusal: 'I cannot analyze this type of image'
        },
        finish_reason: 'stop'
      }],
      usage: { prompt_tokens: 50, completion_tokens: 10, total_tokens: 60 }
    }));

    const useCase = new DiagnoseImageUseCase(
      mockAIService,
      mockCacheService,
      mockImageProcessor,
      mockValidator,
      mockLogger,
      mockPromptService,
      mockRetryService
    );

    const request = MockDataFactory.createDiagnoseImageRequest();

    // Act
    const result = await useCase.execute(request);

    // Assert
    assert(!result.success);
    assert(result.error?.includes('OpenAI refused to process image'));
    assert(result.error?.includes('I cannot analyze this type of image'));
  });

  await t.step('should handle invalid JSON response', async () => {
    // Arrange
    const mockAIService = MockServices.createMockAIService();
    const mockCacheService = MockServices.createMockCacheService();
    const mockImageProcessor = MockServices.createMockImageProcessor();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();
    const mockPromptService = MockServices.createMockPromptService();
    const mockRetryService = MockServices.createMockRetryService();

    // Mock invalid JSON response
    mockRetryService.retryWithBackoff.returns(Promise.resolve({
      choices: [{
        message: { content: 'This is not valid JSON' },
        finish_reason: 'stop'
      }],
      usage: { prompt_tokens: 50, completion_tokens: 10, total_tokens: 60 }
    }));

    const useCase = new DiagnoseImageUseCase(
      mockAIService,
      mockCacheService,
      mockImageProcessor,
      mockValidator,
      mockLogger,
      mockPromptService,
      mockRetryService
    );

    const request = MockDataFactory.createDiagnoseImageRequest();

    // Act
    const result = await useCase.execute(request);

    // Assert
    assert(!result.success);
    assert(result.error?.includes('Invalid JSON from AI'));
  });
});

Deno.test('DiagnoseImageUseCase - Response Processing', async (t) => {
  await t.step('should parse markdown-wrapped JSON correctly', async () => {
    // Arrange
    const mockAIService = MockServices.createMockAIService();
    const mockCacheService = MockServices.createMockCacheService();
    const mockImageProcessor = MockServices.createMockImageProcessor();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();
    const mockPromptService = MockServices.createMockPromptService();
    const mockRetryService = MockServices.createMockRetryService();

    const diagnosis = MockDataFactory.createHairDiagnosis();
    const markdownWrappedResponse = '```json\n' + JSON.stringify(diagnosis) + '\n```';

    mockRetryService.retryWithBackoff.returns(Promise.resolve({
      choices: [{
        message: { content: markdownWrappedResponse },
        finish_reason: 'stop'
      }],
      usage: { prompt_tokens: 150, completion_tokens: 200, total_tokens: 350 }
    }));

    const useCase = new DiagnoseImageUseCase(
      mockAIService,
      mockCacheService,
      mockImageProcessor,
      mockValidator,
      mockLogger,
      mockPromptService,
      mockRetryService
    );

    const request = MockDataFactory.createDiagnoseImageRequest();

    // Act
    const result = await useCase.execute(request);

    // Assert
    assert(result.success);
    assert(result.data);
    TestAssertions.assertValidHairDiagnosis(result.data);
  });

  await t.step('should handle missing required fields in AI response', async () => {
    // Arrange
    const mockAIService = MockServices.createMockAIService();
    const mockCacheService = MockServices.createMockCacheService();
    const mockImageProcessor = MockServices.createMockImageProcessor();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();
    const mockPromptService = MockServices.createMockPromptService();
    const mockRetryService = MockServices.createMockRetryService();

    // Mock validator to reject incomplete response
    mockValidator.validateAIResponse.returns({
      valid: false,
      errors: ['Missing required field: averageLevel']
    });

    const incompleteResponse = {
      overallTone: 'warm',
      // Missing averageLevel and other required fields
    };

    mockRetryService.retryWithBackoff.returns(Promise.resolve({
      choices: [{
        message: { content: JSON.stringify(incompleteResponse) },
        finish_reason: 'stop'
      }],
      usage: { prompt_tokens: 150, completion_tokens: 100, total_tokens: 250 }
    }));

    const useCase = new DiagnoseImageUseCase(
      mockAIService,
      mockCacheService,
      mockImageProcessor,
      mockValidator,
      mockLogger,
      mockPromptService,
      mockRetryService
    );

    const request = MockDataFactory.createDiagnoseImageRequest();

    // Act
    const result = await useCase.execute(request);

    // Assert
    assert(!result.success);
    assert(result.error?.includes('AI response missing fields'));
    assert(result.error?.includes('averageLevel'));
  });

  await t.step('should map compatibility fields correctly', async () => {
    // Arrange
    const mockAIService = MockServices.createMockAIService();
    const mockCacheService = MockServices.createMockCacheService();
    const mockImageProcessor = MockServices.createMockImageProcessor();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();
    const mockPromptService = MockServices.createMockPromptService();
    const mockRetryService = MockServices.createMockRetryService();

    // Response with old field names that should be mapped
    const responseWithOldFields = {
      averageDepthLevel: 6, // Should map to averageLevel
      overallTone: 'warm',
      overallUndertone: 'golden', // Should map to overallReflect
      hairThickness: 'medium',
      hairDensity: 'medium',
      overallCondition: 'good',
      zoneAnalysis: {
        roots: {
          depth: 5, // Should map to level
          tone: 'natural',
          undertone: 'neutral', // Should map to reflect
          confidence: 0.9
        },
        mids: {
          level: 6,
          tone: 'warm',
          reflect: 'golden',
          confidence: 0.85
        },
        ends: {
          level: 7,
          tone: 'warm',
          reflect: 'golden',
          confidence: 0.8
        }
      },
      detectedChemicalProcess: 'previous coloring',
      estimatedLastProcessDate: '2-3 months ago',
      detectedHomeRemedies: [],
      detectedRisks: [],
      recommendations: [],
      overallConfidence: 0.85,
      serviceComplexity: 'medium',
      estimatedTime: 120
    };

    mockRetryService.retryWithBackoff.returns(Promise.resolve({
      choices: [{
        message: { content: JSON.stringify(responseWithOldFields) },
        finish_reason: 'stop'
      }],
      usage: { prompt_tokens: 150, completion_tokens: 200, total_tokens: 350 }
    }));

    const useCase = new DiagnoseImageUseCase(
      mockAIService,
      mockCacheService,
      mockImageProcessor,
      mockValidator,
      mockLogger,
      mockPromptService,
      mockRetryService
    );

    const request = MockDataFactory.createDiagnoseImageRequest();

    // Act
    const result = await useCase.execute(request);

    // Assert
    assert(result.success);
    assert(result.data);
    
    // Verify field mapping occurred
    assertEquals(result.data.averageLevel, 6);
    assertEquals(result.data.overallReflect, 'golden');
    assertEquals(result.data.zoneAnalysis.roots.level, 5);
    assertEquals(result.data.zoneAnalysis.roots.reflect, 'neutral');
    
    // Verify old fields were removed
    assert(!('averageDepthLevel' in result.data));
    assert(!('overallUndertone' in result.data));
    assert(!('depth' in result.data.zoneAnalysis.roots));
    assert(!('undertone' in result.data.zoneAnalysis.roots));
  });
});

Deno.test('DiagnoseImageUseCase - Model Selection and Optimization', async (t) => {
  await t.step('should select optimal model based on complexity', async () => {
    // Arrange
    const mockAIService = MockServices.createMockAIService();
    const mockCacheService = MockServices.createMockCacheService();
    const mockImageProcessor = MockServices.createMockImageProcessor();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();
    const mockPromptService = MockServices.createMockPromptService();
    const mockRetryService = MockServices.createMockRetryService();

    // Mock complexity determination and model selection
    mockAIService.determineComplexity.returns('high');
    mockAIService.selectOptimalModel.returns('gpt-4o');

    const diagnosis = MockDataFactory.createHairDiagnosis();
    mockRetryService.retryWithBackoff.returns(Promise.resolve({
      choices: [{ message: { content: JSON.stringify(diagnosis) }, finish_reason: 'stop' }],
      usage: { prompt_tokens: 200, completion_tokens: 300, total_tokens: 500 }
    }));

    const useCase = new DiagnoseImageUseCase(
      mockAIService,
      mockCacheService,
      mockImageProcessor,
      mockValidator,
      mockLogger,
      mockPromptService,
      mockRetryService
    );

    const request = MockDataFactory.createDiagnoseImageRequest({
      diagnosis: { complexCase: true }
    });

    // Act
    const result = await useCase.execute(request);

    // Assert
    assert(result.success);
    
    // Verify complexity analysis and model selection
    assert(mockAIService.determineComplexity.calledWith(request.diagnosis));
    assert(mockAIService.selectOptimalModel.calledWith('high', true)); // true = needs vision
  });

  await t.step('should optimize prompt for token efficiency', async () => {
    // Arrange
    const mockAIService = MockServices.createMockAIService();
    const mockCacheService = MockServices.createMockCacheService();
    const mockImageProcessor = MockServices.createMockImageProcessor();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();
    const mockPromptService = MockServices.createMockPromptService();
    const mockRetryService = MockServices.createMockRetryService();

    mockPromptService.getDiagnosisPrompt.returns('Long detailed prompt for hair diagnosis...');
    mockPromptService.optimizePrompt.returns('Optimized prompt');

    const diagnosis = MockDataFactory.createHairDiagnosis();
    mockRetryService.retryWithBackoff.returns(Promise.resolve({
      choices: [{ message: { content: JSON.stringify(diagnosis) }, finish_reason: 'stop' }],
      usage: { prompt_tokens: 150, completion_tokens: 200, total_tokens: 350 }
    }));

    const useCase = new DiagnoseImageUseCase(
      mockAIService,
      mockCacheService,
      mockImageProcessor,
      mockValidator,
      mockLogger,
      mockPromptService,
      mockRetryService
    );

    const request = MockDataFactory.createDiagnoseImageRequest();

    // Act
    const result = await useCase.execute(request);

    // Assert
    assert(result.success);
    
    // Verify prompt optimization
    assert(mockPromptService.getDiagnosisPrompt.calledWith('full', 'es'));
    assert(mockPromptService.optimizePrompt.calledWith('Long detailed prompt for hair diagnosis...', 1500));
  });
});

Deno.test('DiagnoseImageUseCase - Metrics and Performance', async (t) => {
  await t.step('should track success metrics correctly', async () => {
    // Arrange
    const mockAIService = MockServices.createMockAIService();
    const mockCacheService = MockServices.createMockCacheService();
    const mockImageProcessor = MockServices.createMockImageProcessor();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();
    const mockPromptService = MockServices.createMockPromptService();
    const mockRetryService = MockServices.createMockRetryService();

    mockAIService.selectOptimalModel.returns('gpt-4o');
    mockAIService.calculateCost.returns(0.0042);

    const diagnosis = MockDataFactory.createHairDiagnosis();
    mockRetryService.retryWithBackoff.returns(Promise.resolve({
      choices: [{ message: { content: JSON.stringify(diagnosis) }, finish_reason: 'stop' }],
      usage: { prompt_tokens: 150, completion_tokens: 200, total_tokens: 350 }
    }));

    const useCase = new DiagnoseImageUseCase(
      mockAIService,
      mockCacheService,
      mockImageProcessor,
      mockValidator,
      mockLogger,
      mockPromptService,
      mockRetryService
    );

    const request = MockDataFactory.createDiagnoseImageRequest();

    // Act
    const result = await useCase.execute(request);

    // Assert
    assert(result.success);
    assert(mockLogger.trackMetrics.called);
    
    // Verify success metrics
    const metricsCall = mockLogger.trackMetrics.calls.find(call => 
      call.args[0].success === true
    );
    assert(metricsCall);
    assertEquals(metricsCall.args[0].model, 'gpt-4o');
    assertEquals(metricsCall.args[0].tokens, 350);
    assertEquals(metricsCall.args[0].cost, 0.0042);
    assert(typeof metricsCall.args[0].latency === 'number');
    assert(metricsCall.args[0].latency >= 0);
  });

  await t.step('should track error metrics correctly', async () => {
    // Arrange
    const mockAIService = MockServices.createMockAIService();
    const mockCacheService = MockServices.createMockCacheService();
    const mockImageProcessor = MockServices.createMockImageProcessor();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();
    const mockPromptService = MockServices.createMockPromptService();
    const mockRetryService = MockServices.createMockRetryService();

    // Mock validation error
    mockValidator.validateRequest.returns({
      valid: false,
      errors: ['Test validation error']
    });

    const useCase = new DiagnoseImageUseCase(
      mockAIService,
      mockCacheService,
      mockImageProcessor,
      mockValidator,
      mockLogger,
      mockPromptService,
      mockRetryService
    );

    const request = MockDataFactory.createDiagnoseImageRequest();

    // Act
    const result = await useCase.execute(request);

    // Assert
    assert(!result.success);
    assert(mockLogger.trackMetrics.called);
    
    // Verify error metrics
    const errorMetricsCall = mockLogger.trackMetrics.calls.find(call => 
      call.args[0].success === false
    );
    assert(errorMetricsCall);
    assertEquals(errorMetricsCall.args[0].model, 'unknown');
    assertEquals(errorMetricsCall.args[0].tokens, 0);
    assertEquals(errorMetricsCall.args[0].cost, 0);
    assertEquals(errorMetricsCall.args[0].errorType, 'Error');
    assert(typeof errorMetricsCall.args[0].latency === 'number');
  });
});

Deno.test('DiagnoseImageUseCase - Zone Analysis Validation', async (t) => {
  await t.step('should validate complete zone analysis', async () => {
    // Arrange
    const mockAIService = MockServices.createMockAIService();
    const mockCacheService = MockServices.createMockCacheService();
    const mockImageProcessor = MockServices.createMockImageProcessor();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();
    const mockPromptService = MockServices.createMockPromptService();
    const mockRetryService = MockServices.createMockRetryService();

    const incompleteZoneAnalysis = {
      ...MockDataFactory.createHairDiagnosis(),
      zoneAnalysis: {
        roots: { level: 5, tone: 'natural', reflect: 'neutral', confidence: 0.9 },
        mids: { level: 6, tone: 'warm', reflect: 'golden', confidence: 0.85 },
        // Missing 'ends' zone
      }
    };

    mockRetryService.retryWithBackoff.returns(Promise.resolve({
      choices: [{
        message: { content: JSON.stringify(incompleteZoneAnalysis) },
        finish_reason: 'stop'
      }],
      usage: { prompt_tokens: 150, completion_tokens: 200, total_tokens: 350 }
    }));

    const useCase = new DiagnoseImageUseCase(
      mockAIService,
      mockCacheService,
      mockImageProcessor,
      mockValidator,
      mockLogger,
      mockPromptService,
      mockRetryService
    );

    const request = MockDataFactory.createDiagnoseImageRequest();

    // Act
    const result = await useCase.execute(request);

    // Assert
    assert(!result.success);
    assert(result.error?.includes('Zone analysis missing for: ends'));
  });

  await t.step('should warn about missing zone fields but still process', async () => {
    // Arrange
    const mockAIService = MockServices.createMockAIService();
    const mockCacheService = MockServices.createMockCacheService();
    const mockImageProcessor = MockServices.createMockImageProcessor();
    const mockValidator = MockServices.createMockValidator();
    const mockLogger = MockServices.createMockLogger();
    const mockPromptService = MockServices.createMockPromptService();
    const mockRetryService = MockServices.createMockRetryService();

    const partialZoneData = {
      ...MockDataFactory.createHairDiagnosis(),
      zoneAnalysis: {
        roots: { level: 5, tone: 'natural', reflect: 'neutral' }, // Missing confidence
        mids: { level: 6, tone: 'warm', reflect: 'golden', confidence: 0.85 },
        ends: { level: 7, tone: 'warm', reflect: 'golden', confidence: 0.8 }
      }
    };

    mockRetryService.retryWithBackoff.returns(Promise.resolve({
      choices: [{
        message: { content: JSON.stringify(partialZoneData) },
        finish_reason: 'stop'
      }],
      usage: { prompt_tokens: 150, completion_tokens: 200, total_tokens: 350 }
    }));

    const useCase = new DiagnoseImageUseCase(
      mockAIService,
      mockCacheService,
      mockImageProcessor,
      mockValidator,
      mockLogger,
      mockPromptService,
      mockRetryService
    );

    const request = MockDataFactory.createDiagnoseImageRequest();

    // Act
    const result = await useCase.execute(request);

    // Assert
    assert(result.success); // Should still succeed
    assert(mockLogger.warn.called); // But should log warning
    
    // Verify warning was logged for missing field
    const warnCall = mockLogger.warn.calls.find(call => 
      call.args[0].includes('Missing confidence in roots analysis')
    );
    assert(warnCall);
  });
});