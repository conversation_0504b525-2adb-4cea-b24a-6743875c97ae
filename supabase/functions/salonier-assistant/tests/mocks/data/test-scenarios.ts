/**
 * Test Scenarios and Mock Data
 * 
 * Comprehensive test data covering various hair diagnosis and 
 * coloration scenarios for thorough testing coverage.
 */

import { MockDataFactory } from '../../test-utils.ts';

/**
 * Comprehensive test scenarios covering different hair conditions,
 * damage levels, and complexity cases.
 */
export const TestScenarios = {
  
  // Basic healthy hair scenarios
  healthyVirginHair: {
    diagnosis: MockDataFactory.createHairDiagnosis({
      averageLevel: 4,
      overallTone: 'natural brown',
      overallReflect: 'neutral',
      overallCondition: 'excellent',
      detectedRisks: [],
      detectedChemicalProcess: 'none detected',
      estimatedLastProcessDate: 'never',
      overallConfidence: 0.95,
      serviceComplexity: 'low',
      estimatedTime: 90,
      zoneAnalysis: {
        roots: { level: 4, tone: 'natural', reflect: 'neutral', confidence: 0.95 },
        mids: { level: 4, tone: 'natural', reflect: 'neutral', confidence: 0.95 },
        ends: { level: 4, tone: 'natural', reflect: 'neutral', confidence: 0.95 }
      }
    }),
    desiredLook: MockDataFactory.createDesiredLookAnalysis({
      targetLevel: 6,
      targetTone: 'warm',
      targetReflect: 'golden',
      viability: {
        achievable: true,
        sessionCount: 1,
        riskLevel: 'low',
        concerns: []
      }
    })
  },

  // Previously colored hair needing refresh
  coloredHairRefresh: {
    diagnosis: MockDataFactory.createHairDiagnosis({
      averageLevel: 6,
      overallTone: 'warm golden',
      overallReflect: 'golden',
      overallCondition: 'good',
      detectedRisks: ['slight fading at ends'],
      detectedChemicalProcess: 'previous permanent color',
      estimatedLastProcessDate: '8-10 weeks ago',
      overallConfidence: 0.88,
      serviceComplexity: 'medium',
      estimatedTime: 105,
      zoneAnalysis: {
        roots: { level: 5, tone: 'natural', reflect: 'neutral', confidence: 0.9 },
        mids: { level: 6, tone: 'warm', reflect: 'golden', confidence: 0.85 },
        ends: { level: 7, tone: 'warm', reflect: 'golden', confidence: 0.8 }
      }
    }),
    desiredLook: MockDataFactory.createDesiredLookAnalysis({
      targetLevel: 6,
      targetTone: 'warm',
      targetReflect: 'golden',
      viability: {
        achievable: true,
        sessionCount: 1,
        riskLevel: 'low',
        concerns: ['May need toner to even out ends']
      }
    })
  },

  // Damaged hair requiring careful handling
  damagedHair: {
    diagnosis: MockDataFactory.createHairDiagnosis({
      averageLevel: 8,
      overallTone: 'brassy yellow',
      overallReflect: 'yellow-orange',
      overallCondition: 'damaged',
      detectedRisks: [
        'high porosity throughout',
        'significant breakage potential',
        'compromised cuticle integrity'
      ],
      detectedChemicalProcess: 'multiple bleaching sessions',
      estimatedLastProcessDate: '3-4 weeks ago',
      overallConfidence: 0.82,
      serviceComplexity: 'high',
      estimatedTime: 180,
      unwantedTone: 'brassy yellow undertones',
      zoneAnalysis: {
        roots: { level: 6, tone: 'natural', reflect: 'neutral', confidence: 0.9 },
        mids: { level: 8, tone: 'yellow', reflect: 'brassy', confidence: 0.85 },
        ends: { level: 9, tone: 'yellow', reflect: 'brassy', confidence: 0.7 }
      }
    }),
    desiredLook: MockDataFactory.createDesiredLookAnalysis({
      targetLevel: 8,
      targetTone: 'cool',
      targetReflect: 'ash',
      viability: {
        achievable: true,
        sessionCount: 1,
        riskLevel: 'high',
        concerns: [
          'High breakage risk',
          'May require bond builder',
          'Processing time must be monitored closely'
        ]
      }
    })
  },

  // Gray hair coverage scenario
  grayHairCoverage: {
    diagnosis: MockDataFactory.createHairDiagnosis({
      averageLevel: 5,
      overallTone: 'natural brown with gray',
      overallReflect: 'neutral',
      overallCondition: 'good',
      detectedRisks: ['gray resistance to color'],
      detectedChemicalProcess: 'previous semi-permanent color',
      estimatedLastProcessDate: '12+ weeks ago',
      overallConfidence: 0.85,
      serviceComplexity: 'medium',
      estimatedTime: 135,
      grayType: 'coarse resistant',
      grayPattern: 'concentrated at temples and crown',
      zoneAnalysis: {
        roots: { 
          level: 5, 
          tone: 'natural with gray', 
          reflect: 'neutral', 
          confidence: 0.9,
          grayPercentage: 60
        },
        mids: { 
          level: 5, 
          tone: 'faded brown', 
          reflect: 'neutral', 
          confidence: 0.85,
          grayPercentage: 35
        },
        ends: { 
          level: 6, 
          tone: 'faded brown', 
          reflect: 'neutral', 
          confidence: 0.8,
          grayPercentage: 20
        }
      }
    }),
    desiredLook: MockDataFactory.createDesiredLookAnalysis({
      targetLevel: 5,
      targetTone: 'neutral',
      targetReflect: 'neutral',
      viability: {
        achievable: true,
        sessionCount: 1,
        riskLevel: 'medium',
        concerns: ['May need extended processing time for gray coverage']
      }
    })
  },

  // Complex color correction case
  colorCorrection: {
    diagnosis: MockDataFactory.createHairDiagnosis({
      averageLevel: 7,
      overallTone: 'muddy green-brown',
      overallReflect: 'ashy-green',
      overallCondition: 'fair',
      detectedRisks: [
        'conflicting undertones',
        'uneven porosity',
        'possible metallic buildup',
        'color build-up from multiple processes'
      ],
      detectedChemicalProcess: 'multiple conflicting color treatments',
      estimatedLastProcessDate: '2-3 weeks ago',
      detectedHomeRemedies: ['box dye application', 'attempted color stripper'],
      overallConfidence: 0.75,
      serviceComplexity: 'high',
      estimatedTime: 240,
      unwantedTone: 'green-ashy muddy tones',
      zoneAnalysis: {
        roots: { level: 5, tone: 'natural', reflect: 'neutral', confidence: 0.85 },
        mids: { level: 7, tone: 'muddy brown', reflect: 'green-ash', confidence: 0.7 },
        ends: { level: 8, tone: 'muddy green', reflect: 'ashy-green', confidence: 0.65 }
      }
    }),
    desiredLook: MockDataFactory.createDesiredLookAnalysis({
      targetLevel: 6,
      targetTone: 'warm',
      targetReflect: 'golden',
      viability: {
        achievable: true,
        sessionCount: 2,
        riskLevel: 'high',
        concerns: [
          'Requires color removal first',
          'High risk of further damage',
          'May need multiple sessions',
          'Strand test absolutely required'
        ]
      }
    })
  },

  // Lightening virgin hair
  lighteningVirginHair: {
    diagnosis: MockDataFactory.createHairDiagnosis({
      averageLevel: 3,
      overallTone: 'natural dark brown',
      overallReflect: 'neutral',
      overallCondition: 'excellent',
      detectedRisks: [],
      detectedChemicalProcess: 'none detected',
      estimatedLastProcessDate: 'never',
      overallConfidence: 0.92,
      serviceComplexity: 'medium',
      estimatedTime: 150,
      elasticity: 'excellent',
      resistance: 'normal',
      cuticleState: 'tight and healthy',
      zoneAnalysis: {
        roots: { level: 3, tone: 'natural dark', reflect: 'neutral', confidence: 0.95 },
        mids: { level: 3, tone: 'natural dark', reflect: 'neutral', confidence: 0.92 },
        ends: { level: 3, tone: 'natural dark', reflect: 'neutral', confidence: 0.9 }
      }
    }),
    desiredLook: MockDataFactory.createDesiredLookAnalysis({
      targetLevel: 7,
      targetTone: 'cool',
      targetReflect: 'ash',
      viability: {
        achievable: true,
        sessionCount: 1,
        riskLevel: 'medium',
        concerns: [
          'Requires significant lightening (4 levels)',
          'Orange stage will need neutralization',
          'Processing time will be extended'
        ]
      }
    })
  },

  // Edge case: Very low confidence diagnosis
  lowConfidenceDiagnosis: {
    diagnosis: MockDataFactory.createHairDiagnosis({
      averageLevel: 6,
      overallTone: 'unclear mixed tones',
      overallReflect: 'varied',
      overallCondition: 'unclear',
      detectedRisks: ['unclear hair history', 'possible chemical conflicts'],
      detectedChemicalProcess: 'multiple unknown treatments',
      estimatedLastProcessDate: 'unknown timeframe',
      overallConfidence: 0.55, // Very low confidence
      serviceComplexity: 'high',
      estimatedTime: 180,
      zoneAnalysis: {
        roots: { level: 5, tone: 'unclear', reflect: 'mixed', confidence: 0.6 },
        mids: { level: 6, tone: 'varied', reflect: 'mixed', confidence: 0.5 },
        ends: { level: 7, tone: 'unclear', reflect: 'varied', confidence: 0.45 }
      }
    }),
    desiredLook: MockDataFactory.createDesiredLookAnalysis({
      targetLevel: 6,
      targetTone: 'neutral',
      targetReflect: 'neutral',
      viability: {
        achievable: false,
        sessionCount: 0,
        riskLevel: 'critical',
        concerns: [
          'Insufficient diagnostic confidence',
          'Unknown chemical history',
          'High risk of unpredictable results'
        ]
      }
    })
  },

  // Perfect salon scenario
  idealClient: {
    diagnosis: MockDataFactory.createHairDiagnosis({
      averageLevel: 5,
      overallTone: 'natural medium brown',
      overallReflect: 'neutral warm',
      overallCondition: 'excellent',
      detectedRisks: [],
      detectedChemicalProcess: 'professional color 16 weeks ago',
      estimatedLastProcessDate: '16 weeks ago',
      overallConfidence: 0.96,
      serviceComplexity: 'low',
      estimatedTime: 75,
      elasticity: 'excellent',
      resistance: 'normal',
      cuticleState: 'healthy and intact',
      zoneAnalysis: {
        roots: { level: 4, tone: 'natural', reflect: 'neutral', confidence: 0.98 },
        mids: { level: 5, tone: 'slightly faded', reflect: 'neutral warm', confidence: 0.95 },
        ends: { level: 5, tone: 'slightly faded', reflect: 'neutral warm', confidence: 0.94 }
      }
    }),
    desiredLook: MockDataFactory.createDesiredLookAnalysis({
      targetLevel: 6,
      targetTone: 'warm',
      targetReflect: 'golden honey',
      viability: {
        achievable: true,
        sessionCount: 1,
        riskLevel: 'low',
        concerns: []
      }
    })
  }
};

/**
 * Formula test scenarios for different complexity levels
 */
export const FormulaScenarios = {
  
  simpleColorRefresh: MockDataFactory.createColorFormula({
    technique: 'root touch-up with gloss',
    processingTime: 25,
    applicationMethod: 'roots first, then pull through',
    formula: {
      base: '6 Dark Blonde',
      additives: ['Color Seal Complex'],
      mixingRatio: '1:1',
      developer: { volume: 20, amount: '60ml' }
    },
    stepByStep: [
      'Mix color and developer 1:1 ratio',
      'Apply to roots first, process 20 minutes',
      'Pull through to ends for final 5 minutes',
      'Rinse thoroughly and condition'
    ],
    warnings: ['Monitor timing carefully on ends'],
    expectedResult: 'Even color refresh with natural-looking roots',
    maintenanceInstructions: ['Color-safe shampoo', 'Weekly deep conditioning']
  }),

  complexColorCorrection: MockDataFactory.createColorFormula({
    technique: 'color removal and toning',
    processingTime: 90,
    applicationMethod: 'sectional processing with multiple steps',
    formula: {
      base: 'Color Remover + 7.1 Ash Blonde',
      additives: ['Bond Builder', 'Anti-Brass Concentrate'],
      mixingRatio: 'As directed per step',
      developer: { volume: 20, amount: '100ml' }
    },
    stepByStep: [
      'Apply color remover to mid-lengths and ends',
      'Process for 20 minutes, check every 5 minutes',
      'Rinse and assess color removal',
      'Mix 7.1 + developer + additives',
      'Apply new color formula globally',
      'Process for 35 minutes',
      'Rinse and apply toner if needed'
    ],
    warnings: [
      'Perform strand test before full application',
      'Monitor hair integrity throughout process',
      'Stop process if excessive breakage occurs'
    ],
    expectedResult: 'Corrected to ash blonde, unwanted tones neutralized',
    maintenanceInstructions: [
      'Purple shampoo twice weekly',
      'Protein treatment monthly',
      'Trim every 6 weeks'
    ]
  }),

  highLevelLightening: MockDataFactory.createColorFormula({
    technique: 'global lightening with toner',
    processingTime: 55,
    applicationMethod: '1/4 inch sections, back to front application',
    formula: {
      base: 'Lightening Powder + T18 Lightest Ash Blonde',
      additives: ['Bond Multiplier', 'Anti-Yellow Booster'],
      mixingRatio: 'Lightener 1:2, Color 1:1.5',
      developer: { volume: 30, amount: '120ml' }
    },
    stepByStep: [
      'Mix lightening powder with 30vol developer 1:2',
      'Add bond multiplier as directed',
      'Apply in 1/4 inch sections starting at back',
      'Process for 30-40 minutes checking every 10 minutes',
      'Rinse when desired lightness achieved',
      'Mix toner with 20vol developer',
      'Apply toner for 15-20 minutes',
      'Rinse and deep condition'
    ],
    warnings: [
      'Do not exceed 45 minutes lightening time',
      'Perform elasticity test during processing',
      'Use heat protection if applying heat'
    ],
    expectedResult: 'Level 9-10 with ash tones, no yellow undertones',
    maintenanceInstructions: [
      'Purple shampoo weekly',
      'Deep conditioning treatments weekly',
      'Avoid heat styling when possible'
    ]
  })
};

/**
 * Chat conversation scenarios for testing assistant responses
 */
export const ChatScenarios = {
  
  technicalQuestion: {
    message: 'What volume developer should I use to lift natural level 4 hair to level 7?',
    expectedIntent: 'technical_advice',
    expectedActions: ['Use 30 volume developer', 'Monitor processing time', 'Consider strand test'],
    context: { currentService: { type: 'color_service', targetLevel: 7 } }
  },

  colorCorrectionAdvice: {
    message: 'My client has orange undertones after lightening. How do I neutralize them?',
    expectedIntent: 'color_correction_advice',
    expectedActions: ['Apply ash-based toner', 'Use blue/purple pigments', 'Check processing time'],
    context: { currentService: { type: 'color_correction', currentLevel: 6 } }
  },

  productRecommendation: {
    message: 'What product would work best for covering 50% gray on coarse hair?',
    expectedIntent: 'product_recommendation',
    expectedActions: ['Use permanent color', 'Consider pre-softening', 'Extended processing time'],
    context: { currentService: { type: 'gray_coverage', grayPercentage: 50 } }
  },

  safetyQuestion: {
    message: 'Is it safe to bleach over hair that was colored with box dye 2 weeks ago?',
    expectedIntent: 'safety_concern',
    expectedActions: ['Perform strand test', 'Consider color removal first', 'Consult senior colorist'],
    context: { currentService: { type: 'lightening', previousTreatment: 'box_dye' } }
  },

  generalInquiry: {
    message: 'How long should I wait between color services for damaged hair?',
    expectedIntent: 'general_advice',
    expectedActions: ['Allow 6-8 weeks between services', 'Focus on conditioning treatments', 'Assess hair condition'],
    context: { currentService: { type: 'consultation', hairCondition: 'damaged' } }
  }
};

/**
 * Error scenarios for comprehensive error handling testing
 */
export const ErrorScenarios = {
  
  invalidImageData: {
    request: {
      imageBase64: 'invalid-base64-data',
      salonId: 'salon-123'
    },
    expectedError: 'Invalid image format'
  },

  missingRequiredFields: {
    request: {
      imageUrl: 'test.jpg'
      // Missing salonId
    },
    expectedError: 'Validation failed'
  },

  networkTimeout: {
    request: {
      imageUrl: 'https://slow-server.example.com/image.jpg',
      salonId: 'salon-123'
    },
    expectedError: 'Request timeout'
  },

  aiServiceUnavailable: {
    request: {
      imageUrl: 'test.jpg',
      salonId: 'salon-123'
    },
    expectedError: 'AI service temporarily unavailable'
  },

  contentFilterRejection: {
    request: {
      imageUrl: 'inappropriate-image.jpg',
      salonId: 'salon-123'
    },
    expectedError: 'Image was blocked by content filters'
  }
};

/**
 * Performance test scenarios with varying complexity
 */
export const PerformanceScenarios = {
  
  lightworkload: {
    concurrency: 5,
    duration: 3000,
    expectedLatency: 1000,
    expectedSuccessRate: 0.98
  },

  moderateLoad: {
    concurrency: 15,
    duration: 5000,
    expectedLatency: 2000,
    expectedSuccessRate: 0.95
  },

  heavyLoad: {
    concurrency: 30,
    duration: 10000,
    expectedLatency: 3000,
    expectedSuccessRate: 0.90
  },

  stressTest: {
    concurrency: 50,
    duration: 15000,
    expectedLatency: 5000,
    expectedSuccessRate: 0.80
  }
};

export default {
  TestScenarios,
  FormulaScenarios,
  ChatScenarios,
  ErrorScenarios,
  PerformanceScenarios
};