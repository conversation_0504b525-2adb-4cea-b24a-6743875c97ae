/**
 * Request Mapper
 * 
 * Utility for mapping HTTP requests to internal DTOs and vice versa.
 * Handles data transformation, validation, and normalization between
 * external HTTP interfaces and internal service contracts.
 */

import { ILogger } from '../../services/interfaces.ts';
import { HairDiagnosis, DesiredLookAnalysis, ProductInventory, ClientPreferences } from '../../types/use-case.types.ts';

export interface MappingResult<T> {
  success: boolean;
  data?: T;
  errors?: string[];
  warnings?: string[];
}

export interface AuthContext {
  salonId: string;
  userId: string;
  userRole: 'stylist' | 'client' | 'manager';
  permissions?: string[];
}

export class RequestMapper {
  constructor(private logger: ILogger) {}

  /**
   * Extract and validate authentication context from HTTP headers
   */
  extractAuthContext(request: Request): MappingResult<AuthContext> {
    try {
      const authorization = request.headers.get('Authorization');
      const salonId = request.headers.get('X-Salon-ID');
      const userId = request.headers.get('X-User-ID');
      const userRole = request.headers.get('X-User-Role') as 'stylist' | 'client' | 'manager';
      const permissions = request.headers.get('X-User-Permissions')?.split(',').map(p => p.trim());

      const errors: string[] = [];

      if (!authorization) {
        errors.push('Authorization header is required');
      } else if (!authorization.startsWith('Bearer ')) {
        errors.push('Authorization header must be a Bearer token');
      }

      if (!salonId) {
        errors.push('X-Salon-ID header is required');
      } else if (!/^[a-f0-9-]{36}$/.test(salonId)) {
        errors.push('Invalid salon ID format');
      }

      if (!userId) {
        errors.push('X-User-ID header is required');
      } else if (!/^[a-f0-9-]{36}$/.test(userId)) {
        errors.push('Invalid user ID format');
      }

      if (!userRole || !['stylist', 'client', 'manager'].includes(userRole)) {
        errors.push('Valid X-User-Role header is required (stylist, client, or manager)');
      }

      if (errors.length > 0) {
        return { success: false, errors };
      }

      return {
        success: true,
        data: {
          salonId: salonId!,
          userId: userId!,
          userRole: userRole!,
          permissions
        }
      };

    } catch (error) {
      this.logger.error('Failed to extract auth context', error);
      return {
        success: false,
        errors: ['Failed to parse authentication headers']
      };
    }
  }

  /**
   * Map HTTP request body to hair diagnosis structure
   */
  mapToDiagnosisRequest(body: any): MappingResult<{
    imageUrl?: string;
    imageBase64?: string;
    metadata?: any;
  }> {
    try {
      const errors: string[] = [];
      const warnings: string[] = [];

      // Validate image data
      if (!body.imageUrl && !body.imageBase64) {
        errors.push('Either imageUrl or imageBase64 is required');
      }

      // Validate image URL format
      if (body.imageUrl) {
        try {
          new URL(body.imageUrl);
          
          // Check for supported image formats
          const supportedFormats = ['.jpg', '.jpeg', '.png', '.webp'];
          const hasValidFormat = supportedFormats.some(format => 
            body.imageUrl.toLowerCase().includes(format)
          );
          
          if (!hasValidFormat) {
            warnings.push('Image URL format may not be supported');
          }
        } catch {
          errors.push('Invalid image URL format');
        }
      }

      // Validate base64 image
      if (body.imageBase64) {
        const base64Regex = /^data:image\/(jpeg|jpg|png|webp);base64,/;
        if (!base64Regex.test(body.imageBase64)) {
          errors.push('Invalid base64 image format');
        } else {
          // Check approximate size
          const sizeEstimate = this.estimateBase64Size(body.imageBase64);
          const maxSizeMB = 10;
          
          if (sizeEstimate > maxSizeMB * 1024 * 1024) {
            errors.push(`Image too large (estimated ${Math.round(sizeEstimate / 1024 / 1024)}MB, max ${maxSizeMB}MB)`);
          }
        }
      }

      // Validate and normalize metadata
      const metadata = this.normalizeMetadata(body.metadata);

      if (errors.length > 0) {
        return { success: false, errors, warnings };
      }

      return {
        success: true,
        data: {
          imageUrl: body.imageUrl,
          imageBase64: body.imageBase64,
          metadata
        },
        warnings: warnings.length > 0 ? warnings : undefined
      };

    } catch (error) {
      this.logger.error('Failed to map diagnosis request', error);
      return {
        success: false,
        errors: ['Failed to process diagnosis request data']
      };
    }
  }

  /**
   * Map and validate hair diagnosis data
   */
  mapHairDiagnosis(data: any): MappingResult<HairDiagnosis> {
    try {
      const errors: string[] = [];
      const warnings: string[] = [];

      // Validate required fields
      if (!data.averageLevel || data.averageLevel < 1 || data.averageLevel > 10) {
        errors.push('averageLevel must be between 1 and 10');
      }

      if (!data.overallTone) {
        errors.push('overallTone is required');
      }

      if (!data.overallCondition) {
        errors.push('overallCondition is required');
      }

      // Validate confidence score
      if (data.overallConfidence !== undefined) {
        if (data.overallConfidence < 0 || data.overallConfidence > 1) {
          errors.push('overallConfidence must be between 0 and 1');
        }
        if (data.overallConfidence < 0.6) {
          warnings.push('Low confidence score detected');
        }
      }

      // Validate zone analysis
      if (data.zoneAnalysis) {
        const zones = ['roots', 'mids', 'ends'];
        zones.forEach(zone => {
          if (data.zoneAnalysis[zone]) {
            if (data.zoneAnalysis[zone].level < 1 || data.zoneAnalysis[zone].level > 10) {
              errors.push(`${zone} level must be between 1 and 10`);
            }
            if (data.zoneAnalysis[zone].confidence < 0 || data.zoneAnalysis[zone].confidence > 1) {
              errors.push(`${zone} confidence must be between 0 and 1`);
            }
          }
        });
      }

      // Validate arrays
      if (data.detectedRisks && !Array.isArray(data.detectedRisks)) {
        errors.push('detectedRisks must be an array');
      }

      if (data.recommendations && !Array.isArray(data.recommendations)) {
        errors.push('recommendations must be an array');
      }

      if (errors.length > 0) {
        return { success: false, errors, warnings };
      }

      // Normalize and clean data
      const normalizedDiagnosis: HairDiagnosis = {
        averageLevel: Math.round(data.averageLevel * 10) / 10, // Round to 1 decimal
        overallTone: this.normalizeTone(data.overallTone),
        overallReflect: this.normalizeReflect(data.overallReflect),
        hairThickness: this.normalizeThickness(data.hairThickness),
        hairDensity: this.normalizeDensity(data.hairDensity),
        overallCondition: this.normalizeCondition(data.overallCondition),
        zoneAnalysis: this.normalizeZoneAnalysis(data.zoneAnalysis),
        detectedChemicalProcess: this.normalizeChemicalProcess(data.detectedChemicalProcess),
        estimatedLastProcessDate: data.estimatedLastProcessDate || 'unknown',
        detectedHomeRemedies: Array.isArray(data.detectedHomeRemedies) ? data.detectedHomeRemedies : [],
        detectedRisks: Array.isArray(data.detectedRisks) ? data.detectedRisks : [],
        recommendations: Array.isArray(data.recommendations) ? data.recommendations : [],
        overallConfidence: data.overallConfidence || 0.8,
        serviceComplexity: this.determineComplexity(data),
        estimatedTime: data.estimatedTime || this.estimateServiceTime(data),
        elasticity: data.elasticity,
        resistance: data.resistance,
        cuticleState: data.cuticleState,
        grayType: data.grayType,
        grayPattern: data.grayPattern,
        unwantedTone: data.unwantedTone
      };

      return {
        success: true,
        data: normalizedDiagnosis,
        warnings: warnings.length > 0 ? warnings : undefined
      };

    } catch (error) {
      this.logger.error('Failed to map hair diagnosis', error);
      return {
        success: false,
        errors: ['Failed to process hair diagnosis data']
      };
    }
  }

  /**
   * Map and validate product inventory data
   */
  mapProductInventory(products: any[]): MappingResult<ProductInventory[]> {
    try {
      if (!Array.isArray(products)) {
        return {
          success: false,
          errors: ['Product inventory must be an array']
        };
      }

      const errors: string[] = [];
      const warnings: string[] = [];
      const mappedProducts: ProductInventory[] = [];

      products.forEach((product, index) => {
        if (!product.brand || typeof product.brand !== 'string') {
          errors.push(`Product[${index}]: brand is required and must be a string`);
          return;
        }

        if (!product.line || typeof product.line !== 'string') {
          errors.push(`Product[${index}]: line is required and must be a string`);
          return;
        }

        if (!product.type || typeof product.type !== 'string') {
          errors.push(`Product[${index}]: type is required and must be a string`);
          return;
        }

        if (!product.shade || typeof product.shade !== 'string') {
          errors.push(`Product[${index}]: shade is required and must be a string`);
          return;
        }

        // Validate optional numeric fields
        if (product.level !== undefined && (typeof product.level !== 'number' || product.level < 1 || product.level > 10)) {
          errors.push(`Product[${index}]: level must be a number between 1 and 10`);
        }

        if (product.volume !== undefined && (typeof product.volume !== 'number' || product.volume <= 0)) {
          errors.push(`Product[${index}]: volume must be a positive number`);
        }

        // Normalize product data
        const normalizedProduct: ProductInventory = {
          brand: this.normalizeBrand(product.brand),
          line: product.line.trim(),
          type: this.normalizeProductType(product.type),
          shade: product.shade.trim(),
          level: product.level,
          reflect: product.reflect ? this.normalizeReflect(product.reflect) : undefined,
          volume: product.volume
        };

        mappedProducts.push(normalizedProduct);
      });

      if (errors.length > 0) {
        return { success: false, errors, warnings };
      }

      return {
        success: true,
        data: mappedProducts,
        warnings: warnings.length > 0 ? warnings : undefined
      };

    } catch (error) {
      this.logger.error('Failed to map product inventory', error);
      return {
        success: false,
        errors: ['Failed to process product inventory data']
      };
    }
  }

  /**
   * Map and validate client preferences
   */
  mapClientPreferences(preferences: any): MappingResult<ClientPreferences> {
    try {
      const errors: string[] = [];
      const warnings: string[] = [];

      // Validate allergies
      if (preferences.allergies && !Array.isArray(preferences.allergies)) {
        errors.push('allergies must be an array');
      }

      // Validate preferred brands
      if (preferences.preferredBrands && !Array.isArray(preferences.preferredBrands)) {
        errors.push('preferredBrands must be an array');
      }

      // Validate previous reactions
      if (preferences.previousReactions && !Array.isArray(preferences.previousReactions)) {
        errors.push('previousReactions must be an array');
      }

      // Validate maintenance level
      const validMaintenanceLevels = ['low', 'medium', 'high'];
      if (preferences.maintenanceLevel && !validMaintenanceLevels.includes(preferences.maintenanceLevel)) {
        errors.push('maintenanceLevel must be one of: ' + validMaintenanceLevels.join(', '));
      }

      if (errors.length > 0) {
        return { success: false, errors, warnings };
      }

      const mappedPreferences: ClientPreferences = {
        allergies: preferences.allergies ? 
          preferences.allergies.map((allergy: string) => allergy.trim().toLowerCase()) : 
          undefined,
        preferredBrands: preferences.preferredBrands ? 
          preferences.preferredBrands.map((brand: string) => this.normalizeBrand(brand)) : 
          undefined,
        previousReactions: preferences.previousReactions ? 
          preferences.previousReactions.map((reaction: string) => reaction.trim()) : 
          undefined,
        maintenanceLevel: preferences.maintenanceLevel
      };

      return {
        success: true,
        data: mappedPreferences,
        warnings: warnings.length > 0 ? warnings : undefined
      };

    } catch (error) {
      this.logger.error('Failed to map client preferences', error);
      return {
        success: false,
        errors: ['Failed to process client preferences data']
      };
    }
  }

  // Private helper methods

  private estimateBase64Size(base64: string): number {
    const base64Data = base64.split(',')[1] || base64;
    return (base64Data.length * 3) / 4;
  }

  private normalizeMetadata(metadata: any): any {
    if (!metadata || typeof metadata !== 'object') {
      return {};
    }

    return {
      clientAge: metadata.clientAge && typeof metadata.clientAge === 'number' ? 
        Math.max(0, Math.min(120, Math.round(metadata.clientAge))) : undefined,
      previousServices: Array.isArray(metadata.previousServices) ? 
        metadata.previousServices.map((service: string) => service.trim()) : undefined,
      skinTone: metadata.skinTone ? metadata.skinTone.toLowerCase().trim() : undefined,
      naturalHairColor: metadata.naturalHairColor ? metadata.naturalHairColor.trim() : undefined,
      allergies: Array.isArray(metadata.allergies) ? 
        metadata.allergies.map((allergy: string) => allergy.trim().toLowerCase()) : undefined
    };
  }

  private normalizeTone(tone: string): string {
    if (!tone) return 'neutral';
    
    const toneMap: Record<string, string> = {
      'warm': 'warm',
      'cool': 'cool', 
      'neutral': 'neutral',
      'ash': 'cool',
      'golden': 'warm',
      'red': 'warm',
      'orange': 'warm',
      'yellow': 'warm',
      'violet': 'cool',
      'blue': 'cool'
    };

    const normalizedTone = tone.toLowerCase().trim();
    return toneMap[normalizedTone] || normalizedTone;
  }

  private normalizeReflect(reflect: string): string {
    if (!reflect) return 'neutral';
    return reflect.toLowerCase().trim();
  }

  private normalizeThickness(thickness: string): string {
    if (!thickness) return 'medium';
    
    const thicknessMap: Record<string, string> = {
      'fine': 'fine',
      'thin': 'fine',
      'medium': 'medium',
      'thick': 'thick',
      'coarse': 'thick'
    };

    const normalizedThickness = thickness.toLowerCase().trim();
    return thicknessMap[normalizedThickness] || normalizedThickness;
  }

  private normalizeDensity(density: string): string {
    if (!density) return 'medium';
    
    const densityMap: Record<string, string> = {
      'low': 'low',
      'sparse': 'low',
      'medium': 'medium',
      'high': 'high',
      'dense': 'high'
    };

    const normalizedDensity = density.toLowerCase().trim();
    return densityMap[normalizedDensity] || normalizedDensity;
  }

  private normalizeCondition(condition: string): string {
    if (!condition) return 'fair';
    
    const conditionMap: Record<string, string> = {
      'excellent': 'excellent',
      'very good': 'good',
      'good': 'good',
      'fair': 'fair',
      'poor': 'poor',
      'damaged': 'damaged',
      'severely damaged': 'damaged'
    };

    const normalizedCondition = condition.toLowerCase().trim();
    return conditionMap[normalizedCondition] || normalizedCondition;
  }

  private normalizeZoneAnalysis(zoneAnalysis: any): HairDiagnosis['zoneAnalysis'] {
    if (!zoneAnalysis || typeof zoneAnalysis !== 'object') {
      return {
        roots: { level: 5, tone: 'neutral', reflect: 'neutral', confidence: 0.5 },
        mids: { level: 5, tone: 'neutral', reflect: 'neutral', confidence: 0.5 },
        ends: { level: 5, tone: 'neutral', reflect: 'neutral', confidence: 0.5 }
      };
    }

    const normalizeZone = (zone: any) => ({
      level: zone?.level || 5,
      tone: this.normalizeTone(zone?.tone),
      reflect: this.normalizeReflect(zone?.reflect),
      confidence: Math.max(0, Math.min(1, zone?.confidence || 0.5)),
      condition: zone?.condition ? this.normalizeCondition(zone.condition) : undefined,
      porosity: zone?.porosity,
      grayPercentage: zone?.grayPercentage !== undefined ? 
        Math.max(0, Math.min(100, zone.grayPercentage)) : undefined
    });

    return {
      roots: normalizeZone(zoneAnalysis.roots),
      mids: normalizeZone(zoneAnalysis.mids),
      ends: normalizeZone(zoneAnalysis.ends)
    };
  }

  private normalizeChemicalProcess(process: string): string {
    if (!process) return 'none';
    
    const processMap: Record<string, string> = {
      'none': 'none',
      'natural': 'none',
      'color': 'color',
      'dye': 'color',
      'bleach': 'bleach',
      'highlight': 'bleach',
      'relaxer': 'relaxer',
      'perm': 'perm',
      'straightening': 'relaxer',
      'henna': 'henna',
      'keratin': 'keratin'
    };

    const normalizedProcess = process.toLowerCase().trim();
    return processMap[normalizedProcess] || normalizedProcess;
  }

  private normalizeBrand(brand: string): string {
    return brand.trim().replace(/\s+/g, ' ');
  }

  private normalizeProductType(type: string): string {
    const typeMap: Record<string, string> = {
      'color': 'color',
      'tint': 'color',
      'dye': 'color',
      'bleach': 'bleach',
      'lightener': 'bleach',
      'developer': 'developer',
      'peroxide': 'developer',
      'toner': 'toner',
      'gloss': 'gloss'
    };

    const normalizedType = type.toLowerCase().trim();
    return typeMap[normalizedType] || normalizedType;
  }

  private determineComplexity(data: any): string {
    let complexityScore = 0;

    // Level variation increases complexity
    if (data.zoneAnalysis) {
      const levels = [data.zoneAnalysis.roots?.level, data.zoneAnalysis.mids?.level, data.zoneAnalysis.ends?.level];
      const levelVariation = Math.max(...levels) - Math.min(...levels);
      if (levelVariation > 2) complexityScore += 2;
      else if (levelVariation > 1) complexityScore += 1;
    }

    // Poor condition increases complexity
    if (data.overallCondition === 'poor' || data.overallCondition === 'damaged') {
      complexityScore += 2;
    } else if (data.overallCondition === 'fair') {
      complexityScore += 1;
    }

    // Multiple risks increase complexity
    if (data.detectedRisks?.length > 2) {
      complexityScore += 2;
    } else if (data.detectedRisks?.length > 0) {
      complexityScore += 1;
    }

    // Chemical history increases complexity
    if (data.detectedChemicalProcess && data.detectedChemicalProcess !== 'none') {
      complexityScore += 1;
    }

    // Low confidence increases complexity
    if (data.overallConfidence < 0.7) {
      complexityScore += 1;
    }

    if (complexityScore >= 4) return 'high';
    if (complexityScore >= 2) return 'medium';
    return 'low';
  }

  private estimateServiceTime(data: any): number {
    const baseTime = 90; // Base service time in minutes
    let timeAdjustment = 0;

    const complexity = this.determineComplexity(data);
    switch (complexity) {
      case 'high':
        timeAdjustment += 60;
        break;
      case 'medium':
        timeAdjustment += 30;
        break;
      default:
        break;
    }

    // Add time for poor condition
    if (data.overallCondition === 'poor' || data.overallCondition === 'damaged') {
      timeAdjustment += 30;
    }

    // Add time for multiple risks
    if (data.detectedRisks?.length > 0) {
      timeAdjustment += data.detectedRisks.length * 10;
    }

    return baseTime + timeAdjustment;
  }
}