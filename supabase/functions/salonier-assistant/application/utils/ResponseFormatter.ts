/**
 * Response Formatter
 * 
 * Utility for formatting consistent HTTP responses across all endpoints.
 * Handles success/error formatting, metadata inclusion, CORS headers,
 * and role-based data filtering for professional salon applications.
 */

import { ILogger } from '../../services/interfaces.ts';

export interface ResponseMetadata {
  requestId: string;
  timestamp?: string;
  version?: string;
  processingTime?: number;
  cached?: boolean;
  serverInfo?: {
    region?: string;
    instance?: string;
  };
}

export interface ErrorDetails {
  code: string;
  message: string;
  details?: any;
  field?: string;
  validationErrors?: string[];
  supportReference?: string;
}

export interface SuccessResponseFormat<T> {
  success: true;
  data: T;
  metadata: ResponseMetadata;
}

export interface ErrorResponseFormat {
  success: false;
  error: ErrorDetails;
  metadata: ResponseMetadata;
}

export type FormattedResponse<T> = SuccessResponseFormat<T> | ErrorResponseFormat;

export class ResponseFormatter {
  private readonly version = '1.0.0';
  private readonly defaultHeaders = {
    'Content-Type': 'application/json',
    'X-API-Version': this.version
  };

  constructor(private logger: ILogger) {}

  /**
   * Format successful response with proper structure and headers
   */
  success<T>(
    data: T,
    metadata: Partial<ResponseMetadata> = {},
    options: {
      status?: number;
      headers?: Record<string, string>;
      userRole?: string;
      filterSensitive?: boolean;
    } = {}
  ): Response {
    try {
      const responseMetadata: ResponseMetadata = {
        requestId: metadata.requestId || this.generateRequestId(),
        timestamp: metadata.timestamp || new Date().toISOString(),
        version: metadata.version || this.version,
        processingTime: metadata.processingTime,
        cached: metadata.cached || false,
        serverInfo: {
          region: 'us-east-1', // This would typically come from environment
          instance: 'edge-function'
        }
      };

      // Filter sensitive data based on user role
      const filteredData = options.filterSensitive && options.userRole ? 
        this.filterSensitiveData(data, options.userRole) : data;

      const response: SuccessResponseFormat<T> = {
        success: true,
        data: filteredData,
        metadata: responseMetadata
      };

      const headers = this.buildHeaders(options.headers);
      const status = options.status || 200;

      this.logger.debug('Formatted success response', {
        requestId: responseMetadata.requestId,
        status,
        dataType: typeof data,
        processingTime: responseMetadata.processingTime
      });

      return new Response(JSON.stringify(response, null, 2), {
        status,
        headers
      });

    } catch (error) {
      this.logger.error('Failed to format success response', error);
      
      // Fallback to basic success response
      return new Response(JSON.stringify({
        success: true,
        data: data,
        metadata: {
          requestId: this.generateRequestId(),
          timestamp: new Date().toISOString(),
          version: this.version,
          error: 'Response formatting partially failed'
        }
      }), {
        status: 200,
        headers: this.buildHeaders()
      });
    }
  }

  /**
   * Format error response with appropriate HTTP status and details
   */
  error(
    error: ErrorDetails,
    metadata: Partial<ResponseMetadata> = {},
    options: {
      status?: number;
      headers?: Record<string, string>;
      includeStackTrace?: boolean;
    } = {}
  ): Response {
    try {
      const responseMetadata: ResponseMetadata = {
        requestId: metadata.requestId || this.generateRequestId(),
        timestamp: metadata.timestamp || new Date().toISOString(),
        version: metadata.version || this.version,
        processingTime: metadata.processingTime,
        cached: false,
        serverInfo: {
          region: 'us-east-1',
          instance: 'edge-function'
        }
      };

      // Determine appropriate HTTP status based on error code
      const status = options.status || this.mapErrorCodeToStatus(error.code);

      // Add support reference for tracking
      const enhancedError: ErrorDetails = {
        ...error,
        supportReference: error.supportReference || `ERR-${responseMetadata.requestId.slice(-8).toUpperCase()}`
      };

      // Remove sensitive information from error details in production
      if (!options.includeStackTrace && enhancedError.details?.stack) {
        delete enhancedError.details.stack;
      }

      const response: ErrorResponseFormat = {
        success: false,
        error: enhancedError,
        metadata: responseMetadata
      };

      const headers = this.buildHeaders(options.headers);

      this.logger.error('Formatted error response', {
        requestId: responseMetadata.requestId,
        status,
        errorCode: error.code,
        errorMessage: error.message,
        supportReference: enhancedError.supportReference
      });

      return new Response(JSON.stringify(response, null, 2), {
        status,
        headers
      });

    } catch (formattingError) {
      this.logger.error('Failed to format error response', formattingError);
      
      // Fallback to basic error response
      return new Response(JSON.stringify({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'An internal error occurred',
          supportReference: 'ERR-FALLBACK'
        },
        metadata: {
          requestId: this.generateRequestId(),
          timestamp: new Date().toISOString(),
          version: this.version
        }
      }), {
        status: 500,
        headers: this.buildHeaders()
      });
    }
  }

  /**
   * Format validation error response
   */
  validationError(
    message: string,
    validationErrors: string[],
    field?: string,
    metadata: Partial<ResponseMetadata> = {}
  ): Response {
    return this.error({
      code: 'VALIDATION_ERROR',
      message,
      field,
      validationErrors,
      details: {
        errorCount: validationErrors.length,
        field: field
      }
    }, metadata, { status: 400 });
  }

  /**
   * Format authentication error response
   */
  authError(
    message: string = 'Authentication required',
    metadata: Partial<ResponseMetadata> = {}
  ): Response {
    return this.error({
      code: 'AUTH_ERROR',
      message,
      details: {
        authenticationRequired: true,
        supportedMethods: ['Bearer Token']
      }
    }, metadata, { 
      status: 401,
      headers: {
        'WWW-Authenticate': 'Bearer realm="Salonier API"'
      }
    });
  }

  /**
   * Format authorization error response
   */
  authorizationError(
    message: string = 'Insufficient permissions',
    requiredPermissions?: string[],
    metadata: Partial<ResponseMetadata> = {}
  ): Response {
    return this.error({
      code: 'AUTHORIZATION_ERROR',
      message,
      details: {
        requiredPermissions,
        authorizationRequired: true
      }
    }, metadata, { status: 403 });
  }

  /**
   * Format rate limiting error response
   */
  rateLimitError(
    message: string = 'Rate limit exceeded',
    retryAfter?: number,
    metadata: Partial<ResponseMetadata> = {}
  ): Response {
    const headers: Record<string, string> = {};
    if (retryAfter) {
      headers['Retry-After'] = retryAfter.toString();
      headers['X-RateLimit-Reset'] = new Date(Date.now() + retryAfter * 1000).toISOString();
    }

    return this.error({
      code: 'RATE_LIMIT_ERROR',
      message,
      details: {
        retryAfter,
        resetTime: retryAfter ? new Date(Date.now() + retryAfter * 1000).toISOString() : undefined
      }
    }, metadata, { 
      status: 429,
      headers
    });
  }

  /**
   * Format method not allowed error response
   */
  methodNotAllowedError(
    allowedMethods: string[],
    metadata: Partial<ResponseMetadata> = {}
  ): Response {
    return this.error({
      code: 'METHOD_NOT_ALLOWED',
      message: `Method not allowed. Supported methods: ${allowedMethods.join(', ')}`,
      details: {
        allowedMethods
      }
    }, metadata, { 
      status: 405,
      headers: {
        'Allow': allowedMethods.join(', ')
      }
    });
  }

  /**
   * Format not found error response
   */
  notFoundError(
    resource: string = 'Resource',
    metadata: Partial<ResponseMetadata> = {}
  ): Response {
    return this.error({
      code: 'NOT_FOUND',
      message: `${resource} not found`,
      details: {
        resource
      }
    }, metadata, { status: 404 });
  }

  /**
   * Format conflict error response
   */
  conflictError(
    message: string,
    conflictDetails?: any,
    metadata: Partial<ResponseMetadata> = {}
  ): Response {
    return this.error({
      code: 'CONFLICT',
      message,
      details: conflictDetails
    }, metadata, { status: 409 });
  }

  /**
   * Format service unavailable error response
   */
  serviceUnavailableError(
    message: string = 'Service temporarily unavailable',
    retryAfter?: number,
    metadata: Partial<ResponseMetadata> = {}
  ): Response {
    const headers: Record<string, string> = {};
    if (retryAfter) {
      headers['Retry-After'] = retryAfter.toString();
    }

    return this.error({
      code: 'SERVICE_UNAVAILABLE',
      message,
      details: {
        retryAfter,
        temporary: true
      }
    }, metadata, { 
      status: 503,
      headers
    });
  }

  /**
   * Format streaming response for long-running operations
   */
  streamingResponse(
    generator: AsyncGenerator<any, void, unknown>,
    metadata: Partial<ResponseMetadata> = {}
  ): Response {
    const stream = new ReadableStream({
      async start(controller) {
        const responseMetadata: ResponseMetadata = {
          requestId: metadata.requestId || this.generateRequestId(),
          timestamp: metadata.timestamp || new Date().toISOString(),
          version: metadata.version || this.version,
          cached: false
        };

        // Send initial metadata
        controller.enqueue(new TextEncoder().encode(
          `data: ${JSON.stringify({ type: 'metadata', data: responseMetadata })}\n\n`
        ));

        try {
          for await (const chunk of generator) {
            controller.enqueue(new TextEncoder().encode(
              `data: ${JSON.stringify({ type: 'data', data: chunk })}\n\n`
            ));
          }

          controller.enqueue(new TextEncoder().encode(
            `data: ${JSON.stringify({ type: 'complete' })}\n\n`
          ));
        } catch (error) {
          controller.enqueue(new TextEncoder().encode(
            `data: ${JSON.stringify({ 
              type: 'error', 
              error: {
                code: 'STREAMING_ERROR',
                message: error instanceof Error ? error.message : 'Streaming failed'
              }
            })}\n\n`
          ));
        }

        controller.close();
      }
    });

    return new Response(stream, {
      headers: {
        ...this.buildHeaders(),
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive'
      }
    });
  }

  // Private helper methods

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private buildHeaders(additionalHeaders?: Record<string, string>): Record<string, string> {
    return {
      ...this.defaultHeaders,
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Salon-ID, X-User-ID, X-User-Role, X-User-Permissions',
      'Access-Control-Max-Age': '86400',
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      ...additionalHeaders
    };
  }

  private mapErrorCodeToStatus(errorCode: string): number {
    const statusMap: Record<string, number> = {
      'VALIDATION_ERROR': 400,
      'BAD_REQUEST': 400,
      'AUTH_ERROR': 401,
      'AUTHENTICATION_REQUIRED': 401,
      'AUTHORIZATION_ERROR': 403,
      'FORBIDDEN': 403,
      'NOT_FOUND': 404,
      'METHOD_NOT_ALLOWED': 405,
      'CONFLICT': 409,
      'RATE_LIMIT_ERROR': 429,
      'INTERNAL_ERROR': 500,
      'SERVICE_ERROR': 500,
      'AI_SERVICE_ERROR': 502,
      'SERVICE_UNAVAILABLE': 503,
      'TIMEOUT_ERROR': 504
    };

    return statusMap[errorCode] || 500;
  }

  private filterSensitiveData<T>(data: T, userRole: string): T {
    if (!data || typeof data !== 'object') {
      return data;
    }

    // Create a deep copy to avoid mutating original data
    const filtered = JSON.parse(JSON.stringify(data));

    // Define sensitive fields by user role
    const sensitiveFields: Record<string, string[]> = {
      client: [
        'cost', 'profit', 'margin', 'internalNotes', 'systemData',
        'debugInfo', 'adminNotes', 'businessMetrics', 'serverInfo',
        'processingTime', 'tokens', 'aiCosts', 'cacheStats'
      ],
      stylist: [
        'profit', 'margin', 'businessMetrics', 'adminNotes',
        'systemData', 'debugInfo', 'serverInfo'
      ],
      manager: [
        'systemData', 'debugInfo', 'serverInfo'
      ]
    };

    const fieldsToFilter = sensitiveFields[userRole] || [];

    // Recursively filter sensitive fields
    const filterObject = (obj: any): any => {
      if (Array.isArray(obj)) {
        return obj.map(filterObject);
      }

      if (obj && typeof obj === 'object') {
        const result: any = {};
        
        Object.keys(obj).forEach(key => {
          const keyLower = key.toLowerCase();
          
          // Check if field should be filtered
          const shouldFilter = fieldsToFilter.some(field => 
            keyLower.includes(field.toLowerCase()) ||
            key === field
          );

          if (shouldFilter) {
            // Replace with placeholder or omit entirely
            result[key] = '[FILTERED]';
          } else {
            result[key] = filterObject(obj[key]);
          }
        });

        return result;
      }

      return obj;
    };

    return filterObject(filtered);
  }

  /**
   * Create CORS preflight response
   */
  corsResponse(): Response {
    return new Response(null, {
      status: 204,
      headers: this.buildHeaders()
    });
  }

  /**
   * Create health check response
   */
  healthResponse(
    status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy',
    checks?: Record<string, boolean>,
    metadata: Partial<ResponseMetadata> = {}
  ): Response {
    const healthData = {
      status,
      timestamp: new Date().toISOString(),
      version: this.version,
      checks: checks || {},
      uptime: process.uptime?.() || 'N/A'
    };

    const httpStatus = status === 'healthy' ? 200 : 
                      status === 'degraded' ? 200 : 503;

    return this.success(healthData, metadata, { 
      status: httpStatus,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });
  }
}