/**
 * Validation Helper
 * 
 * Utility for common validation patterns and request sanitization.
 * Provides reusable validation functions for HTTP requests, data formats,
 * and professional salon-specific business rules.
 */

import { ILogger } from '../../services/interfaces.ts';

export interface ValidationRule {
  field: string;
  required?: boolean;
  type?: 'string' | 'number' | 'boolean' | 'array' | 'object' | 'email' | 'url' | 'uuid';
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: RegExp;
  enum?: string[];
  custom?: (value: any) => boolean | string;
}

export interface ValidationResult {
  valid: boolean;
  errors: Array<{
    field: string;
    message: string;
    code: string;
  }>;
  warnings: Array<{
    field: string;
    message: string;
  }>;
  sanitizedData?: any;
}

export interface SanitizerOptions {
  trimStrings?: boolean;
  removeEmptyStrings?: boolean;
  normalizeEmails?: boolean;
  sanitizeHtml?: boolean;
  maxStringLength?: number;
}

export class ValidationHelper {
  private readonly UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  private readonly EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  private readonly URL_REGEX = /^https?:\/\/.+$/;
  private readonly PHONE_REGEX = /^\+?[\d\s\-\(\)\.]{10,}$/;
  private readonly BASE64_IMAGE_REGEX = /^data:image\/(jpeg|jpg|png|gif|webp);base64,/;

  // Professional salon-specific validation patterns
  private readonly HAIR_LEVEL_RANGE = { min: 1, max: 10 };
  private readonly PROCESSING_TIME_RANGE = { min: 5, max: 180 }; // minutes
  private readonly CONFIDENCE_RANGE = { min: 0, max: 1 };

  constructor(private logger: ILogger) {}

  /**
   * Validate data against a set of rules
   */
  validate(data: any, rules: ValidationRule[]): ValidationResult {
    const errors: ValidationResult['errors'] = [];
    const warnings: ValidationResult['warnings'] = [];
    const sanitizedData: any = Array.isArray(data) ? [] : {};

    try {
      for (const rule of rules) {
        const value = this.getNestedValue(data, rule.field);
        const sanitizedValue = this.sanitizeValue(value, rule);
        
        // Set sanitized value
        this.setNestedValue(sanitizedData, rule.field, sanitizedValue);

        // Check required fields
        if (rule.required && (value === undefined || value === null || value === '')) {
          errors.push({
            field: rule.field,
            message: `${rule.field} is required`,
            code: 'REQUIRED_FIELD_MISSING'
          });
          continue;
        }

        // Skip validation for optional fields that are not provided
        if (!rule.required && (value === undefined || value === null)) {
          continue;
        }

        // Type validation
        if (rule.type && !this.validateType(value, rule.type)) {
          errors.push({
            field: rule.field,
            message: `${rule.field} must be of type ${rule.type}`,
            code: 'INVALID_TYPE'
          });
          continue;
        }

        // String-specific validations
        if (typeof value === 'string') {
          if (rule.minLength !== undefined && value.length < rule.minLength) {
            errors.push({
              field: rule.field,
              message: `${rule.field} must be at least ${rule.minLength} characters`,
              code: 'MIN_LENGTH_VIOLATION'
            });
          }

          if (rule.maxLength !== undefined && value.length > rule.maxLength) {
            errors.push({
              field: rule.field,
              message: `${rule.field} cannot exceed ${rule.maxLength} characters`,
              code: 'MAX_LENGTH_VIOLATION'
            });
          }

          if (rule.pattern && !rule.pattern.test(value)) {
            errors.push({
              field: rule.field,
              message: `${rule.field} format is invalid`,
              code: 'PATTERN_MISMATCH'
            });
          }
        }

        // Number-specific validations
        if (typeof value === 'number') {
          if (rule.min !== undefined && value < rule.min) {
            errors.push({
              field: rule.field,
              message: `${rule.field} must be at least ${rule.min}`,
              code: 'MIN_VALUE_VIOLATION'
            });
          }

          if (rule.max !== undefined && value > rule.max) {
            errors.push({
              field: rule.field,
              message: `${rule.field} cannot exceed ${rule.max}`,
              code: 'MAX_VALUE_VIOLATION'
            });
          }
        }

        // Enum validation
        if (rule.enum && !rule.enum.includes(value)) {
          errors.push({
            field: rule.field,
            message: `${rule.field} must be one of: ${rule.enum.join(', ')}`,
            code: 'ENUM_VIOLATION'
          });
        }

        // Custom validation
        if (rule.custom) {
          const customResult = rule.custom(value);
          if (customResult !== true) {
            errors.push({
              field: rule.field,
              message: typeof customResult === 'string' ? customResult : `${rule.field} failed custom validation`,
              code: 'CUSTOM_VALIDATION_FAILED'
            });
          }
        }

        // Generate warnings for potentially problematic values
        const warning = this.generateWarning(rule.field, value, rule);
        if (warning) {
          warnings.push(warning);
        }
      }

      return {
        valid: errors.length === 0,
        errors,
        warnings,
        sanitizedData
      };

    } catch (error) {
      this.logger.error('Validation failed with exception', error);
      
      return {
        valid: false,
        errors: [{
          field: 'validation',
          message: 'Validation process failed',
          code: 'VALIDATION_ERROR'
        }],
        warnings: [],
        sanitizedData: data
      };
    }
  }

  /**
   * Validate common HTTP request headers
   */
  validateHeaders(headers: Headers): ValidationResult {
    const rules: ValidationRule[] = [
      {
        field: 'authorization',
        required: true,
        type: 'string',
        pattern: /^Bearer\s+.+$/,
        custom: (value: string) => value.length > 20 || 'Authorization token too short'
      },
      {
        field: 'x-salon-id',
        required: true,
        type: 'uuid'
      },
      {
        field: 'x-user-id',
        required: true,
        type: 'uuid'
      },
      {
        field: 'x-user-role',
        required: true,
        enum: ['stylist', 'client', 'manager']
      },
      {
        field: 'content-type',
        required: false,
        enum: ['application/json', 'multipart/form-data']
      }
    ];

    const headerData: any = {};
    rules.forEach(rule => {
      const headerValue = headers.get(rule.field.replace(/x-/, '').replace(/-/g, '_'));
      if (headerValue) {
        headerData[rule.field] = headerValue;
      }
    });

    return this.validate(headerData, rules);
  }

  /**
   * Validate hair diagnosis data with professional standards
   */
  validateHairDiagnosis(diagnosis: any): ValidationResult {
    const rules: ValidationRule[] = [
      {
        field: 'averageLevel',
        required: true,
        type: 'number',
        min: this.HAIR_LEVEL_RANGE.min,
        max: this.HAIR_LEVEL_RANGE.max
      },
      {
        field: 'overallTone',
        required: true,
        type: 'string',
        enum: ['warm', 'cool', 'neutral', 'ash', 'golden', 'red', 'orange', 'yellow']
      },
      {
        field: 'overallReflect',
        required: true,
        type: 'string',
        minLength: 1,
        maxLength: 50
      },
      {
        field: 'hairThickness',
        required: true,
        enum: ['fine', 'medium', 'thick']
      },
      {
        field: 'hairDensity',
        required: true,
        enum: ['low', 'medium', 'high']
      },
      {
        field: 'overallCondition',
        required: true,
        enum: ['excellent', 'good', 'fair', 'poor', 'damaged']
      },
      {
        field: 'overallConfidence',
        required: true,
        type: 'number',
        min: this.CONFIDENCE_RANGE.min,
        max: this.CONFIDENCE_RANGE.max
      },
      {
        field: 'serviceComplexity',
        required: true,
        enum: ['low', 'medium', 'high']
      },
      {
        field: 'estimatedTime',
        required: true,
        type: 'number',
        min: this.PROCESSING_TIME_RANGE.min,
        max: this.PROCESSING_TIME_RANGE.max
      },
      {
        field: 'detectedRisks',
        required: false,
        type: 'array'
      },
      {
        field: 'recommendations',
        required: false,
        type: 'array'
      }
    ];

    const result = this.validate(diagnosis, rules);
    
    // Additional professional validation
    if (diagnosis.zoneAnalysis) {
      const zoneResult = this.validateZoneAnalysis(diagnosis.zoneAnalysis);
      result.errors.push(...zoneResult.errors);
      result.warnings.push(...zoneResult.warnings);
    }

    return result;
  }

  /**
   * Validate hair formula data with safety standards
   */
  validateHairFormula(formula: any): ValidationResult {
    const rules: ValidationRule[] = [
      {
        field: 'technique',
        required: true,
        type: 'string',
        minLength: 3,
        maxLength: 100
      },
      {
        field: 'processingTime',
        required: true,
        type: 'number',
        min: this.PROCESSING_TIME_RANGE.min,
        max: this.PROCESSING_TIME_RANGE.max
      },
      {
        field: 'applicationMethod',
        required: true,
        type: 'string',
        minLength: 5,
        maxLength: 200
      },
      {
        field: 'formula.base',
        required: true,
        type: 'string',
        minLength: 2
      },
      {
        field: 'formula.developer.volume',
        required: true,
        type: 'number',
        min: 10,
        max: 40,
        custom: (value: number) => 
          value % 10 === 0 || 'Developer volume must be in 10vol increments'
      },
      {
        field: 'formula.developer.amount',
        required: true,
        type: 'string',
        pattern: /^\d+\s*(ml|oz|g)$/i
      },
      {
        field: 'formula.mixingRatio',
        required: false,
        type: 'string',
        pattern: /^\d+:\d+(:\d+)*$/
      },
      {
        field: 'stepByStep',
        required: true,
        type: 'array',
        custom: (value: any[]) => 
          Array.isArray(value) && value.length >= 3 || 'At least 3 application steps required'
      },
      {
        field: 'expectedResult',
        required: true,
        type: 'string',
        minLength: 10,
        maxLength: 200
      }
    ];

    return this.validate(formula, rules);
  }

  /**
   * Validate product inventory data
   */
  validateProductInventory(products: any[]): ValidationResult {
    if (!Array.isArray(products)) {
      return {
        valid: false,
        errors: [{
          field: 'products',
          message: 'Products must be an array',
          code: 'INVALID_TYPE'
        }],
        warnings: []
      };
    }

    const allErrors: ValidationResult['errors'] = [];
    const allWarnings: ValidationResult['warnings'] = [];
    const sanitizedProducts: any[] = [];

    products.forEach((product, index) => {
      const rules: ValidationRule[] = [
        {
          field: 'brand',
          required: true,
          type: 'string',
          minLength: 2,
          maxLength: 50
        },
        {
          field: 'line',
          required: true,
          type: 'string',
          minLength: 1,
          maxLength: 50
        },
        {
          field: 'type',
          required: true,
          type: 'string',
          enum: ['color', 'bleach', 'developer', 'toner', 'gloss', 'treatment']
        },
        {
          field: 'shade',
          required: true,
          type: 'string',
          minLength: 1,
          maxLength: 30
        },
        {
          field: 'level',
          required: false,
          type: 'number',
          min: this.HAIR_LEVEL_RANGE.min,
          max: this.HAIR_LEVEL_RANGE.max
        },
        {
          field: 'volume',
          required: false,
          type: 'number',
          min: 10,
          max: 40
        }
      ];

      const result = this.validate(product, rules);
      
      // Prefix field names with array index
      result.errors.forEach(error => {
        allErrors.push({
          ...error,
          field: `products[${index}].${error.field}`,
          message: error.message.replace(error.field, `products[${index}].${error.field}`)
        });
      });

      result.warnings.forEach(warning => {
        allWarnings.push({
          ...warning,
          field: `products[${index}].${warning.field}`,
          message: warning.message.replace(warning.field, `products[${index}].${warning.field}`)
        });
      });

      if (result.sanitizedData) {
        sanitizedProducts.push(result.sanitizedData);
      }
    });

    return {
      valid: allErrors.length === 0,
      errors: allErrors,
      warnings: allWarnings,
      sanitizedData: sanitizedProducts
    };
  }

  /**
   * Sanitize input data with configurable options
   */
  sanitize(data: any, options: SanitizerOptions = {}): any {
    const defaultOptions: SanitizerOptions = {
      trimStrings: true,
      removeEmptyStrings: true,
      normalizeEmails: true,
      sanitizeHtml: true,
      maxStringLength: 10000
    };

    const opts = { ...defaultOptions, ...options };

    return this.sanitizeRecursive(data, opts);
  }

  /**
   * Validate file upload constraints
   */
  validateFileUpload(file: {
    name: string;
    type: string;
    size: number;
    content?: string; // base64 content
  }): ValidationResult {
    const errors: ValidationResult['errors'] = [];
    const warnings: ValidationResult['warnings'] = [];

    // File type validation
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      errors.push({
        field: 'fileType',
        message: `File type ${file.type} not allowed. Allowed types: ${allowedTypes.join(', ')}`,
        code: 'INVALID_FILE_TYPE'
      });
    }

    // File size validation
    const maxSizeBytes = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSizeBytes) {
      errors.push({
        field: 'fileSize',
        message: `File size ${Math.round(file.size / 1024 / 1024)}MB exceeds limit of ${maxSizeBytes / 1024 / 1024}MB`,
        code: 'FILE_TOO_LARGE'
      });
    }

    // File name validation
    if (file.name.length > 255) {
      errors.push({
        field: 'fileName',
        message: 'File name too long (max 255 characters)',
        code: 'FILENAME_TOO_LONG'
      });
    }

    const dangerousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.js', '.vbs'];
    if (dangerousExtensions.some(ext => file.name.toLowerCase().endsWith(ext))) {
      errors.push({
        field: 'fileName',
        message: 'File type not allowed for security reasons',
        code: 'DANGEROUS_FILE_TYPE'
      });
    }

    // Base64 content validation if present
    if (file.content) {
      if (!this.BASE64_IMAGE_REGEX.test(file.content)) {
        errors.push({
          field: 'fileContent',
          message: 'Invalid base64 image format',
          code: 'INVALID_BASE64_FORMAT'
        });
      }
    }

    // Warnings for large files
    if (file.size > 5 * 1024 * 1024) { // 5MB
      warnings.push({
        field: 'fileSize',
        message: 'Large file size may affect upload performance'
      });
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  // Private helper methods

  private validateType(value: any, type: ValidationRule['type']): boolean {
    switch (type) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'array':
        return Array.isArray(value);
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      case 'email':
        return typeof value === 'string' && this.EMAIL_REGEX.test(value);
      case 'url':
        return typeof value === 'string' && this.URL_REGEX.test(value);
      case 'uuid':
        return typeof value === 'string' && this.UUID_REGEX.test(value);
      default:
        return true;
    }
  }

  private sanitizeValue(value: any, rule: ValidationRule): any {
    if (value === null || value === undefined) {
      return value;
    }

    switch (rule.type) {
      case 'string':
        if (typeof value === 'string') {
          let sanitized = value.trim();
          
          // Remove potentially dangerous HTML
          sanitized = sanitized.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
          sanitized = sanitized.replace(/javascript:/gi, '');
          sanitized = sanitized.replace(/on\w+\s*=/gi, '');
          
          // Limit length
          if (sanitized.length > 10000) {
            sanitized = sanitized.substring(0, 10000);
          }

          return sanitized;
        }
        return String(value).trim();

      case 'email':
        if (typeof value === 'string') {
          return value.toLowerCase().trim();
        }
        return value;

      case 'number':
        if (typeof value === 'string' && !isNaN(Number(value))) {
          return Number(value);
        }
        return value;

      case 'array':
        if (Array.isArray(value)) {
          return value.filter(item => item !== null && item !== undefined);
        }
        return value;

      default:
        return value;
    }
  }

  private generateWarning(field: string, value: any, rule: ValidationRule): ValidationResult['warnings'][0] | null {
    // Hair level warnings
    if (field.toLowerCase().includes('level') && typeof value === 'number') {
      if (value <= 2) {
        return {
          field,
          message: 'Very dark hair level - limited lightening options'
        };
      }
      if (value >= 9) {
        return {
          field,
          message: 'Very light hair level - high damage risk'
        };
      }
    }

    // Processing time warnings
    if (field.toLowerCase().includes('time') && typeof value === 'number') {
      if (value > 60) {
        return {
          field,
          message: 'Extended processing time - monitor closely'
        };
      }
    }

    // Confidence warnings
    if (field.toLowerCase().includes('confidence') && typeof value === 'number') {
      if (value < 0.7) {
        return {
          field,
          message: 'Low confidence score - consider manual verification'
        };
      }
    }

    // String length warnings
    if (typeof value === 'string' && value.length > 500) {
      return {
        field,
        message: 'Very long text field - consider summarizing'
      };
    }

    return null;
  }

  private validateZoneAnalysis(zoneAnalysis: any): ValidationResult {
    const errors: ValidationResult['errors'] = [];
    const warnings: ValidationResult['warnings'] = [];

    const zones = ['roots', 'mids', 'ends'];
    
    zones.forEach(zone => {
      if (zoneAnalysis[zone]) {
        const zoneData = zoneAnalysis[zone];
        
        if (typeof zoneData.level !== 'number' || zoneData.level < 1 || zoneData.level > 10) {
          errors.push({
            field: `zoneAnalysis.${zone}.level`,
            message: `${zone} level must be between 1 and 10`,
            code: 'INVALID_HAIR_LEVEL'
          });
        }

        if (typeof zoneData.confidence !== 'number' || zoneData.confidence < 0 || zoneData.confidence > 1) {
          errors.push({
            field: `zoneAnalysis.${zone}.confidence`,
            message: `${zone} confidence must be between 0 and 1`,
            code: 'INVALID_CONFIDENCE'
          });
        }

        if (zoneData.confidence < 0.6) {
          warnings.push({
            field: `zoneAnalysis.${zone}.confidence`,
            message: `Low confidence for ${zone} analysis`
          });
        }
      }
    });

    // Check for significant level variations
    const levels = zones
      .map(zone => zoneAnalysis[zone]?.level)
      .filter(level => typeof level === 'number');

    if (levels.length >= 2) {
      const levelVariation = Math.max(...levels) - Math.min(...levels);
      if (levelVariation > 3) {
        warnings.push({
          field: 'zoneAnalysis',
          message: 'Significant level variation between zones - consider sectional processing'
        });
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  private sanitizeRecursive(data: any, options: SanitizerOptions): any {
    if (data === null || data === undefined) {
      return data;
    }

    if (typeof data === 'string') {
      let sanitized = options.trimStrings ? data.trim() : data;
      
      if (options.removeEmptyStrings && sanitized === '') {
        return undefined;
      }

      if (options.sanitizeHtml) {
        sanitized = sanitized.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
        sanitized = sanitized.replace(/javascript:/gi, '');
        sanitized = sanitized.replace(/on\w+\s*=/gi, '');
      }

      if (options.maxStringLength && sanitized.length > options.maxStringLength) {
        sanitized = sanitized.substring(0, options.maxStringLength);
      }

      if (options.normalizeEmails && this.EMAIL_REGEX.test(sanitized)) {
        sanitized = sanitized.toLowerCase();
      }

      return sanitized;
    }

    if (Array.isArray(data)) {
      return data
        .map(item => this.sanitizeRecursive(item, options))
        .filter(item => item !== undefined);
    }

    if (typeof data === 'object') {
      const sanitized: any = {};
      
      Object.keys(data).forEach(key => {
        const sanitizedValue = this.sanitizeRecursive(data[key], options);
        if (sanitizedValue !== undefined) {
          sanitized[key] = sanitizedValue;
        }
      });

      return sanitized;
    }

    return data;
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    const target = keys.reduce((current, key) => {
      if (!(key in current)) {
        current[key] = {};
      }
      return current[key];
    }, obj);
    
    target[lastKey] = value;
  }
}