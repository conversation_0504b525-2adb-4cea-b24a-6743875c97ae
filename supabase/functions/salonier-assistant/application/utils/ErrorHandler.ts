/**
 * <PERSON>rro<PERSON>
 * 
 * Centralized error handling utility for the application layer.
 * Provides consistent error response formatting, logging, monitoring,
 * and error recovery strategies for professional salon operations.
 */

import { ResponseFormatter, ErrorDetails } from './ResponseFormatter.ts';
import { ILogger } from '../../services/interfaces.ts';

export interface ErrorContext {
  requestId?: string;
  userId?: string;
  salonId?: string;
  operation?: string;
  userAgent?: string;
  ipAddress?: string;
  timestamp?: string;
}

export interface ErrorRecoveryOptions {
  fallbackData?: any;
  retryable?: boolean;
  retryAfter?: number;
  alternativeEndpoint?: string;
}

export interface ErrorMetrics {
  errorCode: string;
  errorMessage: string;
  context: ErrorContext;
  stackTrace?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'user' | 'system' | 'external' | 'business';
}

export class ErrorHandler {
  private errorCounts: Map<string, number> = new Map();
  private errorHistory: ErrorMetrics[] = [];
  private readonly maxHistorySize = 1000;

  constructor(
    private responseFormatter: ResponseFormatter,
    private logger: ILogger
  ) {
    this.startPeriodicCleanup();
  }

  /**
   * Handle validation errors with detailed field information
   */
  handleValidationError(
    message: string,
    validationErrors?: string[],
    field?: string,
    context?: ErrorContext
  ): Response {
    const errorCode = 'VALIDATION_ERROR';
    
    this.recordError({
      errorCode,
      errorMessage: message,
      context: context || {},
      severity: 'low',
      category: 'user'
    });

    return this.responseFormatter.validationError(
      message,
      validationErrors || [],
      field,
      {
        requestId: context?.requestId,
        timestamp: context?.timestamp
      }
    );
  }

  /**
   * Handle authentication errors
   */
  handleAuthError(
    message: string = 'Authentication required',
    context?: ErrorContext
  ): Response {
    const errorCode = 'AUTH_ERROR';
    
    this.recordError({
      errorCode,
      errorMessage: message,
      context: context || {},
      severity: 'medium',
      category: 'user'
    });

    this.logger.warn('Authentication error', {
      message,
      context,
      userAgent: context?.userAgent,
      ipAddress: context?.ipAddress
    });

    return this.responseFormatter.authError(
      message,
      {
        requestId: context?.requestId,
        timestamp: context?.timestamp
      }
    );
  }

  /**
   * Handle authorization/permission errors
   */
  handleAuthorizationError(
    message: string = 'Insufficient permissions',
    requiredPermissions?: string[],
    context?: ErrorContext
  ): Response {
    const errorCode = 'AUTHORIZATION_ERROR';
    
    this.recordError({
      errorCode,
      errorMessage: message,
      context: context || {},
      severity: 'medium',
      category: 'user'
    });

    this.logger.warn('Authorization error', {
      message,
      requiredPermissions,
      context,
      userId: context?.userId,
      salonId: context?.salonId
    });

    return this.responseFormatter.authorizationError(
      message,
      requiredPermissions,
      {
        requestId: context?.requestId,
        timestamp: context?.timestamp
      }
    );
  }

  /**
   * Handle HTTP method not allowed errors
   */
  handleMethodNotAllowed(
    allowedMethods: string[],
    context?: ErrorContext
  ): Response {
    const errorCode = 'METHOD_NOT_ALLOWED';
    
    this.recordError({
      errorCode,
      errorMessage: `Method not allowed. Allowed: ${allowedMethods.join(', ')}`,
      context: context || {},
      severity: 'low',
      category: 'user'
    });

    return this.responseFormatter.methodNotAllowedError(
      allowedMethods,
      {
        requestId: context?.requestId,
        timestamp: context?.timestamp
      }
    );
  }

  /**
   * Handle service layer errors (business logic, AI, external APIs)
   */
  handleServiceError(
    message: string,
    context?: ErrorContext,
    recovery?: ErrorRecoveryOptions
  ): Response {
    const errorCode = this.categorizeServiceError(message);
    
    this.recordError({
      errorCode,
      errorMessage: message,
      context: context || {},
      severity: this.determineErrorSeverity(errorCode, message),
      category: this.determineErrorCategory(errorCode)
    });

    // Log with appropriate level based on severity
    const severity = this.determineErrorSeverity(errorCode, message);
    if (severity === 'critical' || severity === 'high') {
      this.logger.error('Service error', {
        message,
        errorCode,
        context,
        recovery
      });
    } else {
      this.logger.warn('Service error', {
        message,
        errorCode,
        context
      });
    }

    // Check if error is retryable
    if (recovery?.retryable) {
      return this.responseFormatter.serviceUnavailableError(
        `${message} (retryable)`,
        recovery.retryAfter,
        {
          requestId: context?.requestId,
          timestamp: context?.timestamp
        }
      );
    }

    // Return error with potential fallback data
    const errorDetails: ErrorDetails = {
      code: errorCode,
      message,
      details: {
        category: this.determineErrorCategory(errorCode),
        severity,
        fallbackAvailable: !!recovery?.fallbackData,
        alternativeEndpoint: recovery?.alternativeEndpoint
      }
    };

    return this.responseFormatter.error(
      errorDetails,
      {
        requestId: context?.requestId,
        timestamp: context?.timestamp
      },
      {
        status: this.mapErrorCodeToStatus(errorCode)
      }
    );
  }

  /**
   * Handle internal/unexpected errors with fallback responses
   */
  handleInternalError(
    message: string,
    requestId?: string,
    error?: Error,
    context?: ErrorContext
  ): Response {
    const errorCode = 'INTERNAL_ERROR';
    const severity = 'critical';
    
    this.recordError({
      errorCode,
      errorMessage: message,
      context: {
        ...context,
        requestId
      },
      stackTrace: error?.stack,
      severity,
      category: 'system'
    });

    // Always log internal errors at error level
    this.logger.error('Internal error', {
      message,
      error: error?.message,
      stack: error?.stack,
      requestId,
      context
    });

    // Sanitize error message for production
    const publicMessage = this.sanitizeErrorMessage(message, error);

    const errorDetails: ErrorDetails = {
      code: errorCode,
      message: publicMessage,
      supportReference: `ERR-${requestId?.slice(-8).toUpperCase() || 'UNKNOWN'}`,
      details: {
        timestamp: new Date().toISOString(),
        severity,
        category: 'system'
      }
    };

    return this.responseFormatter.error(
      errorDetails,
      {
        requestId,
        timestamp: new Date().toISOString()
      },
      {
        status: 500,
        includeStackTrace: false // Never include stack traces in production
      }
    );
  }

  /**
   * Handle rate limiting errors
   */
  handleRateLimitError(
    message: string = 'Rate limit exceeded',
    retryAfter: number = 60,
    context?: ErrorContext
  ): Response {
    const errorCode = 'RATE_LIMIT_ERROR';
    
    this.recordError({
      errorCode,
      errorMessage: message,
      context: context || {},
      severity: 'medium',
      category: 'user'
    });

    this.logger.warn('Rate limit exceeded', {
      message,
      retryAfter,
      context,
      userId: context?.userId,
      ipAddress: context?.ipAddress
    });

    return this.responseFormatter.rateLimitError(
      message,
      retryAfter,
      {
        requestId: context?.requestId,
        timestamp: context?.timestamp
      }
    );
  }

  /**
   * Handle external API errors with circuit breaker logic
   */
  handleExternalAPIError(
    service: string,
    error: any,
    context?: ErrorContext,
    fallbackData?: any
  ): Response {
    const errorCode = 'EXTERNAL_API_ERROR';
    const message = `External service error: ${service}`;
    
    this.recordError({
      errorCode,
      errorMessage: message,
      context: {
        ...context,
        service
      },
      severity: 'high',
      category: 'external'
    });

    this.logger.error('External API error', {
      service,
      error: error?.message || error,
      context,
      fallbackAvailable: !!fallbackData
    });

    // Check if we should trigger circuit breaker
    const errorKey = `external_${service}`;
    const errorCount = this.errorCounts.get(errorKey) || 0;
    this.errorCounts.set(errorKey, errorCount + 1);

    if (errorCount > 5) {
      this.logger.error('Circuit breaker triggered', {
        service,
        errorCount
      });
    }

    const errorDetails: ErrorDetails = {
      code: errorCode,
      message: fallbackData ? 
        `${message} (using fallback data)` : 
        `${message} (service unavailable)`,
      details: {
        service,
        fallbackUsed: !!fallbackData,
        circuitBreakerTriggered: errorCount > 5
      }
    };

    const status = fallbackData ? 200 : 502;

    if (fallbackData) {
      // Return success with fallback data and warning
      return this.responseFormatter.success(
        {
          ...fallbackData,
          _warnings: [`External service ${service} unavailable, using cached/fallback data`]
        },
        {
          requestId: context?.requestId,
          timestamp: context?.timestamp
        },
        {
          status: 200,
          headers: {
            'X-Fallback-Used': 'true',
            'X-Service-Status': 'degraded'
          }
        }
      );
    }

    return this.responseFormatter.error(
      errorDetails,
      {
        requestId: context?.requestId,
        timestamp: context?.timestamp
      },
      { status }
    );
  }

  /**
   * Handle business rule violations
   */
  handleBusinessRuleError(
    ruleName: string,
    message: string,
    details?: any,
    context?: ErrorContext
  ): Response {
    const errorCode = 'BUSINESS_RULE_VIOLATION';
    
    this.recordError({
      errorCode,
      errorMessage: `Business rule violation: ${ruleName} - ${message}`,
      context: {
        ...context,
        ruleName
      },
      severity: 'medium',
      category: 'business'
    });

    this.logger.warn('Business rule violation', {
      ruleName,
      message,
      details,
      context
    });

    const errorDetails: ErrorDetails = {
      code: errorCode,
      message,
      details: {
        ruleName,
        ruleDetails: details,
        category: 'business'
      }
    };

    return this.responseFormatter.error(
      errorDetails,
      {
        requestId: context?.requestId,
        timestamp: context?.timestamp
      },
      { status: 422 }
    );
  }

  /**
   * Get error statistics for monitoring
   */
  getErrorStatistics(timeframe: 'hour' | 'day' | 'week' = 'hour'): {
    totalErrors: number;
    errorsByCode: Record<string, number>;
    errorsBySeverity: Record<string, number>;
    errorsByCategory: Record<string, number>;
    topErrors: Array<{
      code: string;
      count: number;
      lastOccurrence: string;
    }>;
  } {
    const cutoffTime = this.getCutoffTime(timeframe);
    const recentErrors = this.errorHistory.filter(error => 
      new Date(error.context.timestamp || 0).getTime() > cutoffTime
    );

    const errorsByCode: Record<string, number> = {};
    const errorsBySeverity: Record<string, number> = {};
    const errorsByCategory: Record<string, number> = {};

    recentErrors.forEach(error => {
      errorsByCode[error.errorCode] = (errorsByCode[error.errorCode] || 0) + 1;
      errorsBySeverity[error.severity] = (errorsBySeverity[error.severity] || 0) + 1;
      errorsByCategory[error.category] = (errorsByCategory[error.category] || 0) + 1;
    });

    const topErrors = Object.entries(errorsByCode)
      .map(([code, count]) => ({
        code,
        count,
        lastOccurrence: recentErrors
          .filter(e => e.errorCode === code)
          .sort((a, b) => new Date(b.context.timestamp || 0).getTime() - new Date(a.context.timestamp || 0).getTime())[0]
          ?.context.timestamp || 'unknown'
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    return {
      totalErrors: recentErrors.length,
      errorsByCode,
      errorsBySeverity,
      errorsByCategory,
      topErrors
    };
  }

  /**
   * Clear error history for a specific user or salon
   */
  clearErrorHistory(filter: { userId?: string; salonId?: string; errorCode?: string }): number {
    const originalLength = this.errorHistory.length;
    
    this.errorHistory = this.errorHistory.filter(error => {
      if (filter.userId && error.context.userId === filter.userId) return false;
      if (filter.salonId && error.context.salonId === filter.salonId) return false;
      if (filter.errorCode && error.errorCode === filter.errorCode) return false;
      return true;
    });

    const clearedCount = originalLength - this.errorHistory.length;
    
    this.logger.info('Error history cleared', {
      filter,
      clearedCount,
      remainingCount: this.errorHistory.length
    });

    return clearedCount;
  }

  // Private helper methods

  private recordError(error: ErrorMetrics): void {
    error.context.timestamp = error.context.timestamp || new Date().toISOString();
    
    this.errorHistory.push(error);

    // Keep history size manageable
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory = this.errorHistory.slice(-this.maxHistorySize * 0.8);
    }

    // Update error counts
    const errorKey = `${error.errorCode}_${error.context.userId || 'unknown'}`;
    this.errorCounts.set(errorKey, (this.errorCounts.get(errorKey) || 0) + 1);
  }

  private categorizeServiceError(message: string): string {
    const messageLower = message.toLowerCase();
    
    if (messageLower.includes('ai') || messageLower.includes('openai') || messageLower.includes('gpt')) {
      return 'AI_SERVICE_ERROR';
    } else if (messageLower.includes('database') || messageLower.includes('supabase')) {
      return 'DATABASE_ERROR';
    } else if (messageLower.includes('cache') || messageLower.includes('redis')) {
      return 'CACHE_ERROR';
    } else if (messageLower.includes('timeout')) {
      return 'TIMEOUT_ERROR';
    } else if (messageLower.includes('network') || messageLower.includes('connection')) {
      return 'NETWORK_ERROR';
    } else {
      return 'SERVICE_ERROR';
    }
  }

  private determineErrorSeverity(errorCode: string, message: string): ErrorMetrics['severity'] {
    const criticalErrors = ['INTERNAL_ERROR', 'DATABASE_ERROR', 'AI_SERVICE_ERROR'];
    const highErrors = ['EXTERNAL_API_ERROR', 'NETWORK_ERROR', 'TIMEOUT_ERROR'];
    const mediumErrors = ['AUTH_ERROR', 'AUTHORIZATION_ERROR', 'BUSINESS_RULE_VIOLATION'];
    
    if (criticalErrors.includes(errorCode)) return 'critical';
    if (highErrors.includes(errorCode)) return 'high';
    if (mediumErrors.includes(errorCode)) return 'medium';
    
    // Check message content for severity indicators
    const messageLower = message.toLowerCase();
    if (messageLower.includes('critical') || messageLower.includes('fatal')) return 'critical';
    if (messageLower.includes('error') || messageLower.includes('failed')) return 'medium';
    
    return 'low';
  }

  private determineErrorCategory(errorCode: string): ErrorMetrics['category'] {
    const userErrors = ['VALIDATION_ERROR', 'AUTH_ERROR', 'AUTHORIZATION_ERROR', 'RATE_LIMIT_ERROR'];
    const systemErrors = ['INTERNAL_ERROR', 'DATABASE_ERROR', 'CACHE_ERROR'];
    const externalErrors = ['EXTERNAL_API_ERROR', 'AI_SERVICE_ERROR', 'NETWORK_ERROR'];
    const businessErrors = ['BUSINESS_RULE_VIOLATION'];
    
    if (userErrors.includes(errorCode)) return 'user';
    if (systemErrors.includes(errorCode)) return 'system';
    if (externalErrors.includes(errorCode)) return 'external';
    if (businessErrors.includes(errorCode)) return 'business';
    
    return 'system';
  }

  private mapErrorCodeToStatus(errorCode: string): number {
    const statusMap: Record<string, number> = {
      'VALIDATION_ERROR': 400,
      'AUTH_ERROR': 401,
      'AUTHORIZATION_ERROR': 403,
      'METHOD_NOT_ALLOWED': 405,
      'BUSINESS_RULE_VIOLATION': 422,
      'RATE_LIMIT_ERROR': 429,
      'INTERNAL_ERROR': 500,
      'SERVICE_ERROR': 500,
      'DATABASE_ERROR': 500,
      'CACHE_ERROR': 500,
      'AI_SERVICE_ERROR': 502,
      'EXTERNAL_API_ERROR': 502,
      'NETWORK_ERROR': 502,
      'TIMEOUT_ERROR': 504
    };

    return statusMap[errorCode] || 500;
  }

  private sanitizeErrorMessage(message: string, error?: Error): string {
    // Remove sensitive information from error messages
    let sanitized = message;
    
    // Remove stack traces
    sanitized = sanitized.replace(/at\s+.*?\n?/g, '');
    
    // Remove file paths
    sanitized = sanitized.replace(/\/[^\s]*\.(ts|js|tsx|jsx)/g, '[file]');
    
    // Remove potential API keys or tokens
    sanitized = sanitized.replace(/[a-z0-9]{32,}/gi, '[token]');
    
    // Remove SQL or database connection strings
    sanitized = sanitized.replace(/postgresql:\/\/[^\s]*/g, '[connection]');
    
    // Generic fallback for production
    if (process.env.NODE_ENV === 'production') {
      return 'An internal error occurred. Please try again or contact support.';
    }
    
    return sanitized;
  }

  private getCutoffTime(timeframe: 'hour' | 'day' | 'week'): number {
    const now = Date.now();
    switch (timeframe) {
      case 'hour':
        return now - (60 * 60 * 1000);
      case 'day':
        return now - (24 * 60 * 60 * 1000);
      case 'week':
        return now - (7 * 24 * 60 * 60 * 1000);
      default:
        return now - (60 * 60 * 1000);
    }
  }

  private startPeriodicCleanup(): void {
    // Clean up old errors every hour
    setInterval(() => {
      const cutoffTime = Date.now() - (24 * 60 * 60 * 1000); // 24 hours
      const originalLength = this.errorHistory.length;
      
      this.errorHistory = this.errorHistory.filter(error => 
        new Date(error.context.timestamp || 0).getTime() > cutoffTime
      );

      // Clean up error counts
      const keysToDelete: string[] = [];
      this.errorCounts.forEach((count, key) => {
        if (count === 0) {
          keysToDelete.push(key);
        } else {
          // Decay error counts over time
          this.errorCounts.set(key, Math.max(0, count - 1));
        }
      });

      keysToDelete.forEach(key => this.errorCounts.delete(key));

      if (originalLength !== this.errorHistory.length) {
        this.logger.debug('Error history cleanup completed', {
          removed: originalLength - this.errorHistory.length,
          remaining: this.errorHistory.length
        });
      }
    }, 60 * 60 * 1000); // Every hour
  }
}