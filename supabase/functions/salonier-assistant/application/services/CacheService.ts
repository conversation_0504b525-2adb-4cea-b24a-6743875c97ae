/**
 * Cache Service
 * 
 * Application service that orchestrates caching strategies across all AI operations.
 * Manages cache lifecycle, optimization strategies, and performance analytics
 * to reduce API costs and improve response times.
 */

import { ICacheService, ILogger } from '../../services/interfaces.ts';
import { AIRequestMetrics, CacheConfig } from '../../types/use-case.types.ts';

export interface AdvancedCacheRequest {
  salonId: string;
  task: string;
  payload: any;
  options?: {
    ttl?: number;
    priority?: 'low' | 'normal' | 'high';
    tags?: string[];
    compression?: boolean;
    versioning?: boolean;
  };
}

export interface CacheAnalytics {
  hitRate: number;
  missRate: number;
  totalRequests: number;
  totalHits: number;
  totalMisses: number;
  costSavings: number;
  averageResponseTime: number;
  topCachedTasks: Array<{
    task: string;
    hits: number;
    savings: number;
  }>;
  salonStats: Record<string, {
    requests: number;
    hits: number;
    savings: number;
  }>;
}

export interface CacheOptimization {
  recommendedTTL: Record<string, number>;
  evictionCandidates: string[];
  compressionRecommendations: string[];
  preloadSuggestions: Array<{
    salonId: string;
    task: string;
    payload: any;
    priority: number;
  }>;
}

export interface IAdvancedCacheService extends ICacheService {
  getWithAnalytics(request: AdvancedCacheRequest): Promise<CacheHitResult>;
  setWithOptimization(
    request: AdvancedCacheRequest,
    result: any,
    model: string,
    tokens: number,
    cost: number
  ): Promise<void>;
  getAnalytics(salonId?: string, timeframe?: string): Promise<CacheAnalytics>;
  optimizeCache(salonId?: string): Promise<CacheOptimization>;
  warmupCache(salonId: string, predictions: CachePrediction[]): Promise<void>;
  invalidateByTags(tags: string[]): Promise<void>;
  exportCacheData(salonId: string): Promise<CacheExport>;
}

export interface CacheHitResult {
  hit: boolean;
  data?: any;
  metadata?: {
    age: number;
    hitCount: number;
    lastAccessed: Date;
    tags?: string[];
    size: number;
  };
  analytics: {
    costSaved: number;
    timeSaved: number;
  };
}

export interface CachePrediction {
  task: string;
  payload: any;
  probability: number;
  estimatedCost: number;
}

export interface CacheExport {
  entries: CacheEntry[];
  metadata: {
    totalEntries: number;
    totalSize: number;
    exportDate: Date;
    salonId: string;
  };
  analytics: CacheAnalytics;
}

export interface CacheEntry {
  key: string;
  salonId: string;
  task: string;
  inputHash: string;
  payload: any;
  result: any;
  model: string;
  tokens: number;
  cost: number;
  timestamp: number;
  ttl: number;
  hits: number;
  lastAccessed: number;
  tags?: string[];
  compressed: boolean;
  size: number;
  version?: number;
}

export class AdvancedCacheService implements IAdvancedCacheService {
  private cache: Map<string, CacheEntry> = new Map();
  private analytics: Map<string, CacheAnalytics> = new Map();
  private readonly defaultTTL = 3600000; // 1 hour
  private readonly maxCacheSize = 1000; // Maximum number of entries
  private readonly compressionThreshold = 50000; // Compress entries larger than 50KB

  constructor(
    private logger: ILogger,
    private config: CacheConfig = {
      ttl: 3600000,
      maxSize: 1000,
      enabled: true
    }
  ) {
    this.startCleanupTimer();
  }

  /**
   * Enhanced cache get with comprehensive analytics
   */
  async getWithAnalytics(request: AdvancedCacheRequest): Promise<CacheHitResult> {
    try {
      if (!this.config.enabled) {
        return {
          hit: false,
          analytics: {
            costSaved: 0,
            timeSaved: 0
          }
        };
      }

      const key = this.generateKey(request.task, request.payload);
      const entry = this.cache.get(key);

      // Check if entry exists and is valid
      if (entry && this.isEntryValid(entry)) {
        // Update hit statistics
        entry.hits++;
        entry.lastAccessed = Date.now();
        this.cache.set(key, entry);

        // Update analytics
        await this.updateHitAnalytics(request.salonId, request.task, entry);

        // Decompress if needed
        const data = entry.compressed ? this.decompress(entry.result) : entry.result;

        this.logger.debug('Cache hit', {
          salonId: request.salonId,
          task: request.task,
          key: key.substring(0, 16),
          age: Date.now() - entry.timestamp
        });

        return {
          hit: true,
          data,
          metadata: {
            age: Date.now() - entry.timestamp,
            hitCount: entry.hits,
            lastAccessed: new Date(entry.lastAccessed),
            tags: entry.tags,
            size: entry.size
          },
          analytics: {
            costSaved: entry.cost,
            timeSaved: this.estimateTimeSaved(request.task)
          }
        };
      }

      // Cache miss
      await this.updateMissAnalytics(request.salonId, request.task);

      this.logger.debug('Cache miss', {
        salonId: request.salonId,
        task: request.task,
        key: key.substring(0, 16)
      });

      return {
        hit: false,
        analytics: {
          costSaved: 0,
          timeSaved: 0
        }
      };

    } catch (error) {
      this.logger.error('Cache get operation failed', error);
      
      return {
        hit: false,
        analytics: {
          costSaved: 0,
          timeSaved: 0
        }
      };
    }
  }

  /**
   * Enhanced cache set with optimization and compression
   */
  async setWithOptimization(
    request: AdvancedCacheRequest,
    result: any,
    model: string,
    tokens: number,
    cost: number
  ): Promise<void> {
    try {
      if (!this.config.enabled) return;

      const key = this.generateKey(request.task, request.payload);
      const size = this.calculateSize(result);
      const ttl = request.options?.ttl || this.defaultTTL;
      
      // Check if we should compress
      const shouldCompress = request.options?.compression !== false && 
                           size > this.compressionThreshold;
      
      const finalResult = shouldCompress ? this.compress(result) : result;

      // Create cache entry
      const entry: CacheEntry = {
        key,
        salonId: request.salonId,
        task: request.task,
        inputHash: this.hashPayload(request.payload),
        payload: request.payload,
        result: finalResult,
        model,
        tokens,
        cost,
        timestamp: Date.now(),
        ttl,
        hits: 0,
        lastAccessed: Date.now(),
        tags: request.options?.tags,
        compressed: shouldCompress,
        size,
        version: request.options?.versioning ? 1 : undefined
      };

      // Check cache size and evict if necessary
      await this.evictIfNecessary();

      // Store entry
      this.cache.set(key, entry);

      // Update analytics
      await this.updateSetAnalytics(request.salonId, request.task, entry);

      this.logger.debug('Cache entry stored', {
        salonId: request.salonId,
        task: request.task,
        key: key.substring(0, 16),
        size,
        compressed: shouldCompress,
        ttl
      });

    } catch (error) {
      this.logger.error('Cache set operation failed', error);
    }
  }

  /**
   * Get comprehensive cache analytics
   */
  async getAnalytics(salonId?: string, timeframe?: string): Promise<CacheAnalytics> {
    try {
      const entries = Array.from(this.cache.values());
      const filteredEntries = salonId ? entries.filter(e => e.salonId === salonId) : entries;
      
      // Calculate timeframe filter
      const cutoffTime = this.calculateCutoffTime(timeframe);
      const recentEntries = cutoffTime ? 
        filteredEntries.filter(e => e.timestamp > cutoffTime) : 
        filteredEntries;

      const totalRequests = recentEntries.reduce((sum, entry) => sum + entry.hits + 1, 0);
      const totalHits = recentEntries.reduce((sum, entry) => sum + entry.hits, 0);
      const totalMisses = totalRequests - totalHits;
      const hitRate = totalRequests > 0 ? totalHits / totalRequests : 0;
      const missRate = 1 - hitRate;
      const costSavings = recentEntries.reduce((sum, entry) => sum + (entry.cost * entry.hits), 0);

      // Calculate top cached tasks
      const taskStats = new Map<string, { hits: number; savings: number }>();
      recentEntries.forEach(entry => {
        const existing = taskStats.get(entry.task) || { hits: 0, savings: 0 };
        taskStats.set(entry.task, {
          hits: existing.hits + entry.hits,
          savings: existing.savings + (entry.cost * entry.hits)
        });
      });

      const topCachedTasks = Array.from(taskStats.entries())
        .map(([task, stats]) => ({ task, ...stats }))
        .sort((a, b) => b.hits - a.hits)
        .slice(0, 10);

      // Calculate salon stats
      const salonStats: Record<string, { requests: number; hits: number; savings: number }> = {};
      if (!salonId) {
        const salonGroups = new Map<string, CacheEntry[]>();
        recentEntries.forEach(entry => {
          const group = salonGroups.get(entry.salonId) || [];
          group.push(entry);
          salonGroups.set(entry.salonId, group);
        });

        salonGroups.forEach((entries, salon) => {
          const requests = entries.reduce((sum, entry) => sum + entry.hits + 1, 0);
          const hits = entries.reduce((sum, entry) => sum + entry.hits, 0);
          const savings = entries.reduce((sum, entry) => sum + (entry.cost * entry.hits), 0);
          salonStats[salon] = { requests, hits, savings };
        });
      }

      const analytics: CacheAnalytics = {
        hitRate,
        missRate,
        totalRequests,
        totalHits,
        totalMisses,
        costSavings,
        averageResponseTime: this.calculateAverageResponseTime(recentEntries),
        topCachedTasks,
        salonStats
      };

      return analytics;

    } catch (error) {
      this.logger.error('Analytics calculation failed', error);
      
      // Return default analytics
      return {
        hitRate: 0,
        missRate: 1,
        totalRequests: 0,
        totalHits: 0,
        totalMisses: 0,
        costSavings: 0,
        averageResponseTime: 0,
        topCachedTasks: [],
        salonStats: {}
      };
    }
  }

  /**
   * Optimize cache configuration and suggest improvements
   */
  async optimizeCache(salonId?: string): Promise<CacheOptimization> {
    try {
      const entries = Array.from(this.cache.values());
      const filteredEntries = salonId ? entries.filter(e => e.salonId === salonId) : entries;

      // Analyze TTL patterns
      const taskTTLStats = new Map<string, { entries: number; avgAge: number; hits: number }>();
      filteredEntries.forEach(entry => {
        const age = Date.now() - entry.timestamp;
        const existing = taskTTLStats.get(entry.task) || { entries: 0, avgAge: 0, hits: 0 };
        taskTTLStats.set(entry.task, {
          entries: existing.entries + 1,
          avgAge: (existing.avgAge * existing.entries + age) / (existing.entries + 1),
          hits: existing.hits + entry.hits
        });
      });

      // Recommend optimal TTL values
      const recommendedTTL: Record<string, number> = {};
      taskTTLStats.forEach((stats, task) => {
        if (stats.hits > 2) {
          // Extend TTL for frequently accessed items
          recommendedTTL[task] = Math.min(this.defaultTTL * 2, stats.avgAge * 1.5);
        } else if (stats.hits === 0) {
          // Reduce TTL for unused items
          recommendedTTL[task] = this.defaultTTL * 0.5;
        } else {
          // Keep current TTL
          recommendedTTL[task] = this.defaultTTL;
        }
      });

      // Identify eviction candidates (old, unused entries)
      const evictionCandidates = filteredEntries
        .filter(entry => entry.hits === 0 && (Date.now() - entry.lastAccessed) > this.defaultTTL)
        .map(entry => entry.key)
        .slice(0, 50); // Limit to 50 candidates

      // Recommend compression for large entries
      const compressionRecommendations = filteredEntries
        .filter(entry => !entry.compressed && entry.size > this.compressionThreshold)
        .map(entry => entry.key)
        .slice(0, 20);

      // Generate preload suggestions based on patterns
      const preloadSuggestions = await this.generatePreloadSuggestions(filteredEntries);

      const optimization: CacheOptimization = {
        recommendedTTL,
        evictionCandidates,
        compressionRecommendations,
        preloadSuggestions
      };

      this.logger.info('Cache optimization completed', {
        salonId,
        ttlRecommendations: Object.keys(recommendedTTL).length,
        evictionCandidates: evictionCandidates.length,
        compressionOpportunities: compressionRecommendations.length,
        preloadSuggestions: preloadSuggestions.length
      });

      return optimization;

    } catch (error) {
      this.logger.error('Cache optimization failed', error);
      
      return {
        recommendedTTL: {},
        evictionCandidates: [],
        compressionRecommendations: [],
        preloadSuggestions: []
      };
    }
  }

  /**
   * Warm up cache with predicted requests
   */
  async warmupCache(salonId: string, predictions: CachePrediction[]): Promise<void> {
    try {
      this.logger.info('Starting cache warmup', {
        salonId,
        predictions: predictions.length
      });

      // Sort by probability and cost
      const sortedPredictions = predictions.sort((a, b) => 
        (b.probability * b.estimatedCost) - (a.probability * a.estimatedCost)
      );

      // Pre-warm top predictions (limited to prevent overload)
      const warmupLimit = 10;
      const topPredictions = sortedPredictions.slice(0, warmupLimit);

      for (const prediction of topPredictions) {
        // This would typically involve calling the actual AI service
        // For now, we'll just log the intention
        this.logger.debug('Warming up cache entry', {
          salonId,
          task: prediction.task,
          probability: prediction.probability
        });
      }

      this.logger.info('Cache warmup completed', {
        salonId,
        warmedEntries: topPredictions.length
      });

    } catch (error) {
      this.logger.error('Cache warmup failed', error);
    }
  }

  // Base interface implementation methods

  async get(salonId: string, task: string, inputHash: string): Promise<any | null> {
    const request: AdvancedCacheRequest = {
      salonId,
      task,
      payload: { inputHash }
    };
    
    const result = await this.getWithAnalytics(request);
    return result.hit ? result.data : null;
  }

  async set(
    salonId: string,
    task: string,
    inputHash: string,
    payload: any,
    result: any,
    model: string,
    tokens: number,
    cost: number,
    ttl?: number
  ): Promise<void> {
    const request: AdvancedCacheRequest = {
      salonId,
      task,
      payload,
      options: { ttl }
    };

    await this.setWithOptimization(request, result, model, tokens, cost);
  }

  generateKey(task: string, payload: any): string {
    const payloadStr = JSON.stringify(payload);
    return `${task}:${this.hashPayload(payloadStr)}`;
  }

  async cleanup(salonId: string): Promise<void> {
    const entries = Array.from(this.cache.entries());
    let cleanedCount = 0;

    for (const [key, entry] of entries) {
      if (entry.salonId === salonId) {
        this.cache.delete(key);
        cleanedCount++;
      }
    }

    this.logger.info('Cache cleanup completed', {
      salonId,
      cleanedEntries: cleanedCount
    });
  }

  async getStats(salonId: string): Promise<any> {
    return await this.getAnalytics(salonId);
  }

  async invalidateByTags(tags: string[]): Promise<void> {
    const entries = Array.from(this.cache.entries());
    let invalidatedCount = 0;

    for (const [key, entry] of entries) {
      if (entry.tags && entry.tags.some(tag => tags.includes(tag))) {
        this.cache.delete(key);
        invalidatedCount++;
      }
    }

    this.logger.info('Cache invalidation by tags completed', {
      tags,
      invalidatedEntries: invalidatedCount
    });
  }

  async exportCacheData(salonId: string): Promise<CacheExport> {
    const entries = Array.from(this.cache.values()).filter(e => e.salonId === salonId);
    const analytics = await this.getAnalytics(salonId);
    const totalSize = entries.reduce((sum, entry) => sum + entry.size, 0);

    return {
      entries,
      metadata: {
        totalEntries: entries.length,
        totalSize,
        exportDate: new Date(),
        salonId
      },
      analytics
    };
  }

  // Private helper methods

  private isEntryValid(entry: CacheEntry): boolean {
    const now = Date.now();
    return (now - entry.timestamp) < entry.ttl;
  }

  private async evictIfNecessary(): Promise<void> {
    if (this.cache.size >= this.config.maxSize) {
      // LRU eviction strategy
      const entries = Array.from(this.cache.entries());
      entries.sort(([,a], [,b]) => a.lastAccessed - b.lastAccessed);
      
      const evictionCount = Math.floor(this.config.maxSize * 0.1); // Evict 10%
      for (let i = 0; i < evictionCount; i++) {
        this.cache.delete(entries[i][0]);
      }

      this.logger.debug('Cache eviction completed', {
        evictedEntries: evictionCount,
        remainingEntries: this.cache.size
      });
    }
  }

  private calculateSize(data: any): number {
    return JSON.stringify(data).length;
  }

  private compress(data: any): any {
    // Simple compression simulation - in practice, use a real compression library
    return { compressed: true, data: JSON.stringify(data) };
  }

  private decompress(data: any): any {
    if (data.compressed) {
      return JSON.parse(data.data);
    }
    return data;
  }

  private hashPayload(payload: any): string {
    const str = typeof payload === 'string' ? payload : JSON.stringify(payload);
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString();
  }

  private estimateTimeSaved(task: string): number {
    // Estimated time savings based on task type
    const timeSavings: Record<string, number> = {
      'diagnose_image': 2500,
      'generate_formula': 3000,
      'analyze_desired_look': 2000,
      'chat_assistant': 1000
    };
    
    return timeSavings[task] || 1500;
  }

  private calculateCutoffTime(timeframe?: string): number | null {
    if (!timeframe) return null;
    
    const now = Date.now();
    switch (timeframe) {
      case '1h': return now - 60 * 60 * 1000;
      case '24h': return now - 24 * 60 * 60 * 1000;
      case '7d': return now - 7 * 24 * 60 * 60 * 1000;
      case '30d': return now - 30 * 24 * 60 * 60 * 1000;
      default: return null;
    }
  }

  private calculateAverageResponseTime(entries: CacheEntry[]): number {
    if (entries.length === 0) return 0;
    
    // Simulate response time calculation
    return entries.reduce((sum, entry) => sum + this.estimateTimeSaved(entry.task), 0) / entries.length;
  }

  private async generatePreloadSuggestions(entries: CacheEntry[]): Promise<CachePrediction[]> {
    // Analyze patterns and generate predictions
    const suggestions: CachePrediction[] = [];
    
    // Group by salon and task
    const patterns = new Map<string, { count: number; avgCost: number }>();
    
    entries.forEach(entry => {
      const key = `${entry.salonId}:${entry.task}`;
      const existing = patterns.get(key) || { count: 0, avgCost: 0 };
      patterns.set(key, {
        count: existing.count + 1,
        avgCost: (existing.avgCost * existing.count + entry.cost) / (existing.count + 1)
      });
    });

    // Convert to predictions
    patterns.forEach((stats, key) => {
      const [salonId, task] = key.split(':');
      if (stats.count > 2) { // Only suggest if seen multiple times
        suggestions.push({
          task,
          payload: { salonId },
          probability: Math.min(stats.count / 10, 0.9),
          estimatedCost: stats.avgCost
        });
      }
    });

    return suggestions;
  }

  private async updateHitAnalytics(salonId: string, task: string, entry: CacheEntry): Promise<void> {
    // Update hit statistics
    // In practice, this might update persistent analytics storage
  }

  private async updateMissAnalytics(salonId: string, task: string): Promise<void> {
    // Update miss statistics
    // In practice, this might update persistent analytics storage
  }

  private async updateSetAnalytics(salonId: string, task: string, entry: CacheEntry): Promise<void> {
    // Update set statistics
    // In practice, this might update persistent analytics storage
  }

  private startCleanupTimer(): void {
    // Cleanup expired entries every hour
    setInterval(() => {
      this.performPeriodicCleanup();
    }, 60 * 60 * 1000);
  }

  private performPeriodicCleanup(): void {
    const entries = Array.from(this.cache.entries());
    let cleanedCount = 0;

    for (const [key, entry] of entries) {
      if (!this.isEntryValid(entry)) {
        this.cache.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      this.logger.debug('Periodic cache cleanup completed', {
        cleanedEntries: cleanedCount,
        remainingEntries: this.cache.size
      });
    }
  }
}