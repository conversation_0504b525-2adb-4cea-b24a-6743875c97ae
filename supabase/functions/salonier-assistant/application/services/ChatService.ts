/**
 * Chat Service
 * 
 * Application service that orchestrates conversational AI interactions.
 * Manages chat context, intent classification, and response generation
 * with professional hair coloration expertise.
 */

import { ChatAssistantUseCase } from '../../use-cases/ChatAssistantUseCase.ts';
import { ICacheService, ILogger, IValidator } from '../../services/interfaces.ts';
import { 
  ChatAssistantRequest,
  ChatAssistantResponse,
  ChatMessage,
  HairDiagnosis,
  HairFormula,
  Result 
} from '../../types/use-case.types.ts';

export interface EnhancedChatRequest {
  message: string;
  conversationHistory?: ChatMessage[];
  context?: {
    currentService?: {
      diagnosis?: HairDiagnosis;
      formula?: HairFormula;
      stage?: 'diagnosis' | 'formulation' | 'application' | 'completed';
    };
    clientProfile?: {
      name?: string;
      preferences?: string[];
      allergies?: string[];
      previousServices?: string[];
    };
    salonInfo?: {
      name?: string;
      location?: string;
      specialties?: string[];
      products?: string[];
    };
  };
  userRole?: 'stylist' | 'client' | 'manager';
  salonId: string;
  userId: string;
  sessionId?: string;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
}

export interface EnhancedChatResponse {
  message: string;
  intent: string;
  confidence: number;
  responseType: 'informational' | 'instructional' | 'warning' | 'recommendation' | 'question';
  suggestedActions?: Array<{
    action: string;
    description: string;
    priority: 'low' | 'medium' | 'high';
  }>;
  followUpQuestions?: string[];
  requiresHumanIntervention: boolean;
  professionalContext: {
    technicalLevel: 'basic' | 'intermediate' | 'advanced';
    safetyConsiderations: string[];
    references?: string[];
  };
  conversationMetadata: {
    sessionLength: number;
    previousIntents: string[];
    topicProgression: string[];
    satisfactionSignals: string[];
  };
  metadata: {
    processingTime: number;
    model: string;
    tokens: number;
    cost: number;
    cached: boolean;
    timestamp: Date;
  };
}

export interface IChatService {
  processChat(request: EnhancedChatRequest): Promise<Result<EnhancedChatResponse>>;
  analyzeIntent(message: string, context?: any): Promise<Result<IntentAnalysis>>;
  generateContextualResponse(
    intent: IntentAnalysis,
    context: any,
    conversationHistory: ChatMessage[]
  ): Promise<Result<ContextualResponse>>;
  updateConversationContext(
    sessionId: string,
    response: EnhancedChatResponse
  ): Promise<void>;
}

export interface IntentAnalysis {
  primaryIntent: string;
  confidence: number;
  entities: Array<{
    type: string;
    value: string;
    confidence: number;
  }>;
  urgencyLevel: 'low' | 'normal' | 'high' | 'critical';
  category: 'technical' | 'safety' | 'product' | 'procedure' | 'client_care' | 'business';
  subIntents?: string[];
}

export interface ContextualResponse {
  content: string;
  suggestedActions: Array<{
    action: string;
    description: string;
    priority: 'low' | 'medium' | 'high';
  }>;
  followUpQuestions: string[];
  safetyWarnings: string[];
  technicalNotes: string[];
}

export class ChatService implements IChatService {
  private conversationContexts: Map<string, ConversationContext> = new Map();

  constructor(
    private chatAssistantUseCase: ChatAssistantUseCase,
    private cacheService: ICacheService,
    private validator: IValidator,
    private logger: ILogger
  ) {}

  /**
   * Process complete chat interaction with enhanced context and professional guidance
   */
  async processChat(request: EnhancedChatRequest): Promise<Result<EnhancedChatResponse>> {
    const startTime = Date.now();

    try {
      this.logger.info('Processing chat request', {
        salonId: request.salonId,
        userRole: request.userRole,
        sessionId: request.sessionId,
        priority: request.priority
      });

      // Validate request
      const validationResult = this.validateChatRequest(request);
      if (!validationResult.valid) {
        this.logger.warn('Chat request validation failed', {
          errors: validationResult.errors
        });
        return {
          success: false,
          error: `Validation failed: ${validationResult.errors.join(', ')}`
        };
      }

      // Get or create conversation context
      const conversationContext = this.getConversationContext(request.sessionId || request.userId);

      // Analyze intent
      const intentResult = await this.analyzeIntent(request.message, {
        ...request.context,
        conversationHistory: request.conversationHistory,
        userRole: request.userRole
      });

      if (!intentResult.success || !intentResult.data) {
        this.logger.error('Intent analysis failed', {
          error: intentResult.error
        });
        return {
          success: false,
          error: intentResult.error || 'Intent analysis failed'
        };
      }

      const intentAnalysis = intentResult.data;

      // Check for urgent safety concerns
      if (intentAnalysis.urgencyLevel === 'critical') {
        this.logger.warn('Critical safety concern detected in chat', {
          intent: intentAnalysis.primaryIntent,
          entities: intentAnalysis.entities
        });
      }

      // Generate contextual response
      const responseResult = await this.generateContextualResponse(
        intentAnalysis,
        {
          ...request.context,
          userRole: request.userRole,
          conversationContext
        },
        request.conversationHistory || []
      );

      if (!responseResult.success || !responseResult.data) {
        this.logger.error('Response generation failed', {
          error: responseResult.error
        });
        return {
          success: false,
          error: responseResult.error || 'Response generation failed'
        };
      }

      const contextualResponse = responseResult.data;

      // Execute base chat use case for additional AI processing
      const baseChatRequest: ChatAssistantRequest = {
        message: request.message,
        conversationHistory: request.conversationHistory,
        context: request.context,
        salonId: request.salonId,
        userId: request.userId
      };

      const baseChatResult = await this.chatAssistantUseCase.execute(baseChatRequest);
      
      // Determine response type
      const responseType = this.determineResponseType(intentAnalysis, contextualResponse);

      // Check if human intervention is needed
      const requiresHumanIntervention = this.shouldRequireHumanIntervention(
        intentAnalysis,
        contextualResponse,
        request.userRole
      );

      // Build professional context
      const professionalContext = this.buildProfessionalContext(
        intentAnalysis,
        contextualResponse,
        request.context
      );

      // Update conversation metadata
      conversationContext.sessionLength++;
      conversationContext.previousIntents.push(intentAnalysis.primaryIntent);
      conversationContext.topicProgression.push(intentAnalysis.category);
      
      // Calculate processing time
      const processingTime = Date.now() - startTime;

      // Build enhanced response
      const enhancedResponse: EnhancedChatResponse = {
        message: contextualResponse.content,
        intent: intentAnalysis.primaryIntent,
        confidence: intentAnalysis.confidence,
        responseType,
        suggestedActions: contextualResponse.suggestedActions,
        followUpQuestions: contextualResponse.followUpQuestions,
        requiresHumanIntervention,
        professionalContext,
        conversationMetadata: {
          sessionLength: conversationContext.sessionLength,
          previousIntents: [...conversationContext.previousIntents],
          topicProgression: [...conversationContext.topicProgression],
          satisfactionSignals: [...conversationContext.satisfactionSignals]
        },
        metadata: {
          processingTime,
          model: 'gpt-4o',
          tokens: baseChatResult.success ? 0 : 0, // Should come from base result
          cost: 0, // Should come from base result
          cached: false,
          timestamp: new Date()
        }
      };

      // Update conversation context
      if (request.sessionId) {
        await this.updateConversationContext(request.sessionId, enhancedResponse);
      }

      this.logger.info('Chat processing completed successfully', {
        salonId: request.salonId,
        intent: intentAnalysis.primaryIntent,
        requiresHumanIntervention,
        processingTime
      });

      return {
        success: true,
        data: enhancedResponse
      };

    } catch (error) {
      this.logger.error('Chat processing failed with exception', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown chat processing error'
      };
    }
  }

  /**
   * Analyze user intent and extract entities
   */
  async analyzeIntent(message: string, context?: any): Promise<Result<IntentAnalysis>> {
    try {
      const messageLC = message.toLowerCase();
      
      // Intent classification patterns
      const intentPatterns = {
        'hair_diagnosis': [
          'diagnosis', 'analyze', 'condition', 'level', 'tone', 'damage', 'porosity'
        ],
        'color_formulation': [
          'formula', 'color', 'shade', 'developer', 'mixing', 'bleach', 'tint'
        ],
        'safety_concern': [
          'safe', 'risk', 'danger', 'allergy', 'reaction', 'chemical', 'burn'
        ],
        'technique_question': [
          'how to', 'technique', 'application', 'sectioning', 'timing'
        ],
        'product_inquiry': [
          'product', 'brand', 'recommend', 'substitute', 'alternative'
        ],
        'client_consultation': [
          'client', 'customer', 'consultation', 'explain', 'communicate'
        ],
        'troubleshooting': [
          'problem', 'issue', 'wrong', 'fix', 'correct', 'help'
        ]
      };

      // Find primary intent
      let primaryIntent = 'general_inquiry';
      let confidence = 0.5;

      for (const [intent, keywords] of Object.entries(intentPatterns)) {
        const matches = keywords.filter(keyword => messageLC.includes(keyword));
        const intentConfidence = matches.length / keywords.length;
        
        if (intentConfidence > confidence) {
          primaryIntent = intent;
          confidence = intentConfidence;
        }
      }

      // Extract entities
      const entities = this.extractEntities(message);

      // Determine urgency level
      const urgencyLevel = this.determineUrgencyLevel(messageLC, entities);

      // Determine category
      const category = this.categorizeIntent(primaryIntent);

      // Find sub-intents
      const subIntents = this.findSubIntents(messageLC, primaryIntent);

      const analysis: IntentAnalysis = {
        primaryIntent,
        confidence: Math.min(1.0, confidence + 0.3), // Boost confidence slightly
        entities,
        urgencyLevel,
        category,
        subIntents: subIntents.length > 0 ? subIntents : undefined
      };

      return {
        success: true,
        data: analysis
      };

    } catch (error) {
      this.logger.error('Intent analysis failed', error);
      
      return {
        success: false,
        error: 'Intent analysis process failed'
      };
    }
  }

  /**
   * Generate contextual response based on intent and conversation history
   */
  async generateContextualResponse(
    intent: IntentAnalysis,
    context: any,
    conversationHistory: ChatMessage[]
  ): Promise<Result<ContextualResponse>> {
    try {
      let content = '';
      const suggestedActions: ContextualResponse['suggestedActions'] = [];
      const followUpQuestions: string[] = [];
      const safetyWarnings: string[] = [];
      const technicalNotes: string[] = [];

      // Generate response based on intent
      switch (intent.primaryIntent) {
        case 'safety_concern':
          content = this.generateSafetyResponse(intent, context);
          safetyWarnings.push(...this.getSafetyWarnings(intent));
          suggestedActions.push({
            action: 'consult_senior',
            description: 'Consult with senior colorist',
            priority: 'high'
          });
          break;

        case 'hair_diagnosis':
          content = this.generateDiagnosisResponse(intent, context);
          if (context.currentService?.diagnosis) {
            technicalNotes.push('Current diagnosis available in service context');
          }
          followUpQuestions.push('Would you like me to analyze specific areas of concern?');
          break;

        case 'color_formulation':
          content = this.generateFormulationResponse(intent, context);
          technicalNotes.push('Always perform strand test before full application');
          suggestedActions.push({
            action: 'strand_test',
            description: 'Perform strand test',
            priority: 'medium'
          });
          break;

        case 'technique_question':
          content = this.generateTechniqueResponse(intent, context);
          followUpQuestions.push('Do you need specific timing recommendations?');
          break;

        case 'product_inquiry':
          content = this.generateProductResponse(intent, context);
          if (context.salonInfo?.products) {
            technicalNotes.push('Salon inventory available for reference');
          }
          break;

        case 'troubleshooting':
          content = this.generateTroubleshootingResponse(intent, context);
          suggestedActions.push({
            action: 'document_issue',
            description: 'Document the issue for future reference',
            priority: 'low'
          });
          break;

        default:
          content = this.generateGeneralResponse(intent, context);
          followUpQuestions.push('How can I help you with your hair color service today?');
      }

      // Add context-specific enhancements
      if (context.userRole === 'client') {
        content = this.simplifyForClientAudience(content);
      } else if (context.userRole === 'manager') {
        technicalNotes.push('Business considerations may apply');
      }

      // Check conversation history for context
      if (conversationHistory.length > 0) {
        const recentMessages = conversationHistory.slice(-3);
        content = this.enhanceWithConversationContext(content, recentMessages);
      }

      const response: ContextualResponse = {
        content,
        suggestedActions,
        followUpQuestions,
        safetyWarnings,
        technicalNotes
      };

      return {
        success: true,
        data: response
      };

    } catch (error) {
      this.logger.error('Contextual response generation failed', error);
      
      return {
        success: false,
        error: 'Response generation process failed'
      };
    }
  }

  /**
   * Update conversation context for session tracking
   */
  async updateConversationContext(
    sessionId: string,
    response: EnhancedChatResponse
  ): Promise<void> {
    try {
      const context = this.conversationContexts.get(sessionId) || this.createNewContext();
      
      // Update satisfaction signals
      if (response.responseType === 'recommendation' && response.confidence > 0.8) {
        context.satisfactionSignals.push('high_confidence_recommendation');
      }
      
      if (response.followUpQuestions && response.followUpQuestions.length > 0) {
        context.satisfactionSignals.push('engagement_questions_provided');
      }

      // Update context
      context.lastActivity = new Date();
      this.conversationContexts.set(sessionId, context);

      // Clean up old contexts (older than 24 hours)
      this.cleanupOldContexts();

    } catch (error) {
      this.logger.error('Context update failed', error);
    }
  }

  // Private helper methods

  private validateChatRequest(request: EnhancedChatRequest): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!request.message || request.message.trim().length === 0) {
      errors.push('Message is required');
    }

    if (!request.salonId) {
      errors.push('Salon ID is required');
    }

    if (!request.userId) {
      errors.push('User ID is required');
    }

    if (request.message && request.message.length > 2000) {
      errors.push('Message too long (max 2000 characters)');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  private getConversationContext(sessionId: string): ConversationContext {
    return this.conversationContexts.get(sessionId) || this.createNewContext();
  }

  private createNewContext(): ConversationContext {
    return {
      sessionLength: 0,
      previousIntents: [],
      topicProgression: [],
      satisfactionSignals: [],
      lastActivity: new Date()
    };
  }

  private extractEntities(message: string): IntentAnalysis['entities'] {
    const entities: IntentAnalysis['entities'] = [];
    const messageLC = message.toLowerCase();

    // Hair level patterns (1-10)
    const levelMatch = messageLC.match(/level\s*([1-9]|10)/);
    if (levelMatch) {
      entities.push({
        type: 'hair_level',
        value: levelMatch[1],
        confidence: 0.9
      });
    }

    // Color tones
    const tonePatterns = {
      'ash': ['ash', 'ashy'],
      'warm': ['warm', 'golden', 'honey'],
      'cool': ['cool', 'ash', 'platinum'],
      'neutral': ['neutral', 'natural']
    };

    for (const [tone, patterns] of Object.entries(tonePatterns)) {
      if (patterns.some(pattern => messageLC.includes(pattern))) {
        entities.push({
          type: 'hair_tone',
          value: tone,
          confidence: 0.8
        });
        break;
      }
    }

    // Developer volumes
    const developerMatch = messageLC.match(/(\d+)\s*(vol|volume|developer)/);
    if (developerMatch) {
      entities.push({
        type: 'developer_volume',
        value: `${developerMatch[1]}vol`,
        confidence: 0.9
      });
    }

    // Time references
    const timeMatch = messageLC.match(/(\d+)\s*(minutes?|mins?|hours?)/);
    if (timeMatch) {
      entities.push({
        type: 'processing_time',
        value: `${timeMatch[1]} ${timeMatch[2]}`,
        confidence: 0.8
      });
    }

    return entities;
  }

  private determineUrgencyLevel(message: string, entities: IntentAnalysis['entities']): IntentAnalysis['urgencyLevel'] {
    const criticalKeywords = ['burning', 'pain', 'allergic reaction', 'emergency', 'help'];
    const highKeywords = ['problem', 'issue', 'wrong', 'mistake', 'urgent'];
    
    if (criticalKeywords.some(keyword => message.includes(keyword))) {
      return 'critical';
    } else if (highKeywords.some(keyword => message.includes(keyword))) {
      return 'high';
    } else if (entities.some(e => e.type === 'processing_time' && parseInt(e.value) > 60)) {
      return 'high';
    }
    
    return 'normal';
  }

  private categorizeIntent(intent: string): IntentAnalysis['category'] {
    const categoryMap: Record<string, IntentAnalysis['category']> = {
      'safety_concern': 'safety',
      'hair_diagnosis': 'technical',
      'color_formulation': 'technical',
      'technique_question': 'procedure',
      'product_inquiry': 'product',
      'client_consultation': 'client_care',
      'troubleshooting': 'technical'
    };

    return categoryMap[intent] || 'technical';
  }

  private findSubIntents(message: string, primaryIntent: string): string[] {
    const subIntents: string[] = [];

    // Add sub-intents based on primary intent and message content
    if (primaryIntent === 'color_formulation') {
      if (message.includes('mixing')) subIntents.push('mixing_ratios');
      if (message.includes('developer')) subIntents.push('developer_selection');
      if (message.includes('timing')) subIntents.push('processing_time');
    }

    return subIntents;
  }

  // Response generation methods for different intents
  
  private generateSafetyResponse(intent: IntentAnalysis, context: any): string {
    return "Safety is our top priority. Let me provide you with the appropriate safety guidance for your specific concern.";
  }

  private generateDiagnosisResponse(intent: IntentAnalysis, context: any): string {
    if (context.currentService?.diagnosis) {
      return "Based on your current diagnosis, I can provide specific insights about the hair condition and recommended approach.";
    }
    return "To provide accurate diagnosis guidance, I'll need information about the current hair condition including level, tone, and overall health.";
  }

  private generateFormulationResponse(intent: IntentAnalysis, context: any): string {
    return "For color formulation, I'll consider the current hair condition, desired result, and available products to recommend the most appropriate approach.";
  }

  private generateTechniqueResponse(intent: IntentAnalysis, context: any): string {
    return "I'll provide step-by-step technique guidance based on professional standards and best practices.";
  }

  private generateProductResponse(intent: IntentAnalysis, context: any): string {
    return "Let me help you with product recommendations based on your specific needs and available inventory.";
  }

  private generateTroubleshootingResponse(intent: IntentAnalysis, context: any): string {
    return "I'll help you troubleshoot the issue systematically to find the best solution.";
  }

  private generateGeneralResponse(intent: IntentAnalysis, context: any): string {
    return "I'm here to assist with your hair coloration needs. How can I help you today?";
  }

  private getSafetyWarnings(intent: IntentAnalysis): string[] {
    const warnings: string[] = [];
    
    if (intent.entities.some(e => e.type === 'developer_volume' && parseInt(e.value) > 30)) {
      warnings.push('High volume developer requires careful monitoring');
    }

    return warnings;
  }

  private simplifyForClientAudience(content: string): string {
    // Simplify technical language for clients
    return content
      .replace(/developer/g, 'activator')
      .replace(/processing time/g, 'timing')
      .replace(/formulation/g, 'color recipe');
  }

  private enhanceWithConversationContext(content: string, recentMessages: ChatMessage[]): string {
    // Enhance response based on recent conversation
    return content;
  }

  private determineResponseType(
    intent: IntentAnalysis,
    response: ContextualResponse
  ): EnhancedChatResponse['responseType'] {
    if (response.safetyWarnings.length > 0) return 'warning';
    if (intent.category === 'procedure') return 'instructional';
    if (response.suggestedActions.some(a => a.priority === 'high')) return 'recommendation';
    if (response.followUpQuestions.length > 0) return 'question';
    return 'informational';
  }

  private shouldRequireHumanIntervention(
    intent: IntentAnalysis,
    response: ContextualResponse,
    userRole?: string
  ): boolean {
    return intent.urgencyLevel === 'critical' ||
           response.safetyWarnings.length > 1 ||
           (intent.confidence < 0.6 && intent.category === 'safety');
  }

  private buildProfessionalContext(
    intent: IntentAnalysis,
    response: ContextualResponse,
    context: any
  ): EnhancedChatResponse['professionalContext'] {
    const technicalLevel = intent.category === 'safety' ? 'advanced' :
                          intent.category === 'technical' ? 'intermediate' : 'basic';

    return {
      technicalLevel,
      safetyConsiderations: response.safetyWarnings,
      references: response.technicalNotes.length > 0 ? response.technicalNotes : undefined
    };
  }

  private cleanupOldContexts(): void {
    const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
    
    for (const [sessionId, context] of this.conversationContexts.entries()) {
      if (context.lastActivity < cutoffTime) {
        this.conversationContexts.delete(sessionId);
      }
    }
  }
}

interface ConversationContext {
  sessionLength: number;
  previousIntents: string[];
  topicProgression: string[];
  satisfactionSignals: string[];
  lastActivity: Date;
}