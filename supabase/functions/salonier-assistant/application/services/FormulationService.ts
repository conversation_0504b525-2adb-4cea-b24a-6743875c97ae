/**
 * Formulation Service
 * 
 * Application service that orchestrates hair formula generation and validation.
 * Coordinates desired look analysis, formula generation, and comprehensive validation
 * to ensure safe and effective color formulation.
 */

import { AnalyzeDesiredLookUseCase } from '../../use-cases/AnalyzeDesiredLookUseCase.ts';
import { GenerateFormulaUseCase } from '../../use-cases/GenerateFormulaUseCase.ts';
import { ICacheService, ILogger, IValidator } from '../../services/interfaces.ts';
import { 
  HairDiagnosis, 
  DesiredLookAnalysis, 
  HairFormula,
  AnalyzeDesiredLookRequest,
  GenerateFormulaRequest,
  ProductInventory,
  ClientPreferences,
  Result 
} from '../../types/use-case.types.ts';

export interface FormulationRequest {
  currentDiagnosis: HairDiagnosis;
  desiredLookImage?: string;
  desiredLookBase64?: string;
  targetDescription?: string;
  availableProducts?: ProductInventory[];
  clientPreferences?: ClientPreferences;
  serviceConstraints?: {
    maxProcessingTime?: number;
    budgetLimit?: number;
    appointmentType?: 'standard' | 'express' | 'premium';
  };
  salonId: string;
  userId: string;
  clientId?: string;
  requestId?: string;
}

export interface FormulationResponse {
  desiredLookAnalysis: DesiredLookAnalysis;
  formula: HairFormula;
  viabilityAssessment: {
    achievable: boolean;
    confidence: number;
    sessionCount: number;
    timeEstimate: number;
    costEstimate: number;
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
  };
  alternativeOptions?: {
    conservative: HairFormula;
    progressive: HairFormula;
    maintenance?: HairFormula;
  };
  professionalGuidance: {
    required: boolean;
    level: 'junior' | 'senior' | 'master';
    specializations?: string[];
    consultation: string[];
  };
  clientCommunication: {
    explanation: string;
    expectations: string[];
    aftercare: string[];
    maintenance: string[];
  };
  metadata: {
    processingTime: number;
    model: string;
    tokens: number;
    cost: number;
    cached: boolean;
    timestamp: Date;
  };
}

export interface IFormulationService {
  generateCompleteFormulation(request: FormulationRequest): Promise<Result<FormulationResponse>>;
  validateFormulaSafety(formula: HairFormula, diagnosis: HairDiagnosis): Promise<Result<SafetyReport>>;
  optimizeFormulaForInventory(
    formula: HairFormula, 
    availableProducts: ProductInventory[]
  ): Promise<Result<OptimizedFormula>>;
  generateAlternativeFormulas(
    request: FormulationRequest,
    primaryFormula: HairFormula
  ): Promise<Result<AlternativeFormulas>>;
}

export interface SafetyReport {
  isSafe: boolean;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  warnings: string[];
  contraindications: string[];
  requiredPrecautions: string[];
  recommendedTests: string[];
}

export interface OptimizedFormula extends HairFormula {
  availabilityScore: number;
  substitutions: Array<{
    original: string;
    substitute: string;
    reason: string;
  }>;
  missingProducts: string[];
}

export interface AlternativeFormulas {
  conservative: HairFormula;
  progressive: HairFormula;
  maintenance?: HairFormula;
  explanations: {
    conservative: string;
    progressive: string;
    maintenance?: string;
  };
}

export class FormulationService implements IFormulationService {
  constructor(
    private analyzeDesiredLookUseCase: AnalyzeDesiredLookUseCase,
    private generateFormulaUseCase: GenerateFormulaUseCase,
    private cacheService: ICacheService,
    private validator: IValidator,
    private logger: ILogger
  ) {}

  /**
   * Generate complete formulation including desired look analysis and formula
   */
  async generateCompleteFormulation(request: FormulationRequest): Promise<Result<FormulationResponse>> {
    const startTime = Date.now();

    try {
      this.logger.info('Starting formulation process', {
        salonId: request.salonId,
        requestId: request.requestId,
        hasDesiredLookImage: !!(request.desiredLookImage || request.desiredLookBase64)
      });

      // Validate request
      const validationResult = this.validateFormulationRequest(request);
      if (!validationResult.valid) {
        this.logger.warn('Formulation request validation failed', {
          errors: validationResult.errors
        });
        return {
          success: false,
          error: `Validation failed: ${validationResult.errors.join(', ')}`
        };
      }

      // Step 1: Analyze desired look
      const desiredLookResult = await this.analyzeDesiredLook(request);
      if (!desiredLookResult.success || !desiredLookResult.data) {
        this.logger.error('Desired look analysis failed', {
          error: desiredLookResult.error
        });
        return {
          success: false,
          error: desiredLookResult.error || 'Desired look analysis failed'
        };
      }

      const desiredLookAnalysis = desiredLookResult.data;

      // Step 2: Validate viability
      const viabilityAssessment = this.assessViability(
        request.currentDiagnosis,
        desiredLookAnalysis,
        request.serviceConstraints
      );

      // Step 3: Generate formula
      const formulaResult = await this.generateFormula(request, desiredLookAnalysis);
      if (!formulaResult.success || !formulaResult.data) {
        this.logger.error('Formula generation failed', {
          error: formulaResult.error
        });
        return {
          success: false,
          error: formulaResult.error || 'Formula generation failed'
        };
      }

      const formula = formulaResult.data;

      // Step 4: Safety validation
      const safetyResult = await this.validateFormulaSafety(formula, request.currentDiagnosis);
      if (!safetyResult.success || !safetyResult.data?.isSafe) {
        this.logger.warn('Formula safety concerns detected', {
          warnings: safetyResult.data?.warnings,
          riskLevel: safetyResult.data?.riskLevel
        });
      }

      // Step 5: Generate alternatives
      const alternativesResult = await this.generateAlternativeFormulas(request, formula);
      const alternatives = alternativesResult.success ? alternativesResult.data : undefined;

      // Step 6: Determine professional guidance requirements
      const professionalGuidance = this.determineProfessionalGuidance(
        viabilityAssessment,
        safetyResult.data || { isSafe: false, riskLevel: 'high', warnings: [], contraindications: [], requiredPrecautions: [], recommendedTests: [] }
      );

      // Step 7: Generate client communication
      const clientCommunication = this.generateClientCommunication(
        desiredLookAnalysis,
        formula,
        viabilityAssessment
      );

      // Calculate processing time
      const processingTime = Date.now() - startTime;

      // Build response
      const response: FormulationResponse = {
        desiredLookAnalysis,
        formula,
        viabilityAssessment,
        alternativeOptions: alternatives ? {
          conservative: alternatives.conservative,
          progressive: alternatives.progressive,
          maintenance: alternatives.maintenance
        } : undefined,
        professionalGuidance,
        clientCommunication,
        metadata: {
          processingTime,
          model: 'gpt-4o',
          tokens: 0, // Should come from use cases
          cost: 0, // Should come from use cases
          cached: false, // Should come from cache check
          timestamp: new Date()
        }
      };

      this.logger.info('Formulation process completed successfully', {
        salonId: request.salonId,
        achievable: viabilityAssessment.achievable,
        riskLevel: viabilityAssessment.riskLevel,
        processingTime
      });

      return {
        success: true,
        data: response
      };

    } catch (error) {
      this.logger.error('Formulation process failed with exception', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown formulation error'
      };
    }
  }

  /**
   * Validate formula safety against current diagnosis
   */
  async validateFormulaSafety(formula: HairFormula, diagnosis: HairDiagnosis): Promise<Result<SafetyReport>> {
    try {
      const warnings: string[] = [];
      const contraindications: string[] = [];
      const requiredPrecautions: string[] = [];
      const recommendedTests: string[] = [];
      let riskLevel: 'low' | 'medium' | 'high' | 'critical' = 'low';

      // Check processing time against hair condition
      if (formula.processingTime > 45 && diagnosis.overallCondition === 'poor') {
        warnings.push('Extended processing time on compromised hair');
        riskLevel = 'high';
        requiredPrecautions.push('Monitor closely every 10 minutes');
        recommendedTests.push('Strand test with extended timing');
      }

      // Check for chemical conflicts
      if (diagnosis.detectedChemicalProcess && 
          diagnosis.detectedChemicalProcess.includes('relaxer') &&
          formula.technique.toLowerCase().includes('bleach')) {
        contraindications.push('Bleaching over relaxed hair - CONTRAINDICATED');
        riskLevel = 'critical';
      }

      // Check developer strength against hair level
      if (formula.formula.developer.volume > 30 && diagnosis.averageLevel > 7) {
        warnings.push('High developer volume on light hair');
        riskLevel = riskLevel === 'low' ? 'medium' : riskLevel;
      }

      // Analyze detected risks
      diagnosis.detectedRisks.forEach(risk => {
        if (risk.toLowerCase().includes('metallic')) {
          contraindications.push('Metallic salts detected - chemical services contraindicated');
          riskLevel = 'critical';
        } else if (risk.toLowerCase().includes('henna')) {
          warnings.push('Natural henna present - may affect color results');
          riskLevel = 'high';
          recommendedTests.push('Strand test for henna compatibility');
        }
      });

      // Check zone analysis for uneven processing
      const zoneVariation = this.calculateZoneVariation(diagnosis.zoneAnalysis);
      if (zoneVariation > 2) {
        warnings.push('Significant level variation between zones');
        requiredPrecautions.push('Use sectioning for different processing times');
        recommendedTests.push('Multiple strand tests for each zone');
      }

      const isSafe = contraindications.length === 0 && riskLevel !== 'critical';

      const report: SafetyReport = {
        isSafe,
        riskLevel,
        warnings,
        contraindications,
        requiredPrecautions,
        recommendedTests
      };

      return {
        success: true,
        data: report
      };

    } catch (error) {
      this.logger.error('Safety validation failed', error);
      
      return {
        success: false,
        error: 'Safety validation process failed'
      };
    }
  }

  /**
   * Optimize formula based on available inventory
   */
  async optimizeFormulaForInventory(
    formula: HairFormula,
    availableProducts: ProductInventory[]
  ): Promise<Result<OptimizedFormula>> {
    try {
      const substitutions: OptimizedFormula['substitutions'] = [];
      const missingProducts: string[] = [];
      let availabilityScore = 1.0;

      // Check base color availability
      const baseColorAvailable = this.findProductMatch(formula.formula.base, availableProducts);
      if (!baseColorAvailable) {
        const substitute = this.findBestSubstitute(formula.formula.base, availableProducts);
        if (substitute) {
          substitutions.push({
            original: formula.formula.base,
            substitute: substitute.shade,
            reason: `Original ${formula.formula.base} not available - ${substitute.shade} is compatible alternative`
          });
        } else {
          missingProducts.push(formula.formula.base);
          availabilityScore *= 0.7;
        }
      }

      // Check developer availability
      const developerAvailable = availableProducts.some(p => 
        p.type.toLowerCase().includes('developer') &&
        p.volume === formula.formula.developer.volume
      );
      
      if (!developerAvailable) {
        // Find closest volume developer
        const closestDeveloper = this.findClosestDeveloper(
          formula.formula.developer.volume,
          availableProducts
        );
        
        if (closestDeveloper) {
          substitutions.push({
            original: `${formula.formula.developer.volume}vol developer`,
            substitute: `${closestDeveloper.volume}vol developer`,
            reason: 'Closest available developer volume - adjust processing time accordingly'
          });
        } else {
          missingProducts.push(`${formula.formula.developer.volume}vol developer`);
          availabilityScore *= 0.8;
        }
      }

      // Check additives availability
      formula.formula.additives.forEach(additive => {
        const available = this.findProductMatch(additive, availableProducts);
        if (!available) {
          const substitute = this.findBestSubstitute(additive, availableProducts);
          if (substitute) {
            substitutions.push({
              original: additive,
              substitute: substitute.shade,
              reason: `${additive} substituted with compatible product`
            });
            availabilityScore *= 0.9;
          } else {
            missingProducts.push(additive);
            availabilityScore *= 0.8;
          }
        }
      });

      // Update step-by-step instructions for substitutions
      let updatedSteps = [...formula.stepByStep];
      substitutions.forEach(sub => {
        updatedSteps = updatedSteps.map(step => 
          step.replace(sub.original, `${sub.substitute} (substituted)`)
        );
      });

      const optimizedFormula: OptimizedFormula = {
        ...formula,
        stepByStep: updatedSteps,
        availabilityScore,
        substitutions,
        missingProducts
      };

      // Add warnings for substitutions
      if (substitutions.length > 0) {
        optimizedFormula.warnings = [
          ...formula.warnings,
          `${substitutions.length} product substitutions made - verify compatibility`
        ];
      }

      return {
        success: true,
        data: optimizedFormula
      };

    } catch (error) {
      this.logger.error('Formula optimization failed', error);
      
      return {
        success: false,
        error: 'Formula optimization process failed'
      };
    }
  }

  /**
   * Generate alternative formulas with different approaches
   */
  async generateAlternativeFormulas(
    request: FormulationRequest,
    primaryFormula: HairFormula
  ): Promise<Result<AlternativeFormulas>> {
    try {
      // Create modified requests for alternatives
      const conservativeRequest = {
        ...request,
        serviceConstraints: {
          ...request.serviceConstraints,
          maxProcessingTime: 30,
          budgetLimit: request.serviceConstraints?.budgetLimit ? 
            request.serviceConstraints.budgetLimit * 0.8 : undefined
        }
      };

      const progressiveRequest = {
        ...request,
        serviceConstraints: {
          ...request.serviceConstraints,
          maxProcessingTime: undefined,
          appointmentType: 'premium' as const
        }
      };

      // Generate conservative formula
      const conservativeResult = await this.generateFormulaWithModifications(
        conservativeRequest,
        'conservative'
      );
      
      // Generate progressive formula
      const progressiveResult = await this.generateFormulaWithModifications(
        progressiveRequest,
        'progressive'
      );

      // Generate maintenance formula if applicable
      let maintenanceResult: Result<HairFormula> | undefined;
      if (this.shouldOfferMaintenance(primaryFormula)) {
        maintenanceResult = await this.generateMaintenanceFormula(request, primaryFormula);
      }

      const alternatives: AlternativeFormulas = {
        conservative: conservativeResult.data || this.createFallbackFormula(primaryFormula, 'conservative'),
        progressive: progressiveResult.data || this.createFallbackFormula(primaryFormula, 'progressive'),
        maintenance: maintenanceResult?.data,
        explanations: {
          conservative: 'Gentler approach with reduced processing time and lower risk',
          progressive: 'Advanced technique for optimal results with extended processing',
          maintenance: maintenanceResult?.data ? 
            'Simplified formula for touch-ups and maintenance' : undefined
        }
      };

      return {
        success: true,
        data: alternatives
      };

    } catch (error) {
      this.logger.error('Alternative formula generation failed', error);
      
      return {
        success: false,
        error: 'Alternative formula generation failed'
      };
    }
  }

  // Private helper methods

  private validateFormulationRequest(request: FormulationRequest): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!request.salonId) {
      errors.push('Salon ID is required');
    }

    if (!request.userId) {
      errors.push('User ID is required');
    }

    if (!request.currentDiagnosis) {
      errors.push('Current hair diagnosis is required');
    }

    if (!request.desiredLookImage && !request.desiredLookBase64 && !request.targetDescription) {
      errors.push('Desired look image or description is required');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  private async analyzeDesiredLook(request: FormulationRequest): Promise<Result<DesiredLookAnalysis>> {
    const analyzeRequest: AnalyzeDesiredLookRequest = {
      imageUrl: request.desiredLookImage,
      imageBase64: request.desiredLookBase64,
      currentLevel: request.currentDiagnosis.averageLevel,
      diagnosis: request.currentDiagnosis,
      salonId: request.salonId
    };

    return await this.analyzeDesiredLookUseCase.execute(analyzeRequest);
  }

  private async generateFormula(
    request: FormulationRequest,
    desiredLook: DesiredLookAnalysis
  ): Promise<Result<HairFormula>> {
    const formulaRequest: GenerateFormulaRequest = {
      currentDiagnosis: request.currentDiagnosis,
      desiredLook,
      availableProducts: request.availableProducts,
      clientPreferences: request.clientPreferences,
      salonId: request.salonId
    };

    return await this.generateFormulaUseCase.execute(formulaRequest);
  }

  private assessViability(
    diagnosis: HairDiagnosis,
    desiredLook: DesiredLookAnalysis,
    constraints?: FormulationRequest['serviceConstraints']
  ): FormulationResponse['viabilityAssessment'] {
    let confidence = desiredLook.viability.achievable ? 0.9 : 0.3;
    let achievable = desiredLook.viability.achievable;
    let sessionCount = desiredLook.viability.sessionCount;
    let timeEstimate = desiredLook.estimatedTime;
    let costEstimate = 0; // Should be calculated based on products and time
    let riskLevel = desiredLook.viability.riskLevel;

    // Adjust based on constraints
    if (constraints?.maxProcessingTime && timeEstimate > constraints.maxProcessingTime) {
      achievable = false;
      confidence *= 0.7;
    }

    if (constraints?.budgetLimit && costEstimate > constraints.budgetLimit) {
      achievable = false;
      confidence *= 0.8;
    }

    // Factor in hair condition
    if (diagnosis.overallCondition === 'poor' && riskLevel === 'low') {
      riskLevel = 'medium';
      confidence *= 0.9;
    }

    return {
      achievable,
      confidence,
      sessionCount,
      timeEstimate,
      costEstimate,
      riskLevel
    };
  }

  private determineProfessionalGuidance(
    viability: FormulationResponse['viabilityAssessment'],
    safety: SafetyReport
  ): FormulationResponse['professionalGuidance'] {
    let required = false;
    let level: 'junior' | 'senior' | 'master' = 'junior';
    const specializations: string[] = [];
    const consultation: string[] = [];

    if (safety.riskLevel === 'critical') {
      required = true;
      level = 'master';
      consultation.push('Mandatory consultation due to critical risks');
    } else if (safety.riskLevel === 'high' || viability.riskLevel === 'high') {
      required = true;
      level = 'senior';
      consultation.push('Senior colorist consultation recommended');
    } else if (viability.sessionCount > 2) {
      required = true;
      level = 'senior';
      specializations.push('Complex color transformations');
      consultation.push('Multi-session planning required');
    }

    if (safety.contraindications.length > 0) {
      required = true;
      level = 'master';
      consultation.push('Chemical contraindications present');
    }

    return {
      required,
      level,
      specializations: specializations.length > 0 ? specializations : undefined,
      consultation
    };
  }

  private generateClientCommunication(
    desiredLook: DesiredLookAnalysis,
    formula: HairFormula,
    viability: FormulationResponse['viabilityAssessment']
  ): FormulationResponse['clientCommunication'] {
    const explanation = `Based on your current hair condition and desired look, we ${
      viability.achievable ? 'can achieve' : 'cannot safely achieve'
    } this transformation ${viability.sessionCount > 1 ? `in ${viability.sessionCount} sessions` : 'in one session'}.`;

    const expectations = [
      `Processing time: approximately ${Math.round(viability.timeEstimate / 60)} hours`,
      `Expected result: ${formula.expectedResult}`,
      ...(viability.sessionCount > 1 ? [`This will require ${viability.sessionCount} appointments`] : [])
    ];

    const aftercare = formula.maintenanceInstructions;

    const maintenance = [
      'Use color-safe shampoo and conditioner',
      'Avoid heat styling when possible',
      'Schedule touch-up appointment in 6-8 weeks',
      'Protect hair from sun and chlorine'
    ];

    return {
      explanation,
      expectations,
      aftercare,
      maintenance
    };
  }

  // Additional helper methods for inventory optimization
  
  private findProductMatch(targetProduct: string, inventory: ProductInventory[]): ProductInventory | null {
    return inventory.find(p => 
      p.shade.toLowerCase().includes(targetProduct.toLowerCase()) ||
      targetProduct.toLowerCase().includes(p.shade.toLowerCase())
    ) || null;
  }

  private findBestSubstitute(targetProduct: string, inventory: ProductInventory[]): ProductInventory | null {
    // Simple substitution logic - in practice, this would be more sophisticated
    const targetWords = targetProduct.toLowerCase().split(' ');
    
    return inventory.find(p => 
      targetWords.some(word => p.shade.toLowerCase().includes(word))
    ) || null;
  }

  private findClosestDeveloper(targetVolume: number, inventory: ProductInventory[]): ProductInventory | null {
    const developers = inventory.filter(p => p.type.toLowerCase().includes('developer') && p.volume);
    
    if (developers.length === 0) return null;

    return developers.reduce((closest, current) => 
      Math.abs(current.volume! - targetVolume) < Math.abs(closest.volume! - targetVolume) 
        ? current : closest
    );
  }

  private calculateZoneVariation(zoneAnalysis: any): number {
    const levels = [zoneAnalysis.roots.level, zoneAnalysis.mids.level, zoneAnalysis.ends.level];
    return Math.max(...levels) - Math.min(...levels);
  }

  private async generateFormulaWithModifications(
    request: FormulationRequest,
    approach: 'conservative' | 'progressive'
  ): Promise<Result<HairFormula>> {
    // This would involve calling the formula use case with modified parameters
    // For now, return a placeholder
    return {
      success: false,
      error: 'Alternative formula generation not implemented'
    };
  }

  private async generateMaintenanceFormula(
    request: FormulationRequest,
    primaryFormula: HairFormula
  ): Promise<Result<HairFormula>> {
    // Generate a simplified maintenance version
    return {
      success: false,
      error: 'Maintenance formula generation not implemented'
    };
  }

  private shouldOfferMaintenance(formula: HairFormula): boolean {
    // Logic to determine if maintenance formula is applicable
    return formula.technique.toLowerCase().includes('color') && 
           !formula.technique.toLowerCase().includes('bleach');
  }

  private createFallbackFormula(primaryFormula: HairFormula, approach: string): HairFormula {
    // Create a modified version of the primary formula
    return {
      ...primaryFormula,
      technique: `${approach} ${primaryFormula.technique}`,
      warnings: [...primaryFormula.warnings, `Fallback ${approach} approach applied`]
    };
  }
}