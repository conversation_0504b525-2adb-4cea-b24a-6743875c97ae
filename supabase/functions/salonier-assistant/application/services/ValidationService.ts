/**
 * Validation Service
 * 
 * Application service that provides cross-cutting validation logic
 * across all application services. <PERSON>les request validation,
 * data sanitization, and response validation with professional
 * hair coloration safety standards.
 */

import { IValidator, ILogger, ValidationSchema, ValidationResult } from '../../services/interfaces.ts';
import { HairDiagnosis, HairFormula, DesiredLookAnalysis } from '../../types/use-case.types.ts';

export interface ValidationRequest {
  data: any;
  schema: ValidationSchema;
  context?: {
    salonId?: string;
    userRole?: 'stylist' | 'client' | 'manager';
    operation?: 'create' | 'update' | 'delete' | 'read';
  };
}

export interface SafetyValidationResult extends ValidationResult {
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  safetyWarnings: string[];
  contraindications: string[];
  requiredActions: string[];
}

export interface IValidationService extends IValidator {
  validateDiagnosis(diagnosis: HairDiagnosis): Promise<SafetyValidationResult>;
  validateFormula(formula: HairFormula, diagnosis?: HairDiagnosis): Promise<SafetyValidationResult>;
  validateDesiredLook(desiredLook: DesiredLookAnalysis): Promise<SafetyValidationResult>;
  validateBusinessRules(data: any, rules: BusinessRule[]): Promise<ValidationResult>;
  sanitizeForClient(data: any, userRole: string): any;
  validateChemicalCompatibility(
    existingChemicals: string[],
    newChemicals: string[]
  ): Promise<SafetyValidationResult>;
}

export interface BusinessRule {
  name: string;
  condition: (data: any) => boolean;
  message: string;
  severity: 'error' | 'warning' | 'info';
}

export class ValidationService implements IValidationService {
  private readonly HAIR_LEVEL_RANGE = { min: 1, max: 10 };
  private readonly PROCESSING_TIME_LIMITS = {
    bleach: 45,
    color: 35,
    toner: 20,
    gloss: 15
  };
  private readonly DANGEROUS_COMBINATIONS = [
    ['relaxer', 'bleach'],
    ['henna', 'chemical_color'],
    ['metallic_salts', 'any_chemical'],
    ['ammonia', 'bleach_high_concentration']
  ];

  constructor(private logger: ILogger) {}

  /**
   * Validate hair diagnosis with professional safety standards
   */
  async validateDiagnosis(diagnosis: HairDiagnosis): Promise<SafetyValidationResult> {
    try {
      const errors: string[] = [];
      const warnings: string[] = [];
      const safetyWarnings: string[] = [];
      const contraindications: string[] = [];
      const requiredActions: string[] = [];
      let riskLevel: SafetyValidationResult['riskLevel'] = 'low';

      // Validate essential fields
      if (!diagnosis.averageLevel || 
          diagnosis.averageLevel < this.HAIR_LEVEL_RANGE.min || 
          diagnosis.averageLevel > this.HAIR_LEVEL_RANGE.max) {
        errors.push('Hair level must be between 1-10');
        riskLevel = 'high';
      }

      if (!diagnosis.overallCondition) {
        errors.push('Overall hair condition is required');
        riskLevel = 'medium';
      }

      if (!diagnosis.overallTone) {
        warnings.push('Hair tone not specified - may affect color results');
      }

      // Validate confidence levels
      if (diagnosis.overallConfidence < 0.7) {
        safetyWarnings.push('Low diagnostic confidence - requires manual verification');
        requiredActions.push('Perform additional strand testing');
        riskLevel = 'medium';
      }

      // Validate zone analysis consistency
      if (diagnosis.zoneAnalysis) {
        const zones = [
          diagnosis.zoneAnalysis.roots,
          diagnosis.zoneAnalysis.mids,
          diagnosis.zoneAnalysis.ends
        ];

        const levelVariation = Math.max(...zones.map(z => z.level)) - 
                              Math.min(...zones.map(z => z.level));

        if (levelVariation > 3) {
          safetyWarnings.push('Significant level variation between zones');
          requiredActions.push('Consider sectional processing');
          riskLevel = riskLevel === 'low' ? 'medium' : riskLevel;
        }

        // Check for low confidence zones
        zones.forEach((zone, index) => {
          const zoneNames = ['roots', 'mids', 'ends'];
          if (zone.confidence < 0.6) {
            warnings.push(`Low confidence in ${zoneNames[index]} analysis`);
            requiredActions.push(`Re-examine ${zoneNames[index]} section`);
          }
        });
      }

      // Validate detected risks
      if (diagnosis.detectedRisks?.length > 0) {
        diagnosis.detectedRisks.forEach(risk => {
          const riskLower = risk.toLowerCase();
          
          if (riskLower.includes('severe damage') || 
              riskLower.includes('chemical burn') ||
              riskLower.includes('metallic salts')) {
            contraindications.push(risk);
            riskLevel = 'critical';
            requiredActions.push('Refuse chemical services until resolved');
          } else if (riskLower.includes('damage') || 
                    riskLower.includes('breakage') ||
                    riskLower.includes('over-processed')) {
            safetyWarnings.push(risk);
            riskLevel = riskLevel === 'low' ? 'medium' : riskLevel;
            requiredActions.push('Use protein treatment before chemical services');
          } else {
            warnings.push(risk);
          }
        });
      }

      // Validate chemical process history
      if (diagnosis.detectedChemicalProcess) {
        const processLower = diagnosis.detectedChemicalProcess.toLowerCase();
        
        if (processLower.includes('relaxer') || processLower.includes('straightener')) {
          safetyWarnings.push('Relaxed hair requires special consideration');
          requiredActions.push('Check relaxer compatibility before any chemical service');
          riskLevel = riskLevel === 'low' ? 'medium' : riskLevel;
        }

        if (processLower.includes('henna') || processLower.includes('metallic')) {
          contraindications.push('Natural/metallic deposits incompatible with chemical services');
          riskLevel = 'critical';
        }
      }

      // Validate condition-specific concerns
      if (diagnosis.overallCondition === 'poor' || diagnosis.overallCondition === 'damaged') {
        safetyWarnings.push('Compromised hair condition - proceed with caution');
        requiredActions.push('Mandatory strand test');
        requiredActions.push('Consider pre-treatment');
        riskLevel = riskLevel === 'low' ? 'medium' : riskLevel;
      }

      // Validate elasticity and porosity if provided
      if (diagnosis.elasticity === 'poor') {
        safetyWarnings.push('Poor elasticity indicates protein damage');
        requiredActions.push('Protein treatment recommended before chemical services');
      }

      return {
        valid: errors.length === 0,
        errors,
        warnings,
        riskLevel,
        safetyWarnings,
        contraindications,
        requiredActions
      };

    } catch (error) {
      this.logger.error('Diagnosis validation failed', error);
      
      return {
        valid: false,
        errors: ['Validation process failed'],
        warnings: [],
        riskLevel: 'critical',
        safetyWarnings: ['Unable to validate diagnosis safety'],
        contraindications: ['Validation failure - manual review required'],
        requiredActions: ['Manual diagnosis review required']
      };
    }
  }

  /**
   * Validate hair formula with safety and professional standards
   */
  async validateFormula(formula: HairFormula, diagnosis?: HairDiagnosis): Promise<SafetyValidationResult> {
    try {
      const errors: string[] = [];
      const warnings: string[] = [];
      const safetyWarnings: string[] = [];
      const contraindications: string[] = [];
      const requiredActions: string[] = [];
      let riskLevel: SafetyValidationResult['riskLevel'] = 'low';

      // Validate required fields
      if (!formula.technique) {
        errors.push('Technique is required');
      }

      if (!formula.processingTime || formula.processingTime <= 0) {
        errors.push('Valid processing time is required');
      }

      if (!formula.formula?.base) {
        errors.push('Base color formula is required');
      }

      if (!formula.formula?.developer?.volume || formula.formula.developer.volume <= 0) {
        errors.push('Valid developer volume is required');
      }

      // Validate processing time limits
      const technique = formula.technique.toLowerCase();
      const processingTime = formula.processingTime;

      Object.entries(this.PROCESSING_TIME_LIMITS).forEach(([process, maxTime]) => {
        if (technique.includes(process) && processingTime > maxTime) {
          safetyWarnings.push(`Processing time exceeds safe limit for ${process} (max: ${maxTime} min)`);
          requiredActions.push('Reduce processing time or consider alternative technique');
          riskLevel = 'high';
        }
      });

      // Validate developer volume
      const developerVolume = formula.formula.developer.volume;
      
      if (developerVolume > 40) {
        contraindications.push('Developer volume exceeds professional safety standards');
        riskLevel = 'critical';
      } else if (developerVolume > 30) {
        safetyWarnings.push('High volume developer requires careful monitoring');
        requiredActions.push('Monitor processing every 10 minutes');
        riskLevel = riskLevel === 'low' ? 'medium' : riskLevel;
      }

      // Cross-validate with diagnosis if provided
      if (diagnosis) {
        // Check developer volume against hair condition
        if (diagnosis.overallCondition === 'poor' && developerVolume > 20) {
          safetyWarnings.push('High developer volume on compromised hair');
          requiredActions.push('Consider lower volume developer');
          riskLevel = 'high';
        }

        // Check processing time against hair condition
        if (diagnosis.overallCondition === 'poor' && processingTime > 30) {
          safetyWarnings.push('Extended processing time on damaged hair');
          requiredActions.push('Reduce processing time by 25%');
          riskLevel = 'high';
        }

        // Check for chemical incompatibilities
        const detectedChemicals = this.extractChemicalsFromDiagnosis(diagnosis);
        const formulaChemicals = this.extractChemicalsFromFormula(formula);
        
        const compatibilityResult = await this.validateChemicalCompatibility(
          detectedChemicals,
          formulaChemicals
        );

        if (!compatibilityResult.valid) {
          contraindications.push(...compatibilityResult.contraindications);
          safetyWarnings.push(...compatibilityResult.safetyWarnings);
          riskLevel = 'critical';
        }
      }

      // Validate mixing ratios
      if (formula.formula.mixingRatio) {
        const ratioValid = this.validateMixingRatio(formula.formula.mixingRatio);
        if (!ratioValid) {
          warnings.push('Mixing ratio may not be standard - verify measurements');
        }
      }

      // Validate step-by-step instructions
      if (!formula.stepByStep || formula.stepByStep.length === 0) {
        warnings.push('No application instructions provided');
      } else {
        // Check for safety mentions in instructions
        const hasStrandardTest = formula.stepByStep.some(step => 
          step.toLowerCase().includes('strand test') || 
          step.toLowerCase().includes('patch test')
        );
        
        if (!hasStrandardTest && riskLevel !== 'low') {
          requiredActions.push('Add strand test to instructions');
        }
      }

      // Validate warnings are appropriate
      if (formula.warnings && formula.warnings.length === 0 && riskLevel !== 'low') {
        warnings.push('Formula should include appropriate safety warnings');
      }

      return {
        valid: errors.length === 0 && contraindications.length === 0,
        errors,
        warnings,
        riskLevel,
        safetyWarnings,
        contraindications,
        requiredActions
      };

    } catch (error) {
      this.logger.error('Formula validation failed', error);
      
      return {
        valid: false,
        errors: ['Formula validation process failed'],
        warnings: [],
        riskLevel: 'critical',
        safetyWarnings: ['Unable to validate formula safety'],
        contraindications: ['Validation failure - manual review required'],
        requiredActions: ['Manual formula review required']
      };
    }
  }

  /**
   * Validate desired look analysis for achievability and safety
   */
  async validateDesiredLook(desiredLook: DesiredLookAnalysis): Promise<SafetyValidationResult> {
    try {
      const errors: string[] = [];
      const warnings: string[] = [];
      const safetyWarnings: string[] = [];
      const contraindications: string[] = [];
      const requiredActions: string[] = [];
      let riskLevel: SafetyValidationResult['riskLevel'] = 'low';

      // Validate target level
      if (!desiredLook.targetLevel || 
          desiredLook.targetLevel < this.HAIR_LEVEL_RANGE.min || 
          desiredLook.targetLevel > this.HAIR_LEVEL_RANGE.max) {
        errors.push('Target level must be between 1-10');
      }

      // Validate viability assessment
      if (!desiredLook.viability) {
        errors.push('Viability assessment is required');
      } else {
        if (desiredLook.viability.riskLevel === 'high' || desiredLook.viability.riskLevel === 'medium') {
          safetyWarnings.push(`${desiredLook.viability.riskLevel} risk transformation identified`);
          riskLevel = desiredLook.viability.riskLevel;
        }

        if (!desiredLook.viability.achievable) {
          contraindications.push('Desired look not safely achievable with current hair condition');
          requiredActions.push('Discuss alternative options with client');
          riskLevel = 'high';
        }

        if (desiredLook.viability.sessionCount > 3) {
          warnings.push('Multiple sessions required - manage client expectations');
          requiredActions.push('Create detailed session plan');
        }

        // Validate concerns
        if (desiredLook.viability.concerns && desiredLook.viability.concerns.length > 0) {
          desiredLook.viability.concerns.forEach(concern => {
            if (concern.toLowerCase().includes('damage') || 
                concern.toLowerCase().includes('breakage')) {
              safetyWarnings.push(concern);
              riskLevel = riskLevel === 'low' ? 'medium' : riskLevel;
            } else {
              warnings.push(concern);
            }
          });
        }
      }

      // Validate colorimetry
      if (desiredLook.colorimetryValidation) {
        if (!desiredLook.colorimetryValidation.valid) {
          warnings.push('Color theory concerns identified');
          
          if (desiredLook.colorimetryValidation.warnings) {
            desiredLook.colorimetryValidation.warnings.forEach(warning => {
              if (warning.toLowerCase().includes('clash') || 
                  warning.toLowerCase().includes('unflattering')) {
                warnings.push(`Color concern: ${warning}`);
                requiredActions.push('Discuss color theory with client');
              }
            });
          }
        }
      }

      // Validate estimated time
      if (desiredLook.estimatedTime && desiredLook.estimatedTime > 240) {
        warnings.push('Extended appointment time required (>4 hours)');
        requiredActions.push('Schedule longer appointment or split into sessions');
      }

      // Validate required products
      if (!desiredLook.requiredProducts || desiredLook.requiredProducts.length === 0) {
        warnings.push('No specific products identified');
      }

      return {
        valid: errors.length === 0 && contraindications.length === 0,
        errors,
        warnings,
        riskLevel,
        safetyWarnings,
        contraindications,
        requiredActions
      };

    } catch (error) {
      this.logger.error('Desired look validation failed', error);
      
      return {
        valid: false,
        errors: ['Desired look validation process failed'],
        warnings: [],
        riskLevel: 'critical',
        safetyWarnings: ['Unable to validate desired look safety'],
        contraindications: ['Validation failure - manual review required'],
        requiredActions: ['Manual desired look review required']
      };
    }
  }

  /**
   * Validate chemical compatibility between existing and new chemicals
   */
  async validateChemicalCompatibility(
    existingChemicals: string[],
    newChemicals: string[]
  ): Promise<SafetyValidationResult> {
    try {
      const errors: string[] = [];
      const warnings: string[] = [];
      const safetyWarnings: string[] = [];
      const contraindications: string[] = [];
      const requiredActions: string[] = [];
      let riskLevel: SafetyValidationResult['riskLevel'] = 'low';

      // Normalize chemical names for comparison
      const normalizeChemical = (chem: string) => chem.toLowerCase().trim();
      const existingNormalized = existingChemicals.map(normalizeChemical);
      const newNormalized = newChemicals.map(normalizeChemical);

      // Check dangerous combinations
      this.DANGEROUS_COMBINATIONS.forEach(([chemical1, chemical2]) => {
        const hasChemical1 = existingNormalized.some(c => c.includes(chemical1));
        const hasChemical2 = newNormalized.some(c => 
          chemical2 === 'any_chemical' || c.includes(chemical2)
        );

        if (hasChemical1 && hasChemical2) {
          contraindications.push(`Dangerous combination: ${chemical1} + ${chemical2}`);
          requiredActions.push('Refuse service - chemical incompatibility');
          riskLevel = 'critical';
        }
      });

      // Check for specific problematic combinations
      if (existingNormalized.some(c => c.includes('relaxer'))) {
        if (newNormalized.some(c => c.includes('bleach') || c.includes('lightener'))) {
          contraindications.push('Cannot bleach over relaxed hair');
          riskLevel = 'critical';
        }
        
        if (newNormalized.some(c => c.includes('ammonia'))) {
          safetyWarnings.push('High pH chemicals on relaxed hair require extreme caution');
          requiredActions.push('Use low-ammonia or ammonia-free alternatives');
          riskLevel = 'high';
        }
      }

      if (existingNormalized.some(c => c.includes('henna'))) {
        if (newNormalized.some(c => c.includes('bleach') || c.includes('color'))) {
          contraindications.push('Henna incompatible with chemical color services');
          riskLevel = 'critical';
        }
      }

      if (existingNormalized.some(c => c.includes('metallic'))) {
        contraindications.push('Metallic salts present - all chemical services contraindicated');
        riskLevel = 'critical';
      }

      // Check for timing concerns
      const recentChemicals = existingNormalized.filter(c => 
        c.includes('recent') || c.includes('fresh')
      );
      
      if (recentChemicals.length > 0) {
        safetyWarnings.push('Recent chemical service detected');
        requiredActions.push('Wait appropriate time between chemical services');
        riskLevel = 'medium';
      }

      return {
        valid: contraindications.length === 0,
        errors,
        warnings,
        riskLevel,
        safetyWarnings,
        contraindications,
        requiredActions
      };

    } catch (error) {
      this.logger.error('Chemical compatibility validation failed', error);
      
      return {
        valid: false,
        errors: ['Chemical compatibility validation failed'],
        warnings: [],
        riskLevel: 'critical',
        safetyWarnings: ['Unable to validate chemical compatibility'],
        contraindications: ['Validation failure - assume incompatibility'],
        requiredActions: ['Manual compatibility review required']
      };
    }
  }

  // Base interface implementation
  
  validateRequest<T>(request: T, schema: ValidationSchema): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check required fields
    if (schema.required) {
      schema.required.forEach(field => {
        if (!(request as any)[field]) {
          errors.push(`${field} is required`);
        }
      });
    }

    // Check data types if specified
    if (schema.types) {
      Object.entries(schema.types).forEach(([field, expectedType]) => {
        const value = (request as any)[field];
        if (value !== undefined) {
          const actualType = typeof value;
          if (actualType !== expectedType) {
            errors.push(`${field} must be of type ${expectedType}`);
          }
        }
      });
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  validateAIResponse(response: any, expectedFields: string[]): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    expectedFields.forEach(field => {
      if (!response[field]) {
        errors.push(`AI response missing required field: ${field}`);
      }
    });

    // Check response structure
    if (typeof response !== 'object') {
      errors.push('AI response must be an object');
    }

    // Check for common AI response issues
    if (response && typeof response === 'object') {
      const responseStr = JSON.stringify(response);
      if (responseStr.includes('I cannot') || responseStr.includes('I am unable')) {
        warnings.push('AI response indicates inability to complete request');
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  sanitizeForLog(data: any): any {
    if (!data || typeof data !== 'object') {
      return data;
    }

    const sanitized = { ...data };
    const sensitiveFields = [
      'password', 'token', 'secret', 'key', 'apiKey', 
      'personalInfo', 'phoneNumber', 'email', 'address'
    ];

    const sanitizeRecursive = (obj: any): any => {
      if (Array.isArray(obj)) {
        return obj.map(sanitizeRecursive);
      }
      
      if (obj && typeof obj === 'object') {
        const result: any = {};
        Object.keys(obj).forEach(key => {
          const keyLower = key.toLowerCase();
          if (sensitiveFields.some(field => keyLower.includes(field))) {
            result[key] = '[REDACTED]';
          } else {
            result[key] = sanitizeRecursive(obj[key]);
          }
        });
        return result;
      }
      
      return obj;
    };

    return sanitizeRecursive(sanitized);
  }

  async validateBusinessRules(data: any, rules: BusinessRule[]): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    rules.forEach(rule => {
      try {
        if (!rule.condition(data)) {
          switch (rule.severity) {
            case 'error':
              errors.push(rule.message);
              break;
            case 'warning':
              warnings.push(rule.message);
              break;
            case 'info':
              // Info messages don't affect validation
              break;
          }
        }
      } catch (error) {
        this.logger.error(`Business rule validation failed: ${rule.name}`, error);
        errors.push(`Business rule '${rule.name}' validation failed`);
      }
    });

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  sanitizeForClient(data: any, userRole: string): any {
    if (!data || typeof data !== 'object') {
      return data;
    }

    const sanitized = { ...data };

    // Remove sensitive fields based on user role
    if (userRole === 'client') {
      const clientRestrictedFields = [
        'cost', 'profit', 'internalNotes', 'systemData',
        'debugInfo', 'adminNotes', 'businessMetrics'
      ];

      clientRestrictedFields.forEach(field => {
        delete sanitized[field];
      });

      // Simplify technical terminology
      if (sanitized.diagnosis) {
        sanitized.diagnosis = this.simplifyTechnicalTerms(sanitized.diagnosis);
      }

      if (sanitized.formula) {
        sanitized.formula = this.simplifyFormulaForClient(sanitized.formula);
      }
    }

    return sanitized;
  }

  // Private helper methods

  private extractChemicalsFromDiagnosis(diagnosis: HairDiagnosis): string[] {
    const chemicals: string[] = [];
    
    if (diagnosis.detectedChemicalProcess) {
      chemicals.push(diagnosis.detectedChemicalProcess);
    }
    
    if (diagnosis.detectedRisks) {
      diagnosis.detectedRisks.forEach(risk => {
        if (risk.toLowerCase().includes('chemical') || 
            risk.toLowerCase().includes('relaxer') ||
            risk.toLowerCase().includes('henna') ||
            risk.toLowerCase().includes('metallic')) {
          chemicals.push(risk);
        }
      });
    }

    return chemicals;
  }

  private extractChemicalsFromFormula(formula: HairFormula): string[] {
    const chemicals: string[] = [];
    
    if (formula.technique) {
      chemicals.push(formula.technique);
    }
    
    if (formula.formula.base) {
      chemicals.push(formula.formula.base);
    }
    
    if (formula.formula.additives) {
      chemicals.push(...formula.formula.additives);
    }
    
    if (formula.formula.developer) {
      chemicals.push(`${formula.formula.developer.volume}vol developer`);
    }

    return chemicals;
  }

  private validateMixingRatio(ratio: string): boolean {
    // Simple ratio validation (e.g., "1:1", "2:1", "1:1:0.5")
    const ratioRegex = /^\d+(\.\d+)?(:\d+(\.\d+)?)+$/;
    return ratioRegex.test(ratio);
  }

  private simplifyTechnicalTerms(diagnosis: any): any {
    const simplified = { ...diagnosis };
    
    // Mapping of technical terms to client-friendly terms
    const termMapping: Record<string, string> = {
      'porosity': 'absorption level',
      'elasticity': 'hair strength',
      'cuticle': 'hair surface',
      'cortex': 'hair core',
      'melanin': 'natural pigment'
    };

    Object.keys(termMapping).forEach(technical => {
      const simplified_term = termMapping[technical];
      if (simplified[technical]) {
        simplified[simplified_term] = simplified[technical];
        delete simplified[technical];
      }
    });

    return simplified;
  }

  private simplifyFormulaForClient(formula: any): any {
    const simplified = { ...formula };
    
    // Simplify technical formula details for client consumption
    if (simplified.formula) {
      // Keep essential information, remove technical details
      simplified.colorRecipe = {
        mainColor: simplified.formula.base,
        activator: `${simplified.formula.developer.volume} volume`,
        processingTime: `${simplified.processingTime} minutes`
      };
      
      delete simplified.formula; // Remove technical formula details
    }

    return simplified;
  }
}