/**
 * Diagnosis Service
 * 
 * Application service that orchestrates hair diagnosis workflows.
 * Coordinates multiple use cases and manages the complete diagnosis process
 * from image analysis to comprehensive hair assessment.
 */

import { DiagnoseImageUseCase } from '../../use-cases/DiagnoseImageUseCase.ts';
import { I<PERSON>acheService, ILogger, IValidator } from '../../services/interfaces.ts';
import { HairDiagnosis, DiagnoseImageRequest, Result } from '../../types/use-case.types.ts';

export interface DiagnosisRequest {
  imageUrl?: string;
  imageBase64?: string;
  salonId: string;
  userId: string;
  clientId?: string;
  requestId?: string;
  metadata?: {
    clientAge?: number;
    previousServices?: string[];
    skinTone?: string;
    naturalHairColor?: string;
  };
}

export interface DiagnosisResponse {
  diagnosis: HairDiagnosis;
  confidence: number;
  processingTime: number;
  recommendedActions: string[];
  riskAssessment: {
    level: 'low' | 'medium' | 'high' | 'critical';
    factors: string[];
    recommendations: string[];
  };
  nextSteps: {
    immediate: string[];
    followUp: string[];
    consultation?: boolean;
  };
  metadata: {
    model: string;
    tokens: number;
    cost: number;
    cached: boolean;
    timestamp: Date;
  };
}

export interface IDiagnosisService {
  processImageDiagnosis(request: DiagnosisRequest): Promise<Result<DiagnosisResponse>>;
  validateDiagnosisResults(diagnosis: HairDiagnosis): Promise<Result<ValidationReport>>;
  enhanceDiagnosisWithContext(
    diagnosis: HairDiagnosis,
    clientHistory?: any,
    salonPreferences?: any
  ): Promise<Result<EnhancedDiagnosis>>;
}

export interface ValidationReport {
  isValid: boolean;
  confidence: number;
  warnings: string[];
  criticalIssues: string[];
  recommendations: string[];
}

export interface EnhancedDiagnosis extends HairDiagnosis {
  contextualRecommendations: string[];
  historicalComparison?: {
    previousLevel: number;
    progressionAnalysis: string;
    trendPrediction: string;
  };
  salonSpecificNotes: string[];
}

export class DiagnosisService implements IDiagnosisService {
  constructor(
    private diagnoseImageUseCase: DiagnoseImageUseCase,
    private cacheService: ICacheService,
    private validator: IValidator,
    private logger: ILogger
  ) {}

  /**
   * Process complete image diagnosis workflow
   * Orchestrates image analysis, validation, and enhancement
   */
  async processImageDiagnosis(request: DiagnosisRequest): Promise<Result<DiagnosisResponse>> {
    const startTime = Date.now();
    
    try {
      // Log request initiation
      this.logger.info('Starting diagnosis process', {
        salonId: request.salonId,
        requestId: request.requestId,
        hasImage: !!(request.imageUrl || request.imageBase64)
      });

      // Validate input request
      const validationResult = this.validateDiagnosisRequest(request);
      if (!validationResult.valid) {
        this.logger.warn('Diagnosis request validation failed', {
          errors: validationResult.errors
        });
        return {
          success: false,
          error: `Validation failed: ${validationResult.errors.join(', ')}`
        };
      }

      // Build diagnosis request
      const diagnosisRequest: DiagnoseImageRequest = {
        imageUrl: request.imageUrl,
        imageBase64: request.imageBase64,
        salonId: request.salonId
      };

      // Execute image diagnosis use case
      const diagnosisResult = await this.diagnoseImageUseCase.execute(diagnosisRequest);
      
      if (!diagnosisResult.success || !diagnosisResult.data) {
        this.logger.error('Diagnosis use case failed', {
          error: diagnosisResult.error
        });
        return {
          success: false,
          error: diagnosisResult.error || 'Diagnosis failed'
        };
      }

      // Validate diagnosis results
      const validationReport = await this.validateDiagnosisResults(diagnosisResult.data);
      if (!validationReport.success || !validationReport.data?.isValid) {
        this.logger.warn('Diagnosis validation failed', {
          warnings: validationReport.data?.warnings,
          criticalIssues: validationReport.data?.criticalIssues
        });
      }

      // Enhance diagnosis with context if available
      let enhancedDiagnosis = diagnosisResult.data;
      if (request.metadata || request.clientId) {
        const enhancementResult = await this.enhanceDiagnosisWithContext(
          diagnosisResult.data,
          request.metadata
        );
        
        if (enhancementResult.success && enhancementResult.data) {
          enhancedDiagnosis = enhancementResult.data;
        }
      }

      // Calculate risk assessment
      const riskAssessment = this.calculateRiskAssessment(enhancedDiagnosis);
      
      // Determine next steps
      const nextSteps = this.determineNextSteps(enhancedDiagnosis, riskAssessment);

      // Calculate processing time
      const processingTime = Date.now() - startTime;

      // Build response
      const response: DiagnosisResponse = {
        diagnosis: enhancedDiagnosis,
        confidence: enhancedDiagnosis.overallConfidence,
        processingTime,
        recommendedActions: enhancedDiagnosis.recommendations,
        riskAssessment,
        nextSteps,
        metadata: {
          model: 'gpt-4o', // This should come from the use case result
          tokens: 0, // This should come from the use case result
          cost: 0, // This should come from the use case result
          cached: false, // This should come from cache check
          timestamp: new Date()
        }
      };

      this.logger.info('Diagnosis process completed successfully', {
        salonId: request.salonId,
        confidence: response.confidence,
        processingTime,
        riskLevel: riskAssessment.level
      });

      return {
        success: true,
        data: response
      };

    } catch (error) {
      this.logger.error('Diagnosis process failed with exception', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown diagnosis error'
      };
    }
  }

  /**
   * Validate diagnosis results for accuracy and completeness
   */
  async validateDiagnosisResults(diagnosis: HairDiagnosis): Promise<Result<ValidationReport>> {
    try {
      const warnings: string[] = [];
      const criticalIssues: string[] = [];
      let confidence = diagnosis.overallConfidence;

      // Validate essential fields
      if (!diagnosis.averageLevel || diagnosis.averageLevel < 1 || diagnosis.averageLevel > 10) {
        criticalIssues.push('Invalid hair level detected');
        confidence *= 0.5;
      }

      if (!diagnosis.overallTone) {
        warnings.push('Hair tone not clearly identified');
        confidence *= 0.9;
      }

      if (!diagnosis.overallCondition) {
        warnings.push('Hair condition assessment incomplete');
        confidence *= 0.9;
      }

      // Validate zone analysis consistency
      const zones = [diagnosis.zoneAnalysis.roots, diagnosis.zoneAnalysis.mids, diagnosis.zoneAnalysis.ends];
      const levelVariation = Math.max(...zones.map(z => z.level)) - Math.min(...zones.map(z => z.level));
      
      if (levelVariation > 4) {
        warnings.push('Significant level variation between zones - may require sectional processing');
      }

      // Check for conflicting assessments
      if (diagnosis.overallCondition === 'excellent' && diagnosis.detectedRisks.length > 2) {
        warnings.push('Conflicting condition and risk assessments');
        confidence *= 0.8;
      }

      // Validate confidence thresholds
      if (confidence < 0.7) {
        criticalIssues.push('Overall confidence too low for safe processing');
      } else if (confidence < 0.8) {
        warnings.push('Moderate confidence - consider additional consultation');
      }

      const report: ValidationReport = {
        isValid: criticalIssues.length === 0,
        confidence: Math.max(0.1, Math.min(1.0, confidence)),
        warnings,
        criticalIssues,
        recommendations: this.generateValidationRecommendations(warnings, criticalIssues)
      };

      return {
        success: true,
        data: report
      };

    } catch (error) {
      this.logger.error('Diagnosis validation failed', error);
      
      return {
        success: false,
        error: 'Validation process failed'
      };
    }
  }

  /**
   * Enhance diagnosis with contextual information
   */
  async enhanceDiagnosisWithContext(
    diagnosis: HairDiagnosis,
    clientHistory?: any,
    salonPreferences?: any
  ): Promise<Result<EnhancedDiagnosis>> {
    try {
      const enhanced: EnhancedDiagnosis = {
        ...diagnosis,
        contextualRecommendations: [],
        salonSpecificNotes: []
      };

      // Add contextual recommendations based on client history
      if (clientHistory) {
        if (clientHistory.previousServices?.includes('bleaching')) {
          enhanced.contextualRecommendations.push(
            'Consider protein treatment due to previous bleaching history'
          );
        }

        if (clientHistory.skinTone === 'cool' && diagnosis.overallTone.includes('warm')) {
          enhanced.contextualRecommendations.push(
            'Cool-toned client with warm undertones - consider ash series for neutralization'
          );
        }

        // Historical comparison if available
        if (clientHistory.previousLevel) {
          enhanced.historicalComparison = {
            previousLevel: clientHistory.previousLevel,
            progressionAnalysis: this.analyzeHairProgression(
              clientHistory.previousLevel,
              diagnosis.averageLevel
            ),
            trendPrediction: this.predictHairTrend(clientHistory, diagnosis)
          };
        }
      }

      // Add salon-specific notes
      if (salonPreferences) {
        if (salonPreferences.preferredBrands) {
          enhanced.salonSpecificNotes.push(
            `Recommended brands: ${salonPreferences.preferredBrands.join(', ')}`
          );
        }
      }

      return {
        success: true,
        data: enhanced
      };

    } catch (error) {
      this.logger.error('Context enhancement failed', error);
      
      return {
        success: false,
        error: 'Context enhancement failed'
      };
    }
  }

  // Private helper methods
  
  private validateDiagnosisRequest(request: DiagnosisRequest): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!request.salonId) {
      errors.push('Salon ID is required');
    }

    if (!request.userId) {
      errors.push('User ID is required');
    }

    if (!request.imageUrl && !request.imageBase64) {
      errors.push('Either image URL or base64 image data is required');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  private calculateRiskAssessment(diagnosis: HairDiagnosis): DiagnosisResponse['riskAssessment'] {
    const riskFactors: string[] = [];
    let riskLevel: 'low' | 'medium' | 'high' | 'critical' = 'low';

    // Analyze detected risks
    if (diagnosis.detectedRisks.length > 0) {
      riskFactors.push(...diagnosis.detectedRisks);
      
      if (diagnosis.detectedRisks.some(risk => 
        risk.toLowerCase().includes('severe') || 
        risk.toLowerCase().includes('critical') ||
        risk.toLowerCase().includes('chemical')
      )) {
        riskLevel = 'critical';
      } else if (diagnosis.detectedRisks.length > 2) {
        riskLevel = 'high';
      } else {
        riskLevel = 'medium';
      }
    }

    // Assess condition-based risks
    if (diagnosis.overallCondition === 'poor' || diagnosis.overallCondition === 'damaged') {
      riskFactors.push('Hair condition requires careful treatment');
      riskLevel = riskLevel === 'low' ? 'medium' : riskLevel;
    }

    // Confidence-based risk adjustment
    if (diagnosis.overallConfidence < 0.7) {
      riskFactors.push('Low diagnostic confidence');
      riskLevel = riskLevel === 'low' ? 'medium' : 'high';
    }

    const recommendations = this.generateRiskRecommendations(riskLevel, riskFactors);

    return {
      level: riskLevel,
      factors: riskFactors,
      recommendations
    };
  }

  private determineNextSteps(
    diagnosis: HairDiagnosis,
    riskAssessment: DiagnosisResponse['riskAssessment']
  ): DiagnosisResponse['nextSteps'] {
    const immediate: string[] = [];
    const followUp: string[] = [];
    let consultation = false;

    // Determine immediate actions
    if (riskAssessment.level === 'critical') {
      immediate.push('Stop - Consult senior colorist before proceeding');
      immediate.push('Perform additional strand test');
      consultation = true;
    } else if (riskAssessment.level === 'high') {
      immediate.push('Perform strand test on inconspicuous area');
      immediate.push('Consider protective pre-treatment');
      consultation = true;
    } else if (riskAssessment.level === 'medium') {
      immediate.push('Review client hair history');
      immediate.push('Consider patch test if using new products');
    } else {
      immediate.push('Proceed with standard color consultation');
    }

    // Determine follow-up actions
    if (diagnosis.serviceComplexity === 'high') {
      followUp.push('Schedule extended appointment time');
      followUp.push('Prepare specialized products');
    }

    if (diagnosis.estimatedTime > 180) {
      followUp.push('Consider splitting service across multiple appointments');
    }

    followUp.push('Document results for client history');
    followUp.push('Schedule follow-up consultation in 4-6 weeks');

    return {
      immediate,
      followUp,
      consultation
    };
  }

  private generateValidationRecommendations(warnings: string[], criticalIssues: string[]): string[] {
    const recommendations: string[] = [];

    if (criticalIssues.length > 0) {
      recommendations.push('Repeat diagnosis with additional images');
      recommendations.push('Perform manual assessment to verify results');
    }

    if (warnings.length > 2) {
      recommendations.push('Consider professional consultation');
    }

    if (warnings.some(w => w.includes('confidence'))) {
      recommendations.push('Perform strand test before full application');
    }

    return recommendations;
  }

  private generateRiskRecommendations(riskLevel: string, factors: string[]): string[] {
    const recommendations: string[] = [];

    switch (riskLevel) {
      case 'critical':
        recommendations.push('Mandatory consultation with senior colorist');
        recommendations.push('Extensive strand testing required');
        recommendations.push('Consider refusing service if risks too high');
        break;
      case 'high':
        recommendations.push('Perform comprehensive consultation');
        recommendations.push('Multiple strand tests recommended');
        recommendations.push('Use conservative approach');
        break;
      case 'medium':
        recommendations.push('Standard consultation and strand test');
        recommendations.push('Monitor closely during processing');
        break;
      case 'low':
        recommendations.push('Standard safety protocols apply');
        break;
    }

    return recommendations;
  }

  private analyzeHairProgression(previousLevel: number, currentLevel: number): string {
    const difference = currentLevel - previousLevel;
    
    if (difference > 1) {
      return 'Hair has lightened significantly since last service';
    } else if (difference < -1) {
      return 'Hair has darkened since last service - may indicate fading or new growth';
    } else {
      return 'Hair level remains relatively stable';
    }
  }

  private predictHairTrend(clientHistory: any, diagnosis: HairDiagnosis): string {
    // Simple trend prediction based on available data
    if (clientHistory.previousServices?.includes('bleaching') && diagnosis.overallCondition === 'good') {
      return 'Hair recovery trending positively - suitable for continued color services';
    } else if (diagnosis.detectedRisks.length > clientHistory.previousRiskCount || 0) {
      return 'Increasing risk factors - recommend more conservative approach';
    } else {
      return 'Stable hair condition - maintain current care routine';
    }
  }
}