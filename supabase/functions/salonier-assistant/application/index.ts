/**
 * Application Layer Index
 * 
 * Central export point for the application layer of the salonier-assistant Edge Function.
 * This layer orchestrates Use Cases and coordinates between the domain and infrastructure layers.
 * 
 * The Application Layer implements:
 * - Service orchestration and workflow coordination
 * - Request/response handling and transformation
 * - Cross-cutting concerns (validation, caching, error handling)
 * - HTTP request processing and response formatting
 * - Business workflow orchestration
 */

// Application Services - Core business workflow orchestration
export { DiagnosisService, type IDiagnosisService, type DiagnosisRequest, type DiagnosisResponse } from './services/DiagnosisService.ts';
export { FormulationService, type IFormulationService, type FormulationRequest, type FormulationResponse } from './services/FormulationService.ts';
export { ChatService, type IChatService, type EnhancedChatRequest, type EnhancedChatResponse } from './services/ChatService.ts';
export { AdvancedCacheService, type IAdvancedCacheService, type CacheAnalytics, type CacheOptimization } from './services/CacheService.ts';
export { ValidationService, type IValidationService, type SafetyValidationResult, type BusinessRule } from './services/ValidationService.ts';

// Request Handlers - HTTP request processing and response formatting
export { DiagnoseImageHandler, type DiagnoseImageRequestBody, type DiagnoseImageResponse } from './handlers/DiagnoseImageHandler.ts';
export { GenerateFormulaHandler, type GenerateFormulaRequestBody, type GenerateFormulaResponse } from './handlers/GenerateFormulaHandler.ts';
export { ChatAssistantHandler, type ChatRequestBody, type ChatResponse } from './handlers/ChatAssistantHandler.ts';
export { UploadPhotoHandler, type UploadPhotoRequestBody, type UploadPhotoResponse } from './handlers/UploadPhotoHandler.ts';
export { ConvertFormulaHandler, type ConvertFormulaRequestBody, type ConvertFormulaResponse } from './handlers/ConvertFormulaHandler.ts';

// Application Utilities - Cross-cutting concerns and helpers
export { RequestMapper, type MappingResult, type AuthContext } from './utils/RequestMapper.ts';
export { ResponseFormatter, type ResponseMetadata, type ErrorDetails, type FormattedResponse } from './utils/ResponseFormatter.ts';
export { ErrorHandler, type ErrorContext, type ErrorMetrics, type ErrorRecoveryOptions } from './utils/ErrorHandler.ts';
export { ValidationHelper, type ValidationRule, type ValidationResult, type SanitizerOptions } from './utils/ValidationHelper.ts';

/**
 * Application Layer Architecture Overview:
 * 
 * ┌─────────────────────────────────────────────────────────────┐
 * │                    HTTP Request                             │
 * └─────────────────────┬───────────────────────────────────────┘
 *                       │
 * ┌─────────────────────▼───────────────────────────────────────┐
 * │                Request Handlers                             │
 * │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
 * │  │ DiagnoseImage   │  │ GenerateFormula │  │ ChatAssist. │ │
 * │  │ Handler         │  │ Handler         │  │ Handler     │ │
 * │  └─────────────────┘  └─────────────────┘  └─────────────┘ │
 * └─────────────────────┬───────────────────────────────────────┘
 *                       │
 * ┌─────────────────────▼───────────────────────────────────────┐
 * │              Application Services                           │
 * │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
 * │  │ Diagnosis       │  │ Formulation     │  │ Chat        │ │
 * │  │ Service         │  │ Service         │  │ Service     │ │
 * │  └─────────────────┘  └─────────────────┘  └─────────────┘ │
 * └─────────────────────┬───────────────────────────────────────┘
 *                       │
 * ┌─────────────────────▼───────────────────────────────────────┐
 * │                  Use Cases                                  │
 * │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
 * │  │ DiagnoseImage   │  │ GenerateFormula │  │ ChatAssist. │ │
 * │  │ UseCase         │  │ UseCase         │  │ UseCase     │ │
 * │  └─────────────────┘  └─────────────────┘  └─────────────┘ │
 * └─────────────────────┬───────────────────────────────────────┘
 *                       │
 * ┌─────────────────────▼───────────────────────────────────────┐
 * │               Domain + Infrastructure                       │
 * └─────────────────────────────────────────────────────────────┘
 * 
 * Key Responsibilities:
 * 
 * 1. **Request Handlers**: Convert HTTP requests to internal format
 *    - Parse and validate request bodies
 *    - Extract authentication context
 *    - Map to service layer requests
 *    - Format responses for HTTP clients
 * 
 * 2. **Application Services**: Orchestrate business workflows
 *    - Coordinate multiple Use Cases
 *    - Implement complex business workflows
 *    - Handle cross-cutting concerns
 *    - Manage transaction boundaries
 * 
 * 3. **Utilities**: Provide cross-cutting functionality
 *    - Request/response transformation
 *    - Validation and sanitization
 *    - Error handling and recovery
 *    - Caching and performance optimization
 * 
 * Design Principles:
 * - Clean separation between HTTP concerns and business logic
 * - Dependency injection for testability
 * - Comprehensive error handling and logging
 * - Performance optimization through caching
 * - Security through validation and sanitization
 * - Professional salon safety standards enforcement
 */

// Type re-exports for convenience
export type {
  // Service interfaces
  IntentAnalysis,
  ContextualResponse,
  ConversationContext,
  
  // Validation types
  SafetyReport,
  OptimizedFormula,
  AlternativeFormulas,
  
  // Cache types
  CacheHitResult,
  CachePrediction,
  CacheExport,
  CacheEntry,
  
  // Error handling types
  ErrorContext,
  ErrorRecoveryOptions,
  ErrorMetrics
} from './services/ChatService.ts';