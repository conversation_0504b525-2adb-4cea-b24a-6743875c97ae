/**
 * Chat Assistant Handler
 * 
 * HTTP request handler for conversational AI interactions.
 * Manages chat sessions, context preservation, and response formatting
 * for professional hair coloration assistance.
 */

import { ChatService, EnhancedChatRequest, EnhancedChatResponse } from '../services/ChatService.ts';
import { RequestMapper } from '../utils/RequestMapper.ts';
import { ResponseFormatter } from '../utils/ResponseFormatter.ts';
import { <PERSON>rrorHand<PERSON> } from '../utils/ErrorHandler.ts';
import { ILogger } from '../../services/interfaces.ts';
import { ChatMessage } from '../../types/use-case.types.ts';

export interface ChatRequestBody {
  message: string;
  conversationHistory?: ChatMessage[];
  context?: {
    currentService?: {
      stage?: 'diagnosis' | 'formulation' | 'application' | 'completed';
      diagnosis?: any;
      formula?: any;
      clientId?: string;
    };
    clientProfile?: {
      name?: string;
      preferences?: string[];
      allergies?: string[];
      previousServices?: string[];
    };
  };
  sessionId?: string;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  options?: {
    includeFollowUp?: boolean;
    detailedResponse?: boolean;
    includeSafetyInfo?: boolean;
  };
}

export interface ChatResponse {
  success: boolean;
  data?: {
    message: string;
    intent: string;
    confidence: number;
    responseType: string;
    suggestedActions?: Array<{
      action: string;
      description: string;
      priority: string;
    }>;
    followUpQuestions?: string[];
    requiresHumanIntervention: boolean;
    professionalContext: {
      technicalLevel: string;
      safetyConsiderations: string[];
      references?: string[];
    };
    conversationContext: {
      sessionLength: number;
      topicProgression: string[];
      satisfactionSignals: string[];
    };
  };
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata: {
    requestId: string;
    timestamp: string;
    sessionId?: string;
    processingTime: number;
    cached: boolean;
  };
}

export class ChatAssistantHandler {
  constructor(
    private chatService: ChatService,
    private requestMapper: RequestMapper,
    private responseFormatter: ResponseFormatter,
    private errorHandler: ErrorHandler,
    private logger: ILogger
  ) {}

  /**
   * Handle HTTP request for chat assistance
   */
  async handle(request: Request): Promise<Response> {
    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      this.logger.info('Processing chat assistant request', {
        requestId,
        method: request.method,
        url: request.url
      });

      // Validate HTTP method
      if (request.method !== 'POST') {
        return this.errorHandler.handleMethodNotAllowed(['POST']);
      }

      // Extract authentication and context
      const authContext = await this.extractAuthContext(request);
      if (!authContext.success) {
        return this.errorHandler.handleAuthError(authContext.error!);
      }

      // Parse request body
      const parseResult = await this.parseRequestBody(request);
      if (!parseResult.success) {
        return this.errorHandler.handleValidationError(
          'Invalid request body',
          parseResult.errors
        );
      }

      const requestBody = parseResult.data!;

      // Map to internal request format
      const chatRequest = this.mapToChatRequest(
        requestBody,
        authContext.data!,
        requestId
      );

      // Validate mapped request
      const validationResult = this.validateChatRequest(chatRequest);
      if (!validationResult.valid) {
        return this.errorHandler.handleValidationError(
          'Request validation failed',
          validationResult.errors
        );
      }

      // Execute chat service
      const serviceResult = await this.chatService.processChat(chatRequest);
      
      if (!serviceResult.success) {
        this.logger.error('Chat service failed', {
          requestId,
          error: serviceResult.error
        });
        
        return this.errorHandler.handleServiceError(
          serviceResult.error || 'Chat processing failed'
        );
      }

      // Format response for client
      const response = this.formatChatResponse(
        serviceResult.data!,
        requestBody,
        requestId,
        authContext.data!.userRole,
        Date.now() - startTime
      );

      this.logger.info('Chat request completed successfully', {
        requestId,
        intent: serviceResult.data!.intent,
        requiresIntervention: serviceResult.data!.requiresHumanIntervention,
        processingTime: Date.now() - startTime
      });

      return this.responseFormatter.success(response, {
        requestId,
        processingTime: Date.now() - startTime
      });

    } catch (error) {
      this.logger.error('Chat handler failed with exception', {
        requestId,
        error
      });

      return this.errorHandler.handleInternalError(
        error instanceof Error ? error.message : 'Unknown error',
        requestId
      );
    }
  }

  /**
   * Parse and validate request body
   */
  private async parseRequestBody(request: Request): Promise<{
    success: boolean;
    data?: ChatRequestBody;
    errors?: string[];
  }> {
    try {
      const body = await request.json();
      const errors: string[] = [];

      // Validate required fields
      if (!body.message || typeof body.message !== 'string') {
        errors.push('message is required and must be a string');
      } else {
        // Validate message length
        if (body.message.trim().length === 0) {
          errors.push('message cannot be empty');
        } else if (body.message.length > 2000) {
          errors.push('message cannot exceed 2000 characters');
        }
      }

      // Validate conversation history if provided
      if (body.conversationHistory) {
        if (!Array.isArray(body.conversationHistory)) {
          errors.push('conversationHistory must be an array');
        } else {
          body.conversationHistory.forEach((message: any, index: number) => {
            if (!message.role || !['user', 'assistant'].includes(message.role)) {
              errors.push(`conversationHistory[${index}].role must be 'user' or 'assistant'`);
            }
            if (!message.content || typeof message.content !== 'string') {
              errors.push(`conversationHistory[${index}].content is required and must be a string`);
            }
            if (!message.timestamp) {
              errors.push(`conversationHistory[${index}].timestamp is required`);
            }
          });
        }
      }

      // Validate context if provided
      if (body.context && typeof body.context !== 'object') {
        errors.push('context must be an object');
      }

      // Validate session ID format if provided
      if (body.sessionId && typeof body.sessionId !== 'string') {
        errors.push('sessionId must be a string');
      }

      // Validate priority if provided
      const validPriorities = ['low', 'normal', 'high', 'urgent'];
      if (body.priority && !validPriorities.includes(body.priority)) {
        errors.push('priority must be one of: ' + validPriorities.join(', '));
      }

      // Validate options if provided
      if (body.options) {
        if (typeof body.options !== 'object') {
          errors.push('options must be an object');
        } else {
          ['includeFollowUp', 'detailedResponse', 'includeSafetyInfo'].forEach(option => {
            if (body.options[option] !== undefined && typeof body.options[option] !== 'boolean') {
              errors.push(`options.${option} must be a boolean`);
            }
          });
        }
      }

      return {
        success: errors.length === 0,
        data: errors.length === 0 ? body : undefined,
        errors: errors.length > 0 ? errors : undefined
      };

    } catch (error) {
      return {
        success: false,
        errors: ['Invalid JSON in request body']
      };
    }
  }

  /**
   * Extract authentication context from request
   */
  private async extractAuthContext(request: Request): Promise<{
    success: boolean;
    data?: {
      salonId: string;
      userId: string;
      userRole: 'stylist' | 'client' | 'manager';
    };
    error?: string;
  }> {
    try {
      const authorization = request.headers.get('Authorization');
      const salonId = request.headers.get('X-Salon-ID');
      const userId = request.headers.get('X-User-ID');
      const userRole = request.headers.get('X-User-Role') as 'stylist' | 'client' | 'manager';

      if (!authorization) {
        return { success: false, error: 'Authorization header required' };
      }

      if (!salonId) {
        return { success: false, error: 'X-Salon-ID header required' };
      }

      if (!userId) {
        return { success: false, error: 'X-User-ID header required' };
      }

      if (!userRole || !['stylist', 'client', 'manager'].includes(userRole)) {
        return { success: false, error: 'Valid X-User-Role header required' };
      }

      return {
        success: true,
        data: { salonId, userId, userRole }
      };

    } catch (error) {
      return {
        success: false,
        error: 'Invalid authentication context'
      };
    }
  }

  /**
   * Map HTTP request to internal chat request format
   */
  private mapToChatRequest(
    requestBody: ChatRequestBody,
    authContext: { salonId: string; userId: string; userRole: 'stylist' | 'client' | 'manager' },
    requestId: string
  ): EnhancedChatRequest {
    return {
      message: requestBody.message.trim(),
      conversationHistory: requestBody.conversationHistory || [],
      context: {
        currentService: requestBody.context?.currentService,
        clientProfile: requestBody.context?.clientProfile,
        salonInfo: {
          // This would typically be fetched from the database
          // For now, we'll use placeholder data
          name: 'Salon Name',
          location: 'City, State'
        }
      },
      userRole: authContext.userRole,
      salonId: authContext.salonId,
      userId: authContext.userId,
      sessionId: requestBody.sessionId || `chat_${authContext.userId}_${Date.now()}`,
      priority: requestBody.priority || 'normal'
    };
  }

  /**
   * Validate internal chat request
   */
  private validateChatRequest(request: EnhancedChatRequest): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!request.salonId) {
      errors.push('Salon ID is required');
    }

    if (!request.userId) {
      errors.push('User ID is required');
    }

    if (!request.message || request.message.trim().length === 0) {
      errors.push('Message is required and cannot be empty');
    }

    // Check for potentially harmful content
    const message = request.message.toLowerCase();
    const harmfulKeywords = ['hack', 'exploit', 'bypass', 'admin', 'password'];
    
    if (harmfulKeywords.some(keyword => message.includes(keyword))) {
      errors.push('Message contains potentially harmful content');
    }

    // Validate conversation history format
    if (request.conversationHistory) {
      request.conversationHistory.forEach((msg, index) => {
        if (!msg.role || !msg.content) {
          errors.push(`Invalid conversation history entry at index ${index}`);
        }
      });
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Format chat response for client consumption
   */
  private formatChatResponse(
    serviceResponse: EnhancedChatResponse,
    originalRequest: ChatRequestBody,
    requestId: string,
    userRole: string,
    totalProcessingTime: number
  ): ChatResponse {
    // Adapt message based on user role
    const adaptedMessage = this.adaptMessageForRole(serviceResponse.message, userRole);

    // Filter suggested actions based on user permissions
    const filteredActions = this.filterActionsForRole(
      serviceResponse.suggestedActions || [],
      userRole
    );

    // Filter follow-up questions if not requested
    const followUpQuestions = originalRequest.options?.includeFollowUp !== false ? 
      serviceResponse.followUpQuestions : undefined;

    // Add safety information if requested and relevant
    const safetyConsiderations = originalRequest.options?.includeSafetyInfo !== false ? 
      serviceResponse.professionalContext.safetyConsiderations : 
      serviceResponse.professionalContext.safetyConsiderations.filter(consideration => 
        consideration.toLowerCase().includes('critical') || 
        consideration.toLowerCase().includes('danger')
      );

    return {
      success: true,
      data: {
        message: adaptedMessage,
        intent: serviceResponse.intent,
        confidence: Math.round(serviceResponse.confidence * 100), // Convert to percentage
        responseType: serviceResponse.responseType,
        suggestedActions: filteredActions,
        followUpQuestions,
        requiresHumanIntervention: serviceResponse.requiresHumanIntervention,
        professionalContext: {
          technicalLevel: serviceResponse.professionalContext.technicalLevel,
          safetyConsiderations,
          references: serviceResponse.professionalContext.references
        },
        conversationContext: {
          sessionLength: serviceResponse.conversationMetadata.sessionLength,
          topicProgression: serviceResponse.conversationMetadata.topicProgression.slice(-3), // Last 3 topics
          satisfactionSignals: serviceResponse.conversationMetadata.satisfactionSignals
        }
      },
      metadata: {
        requestId,
        timestamp: new Date().toISOString(),
        sessionId: originalRequest.sessionId,
        processingTime: totalProcessingTime,
        cached: serviceResponse.metadata.cached
      }
    };
  }

  /**
   * Generate a unique request ID
   */
  private generateRequestId(): string {
    return `chat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Helper methods for response formatting

  private adaptMessageForRole(message: string, userRole: string): string {
    if (userRole === 'client') {
      return message
        .replace(/colorist/gi, 'stylist')
        .replace(/professional/gi, 'expert')
        .replace(/technique/gi, 'method')
        .replace(/formulation/gi, 'color recipe');
    } else if (userRole === 'manager') {
      // Add business context for managers
      if (message.toLowerCase().includes('recommend') || message.toLowerCase().includes('suggest')) {
        return `${message}\n\n*Consider business implications and client satisfaction.*`;
      }
    }

    return message;
  }

  private filterActionsForRole(
    actions: Array<{ action: string; description: string; priority: 'low' | 'medium' | 'high' }>,
    userRole: string
  ): Array<{ action: string; description: string; priority: string }> {
    const rolePermissions = {
      client: ['book_appointment', 'request_consultation', 'view_results'],
      stylist: ['perform_test', 'consult_senior', 'document_service', 'adjust_formula'],
      manager: ['review_service', 'approve_formula', 'escalate_issue', 'train_staff']
    };

    const allowedActions = rolePermissions[userRole as keyof typeof rolePermissions] || [];

    return actions
      .filter(action => 
        allowedActions.some(allowed => action.action.includes(allowed)) ||
        action.priority === 'high' // Always show high-priority actions
      )
      .map(action => ({
        action: action.action,
        description: this.adaptActionDescription(action.description, userRole),
        priority: action.priority
      }));
  }

  private adaptActionDescription(description: string, userRole: string): string {
    if (userRole === 'client') {
      return description
        .replace(/perform/gi, 'ask your stylist to perform')
        .replace(/consult/gi, 'discuss with')
        .replace(/technical/gi, 'detailed');
    }

    return description;
  }
}