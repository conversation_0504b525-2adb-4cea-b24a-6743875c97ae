/**
 * Diagnose Image Handler
 * 
 * HTTP request handler for hair diagnosis operations.
 * Manages request parsing, validation, service orchestration,
 * and response formatting for image-based hair diagnosis.
 */

import { DiagnosisService, DiagnosisRequest, DiagnosisResponse } from '../services/DiagnosisService.ts';
import { RequestMapper } from '../utils/RequestMapper.ts';
import { ResponseFormatter } from '../utils/ResponseFormatter.ts';
import { ErrorHandler } from '../utils/ErrorHandler.ts';
import { ILogger } from '../../services/interfaces.ts';

export interface DiagnoseImageRequestBody {
  imageUrl?: string;
  imageBase64?: string;
  clientId?: string;
  metadata?: {
    clientAge?: number;
    previousServices?: string[];
    skinTone?: string;
    naturalHairColor?: string;
    allergies?: string[];
  };
  options?: {
    priority?: 'low' | 'normal' | 'high' | 'urgent';
    includeRecommendations?: boolean;
    detailedAnalysis?: boolean;
  };
}

export interface DiagnoseImageResponse {
  success: boolean;
  data?: {
    diagnosis: any; // Simplified for client consumption
    confidence: number;
    processingTime: number;
    recommendedActions: string[];
    riskLevel: string;
    nextSteps: {
      immediate: string[];
      followUp: string[];
      requiresConsultation: boolean;
    };
    estimatedServiceTime?: number;
    estimatedCost?: number;
  };
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata: {
    requestId: string;
    timestamp: string;
    version: string;
    cached: boolean;
  };
}

export class DiagnoseImageHandler {
  constructor(
    private diagnosisService: DiagnosisService,
    private requestMapper: RequestMapper,
    private responseFormatter: ResponseFormatter,
    private errorHandler: ErrorHandler,
    private logger: ILogger
  ) {}

  /**
   * Handle HTTP request for image diagnosis
   */
  async handle(request: Request): Promise<Response> {
    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      this.logger.info('Processing diagnose image request', {
        requestId,
        method: request.method,
        url: request.url
      });

      // Validate HTTP method
      if (request.method !== 'POST') {
        return this.errorHandler.handleMethodNotAllowed(['POST']);
      }

      // Extract authentication and context
      const authContext = await this.extractAuthContext(request);
      if (!authContext.success) {
        return this.errorHandler.handleAuthError(authContext.error!);
      }

      // Parse request body
      const parseResult = await this.parseRequestBody(request);
      if (!parseResult.success) {
        return this.errorHandler.handleValidationError(
          'Invalid request body',
          parseResult.errors
        );
      }

      const requestBody = parseResult.data!;

      // Map to internal request format
      const diagnosisRequest = this.mapToDiagnosisRequest(
        requestBody,
        authContext.data!,
        requestId
      );

      // Validate mapped request
      const validationResult = this.validateDiagnosisRequest(diagnosisRequest);
      if (!validationResult.valid) {
        return this.errorHandler.handleValidationError(
          'Request validation failed',
          validationResult.errors
        );
      }

      // Execute diagnosis service
      const serviceResult = await this.diagnosisService.processImageDiagnosis(diagnosisRequest);
      
      if (!serviceResult.success) {
        this.logger.error('Diagnosis service failed', {
          requestId,
          error: serviceResult.error
        });
        
        return this.errorHandler.handleServiceError(
          serviceResult.error || 'Diagnosis processing failed'
        );
      }

      // Format response for client
      const response = this.formatDiagnosisResponse(
        serviceResult.data!,
        requestId,
        authContext.data!.userRole,
        Date.now() - startTime
      );

      this.logger.info('Diagnosis request completed successfully', {
        requestId,
        processingTime: Date.now() - startTime,
        confidence: serviceResult.data!.confidence,
        riskLevel: serviceResult.data!.riskAssessment.level
      });

      return this.responseFormatter.success(response, {
        requestId,
        processingTime: Date.now() - startTime
      });

    } catch (error) {
      this.logger.error('Diagnosis handler failed with exception', {
        requestId,
        error
      });

      return this.errorHandler.handleInternalError(
        error instanceof Error ? error.message : 'Unknown error',
        requestId
      );
    }
  }

  /**
   * Parse and validate request body
   */
  private async parseRequestBody(request: Request): Promise<{
    success: boolean;
    data?: DiagnoseImageRequestBody;
    errors?: string[];
  }> {
    try {
      const body = await request.json();
      const errors: string[] = [];

      // Basic validation
      if (!body.imageUrl && !body.imageBase64) {
        errors.push('Either imageUrl or imageBase64 is required');
      }

      if (body.imageUrl && typeof body.imageUrl !== 'string') {
        errors.push('imageUrl must be a string');
      }

      if (body.imageBase64 && typeof body.imageBase64 !== 'string') {
        errors.push('imageBase64 must be a string');
      }

      if (body.clientId && typeof body.clientId !== 'string') {
        errors.push('clientId must be a string');
      }

      // Validate metadata if provided
      if (body.metadata) {
        if (typeof body.metadata !== 'object') {
          errors.push('metadata must be an object');
        } else {
          if (body.metadata.clientAge && 
              (typeof body.metadata.clientAge !== 'number' || 
               body.metadata.clientAge < 0 || 
               body.metadata.clientAge > 120)) {
            errors.push('clientAge must be between 0 and 120');
          }

          if (body.metadata.previousServices && 
              !Array.isArray(body.metadata.previousServices)) {
            errors.push('previousServices must be an array');
          }
        }
      }

      // Validate options if provided
      if (body.options) {
        if (typeof body.options !== 'object') {
          errors.push('options must be an object');
        } else {
          const validPriorities = ['low', 'normal', 'high', 'urgent'];
          if (body.options.priority && 
              !validPriorities.includes(body.options.priority)) {
            errors.push('priority must be one of: ' + validPriorities.join(', '));
          }
        }
      }

      return {
        success: errors.length === 0,
        data: errors.length === 0 ? body : undefined,
        errors: errors.length > 0 ? errors : undefined
      };

    } catch (error) {
      return {
        success: false,
        errors: ['Invalid JSON in request body']
      };
    }
  }

  /**
   * Extract authentication context from request
   */
  private async extractAuthContext(request: Request): Promise<{
    success: boolean;
    data?: {
      salonId: string;
      userId: string;
      userRole: 'stylist' | 'client' | 'manager';
    };
    error?: string;
  }> {
    try {
      // Extract from headers
      const authorization = request.headers.get('Authorization');
      const salonId = request.headers.get('X-Salon-ID');
      const userId = request.headers.get('X-User-ID');
      const userRole = request.headers.get('X-User-Role') as 'stylist' | 'client' | 'manager';

      if (!authorization) {
        return { success: false, error: 'Authorization header required' };
      }

      if (!salonId) {
        return { success: false, error: 'X-Salon-ID header required' };
      }

      if (!userId) {
        return { success: false, error: 'X-User-ID header required' };
      }

      if (!userRole || !['stylist', 'client', 'manager'].includes(userRole)) {
        return { success: false, error: 'Valid X-User-Role header required' };
      }

      // In a real implementation, verify the JWT token here
      // For now, assume valid if present
      
      return {
        success: true,
        data: { salonId, userId, userRole }
      };

    } catch (error) {
      return {
        success: false,
        error: 'Invalid authentication context'
      };
    }
  }

  /**
   * Map HTTP request to internal diagnosis request format
   */
  private mapToDiagnosisRequest(
    requestBody: DiagnoseImageRequestBody,
    authContext: { salonId: string; userId: string; userRole: string },
    requestId: string
  ): DiagnosisRequest {
    return {
      imageUrl: requestBody.imageUrl,
      imageBase64: requestBody.imageBase64,
      salonId: authContext.salonId,
      userId: authContext.userId,
      clientId: requestBody.clientId,
      requestId,
      metadata: requestBody.metadata
    };
  }

  /**
   * Validate internal diagnosis request
   */
  private validateDiagnosisRequest(request: DiagnosisRequest): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!request.salonId) {
      errors.push('Salon ID is required');
    }

    if (!request.userId) {
      errors.push('User ID is required');
    }

    if (!request.imageUrl && !request.imageBase64) {
      errors.push('Image data is required');
    }

    // Validate image data format
    if (request.imageBase64) {
      try {
        // Basic base64 validation
        const base64Regex = /^data:image\/(jpeg|jpg|png|webp);base64,/;
        if (!base64Regex.test(request.imageBase64)) {
          errors.push('Invalid base64 image format');
        }

        // Check approximate size (rough estimate)
        const sizeEstimate = (request.imageBase64.length * 3) / 4;
        const maxSizeMB = 10;
        if (sizeEstimate > maxSizeMB * 1024 * 1024) {
          errors.push(`Image too large (max ${maxSizeMB}MB)`);
        }
      } catch (error) {
        errors.push('Invalid base64 image data');
      }
    }

    if (request.imageUrl) {
      try {
        new URL(request.imageUrl);
      } catch (error) {
        errors.push('Invalid image URL format');
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Format diagnosis response for client consumption
   */
  private formatDiagnosisResponse(
    serviceResponse: DiagnosisResponse,
    requestId: string,
    userRole: string,
    totalProcessingTime: number
  ): DiagnoseImageResponse {
    // Simplify diagnosis data based on user role
    const simplifiedDiagnosis = this.simplifyDiagnosisForClient(
      serviceResponse.diagnosis,
      userRole
    );

    return {
      success: true,
      data: {
        diagnosis: simplifiedDiagnosis,
        confidence: serviceResponse.confidence,
        processingTime: serviceResponse.processingTime,
        recommendedActions: serviceResponse.recommendedActions,
        riskLevel: serviceResponse.riskAssessment.level,
        nextSteps: {
          immediate: serviceResponse.nextSteps.immediate,
          followUp: serviceResponse.nextSteps.followUp,
          requiresConsultation: serviceResponse.nextSteps.consultation || false
        },
        estimatedServiceTime: this.estimateServiceTime(serviceResponse),
        estimatedCost: userRole !== 'client' ? this.estimateCost(serviceResponse) : undefined
      },
      metadata: {
        requestId,
        timestamp: new Date().toISOString(),
        version: '1.0',
        cached: serviceResponse.metadata.cached
      }
    };
  }

  /**
   * Simplify diagnosis data for client-friendly consumption
   */
  private simplifyDiagnosisForClient(diagnosis: any, userRole: string): any {
    if (userRole === 'client') {
      return {
        hairLevel: diagnosis.averageLevel,
        hairTone: this.simplifyTone(diagnosis.overallTone),
        hairCondition: this.simplifyCondition(diagnosis.overallCondition),
        overallHealth: this.calculateHealthScore(diagnosis),
        keyFindings: this.extractKeyFindings(diagnosis),
        colorReadiness: this.assessColorReadiness(diagnosis)
      };
    }

    // For stylists and managers, return more detailed information
    return {
      ...diagnosis,
      technicalNotes: this.addTechnicalNotes(diagnosis),
      professionalRecommendations: this.generateProfessionalRecommendations(diagnosis)
    };
  }

  /**
   * Generate a unique request ID
   */
  private generateRequestId(): string {
    return `diag_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Helper methods for response formatting

  private simplifyTone(tone: string): string {
    const toneMap: Record<string, string> = {
      'neutral': 'Natural',
      'warm': 'Golden/Warm',
      'cool': 'Ash/Cool',
      'red': 'Red undertones',
      'orange': 'Brassy undertones',
      'yellow': 'Yellow undertones'
    };

    return toneMap[tone?.toLowerCase()] || tone || 'Not determined';
  }

  private simplifyCondition(condition: string): string {
    const conditionMap: Record<string, string> = {
      'excellent': 'Excellent condition',
      'good': 'Good condition',
      'fair': 'Fair condition - some care needed',
      'poor': 'Needs conditioning treatment',
      'damaged': 'Requires repair treatment'
    };

    return conditionMap[condition?.toLowerCase()] || condition || 'Assessment needed';
  }

  private calculateHealthScore(diagnosis: any): number {
    let score = 100;
    
    if (diagnosis.overallCondition === 'poor') score -= 30;
    else if (diagnosis.overallCondition === 'damaged') score -= 50;
    else if (diagnosis.overallCondition === 'fair') score -= 15;

    if (diagnosis.detectedRisks?.length > 0) {
      score -= Math.min(diagnosis.detectedRisks.length * 10, 30);
    }

    if (diagnosis.overallConfidence < 0.8) {
      score -= 10;
    }

    return Math.max(0, Math.min(100, score));
  }

  private extractKeyFindings(diagnosis: any): string[] {
    const findings: string[] = [];
    
    if (diagnosis.averageLevel) {
      findings.push(`Hair level: ${diagnosis.averageLevel}/10`);
    }

    if (diagnosis.overallTone) {
      findings.push(`Primary tone: ${this.simplifyTone(diagnosis.overallTone)}`);
    }

    if (diagnosis.detectedChemicalProcess && diagnosis.detectedChemicalProcess !== 'none') {
      findings.push(`Previous chemical service detected`);
    }

    if (diagnosis.detectedRisks?.length > 0) {
      findings.push(`${diagnosis.detectedRisks.length} concern(s) identified`);
    }

    return findings;
  }

  private assessColorReadiness(diagnosis: any): {
    ready: boolean;
    reason: string;
    recommendations: string[];
  } {
    const recommendations: string[] = [];
    let ready = true;
    let reason = 'Hair appears ready for color services';

    if (diagnosis.overallCondition === 'poor' || diagnosis.overallCondition === 'damaged') {
      ready = false;
      reason = 'Hair requires conditioning before color services';
      recommendations.push('Use deep conditioning treatment');
      recommendations.push('Consider protein treatment');
    }

    if (diagnosis.detectedRisks?.some((risk: string) => 
        risk.toLowerCase().includes('severe') || 
        risk.toLowerCase().includes('damage'))) {
      ready = false;
      reason = 'Significant hair concerns detected';
      recommendations.push('Consult with stylist before proceeding');
    }

    if (diagnosis.overallConfidence < 0.7) {
      ready = false;
      reason = 'Additional assessment needed';
      recommendations.push('In-person consultation recommended');
    }

    return { ready, reason, recommendations };
  }

  private addTechnicalNotes(diagnosis: any): string[] {
    const notes: string[] = [];
    
    if (diagnosis.zoneAnalysis) {
      const rootLevel = diagnosis.zoneAnalysis.roots?.level;
      const endLevel = diagnosis.zoneAnalysis.ends?.level;
      if (rootLevel && endLevel && Math.abs(rootLevel - endLevel) > 2) {
        notes.push(`Significant level variation: roots ${rootLevel}, ends ${endLevel}`);
      }
    }

    if (diagnosis.elasticity) {
      notes.push(`Elasticity: ${diagnosis.elasticity}`);
    }

    if (diagnosis.resistance) {
      notes.push(`Color resistance: ${diagnosis.resistance}`);
    }

    return notes;
  }

  private generateProfessionalRecommendations(diagnosis: any): string[] {
    const recommendations: string[] = [];
    
    if (diagnosis.serviceComplexity === 'high') {
      recommendations.push('Schedule extended appointment time');
      recommendations.push('Consider pre-consultation');
    }

    if (diagnosis.estimatedTime > 120) {
      recommendations.push('Plan for 2+ hour service');
    }

    if (diagnosis.detectedRisks?.length > 0) {
      recommendations.push('Perform strand test before service');
    }

    return recommendations;
  }

  private estimateServiceTime(response: DiagnosisResponse): number {
    const baseTime = 90; // Base service time in minutes
    let adjustedTime = baseTime;

    // Adjust based on complexity
    if (response.diagnosis.serviceComplexity === 'high') {
      adjustedTime += 60;
    } else if (response.diagnosis.serviceComplexity === 'medium') {
      adjustedTime += 30;
    }

    // Adjust based on risk level
    if (response.riskAssessment.level === 'high' || response.riskAssessment.level === 'critical') {
      adjustedTime += 30; // Extra time for careful processing
    }

    return adjustedTime;
  }

  private estimateCost(response: DiagnosisResponse): number {
    // Basic cost estimation - in practice this would be more sophisticated
    const baseCost = 80; // Base service cost
    let adjustedCost = baseCost;

    if (response.diagnosis.serviceComplexity === 'high') {
      adjustedCost += 40;
    } else if (response.diagnosis.serviceComplexity === 'medium') {
      adjustedCost += 20;
    }

    // Add premium for high-risk services
    if (response.riskAssessment.level === 'high') {
      adjustedCost += 25;
    }

    return adjustedCost;
  }
}