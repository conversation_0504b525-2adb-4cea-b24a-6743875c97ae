/**
 * Convert Formula Handler
 * 
 * HTTP request handler for formula conversion operations.
 * Manages conversion between different brand formulations, format translations,
 * and measurement unit conversions for professional hair coloring.
 */

import { RequestMapper } from '../utils/RequestMapper.ts';
import { ResponseFormatter } from '../utils/ResponseFormatter.ts';
import { ErrorHandler } from '../utils/ErrorHandler.ts';
import { ValidationHelper } from '../utils/ValidationHelper.ts';
import { ILogger } from '../../services/interfaces.ts';

export interface ConvertFormulaRequestBody {
  sourceFormula: {
    brand: string;
    line: string;
    formula: string;
    developer?: {
      volume: number;
      ratio: string;
    };
    processingTime?: number;
  };
  targetBrand: string;
  targetLine?: string;
  conversionType: 'brand_conversion' | 'measurement_conversion' | 'format_translation';
  options?: {
    preserveRatio?: boolean;
    adjustForHairType?: 'fine' | 'medium' | 'coarse';
    includeAlternatives?: boolean;
    detailedExplanation?: boolean;
  };
  context?: {
    hairLevel?: number;
    hairCondition?: string;
    availableProducts?: Array<{
      brand: string;
      line: string;
      shade: string;
      level?: number;
    }>;
  };
}

export interface ConvertFormulaResponse {
  success: boolean;
  data?: {
    convertedFormula: {
      brand: string;
      line: string;
      formula: string;
      developer?: {
        volume: number;
        ratio: string;
      };
      processingTime?: number;
      instructions: string[];
    };
    conversionDetails: {
      type: string;
      confidence: number;
      substitutions: Array<{
        original: string;
        converted: string;
        reason: string;
        confidenceLevel: 'high' | 'medium' | 'low';
      }>;
      warnings: string[];
      notes: string[];
    };
    alternatives?: Array<{
      brand: string;
      formula: string;
      similarity: number;
      notes: string;
    }>;
    explanation?: {
      conversionReasoning: string;
      keyDifferences: string[];
      expectedResults: string;
      professionalNotes: string[];
    };
  };
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata: {
    requestId: string;
    timestamp: string;
    processingTime: number;
  };
}

export class ConvertFormulaHandler {
  // Brand compatibility matrix
  private readonly BRAND_COMPATIBILITY = {
    'L\'Oreal': ['Matrix', 'Redken', 'Kerastase'],
    'Matrix': ['L\'Oreal', 'Redken', 'Biolage'],
    'Wella': ['Schwarzkopf', 'Igora', 'Koleston'],
    'Schwarzkopf': ['Wella', 'Igora', 'BlondMe'],
    'Pravana': ['Joico', 'Paul Mitchell', 'Manic Panic'],
    'Joico': ['Pravana', 'Paul Mitchell', 'Redken']
  };

  // Common shade equivalents across brands
  private readonly SHADE_EQUIVALENTS = {
    'natural': ['N', 'Natural', 'Neutral', '0'],
    'ash': ['A', 'Ash', 'Green', '1', '.1'],
    'beige': ['B', 'Beige', 'Brown', '7', '.7'],
    'gold': ['G', 'Gold', 'Yellow', '3', '.3'],
    'copper': ['C', 'Copper', 'Orange', '4', '.4'],
    'mahogany': ['M', 'Mahogany', 'Red', '5', '.5'],
    'red': ['R', 'Red', '6', '.6'],
    'violet': ['V', 'Violet', 'Purple', '2', '.2']
  };

  constructor(
    private requestMapper: RequestMapper,
    private responseFormatter: ResponseFormatter,
    private errorHandler: ErrorHandler,
    private validationHelper: ValidationHelper,
    private logger: ILogger
  ) {}

  /**
   * Handle HTTP request for formula conversion
   */
  async handle(request: Request): Promise<Response> {
    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      this.logger.info('Processing formula conversion request', {
        requestId,
        method: request.method,
        url: request.url
      });

      // Validate HTTP method
      if (request.method !== 'POST') {
        return this.errorHandler.handleMethodNotAllowed(['POST']);
      }

      // Extract authentication and context
      const authContext = await this.requestMapper.extractAuthContext(request);
      if (!authContext.success) {
        return this.errorHandler.handleAuthError(authContext.errors?.[0] || 'Authentication failed');
      }

      // Parse request body
      const parseResult = await this.parseRequestBody(request);
      if (!parseResult.success) {
        return this.errorHandler.handleValidationError(
          'Invalid request body',
          parseResult.errors
        );
      }

      const requestBody = parseResult.data!;

      // Validate conversion request
      const validationResult = this.validateConversionRequest(requestBody);
      if (!validationResult.valid) {
        return this.errorHandler.handleValidationError(
          'Conversion validation failed',
          validationResult.errors.map(e => e.message)
        );
      }

      // Process the conversion
      const conversionResult = await this.processConversion(
        requestBody,
        authContext.data!,
        requestId
      );

      if (!conversionResult.success) {
        return this.errorHandler.handleServiceError(
          conversionResult.error || 'Formula conversion failed'
        );
      }

      // Format response
      const response = this.formatConversionResponse(
        conversionResult.data!,
        requestId,
        Date.now() - startTime
      );

      this.logger.info('Formula conversion completed successfully', {
        requestId,
        sourceFormula: `${requestBody.sourceFormula.brand} ${requestBody.sourceFormula.formula}`,
        targetBrand: requestBody.targetBrand,
        conversionType: requestBody.conversionType,
        processingTime: Date.now() - startTime
      });

      return this.responseFormatter.success(response, {
        requestId,
        processingTime: Date.now() - startTime
      });

    } catch (error) {
      this.logger.error('Conversion handler failed with exception', {
        requestId,
        error
      });

      return this.errorHandler.handleInternalError(
        error instanceof Error ? error.message : 'Unknown error',
        requestId
      );
    }
  }

  /**
   * Parse and validate request body
   */
  private async parseRequestBody(request: Request): Promise<{
    success: boolean;
    data?: ConvertFormulaRequestBody;
    errors?: string[];
  }> {
    try {
      const body = await request.json();
      const errors: string[] = [];

      // Validate source formula
      if (!body.sourceFormula || typeof body.sourceFormula !== 'object') {
        errors.push('sourceFormula is required and must be an object');
      } else {
        if (!body.sourceFormula.brand || typeof body.sourceFormula.brand !== 'string') {
          errors.push('sourceFormula.brand is required and must be a string');
        }

        if (!body.sourceFormula.line || typeof body.sourceFormula.line !== 'string') {
          errors.push('sourceFormula.line is required and must be a string');
        }

        if (!body.sourceFormula.formula || typeof body.sourceFormula.formula !== 'string') {
          errors.push('sourceFormula.formula is required and must be a string');
        }
      }

      // Validate target brand
      if (!body.targetBrand || typeof body.targetBrand !== 'string') {
        errors.push('targetBrand is required and must be a string');
      }

      // Validate conversion type
      const validConversionTypes = ['brand_conversion', 'measurement_conversion', 'format_translation'];
      if (!body.conversionType || !validConversionTypes.includes(body.conversionType)) {
        errors.push(`conversionType must be one of: ${validConversionTypes.join(', ')}`);
      }

      // Validate options if provided
      if (body.options && typeof body.options !== 'object') {
        errors.push('options must be an object');
      }

      // Validate context if provided
      if (body.context && typeof body.context !== 'object') {
        errors.push('context must be an object');
      }

      if (errors.length > 0) {
        return { success: false, errors };
      }

      return { success: true, data: body };

    } catch (error) {
      return {
        success: false,
        errors: ['Invalid JSON in request body']
      };
    }
  }

  /**
   * Validate conversion request
   */
  private validateConversionRequest(requestBody: ConvertFormulaRequestBody): {
    valid: boolean;
    errors: Array<{ field: string; message: string; code: string }>;
  } {
    const errors: Array<{ field: string; message: string; code: string }> = [];

    // Check if brands are different for brand conversion
    if (requestBody.conversionType === 'brand_conversion') {
      if (requestBody.sourceFormula.brand.toLowerCase() === requestBody.targetBrand.toLowerCase()) {
        errors.push({
          field: 'conversionType',
          message: 'Source and target brands must be different for brand conversion',
          code: 'SAME_BRAND_CONVERSION'
        });
      }
    }

    // Validate formula format
    const formulaPattern = /^[\d\/\.\-\s\w]+$/;
    if (!formulaPattern.test(requestBody.sourceFormula.formula)) {
      errors.push({
        field: 'sourceFormula.formula',
        message: 'Formula contains invalid characters',
        code: 'INVALID_FORMULA_FORMAT'
      });
    }

    // Validate developer volume if provided
    if (requestBody.sourceFormula.developer?.volume) {
      const volume = requestBody.sourceFormula.developer.volume;
      if (volume < 10 || volume > 40 || volume % 10 !== 0) {
        errors.push({
          field: 'sourceFormula.developer.volume',
          message: 'Developer volume must be between 10-40 and in 10vol increments',
          code: 'INVALID_DEVELOPER_VOLUME'
        });
      }
    }

    // Validate processing time if provided
    if (requestBody.sourceFormula.processingTime) {
      const time = requestBody.sourceFormula.processingTime;
      if (time < 5 || time > 180) {
        errors.push({
          field: 'sourceFormula.processingTime',
          message: 'Processing time must be between 5-180 minutes',
          code: 'INVALID_PROCESSING_TIME'
        });
      }
    }

    // Validate hair type if provided
    if (requestBody.options?.adjustForHairType) {
      const validTypes = ['fine', 'medium', 'coarse'];
      if (!validTypes.includes(requestBody.options.adjustForHairType)) {
        errors.push({
          field: 'options.adjustForHairType',
          message: `Hair type must be one of: ${validTypes.join(', ')}`,
          code: 'INVALID_HAIR_TYPE'
        });
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Process the formula conversion
   */
  private async processConversion(
    requestBody: ConvertFormulaRequestBody,
    authContext: { salonId: string; userId: string; userRole: string },
    requestId: string
  ): Promise<{
    success: boolean;
    data?: any;
    error?: string;
  }> {
    try {
      switch (requestBody.conversionType) {
        case 'brand_conversion':
          return await this.processBrandConversion(requestBody);
        case 'measurement_conversion':
          return await this.processMeasurementConversion(requestBody);
        case 'format_translation':
          return await this.processFormatTranslation(requestBody);
        default:
          return {
            success: false,
            error: 'Unsupported conversion type'
          };
      }

    } catch (error) {
      this.logger.error('Conversion processing failed', {
        error,
        requestId,
        conversionType: requestBody.conversionType
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Conversion processing failed'
      };
    }
  }

  /**
   * Process brand-to-brand formula conversion
   */
  private async processBrandConversion(requestBody: ConvertFormulaRequestBody): Promise<{
    success: boolean;
    data?: any;
    error?: string;
  }> {
    const sourceBrand = requestBody.sourceFormula.brand;
    const targetBrand = requestBody.targetBrand;
    const sourceFormula = requestBody.sourceFormula.formula;

    // Check brand compatibility
    const isCompatible = this.BRAND_COMPATIBILITY[sourceBrand]?.includes(targetBrand) ||
                        this.BRAND_COMPATIBILITY[targetBrand]?.includes(sourceBrand);

    const confidence = isCompatible ? 0.8 : 0.6;
    const substitutions = this.convertFormulaBetweenBrands(sourceFormula, sourceBrand, targetBrand);
    
    // Generate converted formula
    const convertedFormula = substitutions.map(sub => sub.converted).join(' + ');
    
    // Adjust developer if needed
    let convertedDeveloper = requestBody.sourceFormula.developer;
    if (this.requiresDeveloperAdjustment(sourceBrand, targetBrand)) {
      convertedDeveloper = {
        ...convertedDeveloper,
        volume: Math.min(40, (convertedDeveloper?.volume || 20) + 10)
      } as any;
    }

    // Generate warnings and notes
    const warnings: string[] = [];
    const notes: string[] = [];

    if (!isCompatible) {
      warnings.push('Brands may have different formulation systems - strand test recommended');
    }

    if (confidence < 0.7) {
      warnings.push('Low confidence conversion - professional consultation advised');
    }

    notes.push(`Converted from ${sourceBrand} ${requestBody.sourceFormula.line} to ${targetBrand}`);
    notes.push('Processing time may need adjustment based on brand differences');

    // Generate alternatives
    const alternatives = requestBody.options?.includeAlternatives ? 
      this.generateAlternativeFormulas(sourceFormula, targetBrand) : [];

    // Generate explanation
    const explanation = requestBody.options?.detailedExplanation ? {
      conversionReasoning: `Converted ${sourceBrand} formula to ${targetBrand} equivalent using shade matching and professional compatibility guidelines.`,
      keyDifferences: [
        'Different pigment concentrations may affect final result',
        'Processing times may vary between brand formulations',
        'Developer compatibility should be verified'
      ],
      expectedResults: 'Similar color outcome with potential minor variations in tone and coverage',
      professionalNotes: [
        'Always perform strand test when switching brands',
        'Monitor processing time carefully',
        'Document results for future reference'
      ]
    } : undefined;

    return {
      success: true,
      data: {
        convertedFormula: {
          brand: targetBrand,
          line: requestBody.targetLine || 'Professional',
          formula: convertedFormula,
          developer: convertedDeveloper,
          processingTime: requestBody.sourceFormula.processingTime,
          instructions: this.generateConversionInstructions(sourceBrand, targetBrand)
        },
        conversionDetails: {
          type: 'brand_conversion',
          confidence,
          substitutions,
          warnings,
          notes
        },
        alternatives,
        explanation
      }
    };
  }

  /**
   * Process measurement unit conversions
   */
  private async processMeasurementConversion(requestBody: ConvertFormulaRequestBody): Promise<{
    success: boolean;
    data?: any;
    error?: string;
  }> {
    // Extract measurements from formula
    const measurements = this.extractMeasurements(requestBody.sourceFormula.formula);
    const convertedMeasurements = measurements.map(measurement => this.convertMeasurement(measurement));
    
    const convertedFormula = requestBody.sourceFormula.formula;
    // Replace measurements in formula (simplified - in practice would be more sophisticated)
    
    const substitutions = measurements.map((original, index) => ({
      original: original.text,
      converted: convertedMeasurements[index].text,
      reason: `Converted ${original.unit} to ${convertedMeasurements[index].unit}`,
      confidenceLevel: 'high' as const
    }));

    return {
      success: true,
      data: {
        convertedFormula: {
          ...requestBody.sourceFormula,
          formula: convertedFormula
        },
        conversionDetails: {
          type: 'measurement_conversion',
          confidence: 0.95,
          substitutions,
          warnings: [],
          notes: ['Measurements converted to metric system', 'Ratios preserved']
        }
      }
    };
  }

  /**
   * Process format translations (e.g., numeric to letter codes)
   */
  private async processFormatTranslation(requestBody: ConvertFormulaRequestBody): Promise<{
    success: boolean;
    data?: any;
    error?: string;
  }> {
    const sourceFormula = requestBody.sourceFormula.formula;
    const translatedFormula = this.translateFormulaFormat(sourceFormula);
    
    const substitutions = [{
      original: sourceFormula,
      converted: translatedFormula,
      reason: 'Translated formula format notation',
      confidenceLevel: 'high' as const
    }];

    return {
      success: true,
      data: {
        convertedFormula: {
          ...requestBody.sourceFormula,
          formula: translatedFormula
        },
        conversionDetails: {
          type: 'format_translation',
          confidence: 0.9,
          substitutions,
          warnings: [],
          notes: ['Format notation translated', 'Color values preserved']
        }
      }
    };
  }

  // Private helper methods

  private convertFormulaBetweenBrands(formula: string, sourceBrand: string, targetBrand: string): Array<{
    original: string;
    converted: string;
    reason: string;
    confidenceLevel: 'high' | 'medium' | 'low';
  }> {
    const substitutions: Array<{
      original: string;
      converted: string;
      reason: string;
      confidenceLevel: 'high' | 'medium' | 'low';
    }> = [];

    // Parse formula components
    const components = formula.split(/[\+\s]+/).filter(c => c.trim());
    
    components.forEach(component => {
      const converted = this.findEquivalentShade(component, sourceBrand, targetBrand);
      substitutions.push({
        original: component,
        converted: converted.shade,
        reason: converted.reason,
        confidenceLevel: converted.confidence
      });
    });

    return substitutions;
  }

  private findEquivalentShade(shade: string, sourceBrand: string, targetBrand: string): {
    shade: string;
    reason: string;
    confidence: 'high' | 'medium' | 'low';
  } {
    // Extract level and tone from shade
    const levelMatch = shade.match(/(\d+)/);
    const level = levelMatch ? levelMatch[1] : '6';
    
    // Find tone indicators
    let tone = 'N'; // Default to natural
    for (const [toneName, indicators] of Object.entries(this.SHADE_EQUIVALENTS)) {
      if (indicators.some(indicator => shade.toLowerCase().includes(indicator.toLowerCase()))) {
        tone = indicators[0]; // Use first indicator as standard
        break;
      }
    }

    // Generate equivalent shade for target brand
    const equivalentShade = `${level}${tone}`;
    
    return {
      shade: equivalentShade,
      reason: `Level ${level} ${tone} equivalent`,
      confidence: 'high'
    };
  }

  private requiresDeveloperAdjustment(sourceBrand: string, targetBrand: string): boolean {
    // Some brands require developer adjustments
    const adjustmentRequired = [
      ['Wella', 'L\'Oreal'],
      ['Matrix', 'Schwarzkopf']
    ];

    return adjustmentRequired.some(pair => 
      (pair[0] === sourceBrand && pair[1] === targetBrand) ||
      (pair[1] === sourceBrand && pair[0] === targetBrand)
    );
  }

  private generateAlternativeFormulas(sourceFormula: string, targetBrand: string): Array<{
    brand: string;
    formula: string;
    similarity: number;
    notes: string;
  }> {
    // Generate 2-3 alternative formulations
    return [
      {
        brand: targetBrand,
        formula: sourceFormula.replace(/\d+/g, (match) => String(Number(match) + 1)),
        similarity: 0.85,
        notes: 'Alternative with lighter level'
      },
      {
        brand: targetBrand,
        formula: sourceFormula + ' + 0.1N',
        similarity: 0.8,
        notes: 'Alternative with natural base added'
      }
    ];
  }

  private generateConversionInstructions(sourceBrand: string, targetBrand: string): string[] {
    return [
      `Mixed converted ${targetBrand} formula according to manufacturer specifications`,
      'Perform strand test to verify color match',
      'Apply using standard sectioning technique',
      `Process for recommended time (may vary from original ${sourceBrand} timing)`,
      'Monitor development closely',
      'Rinse thoroughly and apply color-safe conditioner'
    ];
  }

  private extractMeasurements(formula: string): Array<{ text: string; value: number; unit: string }> {
    const measurements: Array<{ text: string; value: number; unit: string }> = [];
    
    // Match patterns like "30ml", "2oz", "1/2 cup", etc.
    const measurementRegex = /(\d+(?:\.\d+)?|\d+\/\d+)\s*(ml|oz|cup|tbsp|tsp|g|lb)/gi;
    let match;
    
    while ((match = measurementRegex.exec(formula)) !== null) {
      measurements.push({
        text: match[0],
        value: this.parseValue(match[1]),
        unit: match[2].toLowerCase()
      });
    }

    return measurements;
  }

  private convertMeasurement(measurement: { text: string; value: number; unit: string }): { text: string; value: number; unit: string } {
    const conversions: Record<string, { factor: number; unit: string }> = {
      'oz': { factor: 29.5735, unit: 'ml' },
      'cup': { factor: 240, unit: 'ml' },
      'tbsp': { factor: 15, unit: 'ml' },
      'tsp': { factor: 5, unit: 'ml' },
      'lb': { factor: 453.592, unit: 'g' }
    };

    const conversion = conversions[measurement.unit];
    if (!conversion) {
      return measurement; // No conversion needed
    }

    const convertedValue = Math.round(measurement.value * conversion.factor * 100) / 100;
    
    return {
      text: `${convertedValue}${conversion.unit}`,
      value: convertedValue,
      unit: conversion.unit
    };
  }

  private parseValue(valueStr: string): number {
    if (valueStr.includes('/')) {
      const [numerator, denominator] = valueStr.split('/').map(Number);
      return numerator / denominator;
    }
    return Number(valueStr);
  }

  private translateFormulaFormat(formula: string): string {
    // Convert between different notation systems (e.g., 6.1 to 6A, etc.)
    return formula.replace(/(\d+)\.(\d+)/g, (match, level, tone) => {
      const toneMap: Record<string, string> = {
        '0': 'N', '1': 'A', '2': 'V', '3': 'G',
        '4': 'C', '5': 'M', '6': 'R', '7': 'B'
      };
      return `${level}${toneMap[tone] || tone}`;
    });
  }

  private formatConversionResponse(
    conversionData: any,
    requestId: string,
    processingTime: number
  ): ConvertFormulaResponse {
    return {
      success: true,
      data: conversionData,
      metadata: {
        requestId,
        timestamp: new Date().toISOString(),
        processingTime
      }
    };
  }

  private generateRequestId(): string {
    return `convert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}