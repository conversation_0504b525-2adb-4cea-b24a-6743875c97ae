/**
 * Generate Formula Handler
 * 
 * HTTP request handler for hair formula generation operations.
 * Manages request parsing, validation, service orchestration,
 * and response formatting for complete formula generation workflows.
 */

import { FormulationService, FormulationRequest, FormulationResponse } from '../services/FormulationService.ts';
import { RequestMapper } from '../utils/RequestMapper.ts';
import { ResponseFormatter } from '../utils/ResponseFormatter.ts';
import { <PERSON>rrorHandler } from '../utils/ErrorHandler.ts';
import { ILogger } from '../../services/interfaces.ts';
import { HairDiagnosis, ProductInventory, ClientPreferences } from '../../types/use-case.types.ts';

export interface GenerateFormulaRequestBody {
  currentDiagnosis: HairDiagnosis;
  desiredLookImage?: string;
  desiredLookBase64?: string;
  targetDescription?: string;
  availableProducts?: ProductInventory[];
  clientPreferences?: ClientPreferences;
  serviceConstraints?: {
    maxProcessingTime?: number;
    budgetLimit?: number;
    appointmentType?: 'standard' | 'express' | 'premium';
    timeLimit?: number;
  };
  options?: {
    includeAlternatives?: boolean;
    detailedInstructions?: boolean;
    optimizeForInventory?: boolean;
    priority?: 'low' | 'normal' | 'high';
  };
  clientId?: string;
}

export interface GenerateFormulaResponse {
  success: boolean;
  data?: {
    primaryFormula: {
      technique: string;
      processingTime: number;
      applicationMethod: string;
      ingredients: any;
      instructions: string[];
      expectedResult: string;
      confidence: number;
    };
    viabilityAssessment: {
      achievable: boolean;
      confidence: number;
      sessionCount: number;
      timeEstimate: number;
      costEstimate?: number;
      riskLevel: string;
    };
    alternativeOptions?: {
      conservative?: any;
      progressive?: any;
      maintenance?: any;
    };
    professionalGuidance: {
      consultationRequired: boolean;
      expertiseLevel: string;
      specialConsiderations: string[];
      safetyNotes: string[];
    };
    clientInformation: {
      explanation: string;
      expectations: string[];
      aftercare: string[];
      maintenance: string[];
      estimatedResults: string;
    };
  };
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata: {
    requestId: string;
    timestamp: string;
    version: string;
    processingTime: number;
    cached: boolean;
  };
}

export class GenerateFormulaHandler {
  constructor(
    private formulationService: FormulationService,
    private requestMapper: RequestMapper,
    private responseFormatter: ResponseFormatter,
    private errorHandler: ErrorHandler,
    private logger: ILogger
  ) {}

  /**
   * Handle HTTP request for formula generation
   */
  async handle(request: Request): Promise<Response> {
    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      this.logger.info('Processing generate formula request', {
        requestId,
        method: request.method,
        url: request.url
      });

      // Validate HTTP method
      if (request.method !== 'POST') {
        return this.errorHandler.handleMethodNotAllowed(['POST']);
      }

      // Extract authentication and context
      const authContext = await this.extractAuthContext(request);
      if (!authContext.success) {
        return this.errorHandler.handleAuthError(authContext.error!);
      }

      // Parse request body
      const parseResult = await this.parseRequestBody(request);
      if (!parseResult.success) {
        return this.errorHandler.handleValidationError(
          'Invalid request body',
          parseResult.errors
        );
      }

      const requestBody = parseResult.data!;

      // Map to internal request format
      const formulationRequest = this.mapToFormulationRequest(
        requestBody,
        authContext.data!,
        requestId
      );

      // Validate mapped request
      const validationResult = this.validateFormulationRequest(formulationRequest);
      if (!validationResult.valid) {
        return this.errorHandler.handleValidationError(
          'Request validation failed',
          validationResult.errors
        );
      }

      // Execute formulation service
      const serviceResult = await this.formulationService.generateCompleteFormulation(formulationRequest);
      
      if (!serviceResult.success) {
        this.logger.error('Formulation service failed', {
          requestId,
          error: serviceResult.error
        });
        
        return this.errorHandler.handleServiceError(
          serviceResult.error || 'Formula generation failed'
        );
      }

      // Format response for client
      const response = this.formatFormulationResponse(
        serviceResult.data!,
        requestBody,
        requestId,
        authContext.data!.userRole,
        Date.now() - startTime
      );

      this.logger.info('Formula generation request completed successfully', {
        requestId,
        processingTime: Date.now() - startTime,
        achievable: serviceResult.data!.viabilityAssessment.achievable,
        riskLevel: serviceResult.data!.viabilityAssessment.riskLevel
      });

      return this.responseFormatter.success(response, {
        requestId,
        processingTime: Date.now() - startTime
      });

    } catch (error) {
      this.logger.error('Formula generation handler failed with exception', {
        requestId,
        error
      });

      return this.errorHandler.handleInternalError(
        error instanceof Error ? error.message : 'Unknown error',
        requestId
      );
    }
  }

  /**
   * Parse and validate request body
   */
  private async parseRequestBody(request: Request): Promise<{
    success: boolean;
    data?: GenerateFormulaRequestBody;
    errors?: string[];
  }> {
    try {
      const body = await request.json();
      const errors: string[] = [];

      // Validate required fields
      if (!body.currentDiagnosis) {
        errors.push('currentDiagnosis is required');
      } else if (typeof body.currentDiagnosis !== 'object') {
        errors.push('currentDiagnosis must be an object');
      }

      // Validate desired look input
      if (!body.desiredLookImage && !body.desiredLookBase64 && !body.targetDescription) {
        errors.push('At least one of: desiredLookImage, desiredLookBase64, or targetDescription is required');
      }

      // Validate image formats if provided
      if (body.desiredLookImage && typeof body.desiredLookImage !== 'string') {
        errors.push('desiredLookImage must be a string');
      }

      if (body.desiredLookBase64) {
        if (typeof body.desiredLookBase64 !== 'string') {
          errors.push('desiredLookBase64 must be a string');
        } else {
          // Basic base64 validation
          const base64Regex = /^data:image\/(jpeg|jpg|png|webp);base64,/;
          if (!base64Regex.test(body.desiredLookBase64)) {
            errors.push('Invalid desiredLookBase64 format');
          }
        }
      }

      if (body.targetDescription && typeof body.targetDescription !== 'string') {
        errors.push('targetDescription must be a string');
      }

      // Validate available products if provided
      if (body.availableProducts) {
        if (!Array.isArray(body.availableProducts)) {
          errors.push('availableProducts must be an array');
        } else {
          body.availableProducts.forEach((product: any, index: number) => {
            if (!product.brand || typeof product.brand !== 'string') {
              errors.push(`availableProducts[${index}].brand is required and must be a string`);
            }
            if (!product.shade || typeof product.shade !== 'string') {
              errors.push(`availableProducts[${index}].shade is required and must be a string`);
            }
            if (!product.type || typeof product.type !== 'string') {
              errors.push(`availableProducts[${index}].type is required and must be a string`);
            }
          });
        }
      }

      // Validate client preferences if provided
      if (body.clientPreferences && typeof body.clientPreferences !== 'object') {
        errors.push('clientPreferences must be an object');
      }

      // Validate service constraints if provided
      if (body.serviceConstraints) {
        if (typeof body.serviceConstraints !== 'object') {
          errors.push('serviceConstraints must be an object');
        } else {
          if (body.serviceConstraints.maxProcessingTime && 
              (typeof body.serviceConstraints.maxProcessingTime !== 'number' || 
               body.serviceConstraints.maxProcessingTime <= 0)) {
            errors.push('maxProcessingTime must be a positive number');
          }

          if (body.serviceConstraints.budgetLimit && 
              (typeof body.serviceConstraints.budgetLimit !== 'number' || 
               body.serviceConstraints.budgetLimit <= 0)) {
            errors.push('budgetLimit must be a positive number');
          }

          const validAppointmentTypes = ['standard', 'express', 'premium'];
          if (body.serviceConstraints.appointmentType && 
              !validAppointmentTypes.includes(body.serviceConstraints.appointmentType)) {
            errors.push('appointmentType must be one of: ' + validAppointmentTypes.join(', '));
          }
        }
      }

      // Validate options if provided
      if (body.options) {
        if (typeof body.options !== 'object') {
          errors.push('options must be an object');
        } else {
          const validPriorities = ['low', 'normal', 'high'];
          if (body.options.priority && !validPriorities.includes(body.options.priority)) {
            errors.push('priority must be one of: ' + validPriorities.join(', '));
          }

          ['includeAlternatives', 'detailedInstructions', 'optimizeForInventory'].forEach(option => {
            if (body.options[option] !== undefined && typeof body.options[option] !== 'boolean') {
              errors.push(`options.${option} must be a boolean`);
            }
          });
        }
      }

      return {
        success: errors.length === 0,
        data: errors.length === 0 ? body : undefined,
        errors: errors.length > 0 ? errors : undefined
      };

    } catch (error) {
      return {
        success: false,
        errors: ['Invalid JSON in request body']
      };
    }
  }

  /**
   * Extract authentication context from request
   */
  private async extractAuthContext(request: Request): Promise<{
    success: boolean;
    data?: {
      salonId: string;
      userId: string;
      userRole: 'stylist' | 'client' | 'manager';
    };
    error?: string;
  }> {
    try {
      const authorization = request.headers.get('Authorization');
      const salonId = request.headers.get('X-Salon-ID');
      const userId = request.headers.get('X-User-ID');
      const userRole = request.headers.get('X-User-Role') as 'stylist' | 'client' | 'manager';

      if (!authorization) {
        return { success: false, error: 'Authorization header required' };
      }

      if (!salonId) {
        return { success: false, error: 'X-Salon-ID header required' };
      }

      if (!userId) {
        return { success: false, error: 'X-User-ID header required' };
      }

      if (!userRole || !['stylist', 'client', 'manager'].includes(userRole)) {
        return { success: false, error: 'Valid X-User-Role header required' };
      }

      return {
        success: true,
        data: { salonId, userId, userRole }
      };

    } catch (error) {
      return {
        success: false,
        error: 'Invalid authentication context'
      };
    }
  }

  /**
   * Map HTTP request to internal formulation request format
   */
  private mapToFormulationRequest(
    requestBody: GenerateFormulaRequestBody,
    authContext: { salonId: string; userId: string; userRole: string },
    requestId: string
  ): FormulationRequest {
    return {
      currentDiagnosis: requestBody.currentDiagnosis,
      desiredLookImage: requestBody.desiredLookImage,
      desiredLookBase64: requestBody.desiredLookBase64,
      targetDescription: requestBody.targetDescription,
      availableProducts: requestBody.availableProducts,
      clientPreferences: requestBody.clientPreferences,
      serviceConstraints: requestBody.serviceConstraints,
      salonId: authContext.salonId,
      userId: authContext.userId,
      clientId: requestBody.clientId,
      requestId
    };
  }

  /**
   * Validate internal formulation request
   */
  private validateFormulationRequest(request: FormulationRequest): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Validate required fields
    if (!request.salonId) {
      errors.push('Salon ID is required');
    }

    if (!request.userId) {
      errors.push('User ID is required');
    }

    if (!request.currentDiagnosis) {
      errors.push('Current diagnosis is required');
    }

    // Validate diagnosis structure
    if (request.currentDiagnosis) {
      if (!request.currentDiagnosis.averageLevel || 
          request.currentDiagnosis.averageLevel < 1 || 
          request.currentDiagnosis.averageLevel > 10) {
        errors.push('Valid hair level (1-10) is required in diagnosis');
      }

      if (!request.currentDiagnosis.overallCondition) {
        errors.push('Hair condition is required in diagnosis');
      }
    }

    // Validate desired look input
    if (!request.desiredLookImage && !request.desiredLookBase64 && !request.targetDescription) {
      errors.push('Desired look specification is required');
    }

    // Validate image URLs if provided
    if (request.desiredLookImage) {
      try {
        new URL(request.desiredLookImage);
      } catch (error) {
        errors.push('Invalid desired look image URL format');
      }
    }

    // Validate service constraints
    if (request.serviceConstraints) {
      if (request.serviceConstraints.maxProcessingTime && 
          request.serviceConstraints.maxProcessingTime > 180) {
        errors.push('Maximum processing time cannot exceed 180 minutes');
      }

      if (request.serviceConstraints.budgetLimit && 
          request.serviceConstraints.budgetLimit < 10) {
        errors.push('Budget limit too low for professional service');
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Format formulation response for client consumption
   */
  private formatFormulationResponse(
    serviceResponse: FormulationResponse,
    originalRequest: GenerateFormulaRequestBody,
    requestId: string,
    userRole: string,
    totalProcessingTime: number
  ): GenerateFormulaResponse {
    // Format primary formula
    const primaryFormula = this.formatFormula(
      serviceResponse.formula,
      userRole,
      originalRequest.options?.detailedInstructions || false
    );

    // Format alternative options if requested and available
    let alternativeOptions;
    if (originalRequest.options?.includeAlternatives && serviceResponse.alternativeOptions) {
      alternativeOptions = {
        conservative: serviceResponse.alternativeOptions.conservative ? 
          this.formatFormula(serviceResponse.alternativeOptions.conservative, userRole) : undefined,
        progressive: serviceResponse.alternativeOptions.progressive ? 
          this.formatFormula(serviceResponse.alternativeOptions.progressive, userRole) : undefined,
        maintenance: serviceResponse.alternativeOptions.maintenance ? 
          this.formatFormula(serviceResponse.alternativeOptions.maintenance, userRole) : undefined
      };
    }

    // Format professional guidance
    const professionalGuidance = {
      consultationRequired: serviceResponse.professionalGuidance.required,
      expertiseLevel: this.translateExpertiseLevel(serviceResponse.professionalGuidance.level),
      specialConsiderations: serviceResponse.professionalGuidance.consultation,
      safetyNotes: this.extractSafetyNotes(serviceResponse)
    };

    // Format client information
    const clientInformation = {
      explanation: this.adaptExplanationForRole(serviceResponse.clientCommunication.explanation, userRole),
      expectations: serviceResponse.clientCommunication.expectations,
      aftercare: serviceResponse.clientCommunication.aftercare,
      maintenance: serviceResponse.clientCommunication.maintenance,
      estimatedResults: serviceResponse.formula.expectedResult
    };

    // Format viability assessment
    const viabilityAssessment = {
      achievable: serviceResponse.viabilityAssessment.achievable,
      confidence: serviceResponse.viabilityAssessment.confidence,
      sessionCount: serviceResponse.viabilityAssessment.sessionCount,
      timeEstimate: serviceResponse.viabilityAssessment.timeEstimate,
      costEstimate: userRole !== 'client' ? serviceResponse.viabilityAssessment.costEstimate : undefined,
      riskLevel: serviceResponse.viabilityAssessment.riskLevel
    };

    return {
      success: true,
      data: {
        primaryFormula,
        viabilityAssessment,
        alternativeOptions,
        professionalGuidance,
        clientInformation
      },
      metadata: {
        requestId,
        timestamp: new Date().toISOString(),
        version: '1.0',
        processingTime: totalProcessingTime,
        cached: serviceResponse.metadata.cached
      }
    };
  }

  /**
   * Format formula data based on user role and detail level
   */
  private formatFormula(
    formula: any,
    userRole: string,
    detailedInstructions: boolean = false
  ): any {
    if (userRole === 'client') {
      return {
        technique: this.simplifyTechniqueName(formula.technique),
        processingTime: formula.processingTime,
        applicationMethod: this.simplifyApplicationMethod(formula.applicationMethod),
        ingredients: this.formatIngredientsForClient(formula.formula),
        instructions: this.simplifyInstructions(formula.stepByStep),
        expectedResult: formula.expectedResult,
        confidence: Math.round(formula.confidence || 0.8 * 100) // Convert to percentage
      };
    }

    // For stylists and managers, provide full technical details
    return {
      technique: formula.technique,
      processingTime: formula.processingTime,
      applicationMethod: formula.applicationMethod,
      ingredients: {
        base: formula.formula.base,
        additives: formula.formula.additives || [],
        developer: formula.formula.developer,
        mixingRatio: formula.formula.mixingRatio
      },
      instructions: detailedInstructions ? 
        formula.stepByStep : 
        this.condenseInstructions(formula.stepByStep),
      expectedResult: formula.expectedResult,
      confidence: formula.confidence || 0.8,
      warnings: formula.warnings || [],
      technicalNotes: this.extractTechnicalNotes(formula)
    };
  }

  /**
   * Generate a unique request ID
   */
  private generateRequestId(): string {
    return `formula_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Helper methods for response formatting

  private simplifyTechniqueName(technique: string): string {
    const techniqueMap: Record<string, string> = {
      'foil_highlight': 'Foil Highlights',
      'balayage': 'Balayage',
      'full_color': 'All-Over Color',
      'root_touch_up': 'Root Touch-Up',
      'color_correction': 'Color Correction',
      'bleach_and_tone': 'Lighten and Tone'
    };

    return techniqueMap[technique] || technique;
  }

  private simplifyApplicationMethod(method: string): string {
    return method.replace(/technical/gi, '').replace(/professional/gi, '').trim();
  }

  private formatIngredientsForClient(formula: any): any {
    return {
      mainColor: formula.base,
      activator: `${formula.developer.volume} Volume Developer`,
      amount: formula.developer.amount,
      mixingRatio: formula.mixingRatio,
      additives: formula.additives?.map((additive: string) => 
        additive.replace(/technical/gi, '').trim()
      ) || []
    };
  }

  private simplifyInstructions(instructions: string[]): string[] {
    return instructions.map(instruction => 
      instruction
        .replace(/technical/gi, '')
        .replace(/professional/gi, '')
        .replace(/precisely/gi, 'carefully')
        .trim()
    ).filter(instruction => instruction.length > 0);
  }

  private condenseInstructions(instructions: string[]): string[] {
    // For professionals, condense but keep technical accuracy
    const condensed: string[] = [];
    let currentStep = '';
    
    instructions.forEach(instruction => {
      if (instruction.toLowerCase().includes('section') || 
          instruction.toLowerCase().includes('apply') ||
          instruction.toLowerCase().includes('process') ||
          instruction.toLowerCase().includes('rinse')) {
        if (currentStep) condensed.push(currentStep);
        currentStep = instruction;
      } else {
        currentStep += ` ${instruction}`;
      }
    });
    
    if (currentStep) condensed.push(currentStep);
    
    return condensed.length > 0 ? condensed : instructions;
  }

  private translateExpertiseLevel(level: string): string {
    const levelMap: Record<string, string> = {
      'junior': 'Entry Level',
      'senior': 'Senior Colorist',
      'master': 'Master Colorist'
    };

    return levelMap[level] || level;
  }

  private extractSafetyNotes(serviceResponse: FormulationResponse): string[] {
    const safetyNotes: string[] = [];
    
    if (serviceResponse.formula.warnings) {
      safetyNotes.push(...serviceResponse.formula.warnings);
    }

    if (serviceResponse.viabilityAssessment.riskLevel === 'high' || 
        serviceResponse.viabilityAssessment.riskLevel === 'critical') {
      safetyNotes.push('High-risk service - proceed with caution');
    }

    if (serviceResponse.professionalGuidance.required) {
      safetyNotes.push('Professional consultation required');
    }

    return safetyNotes;
  }

  private extractTechnicalNotes(formula: any): string[] {
    const notes: string[] = [];
    
    if (formula.processingTime > 45) {
      notes.push('Extended processing time - monitor closely');
    }

    if (formula.formula.developer.volume > 30) {
      notes.push('High volume developer - use with caution');
    }

    if (formula.formula.additives?.length > 2) {
      notes.push('Multiple additives - verify compatibility');
    }

    return notes;
  }

  private adaptExplanationForRole(explanation: string, userRole: string): string {
    if (userRole === 'client') {
      return explanation
        .replace(/technical/gi, '')
        .replace(/professional/gi, '')
        .replace(/colorist/gi, 'stylist')
        .trim();
    }

    return explanation;
  }
}