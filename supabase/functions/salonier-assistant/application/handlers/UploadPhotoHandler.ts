/**
 * Upload Photo Handler
 * 
 * HTTP request handler for photo upload operations.
 * Manages file validation, processing, storage, and response formatting
 * for hair diagnosis and desired look images.
 */

import { RequestMapper } from '../utils/RequestMapper.ts';
import { ResponseFormatter } from '../utils/ResponseFormatter.ts';
import { ErrorHandler } from '../utils/ErrorHandler.ts';
import { ValidationHelper } from '../utils/ValidationHelper.ts';
import { ILogger } from '../../services/interfaces.ts';

export interface UploadPhotoRequestBody {
  file?: {
    name: string;
    type: string;
    size: number;
    content: string; // base64
  };
  imageBase64?: string;
  purpose: 'diagnosis' | 'desired_look' | 'result' | 'before_after';
  clientId?: string;
  serviceId?: string;
  metadata?: {
    description?: string;
    tags?: string[];
    location?: string;
    timestamp?: string;
  };
}

export interface UploadPhotoResponse {
  success: boolean;
  data?: {
    uploadId: string;
    url: string;
    thumbnailUrl?: string;
    metadata: {
      filename: string;
      size: number;
      type: string;
      purpose: string;
      uploadedAt: string;
      processedAt?: string;
    };
    processedImage?: {
      dimensions: {
        width: number;
        height: number;
      };
      optimized: boolean;
      compressionRatio?: number;
    };
  };
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata: {
    requestId: string;
    timestamp: string;
    processingTime: number;
  };
}

export class UploadPhotoHandler {
  constructor(
    private requestMapper: RequestMapper,
    private responseFormatter: ResponseFormatter,
    private errorHandler: ErrorHandler,
    private validationHelper: ValidationHelper,
    private logger: ILogger
  ) {}

  /**
   * Handle HTTP request for photo upload
   */
  async handle(request: Request): Promise<Response> {
    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      this.logger.info('Processing photo upload request', {
        requestId,
        method: request.method,
        url: request.url
      });

      // Validate HTTP method
      if (request.method !== 'POST') {
        return this.errorHandler.handleMethodNotAllowed(['POST']);
      }

      // Extract authentication and context
      const authContext = await this.requestMapper.extractAuthContext(request);
      if (!authContext.success) {
        return this.errorHandler.handleAuthError(authContext.errors?.[0] || 'Authentication failed');
      }

      // Parse request body
      const parseResult = await this.parseRequestBody(request);
      if (!parseResult.success) {
        return this.errorHandler.handleValidationError(
          'Invalid request body',
          parseResult.errors
        );
      }

      const requestBody = parseResult.data!;

      // Validate file upload
      const validationResult = this.validateUploadRequest(requestBody);
      if (!validationResult.valid) {
        return this.errorHandler.handleValidationError(
          'Upload validation failed',
          validationResult.errors.map(e => e.message)
        );
      }

      // Process the upload
      const uploadResult = await this.processUpload(
        requestBody,
        authContext.data!,
        requestId
      );

      if (!uploadResult.success) {
        return this.errorHandler.handleServiceError(
          uploadResult.error || 'Upload processing failed'
        );
      }

      // Format response
      const response = this.formatUploadResponse(
        uploadResult.data!,
        requestId,
        Date.now() - startTime
      );

      this.logger.info('Photo upload completed successfully', {
        requestId,
        uploadId: uploadResult.data!.uploadId,
        purpose: requestBody.purpose,
        processingTime: Date.now() - startTime
      });

      return this.responseFormatter.success(response, {
        requestId,
        processingTime: Date.now() - startTime
      });

    } catch (error) {
      this.logger.error('Upload handler failed with exception', {
        requestId,
        error
      });

      return this.errorHandler.handleInternalError(
        error instanceof Error ? error.message : 'Unknown error',
        requestId
      );
    }
  }

  /**
   * Parse and validate request body
   */
  private async parseRequestBody(request: Request): Promise<{
    success: boolean;
    data?: UploadPhotoRequestBody;
    errors?: string[];
  }> {
    try {
      const contentType = request.headers.get('content-type') || '';
      
      if (contentType.includes('multipart/form-data')) {
        return await this.parseMultipartForm(request);
      } else if (contentType.includes('application/json')) {
        return await this.parseJsonBody(request);
      } else {
        return {
          success: false,
          errors: ['Content-Type must be application/json or multipart/form-data']
        };
      }

    } catch (error) {
      return {
        success: false,
        errors: ['Failed to parse request body']
      };
    }
  }

  /**
   * Parse multipart form data
   */
  private async parseMultipartForm(request: Request): Promise<{
    success: boolean;
    data?: UploadPhotoRequestBody;
    errors?: string[];
  }> {
    try {
      const formData = await request.formData();
      const errors: string[] = [];
      
      const file = formData.get('file') as File;
      const purpose = formData.get('purpose') as string;
      const clientId = formData.get('clientId') as string;
      const serviceId = formData.get('serviceId') as string;
      
      if (!file) {
        errors.push('File is required');
      }

      if (!purpose) {
        errors.push('Purpose is required');
      }

      if (errors.length > 0) {
        return { success: false, errors };
      }

      // Convert file to base64
      const arrayBuffer = await file.arrayBuffer();
      const base64 = this.arrayBufferToBase64(arrayBuffer);
      const base64WithMime = `data:${file.type};base64,${base64}`;

      const requestBody: UploadPhotoRequestBody = {
        file: {
          name: file.name,
          type: file.type,
          size: file.size,
          content: base64WithMime
        },
        purpose: purpose as any,
        clientId: clientId || undefined,
        serviceId: serviceId || undefined,
        metadata: {
          description: formData.get('description') as string || undefined,
          tags: formData.get('tags')?.toString().split(',').map(t => t.trim()).filter(Boolean),
          location: formData.get('location') as string || undefined,
          timestamp: new Date().toISOString()
        }
      };

      return { success: true, data: requestBody };

    } catch (error) {
      return {
        success: false,
        errors: ['Failed to parse multipart form data']
      };
    }
  }

  /**
   * Parse JSON body
   */
  private async parseJsonBody(request: Request): Promise<{
    success: boolean;
    data?: UploadPhotoRequestBody;
    errors?: string[];
  }> {
    try {
      const body = await request.json();
      const errors: string[] = [];

      if (!body.imageBase64 && !body.file) {
        errors.push('Either imageBase64 or file is required');
      }

      if (!body.purpose) {
        errors.push('Purpose is required');
      }

      const validPurposes = ['diagnosis', 'desired_look', 'result', 'before_after'];
      if (body.purpose && !validPurposes.includes(body.purpose)) {
        errors.push(`Purpose must be one of: ${validPurposes.join(', ')}`);
      }

      if (body.file) {
        if (typeof body.file !== 'object') {
          errors.push('File must be an object');
        } else {
          if (!body.file.name || !body.file.type || !body.file.content) {
            errors.push('File object must have name, type, and content properties');
          }
        }
      }

      if (errors.length > 0) {
        return { success: false, errors };
      }

      return { success: true, data: body };

    } catch (error) {
      return {
        success: false,
        errors: ['Invalid JSON in request body']
      };
    }
  }

  /**
   * Validate upload request
   */
  private validateUploadRequest(requestBody: UploadPhotoRequestBody): {
    valid: boolean;
    errors: Array<{ field: string; message: string; code: string }>;
  } {
    const errors: Array<{ field: string; message: string; code: string }> = [];

    // Validate purpose
    const validPurposes = ['diagnosis', 'desired_look', 'result', 'before_after'];
    if (!validPurposes.includes(requestBody.purpose)) {
      errors.push({
        field: 'purpose',
        message: `Purpose must be one of: ${validPurposes.join(', ')}`,
        code: 'INVALID_PURPOSE'
      });
    }

    // Validate file or base64
    if (requestBody.file) {
      const fileValidation = this.validationHelper.validateFileUpload({
        name: requestBody.file.name,
        type: requestBody.file.type,
        size: requestBody.file.size,
        content: requestBody.file.content
      });

      errors.push(...fileValidation.errors);
    } else if (requestBody.imageBase64) {
      const base64Regex = /^data:image\/(jpeg|jpg|png|webp);base64,/;
      if (!base64Regex.test(requestBody.imageBase64)) {
        errors.push({
          field: 'imageBase64',
          message: 'Invalid base64 image format',
          code: 'INVALID_BASE64_FORMAT'
        });
      }

      // Check approximate size
      const sizeEstimate = (requestBody.imageBase64.length * 3) / 4;
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (sizeEstimate > maxSize) {
        errors.push({
          field: 'imageBase64',
          message: `Image too large (estimated ${Math.round(sizeEstimate / 1024 / 1024)}MB, max 10MB)`,
          code: 'IMAGE_TOO_LARGE'
        });
      }
    }

    // Validate metadata if present
    if (requestBody.metadata?.tags && !Array.isArray(requestBody.metadata.tags)) {
      errors.push({
        field: 'metadata.tags',
        message: 'Tags must be an array',
        code: 'INVALID_TAGS_FORMAT'
      });
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Process the upload
   */
  private async processUpload(
    requestBody: UploadPhotoRequestBody,
    authContext: { salonId: string; userId: string; userRole: string },
    requestId: string
  ): Promise<{
    success: boolean;
    data?: {
      uploadId: string;
      url: string;
      thumbnailUrl?: string;
      metadata: any;
      processedImage?: any;
    };
    error?: string;
  }> {
    try {
      const uploadId = this.generateUploadId();
      
      // Extract image data
      let imageData: string;
      let metadata: any;

      if (requestBody.file) {
        imageData = requestBody.file.content;
        metadata = {
          filename: requestBody.file.name,
          size: requestBody.file.size,
          type: requestBody.file.type,
          purpose: requestBody.purpose,
          uploadedAt: new Date().toISOString()
        };
      } else {
        imageData = requestBody.imageBase64!;
        const sizeEstimate = (imageData.length * 3) / 4;
        metadata = {
          filename: `upload_${uploadId}.jpg`,
          size: sizeEstimate,
          type: 'image/jpeg',
          purpose: requestBody.purpose,
          uploadedAt: new Date().toISOString()
        };
      }

      // Process image (in a real implementation, this would involve actual image processing)
      const processedImage = await this.processImage(imageData, requestBody.purpose);
      
      // Generate URLs (in a real implementation, these would be actual storage URLs)
      const baseUrl = 'https://storage.salonier.com';
      const url = `${baseUrl}/uploads/${authContext.salonId}/${uploadId}.jpg`;
      const thumbnailUrl = `${baseUrl}/uploads/${authContext.salonId}/thumbnails/${uploadId}.jpg`;

      // Store metadata (in a real implementation, this would be stored in a database)
      const uploadMetadata = {
        ...metadata,
        salonId: authContext.salonId,
        userId: authContext.userId,
        clientId: requestBody.clientId,
        serviceId: requestBody.serviceId,
        purpose: requestBody.purpose,
        tags: requestBody.metadata?.tags || [],
        description: requestBody.metadata?.description,
        location: requestBody.metadata?.location,
        processedAt: new Date().toISOString()
      };

      this.logger.info('Photo processed successfully', {
        uploadId,
        purpose: requestBody.purpose,
        salonId: authContext.salonId,
        originalSize: metadata.size,
        optimizedSize: processedImage.size
      });

      return {
        success: true,
        data: {
          uploadId,
          url,
          thumbnailUrl,
          metadata: uploadMetadata,
          processedImage
        }
      };

    } catch (error) {
      this.logger.error('Upload processing failed', {
        error,
        requestId,
        purpose: requestBody.purpose
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload processing failed'
      };
    }
  }

  /**
   * Process image for optimization
   */
  private async processImage(imageData: string, purpose: string): Promise<{
    dimensions: { width: number; height: number };
    optimized: boolean;
    compressionRatio: number;
    size: number;
  }> {
    // In a real implementation, this would use actual image processing libraries
    // For now, we'll simulate the processing
    
    const originalSize = (imageData.length * 3) / 4;
    
    // Simulate different processing based on purpose
    const processingConfigs = {
      diagnosis: { maxWidth: 1024, quality: 0.8 },
      desired_look: { maxWidth: 1024, quality: 0.85 },
      result: { maxWidth: 1200, quality: 0.9 },
      before_after: { maxWidth: 1200, quality: 0.9 }
    };

    const config = processingConfigs[purpose as keyof typeof processingConfigs] || processingConfigs.diagnosis;
    
    // Simulate compression
    const compressionRatio = config.quality;
    const optimizedSize = Math.round(originalSize * compressionRatio);
    
    return {
      dimensions: {
        width: config.maxWidth,
        height: Math.round(config.maxWidth * 0.75) // Assume 4:3 aspect ratio
      },
      optimized: true,
      compressionRatio,
      size: optimizedSize
    };
  }

  /**
   * Format upload response
   */
  private formatUploadResponse(
    uploadData: any,
    requestId: string,
    processingTime: number
  ): UploadPhotoResponse {
    return {
      success: true,
      data: {
        uploadId: uploadData.uploadId,
        url: uploadData.url,
        thumbnailUrl: uploadData.thumbnailUrl,
        metadata: {
          filename: uploadData.metadata.filename,
          size: uploadData.metadata.size,
          type: uploadData.metadata.type,
          purpose: uploadData.metadata.purpose,
          uploadedAt: uploadData.metadata.uploadedAt,
          processedAt: uploadData.metadata.processedAt
        },
        processedImage: uploadData.processedImage
      },
      metadata: {
        requestId,
        timestamp: new Date().toISOString(),
        processingTime
      }
    };
  }

  /**
   * Generate unique request ID
   */
  private generateRequestId(): string {
    return `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate unique upload ID
   */
  private generateUploadId(): string {
    return `img_${Date.now()}_${Math.random().toString(36).substr(2, 12)}`;
  }

  /**
   * Convert ArrayBuffer to base64
   */
  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  }
}